2025-07-26 00:00:01,407 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:00:01,426 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:00:01,427 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:00:31,427 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:00:31,427 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:00:31,427 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:01:01,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:01:01,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:01:01,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:01:31,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:01:31,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:01:31,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:02:01,428 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:02:01,429 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:02:01,429 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:02:31,429 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:02:31,429 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:02:31,429 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:03:01,430 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:03:01,430 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:03:01,430 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:03:31,430 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:03:31,430 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:03:31,430 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:04:01,431 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:04:01,431 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:04:01,431 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:04:31,431 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:04:31,432 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:04:31,432 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:05:01,432 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:05:01,432 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:05:01,432 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:05:31,432 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:05:31,433 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:05:31,433 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:06:01,433 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:06:01,434 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:06:01,434 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:06:31,434 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:06:31,434 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:06:31,434 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:07:01,435 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:07:01,435 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:07:01,435 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:07:31,435 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:07:31,435 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:07:31,435 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:08:01,436 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:08:01,436 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:08:01,436 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:08:31,436 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:08:31,436 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:08:31,436 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:09:01,437 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:09:01,437 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:09:01,437 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:09:31,437 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:09:31,437 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:09:31,437 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:10:01,438 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:10:01,438 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:10:01,438 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:10:05,059 DEBUG com.zaxxer.hikari.pool.PoolBase [HikariPool-1 connection closer] HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@4e214b86: (connection has passed maxLifetime)
2025-07-26 00:10:05,079 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 connection adder] HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@ac7b1bc
2025-07-26 00:10:31,438 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:10:31,439 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:10:31,439 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:10:41,307 DEBUG com.zaxxer.hikari.pool.PoolBase [HikariPool-1 connection closer] HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2e000dbe: (connection has passed maxLifetime)
2025-07-26 00:10:41,315 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 connection adder] HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@73528a6e
2025-07-26 00:11:01,439 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:11:01,440 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:11:01,440 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:11:31,440 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:11:31,441 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:11:31,441 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:12:01,441 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:12:01,441 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:12:01,442 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:12:31,442 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:12:31,442 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:12:31,442 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:13:01,442 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:13:01,442 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:13:01,443 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:13:31,443 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:13:31,443 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:13:31,443 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:14:01,443 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:14:01,443 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:14:01,444 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:14:31,444 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:14:31,444 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:14:31,444 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:15:01,444 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:15:01,444 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:15:01,445 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:15:31,445 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:15:31,445 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:15:31,446 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:16:01,446 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:16:01,446 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:16:01,446 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:16:31,446 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:16:31,447 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:16:31,447 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:17:01,447 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:17:01,447 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:17:01,447 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:17:31,448 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:17:31,448 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:17:31,448 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:18:01,448 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:18:01,449 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:18:01,449 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:18:31,449 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:18:31,450 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:18:31,450 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:19:01,450 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:19:01,450 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:19:01,450 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:19:31,451 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:19:31,451 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:19:31,451 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:20:01,451 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:20:01,451 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:20:01,452 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:20:31,452 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:20:31,452 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:20:31,452 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:21:01,452 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:21:01,453 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:21:01,453 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:21:31,453 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:21:31,453 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:21:31,453 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:22:01,453 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:22:01,454 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:22:01,454 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:22:31,454 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:22:31,454 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:22:31,454 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:23:01,454 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:23:01,455 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:23:01,455 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:23:31,455 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:23:31,455 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:23:31,455 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:24:01,455 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:24:01,456 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:24:01,456 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:24:31,456 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:24:31,457 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:24:31,457 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:25:01,457 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:25:01,457 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:25:01,457 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:25:31,457 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:25:31,458 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:25:31,458 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:26:01,458 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:26:01,458 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:26:01,458 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:26:31,459 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:26:31,459 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:26:31,459 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:27:01,459 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:27:01,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:27:01,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:27:31,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:27:31,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:27:31,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:28:01,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:28:01,460 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:28:01,461 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:28:31,461 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:28:31,461 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:28:31,462 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:29:01,462 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:29:01,462 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:29:01,462 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:29:31,462 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:29:31,462 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:29:31,463 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:30:01,463 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:30:01,463 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:30:01,463 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:30:31,463 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:30:31,463 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:30:31,464 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:31:01,464 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:31:01,464 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:31:01,464 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:31:31,464 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:31:31,465 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:31:31,465 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:32:01,465 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:32:01,465 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:32:01,465 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:32:31,466 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:32:31,466 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:32:31,466 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:33:01,466 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:33:01,466 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:33:01,466 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:33:31,467 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:33:31,467 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:33:31,468 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:34:01,468 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:34:01,468 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:34:01,468 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:34:31,469 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:34:31,469 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:34:31,469 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:35:01,470 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:35:01,470 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:35:01,470 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:35:31,471 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:35:31,471 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:35:31,471 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:36:01,471 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:36:01,471 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:36:01,472 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:36:31,472 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:36:31,472 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:36:31,472 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:37:01,472 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:37:01,472 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:37:01,473 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:37:31,473 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:37:31,473 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:37:31,474 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:38:01,474 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:38:01,474 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:38:01,474 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:38:31,474 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:38:31,475 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:38:31,475 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:39:01,475 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:39:01,475 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:39:01,475 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:39:31,475 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:39:31,476 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:39:31,476 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:40:01,476 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:40:01,476 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:40:01,476 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:40:01,596 DEBUG com.zaxxer.hikari.pool.PoolBase [HikariPool-1 connection closer] HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@ac7b1bc: (connection has passed maxLifetime)
2025-07-26 00:40:01,604 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 connection adder] HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@453947d3
2025-07-26 00:40:25,927 DEBUG com.zaxxer.hikari.pool.PoolBase [HikariPool-1 connection closer] HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@73528a6e: (connection has passed maxLifetime)
2025-07-26 00:40:25,965 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 connection adder] HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6907d1fe
2025-07-26 00:40:31,477 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:40:31,477 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:40:31,477 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:41:01,478 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:41:01,478 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:41:01,478 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:41:31,478 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:41:31,478 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:41:31,478 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:42:01,479 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:42:01,479 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:42:01,479 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:42:31,480 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:42:31,480 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:42:31,480 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:43:01,480 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:43:01,480 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:43:01,480 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:43:31,481 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:43:31,481 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:43:31,481 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:44:01,481 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:44:01,482 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:44:01,482 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:44:31,482 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:44:31,482 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:44:31,482 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:45:01,483 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:45:01,483 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:45:01,483 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:45:31,483 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:45:31,483 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:45:31,483 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:46:01,484 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:46:01,484 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:46:01,484 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:46:31,485 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:46:31,485 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:46:31,485 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:47:01,485 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:47:01,485 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:47:01,485 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:47:31,486 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:47:31,487 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:47:31,487 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:48:01,487 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:48:01,487 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:48:01,488 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:48:31,488 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:48:31,488 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:48:31,488 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:49:01,489 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:49:01,489 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:49:01,489 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:49:31,489 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:49:31,489 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:49:31,489 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:50:01,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:50:01,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:50:01,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:50:31,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:50:31,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:50:31,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:51:01,490 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:51:01,491 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:51:01,491 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:51:31,491 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:51:31,491 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:51:31,491 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:52:01,491 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:52:01,492 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:52:01,492 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:52:31,492 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:52:31,492 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:52:31,492 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:53:01,493 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:53:01,493 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:53:01,493 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:53:31,493 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:53:31,494 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:53:31,494 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:54:01,494 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:54:01,494 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:54:01,494 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:54:31,495 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:54:31,495 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:54:31,495 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:55:01,495 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:55:01,495 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:55:01,496 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:55:31,496 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:55:31,496 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:55:31,496 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:56:01,496 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:56:01,496 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:56:01,497 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:56:31,497 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:56:31,497 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:56:31,497 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:57:01,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:57:01,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:57:01,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:57:31,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:57:31,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:57:31,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:58:01,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:58:01,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:58:01,498 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:58:31,499 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:58:31,499 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:58:31,499 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:59:01,499 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:59:01,499 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:59:01,499 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 00:59:31,500 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:59:31,500 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 00:59:31,500 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:00:01,500 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:00:01,500 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:00:01,500 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:00:31,501 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:00:31,501 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:00:31,501 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:01:01,501 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:01:01,501 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:01:01,501 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:01:31,502 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:01:31,502 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:01:31,502 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:02:01,502 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:02:01,502 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:02:01,502 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:02:31,503 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:02:31,503 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:02:31,503 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:03:01,503 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:03:01,504 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:03:01,504 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:03:31,504 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:03:31,505 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:03:31,505 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:04:01,505 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:04:01,506 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:04:01,506 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:04:31,506 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:04:31,506 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:04:31,506 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:05:01,506 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:05:01,507 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:05:01,507 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:05:31,507 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:05:31,507 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:05:31,507 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:06:01,507 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:06:01,508 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:06:01,508 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:06:31,509 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:06:31,509 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:06:31,509 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:07:01,509 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:07:01,510 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:07:01,510 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:07:31,510 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Before cleanup stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:07:31,510 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - After cleanup  stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:07:31,510 DEBUG com.zaxxer.hikari.pool.HikariPool [HikariPool-1 housekeeper] HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-07-26 01:07:44,293 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager is shutting down
2025-07-26 01:07:44,306 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager shut down
2025-07-26 01:07:44,334 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager is shutting down
2025-07-26 01:07:44,334 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager shut down
2025-07-26 01:07:44,347 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager is shutting down
2025-07-26 01:07:44,349 DEBUG org.apache.http.impl.conn.DefaultManagedHttpClientConnection [SpringApplicationShutdownHook] http-outgoing-3: Close connection
2025-07-26 01:07:44,352 DEBUG org.apache.http.impl.conn.DefaultManagedHttpClientConnection [SpringApplicationShutdownHook] http-outgoing-2: Close connection
2025-07-26 01:07:44,353 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager shut down
2025-07-26 01:07:44,354 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager is shutting down
2025-07-26 01:07:44,354 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager shut down
2025-07-26 01:07:44,356 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager is shutting down
2025-07-26 01:07:44,356 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager shut down
2025-07-26 01:07:44,358 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager is shutting down
2025-07-26 01:07:44,358 DEBUG org.apache.http.impl.conn.PoolingHttpClientConnectionManager [SpringApplicationShutdownHook] Connection manager shut down
2025-07-26 01:07:44,364 INFO org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-26 01:07:44,372 DEBUG org.hibernate.internal.SessionFactoryImpl [SpringApplicationShutdownHook] HHH000031: Closing
2025-07-26 01:07:44,404 DEBUG org.hibernate.type.spi.TypeConfiguration$Scope [SpringApplicationShutdownHook] Un-scoping TypeConfiguration [org.hibernate.type.spi.TypeConfiguration$Scope@67c41c2b] from SessionFactory [org.hibernate.internal.SessionFactoryImpl@407873d3]
2025-07-26 01:07:44,414 DEBUG org.hibernate.service.internal.AbstractServiceRegistryImpl [SpringApplicationShutdownHook] Implicitly destroying ServiceRegistry on de-registration of all child ServiceRegistries
2025-07-26 01:07:44,427 DEBUG org.hibernate.boot.registry.internal.BootstrapServiceRegistryImpl [SpringApplicationShutdownHook] Implicitly destroying Boot-strap registry on de-registration of all child ServiceRegistries
2025-07-26 01:07:44,430 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-07-26 01:07:44,430 DEBUG com.zaxxer.hikari.pool.HikariPool [SpringApplicationShutdownHook] HikariPool-1 - Before shutdown stats (total=2, active=0, idle=2, waiting=0)
2025-07-26 01:07:44,438 DEBUG com.zaxxer.hikari.pool.PoolBase [HikariPool-1 connection closer] HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@453947d3: (connection evicted)
2025-07-26 01:07:44,439 DEBUG com.zaxxer.hikari.pool.PoolBase [HikariPool-1 connection closer] HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@6907d1fe: (connection evicted)
2025-07-26 01:07:44,440 DEBUG com.zaxxer.hikari.pool.HikariPool [SpringApplicationShutdownHook] HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-07-26 01:07:44,440 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
