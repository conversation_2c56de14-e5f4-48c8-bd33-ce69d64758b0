host.full.dns.auth.link=http://oauthserver.example.com:8088
app.client.id=test-client
app.client.secret=clientSecret
auth.server.schem=https
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER


app.swagger.module=Azvasa LMS: User-Service
app.swagger.termofurl=https://dev-lms.azvasa.online/
app.swagger.contact.name=Azvasa Learning Management System
app.swagger.contact.url=https://dev-lms.azvasa.online/
app.swagger.contact.email=<EMAIL>
app.swagger.license.type=Open-Source
app.swagger.license.url=https://dev-lms.azvasa.online/
app.swagger.version=2.6.73
