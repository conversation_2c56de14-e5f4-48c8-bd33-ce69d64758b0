
spring.datasource.url=*************************************************
spring.datasource.username= postgres
spring.datasource.password= postgres

spring.datasource.hikari.minimumIdle: 2
spring.datasource.hikari.maximumPoolSize: 15
spring.datasource.hikari.idleTimeout: 120000
spring.datasource.hikari.connectionTimeout: 300000
spring.datasource.hikari.leakDetectionThreshold: 300000

spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation= true
spring.jpa.properties.hibernate.dialect = com.lms.userservice.config.PostgreSQL10JsonDialect
spring.jpa.hibernate.ddl-auto = update

server.port=9001
spring.application.name:USER-SERVICE


jwt.secret=ultra-secure-and-ultra-long-secret-lms
jwt.expirationDateInMs=8600000
jwt.refreshExpirationDateInMs=8600000
jwt.maxIdleTimeoutInMs=30000

#logging levels
logging.level.org.springframework.boot.autoconfigure=ERROR
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.type.descriptor.sql=trace

# HTTP encoding (HttpEncodingProperties)
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.force=true
#server.tomcat.uri-encoding=UTF-8

api.user.service.url=http://localhost:9001
api.notification.url=http://localhost:9002
api.fileupload.url=http://localhost:9003
api.master.service.url=http://localhost:9004
api.content.service.url=http://localhost:9005
api.teacher.service.url=http://localhost:9006
api.student.service.url=http://localhost:9007

# server.ssl.enabled=false
# server.ssl.key-store-type=PKCS12
# server.ssl.key-store=classpath:azvasa_dev_domain.jks
# server.ssl.key-store-password=changeit
# server.ssl.key-alias=server-cert

