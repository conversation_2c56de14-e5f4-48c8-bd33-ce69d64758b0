
#JWT token
malformed.jwt.signature=Malformed JWT, please check the request.

#email/EME/WhatsApp send
share.details.via.mail.failed=Failed to share details via mail.
login.credentials.share.via.mail.failed=Failed to share login credentials via mail.
reset.password.link.sent.failed=Failed to send email for reset-password.
email.send.for.school.faild=Failed to send the email to the school authority.


#common messages
please.provide.madatory.field=Please provide the mandatory data. 
request.handled.success = Request handled successfully.
something.went.wrong = Something went wrong.
mapped.data.not.present = No mapped data present.
invalid.data = Invalid data, please check the request.
are.mandatory.fields = are mandatory fields
update.not.allowed.in.create = Update method is not allowed in create.
convert.to.lower.or.upper = Conversion of String to lower/upper failed.
response.create.failed = Failed to create response.
grade.access.not.provide.user=Given grade is not assigned to this user.
none.active.role.login.denied=Access denied due to the de-active roles, please connect with your admin.
api.is.accessible.for.admin.user=Sorry, no authorization for resource.

enum.listing.success = Successfully listed enumerator.
enum.listing.failed = failed to list enumerator.
specify.application.environment = Please specify the environment of the application
specify.menu=Please specify the menu name.

limit.can.not.be.zero = Please provide limit greater than zero

encrypt.field.mandatory=Encryption field shouldn't be empty.
decrypt.field.mandatory=Decryption field shouldn't be empty.
encrypt.failed=Encryption failed.
decrypt.failed=Decryption failed.

last.modified.time.fetch.success = Successfully get the last modification time.
last.modified.time.fetch.failed = failed to get the last modification time.

school.branch.not.found = The given schools and branches aren't mapped or not exist in the system, please check the data.
internal.server.error=Something went wrong!
update.active.success = Active status updated successfully.
update.active.failed = failed to update active status.
faild.to.check.role=failed to check the roles.

only.admin.users=Admin-Users cannot hold the roles like teacher/student. Please check the request.

checking.for.teach.status.success=Successfully completed the checking for teach-status
checking.for.teach.status.failed=Checking completed with some relation issue among requested data.

checking.for.quiz.release.success=Successfully completed the checking for Quiz release/re-release
checking.for.quiz.release.failed=Checking completed with some relation issue among requested data.

names.extracted.successfully=Name extracted successfully.
names.extract.failed=Name extraction failed.
subject.quiz.not.released=Quiz has not released for this given subject.

institutions.for.assign.assessments.success=Successfully list all the institution for assign assessment.
institutions.for.assign.assessments.failed=Failed to list all the institution for assign assessment.
read.csv.file.failed=Corrupted file, unable to read the data. Please check the file.
csv.validation.failed=Failed to validate the CSV file.
validation.via.regex.failed=Failed to check via regular expression.

csv.upload.success=Successfully upload the CSV.
csv.upload.failed=Failed to upload the CSV. 

#Random messages
field.not.exist = Field not exist.
counts.successful = All counts retrieved successfully
counts.failed = failed to retrieve counts
pecentage.without.scaling.failed=Failed to calculate the percentage w/o scaling.
round.up.half.failed=Failed to round up the data.
bigdecimal.convertion=Failed to convert value.
dob.not.expired.date=Sorry the given date of birth is not an expired date.

#User Message
user.created.successfully = User created successfully
user.create.failed = failed to create the user details
user.not.found = User data not found
user.id.not.found = User id is not found
user.get.all.success = Fetched all the user details
user.get.all.failed = failed to fetch all the user details
user.get.by.id.success = User's details fetched
user.get.by.id.failed = failed to fetch users's details
user.list.failed = failed to fetch users's details
user.list.success = Fetched users's details successfully
user.updated.successfully = Updated user's data successfully
user.update.failed = failed to update the user details
user.already.exist = User already exist
email.sent.successfully = Email sent successfully
email.sent.failed = failed to sent email
password.reset.scucessfully = Password reset successfully
password.reset.failed = failed to reset password
user.role.add.failed = failed to assign role to user.
user.delete.success = User data deleted successfully.
user.delete.failed = failed to delete the user.
user.institute.add.failed = failed to assign institutions to user.
user.institute.return.failed = failed to create the response for users_institutions mapping.
user.role.delete.success = User-role mapping deleted successfully.
user.role.delete.failed = failed to delete the user-role mapping.
user.inst.delete.success = User-institute mapping deleted successfully.
user.inst.delete.failed = failed to delete the user-institute mapping.
user.create.email.failed = failed to send email
user.name.generate.failed = failed to generate user name.
user.mobileNumber.update.success=Successfully mobile number updated.
user.mobileNumber.update.failed=Failed to update the mobile number.
user.check.in.history.created.success=Successfully created the Users check in History.
user.check.in.history.created.failed=Failed to create the Users check in History.
user.not.exist = User unauthorized not exist, please login valid credential.

password.updated.successfully = Updated user's password successfully
password.update.failed = failed to update the user's password

user.otp.send.succes = OTP send successfully.
user.otp.send.failed = failed to send OTP

user.otp.verified.success = OTP verified successfully.
user.otp.verified.failed = failed to verify OTP

user.ids.fetch.success=User ids fetched successfully.
user.ids.fetch.failed=failed to fetch user ids.
user.details.fetch.success=User Details fetched successfully.
user.details.fetch.failed=failed to fetch user Details.

user.details.share.success=Successfully shared the user details.
user.details.share.failed=Failed to shared the user details.

user.count.success=Successfully fetched the Coordinator, Principal, Teacher and Student count's.
user.count.failed=Failed to get the Coordinator, Principal, Teacher, Student count's.
user.use.count.failed=Failed to calculate the count WebUser, MobUser and Both web and mob user count. 
user.check.in.history.total.count.failed=Failed to calculate the total count of user login medium.
user.check.in.history.total.count.success=Successfully calculated the total count of user login medium.

user.not.valid=User should be School ADMIN/Management ADMIN/Super ADMIN or ADMIN user.

user.name.success=Successfully fetched name of Users by user_names.
user.name.failed=Failed to fetch the name of Users by user_names.

#Authentication or Authorization
username.mandatory = userName is mandatory.
token.mandatory = Token is mandatory.
user.access=User access denied
user.invalid.username=Invalid user name or password
user.token = Token not valid
user.authenticated.scucessfully = User authenticated successfully
user.authentication.failed = User authentication failed
token.validate.scucessfully = Token validate successfully
token.validate.failed = Token validation failed
malformed.Jwt = Malformed JWT token.
access.denied = User access denied.

#Student module
school.name.mandatory = School name is mandatory

student.created.successfully = Student created successfully
student.create.failed = failed to create the student details

student.self.registered.scucessfully = Student self registered successfully
student.self.registration.failed = Student self registration failed

student.not.found = Student data not found
student.id.not.found = Student id is not found

all.students.fetched.successfully = Fetched all the students detail successfully
failed.to.fetch.all.students = failed to fetch all the students details

student.details.fetched.successfully = Student's detail fetched successfully
student.details.fetch.failed = failed to fetch student's details

student.details.updated.successfully = Updated student's data successfully
student.details.update.failed = failed to update the student's details

student.already.exist = Student already exist

students.detail.filtered.successfully = Students data filtered successfully
failed.to.filter.students.detail = failed to filtered the details of students

student.deleted.successfully = Student's details deleted successfully
student.delete.failed = failed to delete the student's details

student.mapped.with.grade.success = Students mapped to Grade fetched successfully
student.mapped.with.grade.failed = failed to fetch Students mapped to grade

student.mapped.with.section.success = Students mapped to Section fetched successfully
student.mapped.with.section.failed = failed to fetch Students mapped to section

students.mappings.by.language.fetch.success = Students mapped to language fetched successfully
students.mappings.by.language.fetch.failed = failed to fetch students mapped to language

students.mappings.by.student.category.fetch.success = Students mapped to student category fetched successfully
students.mappings.by.student.category.fetch.failed = failed to fetch students mapped to student category

students.user.ids.fetch.success = User ids fetched successfully.
students.user.ids.fetch.failed = failed to fetch user ids.

student.profile.id.not.found=Student id is not found
student.profile.details.updated.successfully = Updated student's year end data successfully
student.profile.details.updated.failed = failed to update the student's year end data details
student.profile.not.found=Student profile data not found
student.year.end.process.and.same.year = Year end process and same year cannot be same, please check the request.
student.grade.section.mapping.found = This Grade-Section has relation with student.

student.user.id.listing.success=Listing the user id for student success.
student.user.id.listing.failed=Listing the user id for student failed.

student.count.success=Successfully fetched the student count.
student.count.failed=Failed to get the student count.

student.profile.updated.successfully= Updated student's profile successfully
student.profile.updated.failed= failed to update the student's profile

student.userid.from.different.schools.success=Successfully fetched the user_id of students from different schools.
student.userid.from.different.schools.failed=Failed to fetch the user_id of students from different schools.
student.user.name.details.success = student details fetched successfully.
student.user.name.details.failed = student not exists, by this user name.

student.absentees.details.success=Successfully found the absentees details.
student.absentees.details.failed=Failed to find the absentees details.

#Token Module
token.create.success = Token created successfully
token.create.failed = Token creation failed

get.all.token.success = All tokens fetched successfully
get.all.token.failed = failed to fetch all the tokens

get.token.success = Token fetched successfully
get.token.failed = failed to fetch the token

token.updated.successfully = Token updated successfully
token.update.failed = Token update failed

token.not.found = Token not found
token.already.exist = Token already exist
token.id.not.found = Token id not found

delete.token.successfully = Token deleted successfully
delete.token.failed = failed to delete the token

number.of.users = please enter number of users
token.gen.failed = token id generation failed

token.update.with.user.success = user successfully mapped with the token
token.update.with.user.failed = failed to map the user with token

token.usage.limit.exceeded = token usage limit exceeded.

token.expired = Token has expired

used.user.id.listed.success = Successfully fetched all the user id.
used.user.id.listed.failed = failed to fetched all the user id.

#Branch constant messages
branch.get.all.success = Branches list fetched successfully
branch.get.all.failed = failed to fetch the Branches list
branch.get.by.id.success = Branch details fetched successfully
branch.get.by.id.failed = failed to fetch the Branch details
branch.create.success = Branch created successfully
branch.create.failed = failed to create Branch
branch.update.success = Branch updated successfully
branch.update.failed = failed to update Branch
branch.delete.success = Branch deleted successfully
branch.delete.failed = failed to delete Branch
branch.not.found = Branch not found
branch.id.not.found = No such Branch ID found
branch.already.exist = Branch already exists
branch.get.all.plans.success = Retrieved all plans of branch successfully
branch.get.all.plans.failed = failed to retrieve plans of branch
get.all.branches.mapped.with.plans.success = Branches mapped to plan fetched successfully
get.all.branches.mapped.with.plans.failed = failed to fetch Branches mapped to plan
delete.all.branches.mapped.with.plans.success = Branch Mapping for the plan deleted successfully
delete.all.branches.mapped.with.plans.failed = failed to delete branch Mapping for the plan
update.all.branches.mapped.with.plans.success = Branch Mapping for the plan updated successfully
update.all.branches.mapped.with.plans.failed = failed to update branch Mapping for the plan
branch.plan.already.exist = Same branch plan already exists
branch.school.relation=There is no relation between this school and branch, please check the request.
branch.get.max-quiz-release.success=Successfully fetched maximum limit of quiz re-release.
branch.get.max-quiz-release.failed=failed to fetch maximum limit of quiz re-release.
branch.activate.parent.not.active=To activate the branch, school must be activate.
branch.deactivate.has.active.mappings=To deactivate the branch child grade, section must be deactivate.
branch.top.5.retrieve.success=Successfully retrieved the top 5 branches based on the no of students.
branch.top.5.retrieve.failed=Failed to retrieve the top 5 branches based on the no of students.
branch.communication.create.success = Branch Communication created successfully
branch.communication.create.failed = failed to create Branch Communication
branch.communication.sms.email.failed = either SMS or EMAIL must be true
branch.communication.enum.failed = your provided communicationAction not proper

#Teachers constant messages
teacher.get.all.success = Teachers list fetched successfully
teacher.get.list.failed = failed to fetch the Teachers list
teacher.get.by.id.success =	Teacher details fetched successfully
teacher.get.by.id.failed = failed to fetch the Teacher details
teacher.create.success = Teacher created successfully
teacher.create.failed = failed to create Teacher
teacher.update.success = Teacher updated successfully
teacher.update.failed =	failed to update Teacher
teacher.delete.success = Teacher deleted successfully
teacher.delete.failed =	failed to delete Teacher
teacher.not.found =	Teacher not found
teacher.id.not.found = No such Teacher ID found
teacher.already.exist =	Teacher already exists
academic.staff.already.exist =	Academic staff already exists with this same email or mobile.
id.invalid = Invalid ID
id.error = Unable to get ID details
teacher.self.registration.success = Teacher self registered successfully
teacher.self.registration.failed = failed to self register Teacher
teacher.check.profile = Wrong profile set for the staff, give the coordinator type if the profile is COORDINATOR
teacher.profile.set.failed = failed to set academic staff's profile.
teacher.check.role = Wrong role set for the staff, use the matching role as TEACHER
teacher.token.not.exist = No such token exist
teacher.coordinator.type.not.exist = No such coordinator type exists
teacher.coordinator.type.mapping.fetch.success = Successfully fetched teacher mappings for coordinator type
teacher.coordinator.type.mapping.fetch.failed = failed to fetch teacher mappings for coordinator type
teacher.assigned.subjects.success = Added all assigned subjects for teacher successfully
teacher.assigned.subjects.failed = failed to add all assigned subjects for teacher
fetch.teacher.assigned.subjects.success = Fetched all assigned subjects for teacher successfully
fetch.teacher.assigned.subjects.failed = failed to fetch all assigned subjects for teacher
teacher.section.subTopic.already.exit = section, subTopic already assign to particular teacher
teacher.section.already.exit = section already assign to particular teacher
teacher.subTopic.already.exit = subTopic already assign to particular teacher
teacher.grade.subject.already.exit = grade, subject already assign to particular teacher
edit.teacher.access.subjects.success = Edited assign subjects for teacher successfully
edit.teacher.access.subjects.failed = failed to edit assigned subjects for teacher
teacher.not.exist = Teacher unauthorized not exist, please provide teacherId. 
teacher.quiz.attempt.average.failed=Failed to calculate the Quiz attempt and Quiz average.
teacher.subject.subtopicwise.calculate.failed=Failed to calculate the quiz attempt and average subject-wise and subtopic-wise.
teacher.ember.student.fetch.failed=Failed to fetch the ember student list and their percentage.

teaching.acess.list.success=Successfully fetched all teacher-access by teacher id.
teaching.acess.list.failed=Failed to fetch all teacher-access by teacher id.
teacher.grade.wise.quiz.performance.failed=Failed to fetch the teacher grade wise quiz performance.
teacher.grade.wise.quiz.performance.success=Successfully fetched the teacher chapter wise quiz performance.
teacher.chapter.wise.quiz.performance.failed=Failed to fetch the teacher chapter wise quiz performance.
teacher.chapter.wise.quiz.performance.success=Successfully fetched the teacher grade wise quiz performance
teacher.subject.quiz.performance.failed=Failed calculate the subject wise quiz performance.
teacher.grade.section.subject.not.assigned=For this teacher there is no assigned grade, subject and section.
teacher.not.valid=This is not valid for coordinator and principal, Please provide the valid teacher.

assign.teacher.id.not.exist = The assign teacher mapping id does not exist
toggle.active.teacher.assigned.subjects.success = Toggled active field of  for teacher assignment successfully
toggle.active.teacher.assigned.subjects.failed = failed to Toggled active field of  for teacher assignment
fetch.teacher.student.count.success = Fetched student count successfully
fetch.teacher.student.count.failed = failed to fetch student count
fetch.teacher.quiz.count.success = Fetched quiz count successfully
fetch.teacher.quiz.count.failed = failed to fetch quiz count
fetch.teacher.names.success = Fetched teacher names successfully
fetch.teacher.names.failed = failed to fetch teacher names
teacher.assigned.grade.fetch.success= Successfully fetch assigned grades.
teacher.assigned.grade.fetch.failed=Failed to fetch assigned grades.
teacher.access.checking=There is not such access given for this academic staff.
teacher.not.belong=This academic staff is not belong in the given school or branch.
teacher.assigned.section.or.grade.listing.success=successfully assigned grade and section. 
teacher.assigned.section.or.grade.listing.failed=failed to list assigned grade and section.
fetch.teacher.grade.section.student.count.failed=failed to fetch grade, section, student, assigned subject,teacher and coordinator count.
fetch.principal.students.counted.success= Fetched all students count for principal successfully
fetch.principal.students.counted.failed= failed to fetch all students count for principal
fetch.teacher.chapter.quizzes.count.success= Fetched teacher chapters and quizzes count for coordinator successfully.
fetch.teacher.chapter.quizzes.count.failed= failed to fetch assigned subject,teacher and coordinator count.
fetch.student.level.failed=Failed to fetch student percentage and score range.
fetch.teacher.student.level.success=Successfully fetched score range with student percentage.
fetch.teacher.student.level.failed=Failed to fetch score range with student percentage.
teacher.principal.type.not.allowed=Please do not pass principal id.
fetch.teacher.student.details.failed=Failed to fetch the assigned student details for this teacher.
fetch.teacher.student.details.success=Successfully fetched the assigned student details for this teacher.
fetch.principal.grades.students.counted.success= Fetched all grades and students count for principal successfully
fetch.principal.grades.students.counted.failed= failed to fetch all all grades and students count for principal
fetch.principal.grade.wise.performance.success= Fetched grade wise quiz performance for principal, coordinator successfully
fetch.principal.grade.wise.performance.failed= failed to fetch grade wise quiz performance for principal, coordinator
fetch.principal.grade.detailed.performance.success= Fetched grade detailed performance for principal, coordinator successfully
fetch.principal.grade.detailed.performance.failed= failed to fetch grade detailed performance for principal, coordinator

teacher.list.access.success=List all the teacher access successfully
teacher.list.access.failed=List all the teacher access failed
teacher.formative.assessment.failed=Failed to fetch the Formative Assessment of the teacher.
teacher.formative.assessment.success=Successfully fetched the Formative Assessment of the teacher.
teacher.quiz.attempt.average.failed=Failed to calculate the quiz attempt and quiz average.

fetch.teacher.chapter.percentage.success= Fetched teacher chapters percentage for principal successfully.
fetch.teacher.chapter.percentage.failed= failed to fetch chapters percentage principal.
fetch.teacher.student.count.failed=failed to fetch the teacher student count.
fetch.teacher.student.count.success=Successfully fetched the teacher student count.
student.percentage.average.failed= Failed to calculate average percentage of the students.

chapter.completed.quiz.release.success=Successfully find the count of Chapter completed Vs Quiz Released.
chapter.completed.quiz.release.failed=Failed to find the count of Chapter completed Vs Quiz Released.
chapter.not.ended.by.teacher=No any chapter ended By this teacher.

student.unit.quiz.performance.failed= Failed to calculate unit quiz performance of the assigned students.
student.unit.quiz.performance.success= Successfully calculated unit quiz performance of the assigned students.

teachers.listing.for.dashboard.success=Successfully teachers name listed.
teachers.listing.for.dashboard.failed=Failed to list teachers name.
teachers.resource.reserver=This resource is reserved only for Academic staffs.
not.reserverd.for.teacher=Sorry, this resource is not reserved for teachers. 


#From CSV
students.created.successfully = Students created successfully
students.create.failed = failed to create the students details

users.created.successfully = Users created from CSV file successfully
users.create.failed = failed to create the users from CSV file
 	
address.mandatory = Please at least add one address details.

#School Messages
school.create.success = School created successfully
school.create.failed = failed to create the school details

school.not.found = School data not found
school.id.not.found = School id is not found

school.get.all.success = Fetched all the school details
school.get.all.failed = failed to fetch all the school details

school.get.by.id.success = School details fetched successfully
school.get.by.id.failed = failed to fetch school details

school.update.success = Updated school data successfully
school.update.failed = failed to update the school details

school.delete.success = School deleted successfully
school.delete.failed =	failed to delete School

school.already.exist = School already exist
school.bad.request = Locality and Plan Id are required, if there's no Branch of a School

school.mapped.with.boards.data.success = School mapped to board fetched successfully
school.mapped.with.boards.data.failed = failed to fetch Schools mapped to board

school.branch.mapped.with.city.success = School/Branch mapped to city fetched successfully
school.branch.mapped.with.city.failed = failed to fetch Schools/Branches mapped to city

school.branch.mapped.with.board.success = School/Branch mapped to board fetched successfully
school.branch.mapped.with.board.failed = failed to fetch Schools/Branches mapped to board

school.branch.board.not.found = failed to list the board from branch of a related school. 
school.count.username.mandatory=If login as Super-Admin or Management please provide the user-name.
school.deactivate.has.active.branches=school cannot be deactivated because it has active branches.
school.branch.get.by.id.success = school and branch details fetched successfully.
school.branch.get.by.id.failed = failed to fetch school and branch details.
school.details.success=Successfully fetched school details.
school.details.failed=Failed to fetch school details.
school.branches.details.success = school's and branches details fetched successfully.
school.branches.details.failed = failed to fetch school's and branches details.
school.branch.details.failed.msg = Only super-admin can get this details.

#Administration
admin.get.all.success = Administration list fetched successfully
admin.get.all.failed = failed to fetch the administration list

admin.get.by.id.success =	Administration details fetched successfully
admin.get.by.id.failed = failed to fetch the administration details

admin.create.success = Administration created successfully
admin.create.failed = failed to create administration

admin.update.success = Administration updated successfully
admin.update.failed =	failed to update administration

admin.delete.success = Administration deleted successfully
admin.delete.failed =	failed to administration Branches

admin.not.found =	administration not found
admin.id.not.found = No such administration ID found
admin.already.exist = Administration already exists
admin.type.allowed.value=Administration type either SCHOOL_ADMIN or MANAGEMENT, please check your request.

#section data (Enumerator)
section.data.get.all.success = All section data listed successfully!
section.data.get.all.failed = All section data listing failed!
section.data.invalid = Please give valid section data!

#grade data (Enumerator)
grade.get.all.success = All grade data listed successfully!
grade.get.all.failed = All grade data listing failed!
grade.data.invalid = Please give valid grade data!

#grade section mapping gs.mapping
gs.mapping.create.success = Grade & Section mapping created successfully
gs.mapping.create.failed = failed to create grade & section mapping

gs.mapping.update.success = Grade & Section mapping updated successfully
gs.mapping.update.failed =	failed to update grade & section mapping

gs.mapping.get.all.success = Grade & Section mapping list fetched successfully
gs.mapping.get.all.failed = failed to fetch the grade & section mapping list

gs.mapping.get.by.id.success =	Grade & Section mapping details fetched successfully
gs.mapping.get.by.id.failed = failed to fetch the grade & section mapping details

gs.mapping.delete.success = Grade & Section mapping deleted successfully
gs.mapping.delete.failed =	failed to grade & section mapping Branches

gs.mapping.not.found =	Grade & Section mapping not found
gs.mapping.id.not.found = No such grade & section mapping ID found
gs.mapping.already.exist = Grade & Section mapping already exists

gs.mapping.checking.failed = Checking of grade & section mapping failed
gs.mapping.grade.mandatory = Grade is a mandatory field.
gs.mapping.corrupted.data = Please provide the correct grade-section mapping.
gs.mapping.response.create.failed = failed to create grade-section mapping.
gs.mapping.conflict = This can be cause by either the data is already exist or unable to track the data.

gs.mapped.with.grade.success = Grades fetched successfully.
gs.mapped.with.grade.failed = failed to fetch list of grades.

gs.mapped.with.section.success = Sections fetched successfully.
gs.mapped.with.section.failed = failed to fetch list of sections.
gs.mapping.not.exist = This grade-section mapping could not find in the system. Please re-check your request.

grade.id.not.found = No such Grade ID found
percentage.calulation.failed=Failed to calculate the percentage.

gs.deactivate.has.active.student.teacher=Grade Section cannot be deactivated because it has active students and assigned teachers.

#master-service feign
role.menu.fetch.success = Successfully fetched the role-menu.
role.menu.fetch.failed = failed to fetch the role-menu.

role.menu.fetch.all.success = Successfully fetched all the role-menu.
role.menu.fetch.all.failed = failed to fetch all the role-menu.

master.data.checking.failed = Master data checking failed
board.does.not.exist = Board does not exist
city.does.not.exist = City does not exist
subject.or.subtopic.mapping.do.not.exist = The given subject or subtopics do not exist. Please check the data before proceeding

#Admin-user
admin.user.id.madatory=Id of the admin user is mandatory.

user.get.by.username.and.role.success = User's details fetched
user.get.by.username.and.role.failed = failed to fetch users's details

#Student subject mapping
student.subject.groups.subject.mapping.success=Subject mapped successfully with the student
student.subject.groups.subject.mapping.failed=failed to map subject with the student

student.subject.groups.subject.mapping.fetch.success=Successfully fetched the subjects for this student.
student.subject.groups.subject.mapping.fetch.failed=failed to fetch subjects for this student.

#Role Id count
role.id.count.success=Successfully fetched count for role
role.id.count.failed=failed to fetch count for role

#confirmation API
confirmation.api.success=Confirmation API successfully checked the mapping.
confirmation.api.failed=Confirmation API failed to check.

confirmation.api.permission.denied=No Permission to delete or deactivate, as it has existing mapping in the workflow..!
confirmation.api.toggle.active=This data has no other connections, do you want to deactivate this data?
confirmation.api.delete=This data has no other connections, do you want to delete this data?
confirmation.api.default.message=No mapping found in the workflow.

mapping.found=Mapping found in the user-service.
mapping.not.found=Mapping not exist in the user-service.
mapping.api.failed = failed to find the mapping.

#count APIs for feign clients
count.success=count was successful.
count.failed=failed to count.


# formative overview feign call
fetch.student.section.count.success = Fetched student section count successfully
fetch.student.section.count.failed = Failed to fetch section student count
section.id.not.found=No such Section ID found
fetch.formative.assessment.student.details.failed=Failed to fetch the student under the section.
fetch.teacher.formative.student.details.success=Successfully to fetch the student details.
fetch.teacher.formative.student.details.failed=Failed to fetch the assigned student details.

#Student count
student.count.by.grade.id.success=student count by grade id success
student.count.by.grade.id.failed=student count by grade id failed

# Dash-board
school.branch.overview.success=Successfully get the school-branch over view.
school.branch.overview.failed=Failed to get the school-branch over view.

#Student Name Details
fetch.teacher.student.name.details.failed = Failed to fetch Student Name Details.
student.name.details.success = Successfully fetched Student Name Details. 
student.name.details.failed =  Failed to fetch Student Name Details.

#Misc
kudos.message.config.failed=Failed to provide the correct kudos message.

#Dashboard: Academic Staffs
similar.student.not.found=Unable to track the students studying in the same grades.
global.calculation.grade.wise.failes=Failed to calculate the global averages by grade wise.
global.calculation.chapter.wise.failes=Failed to calculate the global averages by chapter wise.
global.calculate.subject.wise.principal.coordinator.failed=Failed to calculate the global score/attempt subject-wise for principal/coordinator.
global.calculate.grade.wise.principal.coordinator.failed=Failed to calculate the global score/attempt by grade-wise.

#Teacher feign
teacher.details.failed=Failed to get the teacher details
teacher.details.success=Teacher details successful

fetch.blueprint.level.details.success=fetch blueprint level details success
fetch.blueprint.level.details.failed=fetch blueprint level details failed

readingpassport.create.success=ReadingPassport created success
readingpassport.create.failed=ReadingPassport create failed
readingpassport.response.failed = ReadingPassport response failed
readingpassport.retrieved.success=ReadingPassport retrieved success
readingpassport.retrieved.failed=ReadingPassport retrieved failed

user.otp.send.failed.emailOrPhone= User otp send failed emailOrPhone
otp.not.matching= OTP not matching
otp.is.expired= OTP is expired

fetch.test.branches.details.success = fetch.test.branches.details.success
fetch.test.branches.details.failed = fetch.test.branches.details.failed

user.Type.not.found=user Type not found