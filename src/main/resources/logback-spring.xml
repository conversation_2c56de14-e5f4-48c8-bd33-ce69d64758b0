<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    
        <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
        </root>
 

   

        <appender name="FILE-ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>app.log</file>

            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>logs/archived/app.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <!-- each archived file, size max 10MB -->
                <maxFileSize>10MB</maxFileSize>
                <!-- total size of all archive files, if total size > 100MB, it will delete old archived file -->
                <totalSizeCap>100MB</totalSizeCap>
                <!-- 10 days to keep -->
                <maxHistory>10</maxHistory>
            </rollingPolicy>

            <encoder>
                <pattern>%d %p %c{1.} [%t] %m%n</pattern>
            </encoder>
        </appender>

        <logger name="org.springframework" level="INFO"/>
        <logger name="com.good.beneficiary" level="DEBUG"/>
        
       <root level="debug">
            <appender-ref ref="FILE-ROLLING"/>
        </root>
   

</configuration>