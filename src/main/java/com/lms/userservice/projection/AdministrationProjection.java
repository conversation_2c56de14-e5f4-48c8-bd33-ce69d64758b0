package com.lms.userservice.projection;

public interface AdministrationProjection {
	
	public String getId();

	public String getFirstName();

	public String getLastName();

	public String getEmail();

	public String getMobile();

	public String getGender();

	public Boolean getActive();

	public Boolean getDeleted();

	public Long getNumberOfSchools();

	public Long getNumberOfBranches();

	public String getSchoolName();
	
	public String getUserId();

	public String getUserName();
}
