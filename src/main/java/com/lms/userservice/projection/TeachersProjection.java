package com.lms.userservice.projection;

import java.time.LocalDate;
import java.time.LocalDateTime;

public interface TeachersProjection {

	public String getId();

	public String getName();

	public String getFirstName();

	public String getLastName();

	public String getEmail();

	public String getMobile();

	public LocalDate getDob();

	public String getGender();

	public LocalDate getJoinDate();

	public String getPreviousWorkExp();

	public String getAddress();

	public String getDesignation();

	public String getDocumentUrl();

	public String getSchoolId();

	public String getSchool();

	public String getSchoolCode();

	public String getBranchId();

	public String getBranch();
	
	public String getBoardId();

	public String getRoleId();

	public String getRole();

	public String getAcademicStaffProfile();

	public String getCoordinatorTypeId();

	public String getCoordinatorType();

	public String getUserId();

	public String getUserName();

	public LocalDateTime getLastLoginTime();

	public boolean isActive();
	
	public String getGradeId();
	
	public String getSubjectId();

}
