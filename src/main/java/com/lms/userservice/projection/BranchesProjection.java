package com.lms.userservice.projection;

public interface BranchesProjection {

	public String getId();

	public String getName();

	public String getCityId();

	public String getLocality();

	public String getPocEmail();

	public String getPhoneNumber();

	public String getBoardId();

	public String getPlanId();

	public String getLogoUrl();

	public String getSchoolId();

	public String getSchool();

	public Long getNumberOfCoordinators();

	public Long getNumberOfTeachers();

	public Long getNumberOfPrincipals();

	public Long getNumberOfStudents();

	public Boolean getActive();

	public String getCode();

	public String getWebsite();

	public long getBranchCount();

	public long getTotalGrades();

}
