package com.lms.userservice.projection;

import java.time.LocalDate;

public interface StudentsProjection {

	public String getId();

	public String getFirstName();

	public String getLastName();

	public String getEmail();

	public String getMobile();

	public LocalDate getDob();

	public String getGender();

	public String getDocumentUrl();

	public String getFirstLanguageId();

	public String getSecondLanguageId();

	public LocalDate getAdmissionDate();

	public String getStudentCategoryId();

	public String getGradeId();

	public String getSectionId();

	public String getBranchId();

	public String getBranchName();

	public String getSchoolId();

	public String getSchoolName();

	public String getSchoolCode();
	
	public String getBoardId();

	public Boolean getIsPromoted();

	public Boolean getYearEndProcess();

	public String getAddress();

	public String getUserName();

	public String getUserId();

	public String getLastLoginTime();

	public boolean isActive();

	public String getLearningGeneration();
	
	public String getGeographicalType();
	
	public String getGradeLevel() ;
	
	public String getIrStatus();
	
	public Boolean getDiagnosticTest();

}
