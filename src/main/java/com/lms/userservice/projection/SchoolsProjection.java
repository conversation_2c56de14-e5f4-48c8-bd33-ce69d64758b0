package com.lms.userservice.projection;

/**
 * The Schools Projection interface
 * <p>
 * Used for retrieving specific columns from the DB
 * </p>
 */
public interface SchoolsProjection {

	public String getId();

	public String getName();

	public String getCode();

	public String getCityId();

	public String getCityName();

	public String getPocEmail();

	public String getPhoneNumber();

	public String getWebsite();

	public String getSignatoryName();

	public String getSignatoryRole();

	public String getLogoUrl();

	public Long getNumberOfBranches();

	public Long getNumberOfCoordinators();

	public Long getNumberOfTeachers();

	public Long getNumberOfPrincipals();

	public Long getNumberOfStudents();

	public Boolean getActive();
	
	public Long getCreatedAt();
	
	public String getCreatedOn();
	
	public String getModifiedOn();
	
	public String getCreatedBy();
	
	public String getModifiedBy();
	
	public String getCreateOrModifiedBy();
}
