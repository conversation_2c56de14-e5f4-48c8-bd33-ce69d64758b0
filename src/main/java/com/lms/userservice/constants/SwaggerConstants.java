package com.lms.userservice.constants;

/**
 * <AUTHOR>
 *
 */
public class SwaggerConstants {
	
	
	/**
	 * Swagger Constants
	 *
	 */
    
    public static final String SWAGGER_READ = "read";
    public static final String SWAGGER_TRUST = "trust";
    public static final String SWAGGER_WRITE = "write";
    
    public static final String SWAGGER_READ_ALL = "read all";
    public static final String SWAGGER_TRUST_ALL = "trust all";
    public static final String SWAGGER_WRITE_ALL = "access all";
    
    public static final String SWAGGER_OAUTH = "oauth2schema";
    public static final String SWAGGER_HEADER = "header";
    public static final String SWAGGER_SECURITY_REFRENCE = "JWT";
    public static final String SWAGGER_AUTH = "Authorization";
    public static final String SWAGGER_SCOPE_TYPE = "Authorization";
    public static final String SWAGGER_SCOPE_VALUE = "Authorization";

}
