package com.lms.userservice.config;

import java.io.InputStream;
import java.security.KeyStore;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;

import feign.Client;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.codec.ErrorDecoder;
import feign.httpclient.ApacheHttpClient;

/**
 * <AUTHOR> Processes an {@link FiegnConfiguration} request.
 */
@Configuration
public class FiegnConfiguration {

	private PoolingHttpClientConnectionManager poolingConnectionManager;

//
	@Bean
	public ErrorDecoder errorDecoder() {
		return new CustomErrorDecoder();
	}
//
//	/**
//	 * Processes an {@link feignLoggerLevel} request.
//	 */
//
	@Bean
	public Logger.Level feignLoggerLevel() {
		return Logger.Level.FULL;
	}
//
//	/**
//	 * This class is similar to {@code RequestInterceptor.intercept()}, except that
//	 * the implementation can read, remove, or otherwise mutate any part of the
//	 * request template.
//	 */
	@Bean
	public RequestInterceptor FeignRequestInterceptor() {
		return new FeignClientInterceptor();
	}


//
//	@Bean
//	public PoolingHttpClientConnectionManager poolingHttpClientConnectionManager() {
//		 connectionManager = new PoolingHttpClientConnectionManager();
//		connectionManager.setMaxTotal(300);
//		connectionManager.setDefaultMaxPerRoute(40);
//		return connectionManager;
//	}
//
//	@Bean
//	@Primary
//	public CloseableHttpClient httpClient(PoolingHttpClientConnectionManager connectionManager) {
//		return HttpClients.custom().setConnectionManager(connectionManager).build();
//	}
//
//	@Bean
//	public ApacheHttpClient apacheHttpClient(CloseableHttpClient httpClient) {
//		return new ApacheHttpClient(httpClient);
//	}
//	
///*	@Bean
//	public RestTemplateTransportClientFactory restTemplateTransportClientFactory(
//			ApacheHttpClientFactory httpClientFactory) {
//		return new RestTemplateTransportClientFactory(httpClientFactory);
//	}
//
//	@Bean
//	public RestTemplate restTemplate() {
//		return new RestTemplate(
//				newHttpComponentsClientHttpRequestFactory(httpClient(poolingHttpClientConnectionManager())));
//	}*/
//
//	@PreDestroy
//	public void close() {
//		if (connectionManager != null) {
//			connectionManager.shutdown();
//		}
//	}

//	@Bean
//    public Client feignClient(okhttp3.OkHttpClient okHttpClient) {
//        return new OkHttpClient(okHttpClient);
//    }

	@Bean
	public PoolingHttpClientConnectionManager poolingConnectionManager() {
		poolingConnectionManager = new PoolingHttpClientConnectionManager();
		poolingConnectionManager.setMaxTotal(4000);
		poolingConnectionManager.setDefaultMaxPerRoute(1000);
		return poolingConnectionManager;
	}

	@Bean
	@Primary
	public CloseableHttpClient httpClient(PoolingHttpClientConnectionManager poolingConnectionManager) throws Exception {
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
		
		   
			 String trustStorePassword = "azvasa123";

			ClassPathResource resource = new ClassPathResource("azvasa.online.jks");

	 
			 KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
        try (InputStream trustStoreStream = resource.getInputStream()) {
            trustStore.load(trustStoreStream, trustStorePassword.toCharArray());
        }

        // Initialize SSL context with the custom trust store
        SSLContext sslContext = SSLContextBuilder.create()
                .loadTrustMaterial(trustStore, null)
                .build();
	 

	 
			 SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
			
		return HttpClients.custom().setSSLSocketFactory(socketFactory).setConnectionManager(poolingConnectionManager)
				.setDefaultRequestConfig(requestConfig).build();
	}

	@Bean
	public Client feignClient(CloseableHttpClient httpClient) {
		return new ApacheHttpClient(httpClient);
	}

	// @Bean
	// public ApacheHttpClient apacheHttpClient(CloseableHttpClient httpClient) {
	// 	return new ApacheHttpClient(httpClient);
	// }

	@Bean
	public Request.Options requestOptions() {
		return new Request.Options(5, TimeUnit.SECONDS, 60, TimeUnit.SECONDS, true);
	}

//    @Bean
//    public Request.Options options() {
//        return new Request.Options(5000, 30000); // Timeout settings
//    }
}
