package com.lms.userservice.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.BeanIds;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configurers.userdetails.DaoAuthenticationConfigurer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.lms.userservice.filter.JwtFilter;
import com.lms.userservice.service.impl.AuthServiceImpl;

/**
 * <AUTHOR> Processes an {@link SecurityConfig} request.
 */

@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

	/**
	 * Authentication Service Implemented Configuration BUild
	 *
	 */
	@Autowired
	private AuthServiceImpl authService;

	/**
	 * JwtFilter doFilterInternal
	 *
	 */
	@Autowired
	private JwtFilter jwtFilter;

	/**
	 * Add authentication based upon the custom {@link UserDetailsService} that is
	 * passed in. It then returns a {@link DaoAuthenticationConfigurer} to allow
	 * customization of the authentication.
	 *
	 * <p>
	 * This method also ensure that the {@link UserDetailsService} is available for
	 * the {@link #getDefaultUserDetailsService()} method. Note that additional
	 * {@link UserDetailsService}'s may override this {@link UserDetailsService} as
	 * the default.
	 * </p>
	 * 
	 * @return a {@link DaoAuthenticationConfigurer} to allow customization of the
	 *         DAO authentication
	 * @throws Exception if an error occurs when adding the
	 *                   {@link UserDetailsService} based authentication
	 */
	@Override
	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
		auth.userDetailsService(authService);
	}

	/**
	 * Encode the raw password. Generally, a good encoding algorithm applies a SHA-1
	 * or greater hash combined with an 8-byte or greater randomly generated salt.
	 */
	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}

	/**
	 * Processes an {@link Authentication} request.
	 *
	 *
	 */
	@Bean(name = BeanIds.AUTHENTICATION_MANAGER)
	@Override
	public AuthenticationManager authenticationManagerBean() throws Exception {
		return super.authenticationManagerBean();
	}

	/**
	 * Configure Access End point in {@link configure}.
	 * ,"/v1/api/user/users/username/**"
	 *
	 */
	@Override
	protected void configure(HttpSecurity http) throws Exception {
		http.csrf().disable().headers().frameOptions().disable().and().authorizeRequests()
				.antMatchers("/v1/api/user/authenticate", "/v1/api/user/authenticate/validatetoken/**",
						"/v1/api/user/users/forgot-password/**", "/v1/api/user/users/reset-password/**",
						"/v1/api/user/users/forgotpasswordMobile/**", "/v1/api/user/users/verify-otp/**",
						"/v1/api/user/users/reset-password-mobile/**", "/v1/api/user/users/extracting-user-name/**",
						"/v1/api/user/users/forgot-password/generate-otp/**","/v1/api/user/users/forgot-password/verify-otp/**","/v1/api/user/users/extracting-user-name-with-user-type/**")
				.permitAll()

				.antMatchers("/v2/api-docs", "/configuration/ui", "/swagger-resources/**", "/configuration/security",
						"/swagger-ui.html", "/webjars/**")
				.permitAll().anyRequest().authenticated().and().cors().and().exceptionHandling().and()
				.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
		http.addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class);
	}
}
