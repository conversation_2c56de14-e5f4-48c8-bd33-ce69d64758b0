package com.lms.userservice.config;

import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.userservice.component.Translator;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.model.LMSResponse;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomErrorDecoder implements ErrorDecoder {

	@Autowired
	private ObjectMapper objectMapper;

	public Exception decode(String methodKey, Response response) {
		try {
			InputStream content = response.body().asInputStream();
			int status = response.status();
			if (status != -1 && content != null) {
				LMSResponse errorResponse = objectMapper.readValue(content, LMSResponse.class);
				if (status == HttpStatus.NOT_FOUND.value()) {
					throw new USException(ErrorCodes.NOT_FOUND, errorResponse.getMessage());
				} else if (status == HttpStatus.FORBIDDEN.value()) {
					throw new USException(ErrorCodes.ACCESS_DENIED, errorResponse.getMessage());
				} else if (status == HttpStatus.UNAUTHORIZED.value()) {
					throw new USException(ErrorCodes.UNAUTHORIZED, errorResponse.getMessage());
				} else if (status == HttpStatus.BAD_REQUEST.value()) {
					throw new USException(ErrorCodes.BAD_REQUEST, errorResponse.getMessage());
				} else if (status == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
					throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
				} else if (status == HttpStatus.CONFLICT.value()) {
					throw new USException(ErrorCodes.CONFLICT, errorResponse.getMessage());
				} else {
					throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
				}
			}
		} catch (IOException ioe) {
			log.error(ExceptionUtils.getStackTrace(ioe));
		}
		throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("internal.server.error", null));
	}
}
