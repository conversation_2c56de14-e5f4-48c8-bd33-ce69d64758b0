package com.lms.userservice.config;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import feign.Client;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Target;

/**
 * <AUTHOR> Processes an {@link FeignClientInterceptor} request.
 */

public class FeignClientInterceptor implements RequestInterceptor {

	private static final String AUTHORIZATION_HEADER = "Authorization";

	@Override

	/**
	 * Zero or more {@code RequestInterceptors} may be configured for purposes such
	 * as adding headers to all requests. No guarantees are given with regards to
	 * the order that interceptors are applied. Once interceptors are applied,
	 * {@link Target#apply(RequestTemplate)} is called to create the immutable http
	 * request sent via {@link Client#execute(Request, feign.Request.Options)}.
	 */

	public void apply(RequestTemplate template) {

		/**
		 * Request Builder for an HTTP Target.
		 * <p>
		 * This class is a variation on a UriTemplate, where, in addition to the uri,
		 * Headers and Query information also support template expressions.
		 * </p>
		 */

		ServletRequestAttributes attributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
		if (attributes != null) {
			HttpServletRequest request = attributes.getRequest();
			String userToken = request.getHeader(AUTHORIZATION_HEADER);
			template.header(AUTHORIZATION_HEADER, userToken);
		}
	}

}
