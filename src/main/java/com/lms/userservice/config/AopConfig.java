package com.lms.userservice.config;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.EmailRequestDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.util.EncryptionAndDecryption;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Noor | April 21 2022
 */
@Aspect
@Configuration
@Slf4j
public class AopConfig {

	@Autowired
	ModelMapper modelMapper;

	@Autowired
	UsersRepository userRepository;

	@Autowired
	private NotificationFeignClient notificationClient;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	/**
	 * {@code pointcut} - This method is for every method in the controller package
	 * that starts with create and ends with AndSendEmail This method makes a call
	 * to the notification service to send an email after a student/teacher is
	 * created
	 * 
	 * @param jp
	 * @param response
	 */
	@SuppressWarnings("rawtypes")
	// @AfterReturning(pointcut = "execution(* com.lms.userservice.controller.*.create*AndSendEmail(..))", returning = "response")
	public void sendEmailAfterAdvice(JoinPoint jp, LMSResponse response) {
		Object dataResponse = response.getData();

		CreateUserEmailRequestDto requestDto = modelMapper.map(dataResponse, CreateUserEmailRequestDto.class);

		// send user-name and password after create the user.
		if (requestDto.getTypeOfEmailSend().equals("CREATE") || requestDto.getTypeOfEmailSend().equals("UPDATE")) {
			String userName = requestDto.getUserName();
			Users users = userRepository.findByUserName(userName);
			requestDto.setToEmail(users.getEmail());
			String userId = EncryptionAndDecryption.encrypt(requestDto.getUserId());
			String resetPasswordUrl = mastersFeignClient.getResetPasswordWithBaseUrl(requestDto.getLmsEnv()).getData();
			requestDto.setForgotPasswordEmailLink(resetPasswordUrl + "?userId=" + userId);
			requestDto.setBaseFEUrl(resetPasswordUrl.substring(0, resetPasswordUrl.indexOf("#")));
			log.info("Sending email started...");

			try {
				if (requestDto.getToEmail() != null && !requestDto.getToEmail().isBlank()
						&& !requestDto.getToEmail().isEmpty()) {
					if (requestDto.getTypeOfEmailSend().equals("CREATE"))
						notificationClient.userCreated(requestDto);
					else
						notificationClient.editUser(requestDto);
				}
			} catch (Exception e) {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("user.create.email.failed", null));
			}
		}

		// Share-Details via mail
		if (requestDto.getTypeOfEmailSend().equals("SHARE")) {
			ShareDetailsResponseDto responseDto = modelMapper.map(dataResponse, ShareDetailsResponseDto.class);
			try {
				log.info("Sharing the user-details via email started...");
				notificationClient.shareDetails(responseDto);
			} catch (Exception e) {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("share.details.via.mail.failed", null));
			}
		}

		// Share login credentials after update-password by Super-Admin
		if (requestDto.getTypeOfEmailSend().equals("UPDATE_PASS_BY_ADMIN")) {
			try {
				log.info("Sharing the updated login credentials via email started...");
				notificationClient.updatePassword(requestDto);
			} catch (Exception e) {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("login.credentials.share.via.mail.failed", null));
			}
		}

		// Send reset password link if user request for forgot-password
		if (requestDto.getTypeOfEmailSend().equals("FORGOT_PASS")) {
			try {
				log.info("Sedning resent password link via email started...");
				EmailRequestDto emailRequestDto = modelMapper.map(dataResponse, EmailRequestDto.class);
				notificationClient.forgotPassword(emailRequestDto);
			} catch (Exception e) {
				log.error(ExceptionUtils.getStackTrace(e));
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("reset.password.link.sent.failed", null));
			}
		}
	}
}
