package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SchoolsBranchsRequestDto {
	
	@ApiModelProperty(value = "Assigned school", example = "2c91808480d1365d0180d148ee3c0000", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;
	
	@ApiModelProperty(value = "Assigned branch of the school", required = true, position = 2)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private String[] branchesId;
}
