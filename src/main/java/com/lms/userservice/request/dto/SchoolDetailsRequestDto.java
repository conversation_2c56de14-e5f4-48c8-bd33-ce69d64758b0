package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 11-Mar-2022
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchoolDetailsRequestDto {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolCode;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeName; //(1st, 2nd STD etc)
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionName; //division of the class 1A, 1B
	
}
