package com.lms.userservice.request.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class CreateUserEmailRequestDto {
	
	private String userId;
	private String userName;
	private String password;
	private String firstName;
	
	private String toEmail;	
	private String forgotPasswordEmailLink;
	private String baseFEUrl;
	
	private String lmsEnv;
	private String typeOfEmailSend;
	private String roleName;
	private String adminName;
	private String roleNameOfAdmin;
	
	public CreateUserEmailRequestDto(String email, String userName, String firstName) {
		this.toEmail = email;
		this.userName = userName;
		this.firstName = firstName;
	}
}
