package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdministrationRequestDto {

	@ApiModelProperty(value = "First part of the name", example = "<PERSON>", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String firstName;

	@ApiModelProperty(value = "Last part of the name", example = "<PERSON><PERSON>", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String lastName;

	@ApiModelProperty(value = "Any type of email address. Must be unique", example = "<EMAIL>", required = true, position = 3)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String email;

	@ApiModelProperty(value = "Indian mob no is acceptable. Must be unique", example = "7025785634", required = true, position = 4)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String mobile;

	@ApiModelProperty(value = "Gender of the user", allowableValues = "MALE, FEMALE, OTHERS", example = "MALE", required = true, position = 5)
	@NotBlank(message = Constants.IS_EMPTY)
	private String gender;

	@ApiModelProperty(value = "Role of the user", example = "SCHOOL_ADMIN", allowableValues = "SCHOOL_ADMIN, MANAGEMENT", required = true, position = 6)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String role;

	@ApiModelProperty(value = "Assigned school and it's brach", required = true, position = 7)
	@NotNull(message = Constants.IS_EMPTY)
	private List<SchoolsBranchsRequestDto> institutions;

	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", required = true, position = 8)
	private String lmsEnv;
}
