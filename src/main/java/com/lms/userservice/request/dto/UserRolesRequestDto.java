package com.lms.userservice.request.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRolesRequestDto {

	@ApiModelProperty(value = "Role of the user", example = "ff80818180cce35f0180ccf1e6b30005", required = true, position = 1)
	private String roleId;

	@ApiModelProperty(value = "Active status of user-role mapping", example = "true", required = true, position = 2)
	private boolean active;

}