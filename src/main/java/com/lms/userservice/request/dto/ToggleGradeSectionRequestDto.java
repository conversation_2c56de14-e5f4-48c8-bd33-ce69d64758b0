package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ToggleGradeSectionRequestDto {

	@ApiModelProperty(value = "Id of section", example = "ff80818180433e890180433f1db60001", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionId;
        
	@ApiModelProperty(value = "Id of grade", example = "ff80818180608cc50180609164ff0002", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeId;

	@ApiModelProperty(value = "Id of branch", example = "2c91808381043f9c0181064aca71002c", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;

	@ApiModelProperty(value = "Id of school", example = "2c91808480bc79330180c20aa7b60004", required = true, position = 4)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;

	@ApiModelProperty(value = "Toggle status", example = "true", required = true, position = 5)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private boolean active;
}
