package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * The {@code UsersRequestDto} class is used to retrieve from the DB.<br>
 * 
 * {@code @Data} annotation is used to generate <br>
 * <i>Getters, Setters, Parameterized Constructor, toString, equals and HashCode
 * methods</i>
 * 
 * <AUTHOR>
 */

@Data
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UsersResetPasswordDto {

	@ApiModelProperty(value = "Password", example = "ABcd@123!", required = true, position = 1)
	@Pattern(regexp = Constants.PASSWORD_REGEX, message = Constants.PASSWORD_CONTAINS)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String password;

}
