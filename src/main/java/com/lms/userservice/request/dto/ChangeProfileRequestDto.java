package com.lms.userservice.request.dto;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.UniqueElements;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> | Dec 29 2022
 * <AUTHOR> | Jan 04 2023
 *
 */
@Data
public class ChangeProfileRequestDto {
	
	@NotEmpty(message = Constants.IS_EMPTY)
	@UniqueElements(message = Constants.UNIQUE_ELEMENTS)
	@ApiModelProperty(value = "Student's Id list", required = true, position = 1)
	private List<@NotBlank(message = Constants.GIVE_VALID_VALUE) String> students = new ArrayList<>();
	
	@ApiModelProperty(value = "From Section Id", example = "ff80818180433e890180433ef2150000", position = 2)
	private String fromSectionId;
	
	@ApiModelProperty(value = "From Grade Id", example = "2ff80818180ead9320180eaff5b0c0008", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String fromGradeId;
	
	@ApiModelProperty(value = "To Section Id", example = "ff80818180433e890180433ef2150000", position = 4)
	private String toSectionId;
	
	@ApiModelProperty(value = "To Grade Id", example = "2ff80818180ead9320180eaff5b0c0008", required = true, position = 5)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String toGradeId;
	
	@ApiModelProperty(value = "Affilicated board of the branch", example = "ff80818180ead9320180eb06357f0014", required = true, position = 6)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;
	
	@ApiModelProperty(value = "School of the banch", example = "22c91808480bc79330180c20aa7b60004", required = true, position = 7)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;
	
	@ApiModelProperty(value = "Action taken same year", example = "true", position = 8)
	private boolean sameYear;	
	
	@ApiModelProperty(value = "Action taken on current year's end", example = "true", position = 9)
	private boolean yearEndProcess;
	
}
