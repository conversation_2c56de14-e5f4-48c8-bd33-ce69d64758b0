package com.lms.userservice.request.dto;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Data
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SchoolRequestDto {

	@ApiModelProperty(value = "Name of the school", example = "Mathews Mar Athanasius Residential School", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String name;

	@ApiModelProperty(value = "Code of the school. This must be unique", example = "MMARS_001", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String code;

	@ApiModelProperty(value = "Id of the city", example = "ff80818180bc92e90180bc9b3c080009", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String cityId;

	@ApiModelProperty(value = "Email address of the school", example = "<EMAIL>", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String pocEmail;

	@ApiModelProperty(value = "Phone number of the school", example = "8906754376", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String phoneNumber;

	@ApiModelProperty(value = "Web address of the school", example = "http://mmarschoolise.com", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.WEBSITE_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String website;

	@ApiModelProperty(value = "Authorized person of the school", example = "Abraham Joy", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String signatoryName;

	@ApiModelProperty(value = "Authorized person's role", example = "President", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String signatoryRole;

	@ApiModelProperty(value = "Logo of the school", example = "https://s3.ap-south-1.amazonaws.com/lmscontentupload/User-Service/b2da6fc0-b089-473b-ad23-eec27f24333e_Smiley.jpg", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String logoUrl;
	
	@ApiModelProperty(value = "Affilicated board of the school", example = "ff80818180ead9320180eb06357f0014", required = true)
	private String boardId;
	
	@ApiModelProperty(value = "Showing does school has branch", allowableValues = "true,false", example = "true", required = true)
	private boolean hasBranch;
	
	@ApiModelProperty(value = "Locality of the school, provide it incase hasBranch=false", example = "Banglore")
	private String locality;
	
	@ApiModelProperty(value = "Selected plan for the school, provide it incase hasBranch=false")
	private List<BranchPlansRequestDto> planIds = new ArrayList<>();
	
	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", required = true)
	private String lmsEnv;

}
