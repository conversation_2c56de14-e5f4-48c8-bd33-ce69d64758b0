package com.lms.userservice.request.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Common request body for toggle the active status.
 * 
 * <AUTHOR> C Achari
 *
 */
@Data
public class ToggleActiveStatusRequestDto {

	@ApiModelProperty(value = "Id of selected row", example = "ff80818180cce35f0180ccf1e6b30005", required = true, position = 1)
	private String id;

	@ApiModelProperty(value = "Active status of selected row", example = "true", required = true, position = 2)
	private boolean active;
}
