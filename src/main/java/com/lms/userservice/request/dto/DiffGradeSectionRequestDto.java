package com.lms.userservice.request.dto;

import java.util.List;

import org.hibernate.validator.constraints.UniqueElements;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Use this class for if {@link com.lms.userservice.enums.SectionData
 * SectionData} is DIFFERENT_SECTIONS
 * 
 * <AUTHOR> <PERSON>
 * @since 1.0.2
 *
 */
@Data
public class DiffGradeSectionRequestDto {
	
	@ApiModelProperty(value = "Id of the selected grade", example = "ff80818180ead9320180eafe48800004", position = 1)
	private String gradeId;
	
	@ApiModelProperty(value = "Id of the selected sections", position = 2)
	@UniqueElements
	private List<String> sectionId;
}
