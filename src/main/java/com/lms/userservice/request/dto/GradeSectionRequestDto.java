package com.lms.userservice.request.dto;

import java.util.List;

import org.hibernate.validator.constraints.UniqueElements;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Use this class for if {@link com.lms.userservice.enums.SectionData
 * SectionData} is either NO_SECTION or SAME_SECTION
 * 
 * <AUTHOR> <PERSON>
 * @since 1.0.2
 *
 */
@Data
public class GradeSectionRequestDto {
	
	@ApiModelProperty(value = "Id of the selected Grades", position = 1)
	@UniqueElements
	private List<String> gradeId;

	/**
	 * This can be null or contain sectionIds
	 */
	@ApiModelProperty(value = "Id of the selected sections", position = 2)
	@UniqueElements
	private List<String> sectionId;
}
