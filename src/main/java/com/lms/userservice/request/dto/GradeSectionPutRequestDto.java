package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Only for update the grade-sections mapping
 * 
 * <AUTHOR>
 * @since 1.0.2
 *
 */
@Data
public class GradeSectionPutRequestDto {

	@ApiModelProperty(value = "Selected Grade-section mapping Id", example = "ff80818180f659540180f6b4aa250008", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String id;

	@ApiModelProperty(value = "School Id", example = "2c9180878614af6b0186163bf5050069", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;

	@ApiModelProperty(value = "Selected branch", example = "2c9180878614af6b0186163cdfc2006a", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;

	@ApiModelProperty(value = "Radio button value", example = "DIFFERENT_SECTIONS", required = true, position = 4)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionData;

	@ApiModelProperty(value = "Grade Id from selected mapping", example = "2c9180878471c1fd01847426d753008c", required = true, position = 5)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeId;

	@ApiModelProperty(value = "Status need to set", example = "true", required = true, position = 6)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private boolean active;

	@ApiModelProperty(value = "Section-Id to set", example = "2c91808684ba0d1a0184bdc81a4f0005", position = 7)
	private String sectionId;
}
