package com.lms.userservice.request.dto;

import com.lms.userservice.util.Constants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class TeacherAssignRequest {
    @ApiModelProperty(value = "Id of the Grade", example = "402880e7818533860181856446b70003", required = true)
    @NotNull(message = Constants.MANDATORY_FIELD)
    private String gradeId;

    @ApiModelProperty(value = "Ids of the sections")
    private List<String> sectionIds = new ArrayList<>();

    @ApiModelProperty(value = "Id of the Subject", example = "ff8080818161f6630181627739a2000a", required = true)
    @NotNull(message = Constants.MANDATORY_FIELD)
    private String subjectId;

    @ApiModelProperty(value = "Ids of the subtopics")
    private List<String> subtopicIds = new ArrayList<>();
}
