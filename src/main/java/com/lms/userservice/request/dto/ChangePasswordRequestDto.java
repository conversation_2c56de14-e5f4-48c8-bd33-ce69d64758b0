package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Change the password by master/super-admin
 * 
 * <AUTHOR>
 *
 */
@Data
public class ChangePasswordRequestDto {
	
	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "userId of the selected persona", example = "ff80818182daf3150182db094abe000a", position = 1, required = true)
	private String userId;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "userName of the selected persona", example = "Huma.Querasi.MMARS_001", position = 2, required = true)
	private String username;
	
	@ApiModelProperty(value = "Password", example = "ABcd@123!", required = true, position = 3)
	@Pattern(regexp = Constants.PASSWORD_REGEX, message = Constants.PASSWORD_CONTAINS)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String password;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "UI screen name", example = "ADMIN_USERS", allowableValues = "ADMINISTRATION, ACADEMIC_STAFF, ADMIN_USERS, STUDENT", position = 4, required = true)
	private String persona;
	
	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", position = 5)
	private String lmsEnv;
}
