package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateMobileNumberRequestDto {

	@ApiModelProperty(value = "Username of the current user" ,example = "shalaka.kartik001", position = 1)
	@NotBlank (message = Constants.MANDATORY_FIELD)
	private String userName;
	
	@ApiModelProperty(value = "Mobile number of the user" ,example = "9786543210", position = 2)
	@NotBlank (message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Size(min = 10, max = 10, message = Constants.PHONE_NUMBER_LENGTH)
	private String mobileNumber;
	
	@ApiModelProperty(value = "Otp for change mobile number" ,example = "5467", position = 3)
	private String otp;
	
	@ApiModelProperty(value = "Role of the user" ,example = "STUDENT", position = 4)
	@NotBlank (message = Constants.MANDATORY_FIELD)
	private String userRole;
}
