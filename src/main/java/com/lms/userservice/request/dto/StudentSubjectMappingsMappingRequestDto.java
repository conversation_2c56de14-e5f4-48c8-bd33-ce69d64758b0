package com.lms.userservice.request.dto;

import com.lms.userservice.util.Constants;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 *
 * <AUTHOR> <PERSON>
 */
@Data
public class StudentSubjectMappingsMappingRequestDto {

    @ApiModelProperty(example = "2c9180838103f93201810435049d0000")
    private String id;

    @ApiModelProperty(example = "2c9180838103f93201810435049d0000", required = true)
    @NotBlank(message = Constants.MANDATORY_FIELD)
    private String subjectGroupsSubjectMappingsId;
}
