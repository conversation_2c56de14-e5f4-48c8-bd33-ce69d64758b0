package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResetPasswordWithoutTokenRequestDto {

	@ApiModelProperty(value = "User id of user who tried change the password", example = "ff80818185f439d50185f47e1ad20013", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String userId;

	@ApiModelProperty(value = "Password", example = "ABcd@123!", required = true, position = 2)
	@Pattern(regexp = Constants.PASSWORD_REGEX, message = Constants.PASSWORD_CONTAINS)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String password;
}
