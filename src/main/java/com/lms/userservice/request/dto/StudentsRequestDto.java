package com.lms.userservice.request.dto;

import java.time.LocalDate;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 11-Mar-2022
 *
 */
@Data
public class StudentsRequestDto {

	@ApiModelProperty(value = "First name of the user", example = "Maneesh", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String firstName;

	@ApiModelProperty(value = "Last name of the user", example = "Patel", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String lastName;

	@ApiModelProperty(value = "Email address of student", example = "<EMAIL>", required = false, position = 3)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	//@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String email;

	@ApiModelProperty(value = "Mobile number of student", example = "8547890756", required = true, position = 4)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String mobile;

	@ApiModelProperty(value = "Student's date of birth", example = "2009-05-01", required = false, position = 5)
	//@NotNull(message = Constants.MANDATORY_FIELD)
	private LocalDate dob;

	@ApiModelProperty(value = "Gender of the user", allowableValues = "MALE, FEMALE, OTHERS", example = "MALE", required = true, position = 6)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gender;

	@ApiModelProperty(value = "not necessary", hidden = true)
	private String documentUrl;

	/**
	 * PK from master table Languages
	 */
	@ApiModelProperty(value = "Mother tongue/ Native language of the student", example = "ff80818180608cc50180609c95b70009", required = false, position = 7)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private String firstLanguageId;

	@ApiModelProperty(value = "Any bilingual of the student", example = "ff80818180608cc50180609e613b0015", required = false, position = 8)
	private String secondLanguageId;

	@ApiModelProperty(value = "Addmission date of the student", example = "2021-05-15", required = false, position = 9)
	//@NotNull(message = Constants.MANDATORY_FIELD)
	private LocalDate admissionDate;

	/**
	 * PK from master table StudentCategory
	 */
	@ApiModelProperty(value = "Category of the student", example = "ff80818180608cc50180609e613b0015", required = true, position = 10)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private String studentCategoryId;

	/**
	 * PK from master table Grades
	 */
	@ApiModelProperty(value = "Selected grade id", example = "ff80818180608cc50180609e613b0015", required = true, position = 11)
	private String gradeId;

	/**
	 * PK from master table Sections
	 */
	@ApiModelProperty(value = "Selected section id", example = "ff80818180433e890180433ef2150000", required = true, position = 12)
	private String sectionId;

	@ApiModelProperty(value = "Selected branch id", example = "4028818380665dd20180667ee7ee0003", required = true, position = 13)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branch;

	@ApiModelProperty(value = "Selected school id", example = "2c91808480bc79330180c20aa7b60004", required = true, position = 14)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private String school;

	@ApiModelProperty(value = "Address of the student", example = "25PL/TVM, Trivandrum", position = 15)
	private String address;

	/**
	 * Demote = false Promote = true default value null
	 * 
	 */
	@ApiModelProperty(value = "Indicate student promoted or not", example = "true", position = 16)
	private Boolean isPromoted;

	/**
	 * To check the demote and promote happen in the same academic year default
	 * value null
	 */
	@ApiModelProperty(value = "Action taken on current year's end", example = "true", position = 17)
	private Boolean yearEndProcess;

	@ApiModelProperty(value = "Token during self-registration", example = "770-1798-MMARS", position = 18)
	private String token;

	@ApiModelProperty(value = "Indicating the active status of student", example = "true", position = 19)
	private boolean active;

	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", position = 20)
	private String lmsEnv;
	
	@ApiModelProperty(value = "learning generation", example = "true", position = 21)
	private String learningGeneration;
	
	@ApiModelProperty(value = "geographical type", example = "true", position = 22)
	private String geographicalType;
	
	@ApiModelProperty(value = "grade level", example = "true", position = 23)
	private String gradeLevel;
	
}
