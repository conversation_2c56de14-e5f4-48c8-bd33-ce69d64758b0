package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * To find out the absentees list from user-service by providing the school,
 * branch and exam attended student Ids.
 * 
 * <AUTHOR> | 13 Dec 2023
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class AbsenteesRequestDto {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "The selected schoolId", example = "402892888b4e5d01018b5b45b2ed0084", position = 1)
	private String schoolId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "The selected branchId", example = "402892888b4e5d01018b5b4924210087", position = 2)
	private String branchId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "The selected gradeId", example = "402892888b3c48fb018b5713ede80036", position = 3)
	private String gradeId;

	@ApiModelProperty(value = "The selected sectionId", example = "402892888b3c48fb018b5b4d86b30164", position = 4)
	private String sectionId;

	@ApiModelProperty(value = "Exam attended student list", position = 5)
	private List<String> studentIds;
}
