package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdminUsersRequestDto {
	
	@ApiModelProperty(value = "Id of the admin user, fill it during the update process", example = "ff80818180f65df90180f67e409a017e", position = 1)
	private String id;
	
	@ApiModelProperty(value = "First name", example = "Zara", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String firstName;
	
	@ApiModelProperty(value = "Last name", example = "Zakir", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String lastName;
	
	@ApiModelProperty(value = "Email address", example = "<EMAIL>", required = true, position = 4)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.BASIC_EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String email;
	
	@ApiModelProperty(value = "Mobile number", example = "9856784590", required = true, position = 5)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String phoneNumber;

	@ApiModelProperty(value = "Roles decided to assign", required = true, position = 6)
	private List<UserRolesRequestDto> userRoles;
	
	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", required = true, position = 8)
	private String lmsEnv;
}
