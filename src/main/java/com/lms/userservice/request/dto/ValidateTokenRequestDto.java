package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ValidateTokenRequestDto {

	@ApiModelProperty(value = "JWT token to validate.", example = "eyJhbGciOiiJ9.eyJzdWIiOiJESFXzQw.NMAvMTjZnPdxi-RKe6Wb22VC88", position = 1, required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String token;
}
