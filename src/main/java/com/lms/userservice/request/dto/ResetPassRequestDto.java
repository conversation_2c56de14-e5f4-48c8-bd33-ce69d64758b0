package com.lms.userservice.request.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 *  <AUTHOR>
 * The {@code ResetPassRequestDto} class
 * is used to retrieve from the DB.
 * <br> {@code @Data} annotation is used to generate
 * <br>
 * <i>Get<PERSON>, Setters, Parameterized Constructor, toString, equals and HashCode methods</i>
 */
@Data
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ResetPassRequestDto {
	
	String resetlink;
	String toEmail;

}
