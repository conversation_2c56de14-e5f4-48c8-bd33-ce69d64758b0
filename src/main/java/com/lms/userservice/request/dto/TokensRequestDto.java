package com.lms.userservice.request.dto;

import java.time.LocalDate;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TokensRequestDto {
	
	@ApiModelProperty(value = "Role id of either teacher/student", example = "ff80818180cce35f0180ccf1e6b30005", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String roleId;
	
	@ApiModelProperty(value = "No of tokens generate in one POST API call", example = "2", required = true)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private Integer numberOfTokens;
	
	@ApiModelProperty(value = "Expiary date of the tokens", example = "2022-05-31", required = true)
	@NotNull(message = Constants.MANDATORY_FIELD)
	@Future(message = Constants.COMING_DATE)
	private LocalDate expiaryDate;
	
	@ApiModelProperty(value = "Can multiple user access one token", example = "true", required = true)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private boolean multiUser;
	
	/**
	 * {@code numberOfUsersPerToken} means how many users can use one token
	 */
	@ApiModelProperty(value = "If multiUser=true, give number of users per token", example = "3")
	private Integer numberOfUsersPerToken;
	
	@ApiModelProperty(value = "Id of selected school", example = "4028818380665dd20180667ee7ee0003", required = true)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private String schoolId;
	
	@ApiModelProperty(value = "Id of selected branch", example = "4028818380665dd20180667ee7ee0003", required = true)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private String branchId;
}
