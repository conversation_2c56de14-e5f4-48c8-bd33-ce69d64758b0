package com.lms.userservice.request.dto;

import java.time.LocalDate;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeacherRequestDto {

	@ApiModelProperty(value = "First name of the user", example = "Hu<PERSON>", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String firstName;

	@ApiModelProperty(value = "Last name of the user", example = "<PERSON><PERSON><PERSON>", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	@Pattern(regexp = Constants.UNICOD_NAME_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String lastName;

	@ApiModelProperty(value = "Email address, must be unique", example = "<EMAIL>", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String email;

	@ApiModelProperty(value = "Mobile number, must be unique", example = "8945658923", required = true)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Size(min = 10, max = 10, message = Constants.PHONE_NUMBER_LENGTH)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String mobile;

	@ApiModelProperty(value = "Gender of the user", allowableValues = "MALE, FEMALE, OTHERS", example = "FEMALE", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String gender;

	@ApiModelProperty(value = "Teacher's date of birth", example = "1983-02-26")
	private LocalDate dob;

	@ApiModelProperty(value = "Date of joining", example = "2000-01-14")
	private LocalDate joinDate;

	@ApiModelProperty(value = "Work experiance", example = "6 yrs")
	private String previousWorkExp;

	@ApiModelProperty(value = "Designation of the academic staff", example = "Maths Teacher")
	private String designation;

	@ApiModelProperty(value = "not necessary", hidden = true)
	private String documentUrl;

	@ApiModelProperty(value = "Token during slef-registration", example = "770-1798-MMARS")
	private String token;

	@ApiModelProperty(value = "Selected school id", example = "2c91808480bc79330180c20aa7b60004", required = false)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private String school;

	@ApiModelProperty(value = "Selected branch id", example = "4028818380665dd20180667ee7ee0003")
	private String branch;

	@ApiModelProperty(value = "Selected data from radio button", allowableValues = "TEACHER,COORDINATOR,PRINCIPAL", example = "PRINCIPAL", required = false)
	//@NotBlank(message = Constants.MANDATORY_FIELD)
	private String academicStaffProfile;

	@ApiModelProperty(value = "Coodinator type division, fill it if the academicStaffProfile='COORDINATOR'", example = "ff80818180efdf230180efe98cf20000")
	private String coordinatorTypeId;

	@ApiModelProperty(value = "Address of the teacher", example = "25PL/TVM, Trivandrum")
	private String address;
	
	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", position = 8)
	private String lmsEnv;
}
