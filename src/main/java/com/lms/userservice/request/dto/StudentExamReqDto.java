package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StudentExamReqDto {

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	private List<String> studentIds;

	
	private String quizId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeId;

	private String sectionId;
	
	private String accadamicYearId;

	private String boardId;
}
