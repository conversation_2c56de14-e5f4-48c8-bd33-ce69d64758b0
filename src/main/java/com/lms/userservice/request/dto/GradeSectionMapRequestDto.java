package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>
 * @since 1.0.2
 *
 */
@Data
public class GradeSectionMapRequestDto {
	
	@ApiModelProperty(value = "School Id", example = "2c91808480bc79330180c20aa7b60004", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;
	
	@ApiModelProperty(value = "Branch Id", example = "4028818380665dd20180667ee7ee0003", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;
	
	@ApiModelProperty(value = "Radio button values", example = "SAME_SECTION", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionData;
	
	@ApiModelProperty(value = "Same section or not section for grades", position = 4)
	private GradeSectionRequestDto gradeSection;
	
	@ApiModelProperty(value = "Different grades and section", position = 5)
	private List<DiffGradeSectionRequestDto> diffGradeSection;
	
	@ApiModelProperty(value = "Acitve/In-Active status", example = "true", required = true, position = 6)
	@NotNull(message = Constants.MANDATORY_FIELD)
	private boolean active;
}
