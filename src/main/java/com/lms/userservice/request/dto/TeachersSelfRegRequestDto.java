package com.lms.userservice.request.dto;

import java.time.LocalDate;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeachersSelfRegRequestDto {

	@ApiModelProperty(value = "First name of the user", example = "Hu<PERSON>", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String firstName;

	@ApiModelProperty(value = "Last name of the user", example = "<PERSON><PERSON><PERSON>", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String lastName;

	@ApiModelProperty(value = "Email address of user", example = "<EMAIL>", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String email;

	@ApiModelProperty(value = "Mobile number of user", example = "8945658923", required = true)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Size(min = 10, max = 10, message = Constants.PHONE_NUMBER_LENGTH)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String mobile;

	@ApiModelProperty(value = "Teacher's date of birth", example = "1983-02-26")
	@NotNull(message = Constants.IS_EMPTY)
	private LocalDate dob;

	@ApiModelProperty(value = "Gender of the user", allowableValues = "MALE, FEMALE, OTHERS", example = "MALE", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String gender;

	@ApiModelProperty(value = "Date of joining", example = "2000-01-14")
	private LocalDate joinDate;

	@ApiModelProperty(value = "Work experiance", example = "6 yrs")
	@NotBlank(message = Constants.IS_EMPTY)
	private String previousWorkExp;

	@ApiModelProperty(value = "Address of the user", example = "25PL/TVM, Trivandrum")
	@NotBlank(message = Constants.IS_EMPTY)
	private String address;

	@ApiModelProperty(value = "Designation of the academic staff", example = "Maths Teacher")
	private String designation;

	@ApiModelProperty(value = "not necessary", hidden = true)
	private String documentUrl;

	@ApiModelProperty(value = "Token during slef-registration", example = "770-1798-MMARS", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String token;

	@ApiModelProperty(value = "Selected school id", example = "2c91808480bc79330180c20aa7b60004", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String school;

	@ApiModelProperty(value = "Selected branch id", example = "4028818380665dd20180667ee7ee0003")
	private String branch;

	@ApiModelProperty(value = "Selected data from radio button", allowableValues = "TEACHER,COORDINATOR,PRINCIPAL", example = "PRINCIPAL", required = true)
	private String academicStaffProfile;

	@ApiModelProperty(value = "Selected coordinator type id", example = "402880e880b81c9a0180b81cebc80000")
	private String coordinatorTypeId;

}
