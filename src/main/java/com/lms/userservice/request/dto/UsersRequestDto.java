package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> The {@code UsersRequestDto} class is used to retrieve from the
 *         DB. <br>
 *         {@code @Data} annotation is used to generate <br>
 *         <i>Get<PERSON>, Setters, Parameterized Constructor, toString, equals and
 *         HashCode methods</i>
 */

@Data
public class UsersRequestDto {

	@ApiModelProperty(value = "Username, must be unique", example = "ZSC_ER45", required = true, position = 1)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String userName;

	@ApiModelProperty(value = "Password", example = "ABcd@123!", required = true, position = 2)
	@Pattern(regexp = Constants.PASSWORD_REGEX, message = Constants.PASSWORD_CONTAINS)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String password;

	@ApiModelProperty(value = "Email address", example = "<EMAIL>", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.BASIC_EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String email;

	@ApiModelProperty(value = "Mobile number", example = "9856784590", required = true, position = 4)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String phoneNumber;

	@ApiModelProperty(value = "User roles", required = true, position = 5)
	private List<UserRolesRequestDto> userRoles;
}
