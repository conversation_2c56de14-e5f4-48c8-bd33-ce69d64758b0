package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BranchCommunicationRequestDto {

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;

	private List<CommunicationRequestDto> communication;
}
