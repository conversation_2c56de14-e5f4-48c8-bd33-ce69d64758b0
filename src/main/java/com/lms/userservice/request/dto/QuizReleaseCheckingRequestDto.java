package com.lms.userservice.request.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QuizReleaseCheckingRequestDto {
	
	@ApiModelProperty(value = "Id of the board", example = "2c918085810427240181053efeab0004", required = true, position = 1)
    @NotBlank(message = Constants.IS_EMPTY)
    private String boardId;

    @ApiModelProperty(value = "Id of the school", example  = "2c91808480bc79330180bcd9414d0000", required = true, position = 2)
    @NotBlank(message = Constants.IS_EMPTY)
    private String schoolId;

    @ApiModelProperty(value = "Id of the branch", example  = "2c91808381043f9c0181064aca71002c", required = true, position = 3)
    @NotBlank(message = Constants.IS_EMPTY)
    private String branchId;

    @ApiModelProperty(value = "Id of the grade", example  = "ff8081818043279f0180433609f70011", required = true, position = 4)
    @NotBlank(message = Constants.IS_EMPTY)
    private String gradeId;
    
    @ApiModelProperty(value = "Id of the subject", example  = "ff80818180433e890180435259fa000c", required = true, position = 5)
    @NotBlank(message = Constants.IS_EMPTY)
    private String subjectId;

    @ApiModelProperty(value = "Id of the subtopic", example  = "2c918085813e5fd3018142e1e260000f", position = 6)
    private String subTopicId;
    
    @ApiModelProperty(value = "Section list", position = 7)
	private List<String> sections;
    
    @ApiModelProperty(value = "Teacher id", example  = "2c918085813e5fd3018142e1e260000f", position = 8)
	@NotBlank(message = Constants.IS_EMPTY)
	private String teacherId;
}
