package com.lms.userservice.request.dto;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.UniqueElements;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeActivateProfileRequestDto {

	@NotEmpty(message = Constants.IS_EMPTY)
	@UniqueElements(message = Constants.UNIQUE_ELEMENTS)
	@ApiModelProperty(value = "Student's Id list", required = true, position = 1)
	private List<@NotBlank(message = Constants.GIVE_VALID_VALUE) String> students = new ArrayList<>();

	@ApiModelProperty(value = "Affilicated board of the branch", example = "ff80818180ead9320180eb06357f0014", required = true, position = 2)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;

	@ApiModelProperty(value = "School of the banch", example = "22c91808480bc79330180c20aa7b60004", required = true, position = 3)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;

	@ApiModelProperty(example = "true", required = true, position = 4)
	private boolean active;

}
