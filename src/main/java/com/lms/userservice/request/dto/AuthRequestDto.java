package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.enums.LoginMedium;
import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 *  <AUTHOR>
 * The {@code AuthRequestDto} class
 * is used to retrieve from the DB.
 * <br> {@code @Data} annotation is used to generate
 * <br>
 * <i>Getters, Setters, Parameterized Constructor, toString, equals and HashCode methods</i>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AuthRequestDto{

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "UserName", example = "Huma.Querasi.MMARS_001", required = true, position = 1)
	private String username;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "Passowrd", example = "ABcd@123!", required = true, position = 2)
	private String password;
	
	@ApiModelProperty(value = "Login Medium", example = "MOB",allowableValues = "WEB,MOB", position = 3)
	private String loginMedium;
	
	@ApiModelProperty(value = "Mac Address", example = "*************", position = 4)
	private String macAddress;

}
