package com.lms.userservice.request.dto;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TokenDetailsRequestDto {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String userId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String firstName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String lastName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = ".*@.*\\..*", message = Constants.GIVE_VALID_VALUE)
	private String email;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String phone;
}
