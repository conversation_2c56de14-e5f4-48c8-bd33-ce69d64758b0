package com.lms.userservice.request.dto;

import com.lms.userservice.util.Constants;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
public class StudentSubjectMappingRequestDto {

    @ApiModelProperty(example = "2c9180838103f93201810435049d0000", required = true)
    @NotBlank(message = Constants.MANDATORY_FIELD)
    private String studentId;

    @ApiModelProperty(value = "id of Academic Year", example = "ff80818180608cc501806096696e0007", required = true)
    @NotBlank(message = Constants.MANDATORY_FIELD)
    private String academicYearId;

    @NotEmpty(message = Constants.MANDATORY_FIELD)
    private List<StudentSubjectMappingsMappingRequestDto> studentSubjectMappingsMappingsList;
}
