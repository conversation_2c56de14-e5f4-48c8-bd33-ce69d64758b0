package com.lms.userservice.request.dto;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import org.hibernate.validator.constraints.UniqueElements;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BranchRequestDto {
	
	@ApiModelProperty(value = "Name of the branch", example = "Mathews Mar Athanasius Residential School-CISCE", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String name;
	
	@ApiModelProperty(value = "Id of the city", example = "ff80818180bc92e90180bc9b3c080009", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String cityId;
	
	@ApiModelProperty(value = "Locality of the branch", example = "Karumady EDB.O", required = true)
	private String locality;
	
	@ApiModelProperty(value = "Email address of the branch", example = "<EMAIL>", required = true)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String pocEmail;
	
	@ApiModelProperty(value = "Phione number of the branch", example = "9057583852", required = true)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Size(min = 10, max = 10, message = Constants.PHONE_NUMBER_LENGTH)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String phoneNumber;
	
	@ApiModelProperty(value = "Affilicated board of the branch", example = "ff80818180ead9320180eb06357f0014", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String boardId;
	
	@ApiModelProperty(value = "Logo of the school", example = "https://s3.ap-south-1.amazonaws.com/lmscontentupload/User-Service/b2da6fc0-b089-473b-ad23-eec27f24333e_Smiley.jpg", required = true)
	private String logoUrl;
	
	@ApiModelProperty(value = "School of the banch", example = "2c91808480bc79330180c20aa7b60004", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String schoolId;
	
	@ApiModelProperty(value = "Plans assigned to the branches", required = true)
	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@UniqueElements(message = Constants.DUPLICATED_VALUE)
	private List<BranchPlansRequestDto> plans = new ArrayList<>();
	
	@ApiModelProperty(value = "Branch of the school", example = "2c91808480bc79330180c20aa7b60004", required = true)
	@NotBlank(message = Constants.IS_EMPTY)
	private String branchCode;
	
	@ApiModelProperty(value = "Test branch either true or false", example = "2c91808480bc79330180c20aa7b60004")
	private Boolean testBranch;

}
