package com.lms.userservice.request.dto;

import java.util.HashMap;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * To find the plan use the combination grade, subject and board. Thus find the
 * schools and branches
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class PlanFinderRequestDto {

	private HashMap<String, List<String>> gradeSubjectMap;

	private String boardId;
}
