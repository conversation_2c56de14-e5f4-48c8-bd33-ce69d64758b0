package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import lombok.Data;

@Data
public class VerifyOtpRequestDto {
	
	@Pattern(regexp = Constants.OTP_FOUR_DIGIT, message = Constants.GIVE_VALID_VALUE)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String otp;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String phoneNumber;
}
