package com.lms.userservice.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class EmailRequestDto {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String resetLink;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.BASIC_EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	private String email;
	
	private String typeOfEmailSend="FORGOT_PASS";
	
	@ApiModelProperty(value = "FE-Base URL", example = "http://dev.lms.giglabz.in/")
	private String baseFEUrl;
	
	public EmailRequestDto(String resetLink, String email) {
		this.resetLink = resetLink;
		this.email = email;
		this.baseFEUrl = resetLink.substring(0, resetLink.indexOf("#"));
	}
	
}