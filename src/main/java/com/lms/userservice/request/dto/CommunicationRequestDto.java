package com.lms.userservice.request.dto;

import com.lms.userservice.enums.CommunicationAction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommunicationRequestDto {

	@ApiModelProperty(example = "UPDATE_PASSWORD")
	private CommunicationAction communicationAction;

	@ApiModelProperty(example = "true")
	private boolean sms;

	@ApiModelProperty(example = "false")
	private boolean email;
}
