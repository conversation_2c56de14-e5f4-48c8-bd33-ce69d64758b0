package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchReceptionistRequestDto {

	@ApiModelProperty(value = "firstName of the selected persona", example = "Huma", position = 1)
	private String firstName;

	@ApiModelProperty(value = "userName of the selected persona", example = "Huma.Querasi.MMARS_001", position = 2)
	private String userName;

	@ApiModelProperty(value = "Mobile number of student", example = "8547890756", position = 3)
	private String phoneNumber;
}
