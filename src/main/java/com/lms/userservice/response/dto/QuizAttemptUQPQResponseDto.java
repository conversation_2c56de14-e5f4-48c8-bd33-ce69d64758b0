package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuizAttemptUQPQResponseDto {

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 1)
	private Integer unitQuizAttemptRate;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 2)
	private Integer practiceQuizAttemptRate;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 3)
	private Integer quizAverageScorePercentage;

}
