package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentSubjectMappingsResponseDto {
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 1)
    private String studentId;
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 2)
    private String studentFirstName;
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 3)
    private String studentLastName;
    
    @ApiModelProperty(example = "ff80818180608cc501806096696e0007", position = 4)
    private String academicYearsId;
    
    @ApiModelProperty(example = "2021-22", position = 5)
    private String academicYears;
    
    private List<StudentSubjectGroupSubjectMappingsResponseDto> subjectGroupSubjectMappings=new ArrayList();

    public StudentSubjectMappingsResponseDto(StudentsResponseDto studentsResponse, String academicYearsId, String academicYears) {
        this.studentId=studentsResponse.getId();
        this.studentFirstName=studentsResponse.getFirstName();
        this.studentLastName=studentsResponse.getLastName();
        this.academicYearsId=academicYearsId;
        this.academicYears=academicYears;
    }
    
}
