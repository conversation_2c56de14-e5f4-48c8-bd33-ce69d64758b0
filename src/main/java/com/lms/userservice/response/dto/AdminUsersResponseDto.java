package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.projection.UsersProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class AdminUsersResponseDto {

	@ApiModelProperty(value = "Id of the admin user.", example = "ff80818180f65df90180f67e409a017e", position = 1)
	private String id;

	@ApiModelProperty(value = "First name", example = "Zara", required = true, position = 2)
	private String firstName;

	@ApiModelProperty(value = "Last name", example = "<PERSON>akir", required = true, position = 3)
	private String lastName;

	@ApiModelProperty(value = "Username", example = "ZKR_0845", required = true, position = 3)
	private String userName;

	@ApiModelProperty(value = "Email address", example = "<EMAIL>", required = true, position = 4)
	private String email;

	@ApiModelProperty(value = "Mobile number", example = "9856784590", required = true, position = 5)
	private String phoneNumber;

	@ApiModelProperty(value = "Roles decided to assign", required = true, position = 6)
	private List<RolesFeignDto> roles;

	@ApiModelProperty(value = "Active status of user", example = "true", required = true, position = 7)
	private boolean active;

	@ApiModelProperty(example = "LOCAL")
	private String lmsEnv;

	@JsonIgnore
	private String password;

	@JsonIgnore //create or edit
	private String typeOfEmailSend;

	@JsonIgnore
	private String roleName;
	
	//first name & last name of person who editing the details.
	@JsonIgnore
	private String adminName;
	
	@JsonIgnore
	private String roleNameOfAdmin;

	// do not remove the tag '@JsonIgnore' above 'id' and 'userId' are same,
	// creating this for sending the email via AOP
	@JsonIgnore
	private String userId;

	public AdminUsersResponseDto(String id, String firstName, String lastName, String userName, String email,
			String phoneNumber, boolean active) {
		this.id = id;
		this.firstName = firstName;
		this.lastName = lastName;
		this.userName = userName;
		this.email = email;
		this.phoneNumber = phoneNumber;
		this.active = active;
	}

	public AdminUsersResponseDto(UsersProjection projection) {
		this.id = projection.getId();
		this.firstName = projection.getFirstName();
		this.lastName = projection.getLastName();
		this.userName = projection.getUserName();
		this.email = projection.getEmail();
		this.phoneNumber = projection.getPhoneNumber();
		this.active = projection.isActive();
	}

}
