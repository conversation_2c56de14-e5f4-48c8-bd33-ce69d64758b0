package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.lms.userservice.feign.master.IrMenuResponseDto;
import com.lms.userservice.feign.master.MenuSubMenuResponseDto;
import com.lms.userservice.feign.master.RolesFeignDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Authentication response. Use this only for login purpose.
 * 
 * @JsonInclude(value = Include.NON_EMPTY) will include the field to JSON if the
 *                    field is not empty.
 * <AUTHOR>
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class AuthResponseDto {

	@ApiModelProperty(value = "JWT token", example = "eyJhbGciJ9.eyJzdWIi.IztDnIsgU_e9DZtz", position = 1)
	private String token;

	@ApiModelProperty(value = "Id of login user", example = "ff80818182daf3150182db094abe000a", position = 2)
	private String id;

	@ApiModelProperty(value = "Username of login user", example = "JAHA_RNA_001", position = 3)
	private String userName;

	@ApiModelProperty(value = "First name of login user", example = "Jeena", position = 4)
	@JsonInclude(value = Include.NON_EMPTY)
	private String firstName;

	@ApiModelProperty(value = "Last name of login user", example = "Mary", position = 4)
	@JsonInclude(value = Include.NON_EMPTY)
	private String lastName;

	@ApiModelProperty(value = "Email of login user", example = "<EMAIL>", position = 5)
	private String email;

	@ApiModelProperty(value = "Mobile of login user", example = "9850365926", position = 6)
	private String phoneNumber;

	@ApiModelProperty(value = "Assigned roles", position = 7)
	private List<RolesFeignDto> roles;

	@ApiModelProperty(value = "Menu/Sub-menu associate with the roles", position = 8)
	private List<MenuSubMenuResponseDto> menus;

	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Purchased features from plan", position = 9)
	private List<PlanTemplateImplResponseDto> purchasedFeatures;
	
	@ApiModelProperty(value = "Menu/Sub-menu associate with the roles", position = 10)
	private List<IrMenuResponseDto> irMenu;
	
	@ApiModelProperty(value = "Student Id", example = "ff80818182daf3150182db094abe000a", position = 11)
	private String studentId;

}
