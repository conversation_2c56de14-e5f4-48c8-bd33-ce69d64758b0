package com.lms.userservice.response.dto;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TokensResponseDto {
	
	@ApiModelProperty(example = "ff80818180ead9320180eb1344340017")
	private String id;
	
	@ApiModelProperty(example = "770-1798-MMARS")
	private String token;

	@ApiModelProperty(example = "2")
	private Integer numberOfTokens;

	@ApiModelProperty(example = "ff80818180cce35f0180ccf1e6b30005")
	private String roleId;
	
	@ApiModelProperty(example = "Teacher")
	private String role;

	@ApiModelProperty(example = "2022-05-31")
	private LocalDate expiaryDate;

	@ApiModelProperty(example = "Yes")
	private boolean multiUser;

	@ApiModelProperty(example = "2")
	private Integer numberOfUsersPerToken;

	@ApiModelProperty(example = "4")
	private Integer tokenUseCount;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String schoolId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String schoolName;

	@ApiModelProperty(example = "MMARS_001")
	private String schoolCode;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branchId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String branchName;

	private List<TokenDetailsResponseDto> tokenDetails = new ArrayList<>();

	private boolean active;
}
