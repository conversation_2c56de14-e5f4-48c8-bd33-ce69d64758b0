package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Chapter completion details by sections
 * 
 * <AUTHOR> C <PERSON>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChaptersVSQuizzesRelasesBySectionResponseDto {

	@ApiModelProperty(value = "Section assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String sectionId;

	@ApiModelProperty(value = "Section Name assigned to teacher", example = "Section A", position = 2)
	private String section;
	
	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Teacher count from chapter.", example = "32", position = 3)
	private Long totalChapters;

	@ApiModelProperty(value = "Teacher count from chapter.", example = "20", position = 4)
	private Long completedChapters;

	@ApiModelProperty(value = "Teacher count from quizzes.", example = "19", position = 5)
	private Long releasedQuizzes;

	public ChaptersVSQuizzesRelasesBySectionResponseDto(String sectionId, String section) {
		this.sectionId = sectionId;
		this.section = section;
	}
}
