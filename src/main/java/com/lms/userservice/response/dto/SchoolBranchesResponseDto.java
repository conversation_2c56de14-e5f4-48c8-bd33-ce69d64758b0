package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchoolBranchesResponseDto {

	private String schoolId;
	private String schoolName;
	private boolean active;
	private List<BranchesMinDataResponseDto> branches = new ArrayList<>();
}
