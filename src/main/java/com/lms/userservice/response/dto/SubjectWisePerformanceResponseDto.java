package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubjectWisePerformanceResponseDto {

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "subject Id assigned to teacher", example = "ff80818180ead9320180eafe48800004", position = 1)
	private String subjectId;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 2)
	private String subject;

	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 3)
	@JsonInclude(value = Include.NON_NULL)
	private Long avgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Global score average of Unit Quiz", example = "70", position = 4)
	@JsonInclude(value = Include.NON_NULL)
	private Long globalAvgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Attempt rate of Unit Quiz", example = "70", position = 5)
	@JsonInclude(value = Include.NON_NULL)
	private Long avgAttemptPercentageUQ;

	@ApiModelProperty(value = "Global attempt rate of Unit Quiz", example = "70", position = 6)
	@JsonInclude(value = Include.NON_NULL)
	private Long globalAvgAttemptPercentageUQ;

	@ApiModelProperty(value = "Attempt rate of Practice Quiz", example = "70", position = 7)
	@JsonInclude(value = Include.NON_NULL)
	private Long avgAttemptPercentagePQ;

	@ApiModelProperty(value = "Global attempt rate of Practice Quiz", example = "70", position = 8)
	@JsonInclude(value = Include.NON_NULL)
	private Long globalAvgAttemptPercentagePQ;

	@ApiModelProperty(value = "Unit Quiz - Message", example = "Good work!", position = 9)
	@JsonInclude(value = Include.NON_NULL)
	private String unitAttemptRateMessage;

	@ApiModelProperty(value = "Practice Quiz - Message", example = "Good work!", position = 10)
	@JsonInclude(value = Include.NON_NULL)
	private String practiceAttemptRateMessage;

	@ApiModelProperty(value = "Sub-topic level details", position = 11)
	@JsonInclude(value = Include.NON_EMPTY)
	public List<SubTopicWisePerformanceResponseDto> subTopicDetails;

	public SubjectWisePerformanceResponseDto(String subjectId, String subject) {
		this.subjectId = subjectId;
		this.subject = subject;
	}

}
