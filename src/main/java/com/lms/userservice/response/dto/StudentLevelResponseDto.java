package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class StudentLevelResponseDto {

	@ApiModelProperty(value = "Name of score range", example = "Ember", position = 1)
	private String rangeName;
	
	@ApiModelProperty(value = "Name with range of score range", example = "Ember (0 - 39%)", position = 2)
	private String scoreRange;

	@ApiModelProperty(value = "Percentage student fall in a Score Range", example = "70", position = 3)
	private Integer percentageMarks;
	
	@ApiModelProperty(value = "Student Count", example = "70", position = 4)
	private Integer studentCount;
}
