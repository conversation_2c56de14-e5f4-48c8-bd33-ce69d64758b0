package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubjectWiseGradePerformanceResponseDto {

	@ApiModelProperty(value = "subject Id assigned to teacher", example = "ff80818180ead9320180eafe48800004", position = 1)
	private String subjectId;

	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 2)
	private String subject;

	@ApiModelProperty(value = "SubTopic Id assigned to teacher", example = "ff80818180ead9320180eafe48800004", position = 3)
	private String subTopicId;

	@ApiModelProperty(value = "SubTopic Name assigned to teacher", example = "Physics", position = 4)
	private String subTopic;

	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 5)
	private double avgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Global score average of Unit Quiz", example = "70", position = 6)
	private double globalAvgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Attempt rate of Unit Quiz", example = "70", position = 7)
	private double avgAttemptPercentageUQ;

	@ApiModelProperty(value = "Global attempt rate of Unit Quiz", example = "70", position = 8)
	private double globalAvgAttemptPercentageUQ;

	@ApiModelProperty(value = "Attempt rate of Practice Quiz", example = "70", position = 9)
	private double avgAttemptPercentagePQ;

	@ApiModelProperty(value = "Global attempt rate of Practice Quiz", example = "70", position = 10)
	private double globalAvgAttemptPercentagePQ;

	public SubjectWiseGradePerformanceResponseDto(String subjectId, String subject) {
		this.subjectId = subjectId;
		this.subject = subject;
	}

}
