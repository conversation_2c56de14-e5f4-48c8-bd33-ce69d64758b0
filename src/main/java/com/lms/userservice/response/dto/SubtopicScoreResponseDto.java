package com.lms.userservice.response.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubtopicScoreResponseDto {

	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String subtopicId;

	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 2)
	private String subtopic;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 3)
	private BigDecimal unitQuizAttemptRate;

	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 4)
	private BigDecimal unitGlobalQuizAttemptRate;

	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 5)
	private String unitAttemptRateMessage;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 6)
	private BigDecimal practiceQuizAttemptRate;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 7)
	private BigDecimal practiceGlobalQuizAttemptRate;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 8)
	private String practiceAttemptRateMessage;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 9)
	private BigDecimal quizAverageScorePercentage;

	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 10)
	private BigDecimal globalQuizAverageScorePercentage;

	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 11)
	private String averageMessage;

}
