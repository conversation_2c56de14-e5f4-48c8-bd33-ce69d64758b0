package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentNameDetails {

    @ApiModelProperty(example = "2c91808480bc79330180bcfc13f00003",position = 1)
    private String id;

    @ApiModelProperty(example = "Manish",position = 2)
    private String firstName;

    @ApiModelProperty(example = "Malhotra",position = 3)
    private String lastName;

}
