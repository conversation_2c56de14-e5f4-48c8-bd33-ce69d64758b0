package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * To get the list of grade id and count of students who studying in the
 * grade
 * <p>
 * Grade can stand without the section. So student count can be give for grade too.
 * 
 * <AUTHOR>
 * 
 * @see <a href=
 *      "https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/2881197b-11e5-4ec5-9778-ddd1c9637bcd/">Grade
 *      Access Information</a>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeAccessResponseDto {

	@ApiModelProperty(value = "Grade assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String gradeId;
	
	@ApiModelProperty(value = "Grade Name assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 2)
	private String grade;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Student count from grade.", example = "32", position = 3)
	private Long studentCount;
	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Section list", position = 4)
	private List<SectionAccessResponseDto> sections;
	
	public GradeAccessResponseDto(String gradeId, String grade) {
		this.gradeId = gradeId;
		this.grade = grade;
	}

}
