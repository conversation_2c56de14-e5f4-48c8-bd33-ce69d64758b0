package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class AllTypeUserMinResponseDto {
	
	@ApiModelProperty(value = "user id", example = "ff80818181293fa2018129458e000001", position = 1)
	@JsonInclude(value = Include.NON_NULL)
	private String id;

	@ApiModelProperty(value = "first/last name combination", example = "Renu David", position = 4)
	@JsonInclude(value = Include.NON_NULL)
	private String name;
	
}
