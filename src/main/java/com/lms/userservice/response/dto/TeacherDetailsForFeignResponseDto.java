package com.lms.userservice.response.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TeacherDetailsForFeignResponseDto {

	private String assignedTeacherId;

	private String assignedTeacherUserName;

	private String assignedTeacherName;

	private String schoolId;

	private String school;

	private String branchId;

	private String branch;

	public TeacherDetailsForFeignResponseDto(String assignedTeacherId, String assignedTeacherUserName,
			String assignedTeacherName) {
		this.assignedTeacherId = assignedTeacherId;
		this.assignedTeacherUserName = assignedTeacherUserName;
		this.assignedTeacherName = assignedTeacherName;
	}

}