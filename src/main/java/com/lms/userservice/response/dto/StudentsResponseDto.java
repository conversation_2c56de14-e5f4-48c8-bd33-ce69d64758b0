package com.lms.userservice.response.dto;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.userservice.projection.StudentsProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 11-Mar-2022
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentsResponseDto {

	@ApiModelProperty(example = "2c91808480bc79330180bcfc13f00003")
	private String id;

	@ApiModelProperty(example = "Maneesh")
	private String firstName;

	@ApiModelProperty(example = "Patele")
	private String lastName;

	//@ApiModelProperty(example = "<EMAIL>")
	private String email;

	@ApiModelProperty(example = "8547890756")
	private String mobile;

	@ApiModelProperty(example = "2009-05-01")
	private LocalDate dob;

	@ApiModelProperty(example = "MALE")
	private String gender;

	private String documentUrl;

	@ApiModelProperty(example = "ff80818180608cc50180609c95b70009")
	private String firstLanguageId;

	@ApiModelProperty(example = "English")
	private String firstLanguage;

	@ApiModelProperty(example = "ff80818180608cc50180609e613b0015")
	private String secondLanguageId;

	@ApiModelProperty(example = "Hindi")
	private String secondLanguage;

	@ApiModelProperty(example = "2021-05-15")
	private LocalDate admissionDate;

	@ApiModelProperty(example = "ff80818180433e890180434436bd0006")
	private String studentCategoryId;

	@ApiModelProperty(example = "RTE Students")
	private String studentCategory;

	@ApiModelProperty(example = "ff80818180608cc50180609e613b0015")
	private String gradeId;

	@ApiModelProperty(example = "Grade 4")
	private String grade;

	@ApiModelProperty(example = "ff80818180433e890180433ef2150000")
	private String sectionId;

	@ApiModelProperty(example = "B")
	private String section;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branchId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School-CISCE")
	private String branchName;

	@ApiModelProperty(example = "2c91808480bc79330180c20aa7b60004")
	private String schoolId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String schoolName;

	@ApiModelProperty(example = "MMARS_001")
	private String schoolCode;

	@ApiModelProperty(value = "Board Id: only for Profile API", example = "2c91808480bc79330180c20aa7b60004")
	public String boardId;

	@ApiModelProperty(example = "true")
	private Boolean isPromoted;

	@ApiModelProperty(example = "true")
	private Boolean yearEndProcess;

	@ApiModelProperty(example = "25PL/TVM, Trivandrum")
	private String address;

	@ApiModelProperty(example = "Maneesh-Patele-MMARS_001")
	private String userName;

	@ApiModelProperty(example = "ff8081818008e999018008f1c4730000")
	private String userId;

	@ApiModelProperty(value = "number of quizzes attend by student", example = "4")
	private Long numberOfQuiz;

	@ApiModelProperty(value = "Date and time of last unit quiz submission", example = "15-08-2022 12:30 PM")
	private String lastUnitQuizSubmission;

	@ApiModelProperty(example = "29-01-2023 10:11:49 AM")
	private String lastLoginTime;

	private boolean active;

	private Boolean examAttended;

	@ApiModelProperty(example = "LOCAL")
	private String lmsEnv;

	@JsonIgnore
	private String password;

	@JsonIgnore
	private String typeOfEmailSend;

	@JsonIgnore
	private String roleName = "Student";

	// first name & last name of person who editing the details.
	@JsonIgnore
	private String adminName;

	@JsonIgnore
	private String roleNameOfAdmin;
	
	private String learningGeneration;
	
	private String geographicalType;
	
	private String gradeLevel ;
	
	private String irStatus;
	
	private Boolean diagnosticTest ;

	private String planId ;

	public StudentsResponseDto(StudentsProjection projection) {
		this.id = projection.getId();
		this.firstName = projection.getFirstName() != null && !projection.getFirstName().isEmpty() && !projection.getFirstName().isBlank() ? projection.getFirstName() : null;
		this.lastName = projection.getLastName() != null && !projection.getLastName().isEmpty() && !projection.getLastName().isBlank() ? projection.getLastName() : null;
		this.email = projection.getEmail() != null && !projection.getEmail().isEmpty() && !projection.getEmail().isBlank() ? projection.getEmail() : null;
		this.mobile = projection.getMobile() != null && !projection.getMobile().isEmpty() && !projection.getMobile().isBlank() ? projection.getMobile() : null;
		this.dob = projection.getDob() != null ? projection.getDob() : null;
		this.gender = projection.getGender() != null && !projection.getGender().isEmpty() && !projection.getGender().isBlank() ? projection.getGender() : null;
		this.documentUrl = projection.getDocumentUrl() != null && !projection.getDocumentUrl().isEmpty() && !projection.getDocumentUrl().isBlank() ? projection.getDocumentUrl() : null;
		this.firstLanguageId = projection.getFirstLanguageId() != null && !projection.getFirstLanguageId().isEmpty() && !projection.getFirstLanguageId().isBlank() ? projection.getFirstLanguageId() : null;
		this.secondLanguageId = projection.getSecondLanguageId() != null && !projection.getSecondLanguageId().isEmpty() && !projection.getSecondLanguageId().isBlank() ? projection.getSecondLanguageId() : null;
		this.admissionDate = projection.getAdmissionDate() != null ? projection.getAdmissionDate() : null;
		this.studentCategoryId = projection.getStudentCategoryId() != null && !projection.getStudentCategoryId().isEmpty() && !projection.getStudentCategoryId().isBlank() ? projection.getStudentCategoryId() : null;
		this.gradeId = projection.getGradeId() != null && !projection.getGradeId().isEmpty() && !projection.getGradeId().isBlank() ? projection.getGradeId() : null;
		this.sectionId = projection.getSectionId() != null && !projection.getSectionId().isEmpty() && !projection.getSectionId().isBlank() ? projection.getSectionId() : null;
		this.branchId = projection.getBranchId() != null && !projection.getBranchId().isEmpty() && !projection.getBranchId().isBlank() ? projection.getBranchId() : null;
		this.branchName = projection.getBranchName() != null && !projection.getBranchName().isEmpty() && !projection.getBranchName().isBlank() ? projection.getBranchName() : null;
		this.schoolId = projection.getSchoolId() != null && !projection.getSchoolId().isEmpty() && !projection.getSchoolId().isBlank() ? projection.getSchoolId() : null;
		this.schoolName = projection.getSchoolName() != null && !projection.getSchoolName().isEmpty() && !projection.getSchoolName().isBlank() ? projection.getSchoolName() : null;
		this.schoolCode = projection.getSchoolCode() != null && !projection.getSchoolCode().isEmpty() && !projection.getSchoolCode().isBlank() ? projection.getSchoolCode() : null;
		this.boardId = projection.getBoardId() != null && !projection.getBoardId().isEmpty() && !projection.getBoardId().isBlank() ? projection.getBoardId() : null;
		this.isPromoted = projection.getIsPromoted() != null ? projection.getIsPromoted() : null;
		this.yearEndProcess = projection.getYearEndProcess() != null ? projection.getYearEndProcess() : null;
		this.address = projection.getAddress() != null && !projection.getAddress().isEmpty() && !projection.getAddress().isBlank() ? projection.getAddress() : null;
		this.userName = projection.getUserName() != null && !projection.getUserName().isEmpty() && !projection.getUserName().isBlank() ? projection.getUserName() : null;
		this.userId = projection.getUserId() != null && !projection.getUserId().isEmpty() && !projection.getUserId().isBlank() ? projection.getUserId() : null;
		this.lastLoginTime = projection.getLastLoginTime() != null && !projection.getLastLoginTime().isEmpty() && !projection.getLastLoginTime().isBlank() ? projection.getLastLoginTime() : null;
		this.active = projection.isActive();
		this.learningGeneration=projection.getLearningGeneration();
		this.geographicalType=projection.getGeographicalType();
		this.gradeLevel=projection.getGradeLevel();
		this.irStatus=projection.getIrStatus();
		this.diagnosticTest=projection.getDiagnosticTest();
	}
}
