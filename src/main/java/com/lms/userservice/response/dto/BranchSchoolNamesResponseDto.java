package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BranchSchoolNamesResponseDto {

	@ApiModelProperty(value = "Name of the branch for dashboard", example = "LMS branch", position = 1)
	private String branchName;

	@ApiModelProperty(value = "Name of the school for dashboard", example = "Navaudaya", position = 2)
	private String schoolName;
}
