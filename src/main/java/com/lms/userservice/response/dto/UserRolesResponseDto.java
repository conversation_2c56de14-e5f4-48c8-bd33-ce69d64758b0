package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRolesResponseDto {

	@ApiModelProperty(value = "User-role mapping id", example = "ff80818180cce30c0180cd26560d0000", position = 1)
	private String id;

	@ApiModelProperty(value = "Role id mapped with the user", example = "ff80818180cce35f0180ccf1e6b30005", position = 2)
	private String roleId;

	@ApiModelProperty(value = "Status of User-role mapping", example = "true", position = 3)
	private boolean active;
}
