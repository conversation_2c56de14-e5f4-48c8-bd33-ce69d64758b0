package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashboardUnitQuizPerformanceResponseDto {

	@ApiModelProperty(value = "Id of the chapter.", example = "ff808181814f00f501814f0167470001", position = 1)
	private String ChapterId;

	@ApiModelProperty(value = "Name of the chapter.", example = "biodiversity", position = 2)
	private String chapterName;

	@ApiModelProperty(value = "Id of the quiz type.", example = "ff808181814f00f501814f0167470001", position = 3)
	private String QuizTypeId;

	@ApiModelProperty(value = "Type of quiz", example = "Unit Quiz", position = 4)
	private String QuizType;

	@ApiModelProperty(value = "Id of the grade.", example = "ff808181814f00f501814f0167470001", position = 5)
	private String gradeId;

	@ApiModelProperty(value = "Name of the grade.", example = "10", position = 6)
	private String grade;

	@ApiModelProperty(value = "Id of the subject.", example = "ff808181814f00f501814f0167470001", position = 7)
	private String subjectId;

	@ApiModelProperty(value = "Name of the subject.", example = "Science", position = 8)
	private String subject;

	@ApiModelProperty(value = "Id of the subtopic.", example = "ff808181814f00f501814f0167470001", position = 9)
	private String subtopicId;

	@ApiModelProperty(value = "Name of the subtopic.", example = "Physics", position = 10)
	private String subtopic;
	
	@ApiModelProperty(value = "List of section average.", position = 11)
	private List<AverageResponseDto> averageResponseDto;
	
	@ApiModelProperty(value = "Average unit quiz performance of grade.", example = "80", position = 12)
	private Integer gradeAverage;

	@ApiModelProperty(value = "Average of unit quiz performance by attendance.", example = "70", position = 13)
	private Integer attendeesAvg;

	
}
