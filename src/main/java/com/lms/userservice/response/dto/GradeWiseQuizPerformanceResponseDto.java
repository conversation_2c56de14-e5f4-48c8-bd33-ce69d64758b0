package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GradeWiseQuizPerformanceResponseDto {

	@ApiModelProperty(value = "Grade Id", example = "ff80818180ead9320180eafe48800004", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade", example = "Grade 1", position = 2)
	private String grade;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Section Id", example = "ff80818180433e890180433ef2150000", position = 3)
	private String sectionId;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Section", example = "Section A", position = 4)
	private String section;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 5)
	private Long avgScorePercentageUQ; // only unit quiz

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 6)
	private Long globalAvgScorePercentageUQ; // only unit quiz

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 7)
	private Long avgAttemptPercentageUQ;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 8)
	private Long globalAvgAttemptPercentageUQ;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Practice Quiz", example = "70", position = 9)
	private Long avgAttemptPercentagePQ;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Practice Quiz", example = "70", position = 10)
	private Long globalAvgAttemptPercentagePQ;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz", example = "Good work!", position = 11)
	private String unitAttemptRateMessage;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Practice Quiz", example = "Good work!", position = 12)
	private String practiceAttemptRateMessage;

	public GradeWiseQuizPerformanceResponseDto(String gradeId, String grade) {
		this.gradeId = gradeId;
		this.grade = grade;
	}
}
