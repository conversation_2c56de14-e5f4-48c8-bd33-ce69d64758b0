package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Get the Subjects and grade/section which are assigned to the teacher.
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeAccessInfoResponseDto {

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String subjectId;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 2)
	private String subject;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Grade list", position = 3)
	private List<GradeAccessResponseDto> grades;
	
	@ApiModelProperty(example = "true", position = 4)
	private boolean skilledSubject;

	public GradeAccessInfoResponseDto(String subjectId, String subject,boolean skilledSubject) {
		this.subjectId = subjectId;
		this.subject = subject;
		this.skilledSubject = skilledSubject;
	}

}
