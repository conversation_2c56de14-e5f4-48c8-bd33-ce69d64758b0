package com.lms.userservice.response.dto;

import com.lms.userservice.projection.BranchesProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SchoolDetailsResponseDto {

	@ApiModelProperty(example = "2c91808480bc79330180c20aa7b60004")
	private String id;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String name;

	@ApiModelProperty(example = "MMARS_001")
	private String code;

	@ApiModelProperty(example = "<EMAIL>")
	private String pocEmail;

	@ApiModelProperty(example = "8906754376")
	private String phoneNumber;

	@ApiModelProperty(example = "http://mmarschoolise.com")
	private String website;

	@ApiModelProperty(example = "https://s3.ap-south-1.amazonaws.com/lmscontentupload/User-Service/b2da6fc0-b089-473b-ad23-eec27f24333e_Smiley.jpg")
	private String logoUrl;

	@ApiModelProperty(example = "Bangalore")
	private String locality;

	@ApiModelProperty(value = "branch count", example = "70", position = 9)
	private long branchCount;

	@ApiModelProperty(value = "Location of school", example = "10", position = 10)
	private long totalGrades;

	@ApiModelProperty(value = "Location of school", example = "75", position = 11)
	private long totalStudents;

	@ApiModelProperty(value = "Id of city", example = "40289288831da2ee018330862ea10025", position = 12)
	private String cityId;

	public SchoolDetailsResponseDto(BranchesProjection projection) {
		this.id = projection.getSchoolId();
		this.name = projection.getName();
		this.code = projection.getCode();
		this.pocEmail = projection.getPocEmail();
		this.phoneNumber = projection.getPhoneNumber();
		this.website = projection.getWebsite();
		this.logoUrl = projection.getLogoUrl();
		this.branchCount = projection.getBranchCount();
		this.totalGrades = projection.getTotalGrades();
		this.totalStudents = projection.getNumberOfStudents();
		this.cityId = projection.getCityId();
	}

}
