package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ConceptQuestionResponseDto {

	@ApiModelProperty(value = "Id of the chapter", example = "ff808181870a831f06789a8a354d0000", position = 1)
	private String conceptId;

	@ApiModelProperty(value = "Name of the Chapter", example = "Biology", position = 2)
	private String concept;

	@ApiModelProperty(value = "Percentage in quiz", example = "67", position = 3)
	private Integer percentageInQuiz;

	@ApiModelProperty(value = "Question wise details", position = 4)
	private List<QuestionResponseDto> questions;

}
