package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailsResponseDto {

	@ApiModelProperty(example = "ff8081818041c904018041d079c50000", position = 1)
	private String id;

	@ApiModelProperty(example = "SUPER_ADMIN", position = 2)
	private String userName;

	@ApiModelProperty(example = "Tony", position = 3)
	private String firstName;

	@ApiModelProperty(example = "Stark", position = 4)
	private String lastName;

	@ApiModelProperty(example = "<EMAIL>", position = 5)
	private String email;

	@ApiModelProperty(example = "9856784590", position = 6)
	private String phoneNumber;

	@ApiModelProperty(value = "active", example = "true", position = 7)
	private boolean active;

}
