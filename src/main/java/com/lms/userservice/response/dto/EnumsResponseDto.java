package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class EnumsResponseDto {

	@ApiModelProperty(value = "This value has send to back-end while calling the API", example = "MALE")
	private String code;

	@ApiModelProperty(value = "This value has to display in the dropdown/radio button/check box, whenever it is applicable", example = "Male")
	private String name;
}