package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeacherGradeWiseQuizPerformanceResponseDto {

	@ApiModelProperty(value = "Id of the Board", example = "402880e78262feb901826304a0350001", position = 1)
	private String boardId;

	@ApiModelProperty(value = "Name of the Board", example = "ICC", position = 2)
	private String board;

	@ApiModelProperty(value = "Id of the School", example = "2c91808480bc79330180c20aa7b60004", position = 3)
	private String schoolId;

	@ApiModelProperty(value = "Name of the School", example = "Mathews Mar Athanasius Residential School", position = 4)
	private String school;

	@ApiModelProperty(value = "Id of the Branch", example = "4028818380665dd20180667ee7ee0003", position = 5)
	private String branchId;

	@ApiModelProperty(value = "Name of the Branch", example = "Mathews Mar Athanasius Residential School-CISCE", position = 6)
	private String branch;

	@ApiModelProperty(value = "Grades", position = 7)
	private List<GradeWisePerformanceResponseDto> grades;

}
