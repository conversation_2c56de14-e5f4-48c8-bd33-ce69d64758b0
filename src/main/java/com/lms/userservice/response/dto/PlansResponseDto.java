package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlansResponseDto {

	@ApiModelProperty(example = "ff80818180f65df90180f67635db0004", position = 1)
	private String id;

	@ApiModelProperty(example = "Term-one-plan", position = 2)
	private String plan;

	@ApiModelProperty(example = "Term-one-plan", position = 3)
	private String discription;

	@ApiModelProperty(example = "ff80818180ead9320180eb06357f0014", position = 4)
	private String boardId;

	@ApiModelProperty(example = "CBSE", position = 5)
	private String board;

	@ApiModelProperty(example = "Central Board of Secondary Education", position = 6)
	private String boardDiscription;

	@ApiModelProperty(example = "Mapped grades and subject lists", position = 7)
	private List<PlansGradesMappingResponseDto> planGrades = new ArrayList<>();

	@ApiModelProperty(example = "Blueprint levels", position = 8)
	private List<BlueprintLevelResponseDto> blueprintLevels;

	@ApiModelProperty(example = "true", position = 9)
	private boolean active;

	@JsonInclude(value = Include.NON_NULL)
	private Long gradeCount;

//	public PlansResponseDto(String id, String plan, String discription, String boardId, String board,
//			String boardDiscription, boolean active) {
//		this.id = id;
//		this.plan = plan;
//		this.discription = discription;
//		this.boardId = boardId;
//		this.board = board;
//		this.boardDiscription = boardDiscription;
//		this.active = active;
//	}
//
//	public PlansResponseDto(PlansProjection projection) {
//		this.id = projection.getId();
//		this.plan = projection.getPlan();
//		this.discription = projection.getDiscription();
//		this.boardId = projection.getBoardId();
//		this.board = projection.getBoard();
//		this.boardDiscription = projection.getBoardDiscription();
//		this.active = projection.isActive();
//	}
//
//	public PlansResponseDto(String id, String plan, boolean active) {
//		this.id = id;
//		this.plan = plan;
//		this.active = active;
//	}
//
//	public PlansResponseDto(String id, String plan, boolean active, Long gradeCount) {
//		this.id = id;
//		this.plan = plan;
//		this.active = active;
//		this.gradeCount = gradeCount;
//	}

}
