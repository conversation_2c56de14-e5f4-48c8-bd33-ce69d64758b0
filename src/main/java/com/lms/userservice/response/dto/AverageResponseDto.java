package com.lms.userservice.response.dto;

import java.time.LocalDate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AverageResponseDto {

	@ApiModelProperty(value = "Id of the section.", example = "ff808181814f00f501814f0167470001", position = 1)
	private String sectionId;

	@ApiModelProperty(value = "Name of the section.", example = "A", position = 2)
	private String sectionName;

	@ApiModelProperty(value = "Average unit quiz performance of section.", example = "80", position = 3)
	private Integer sectionAverage;

	@ApiModelProperty(value = "Average of unit quiz performance by attendance.", example = "70", position = 4)
	private Long attendeesAvg;

}
