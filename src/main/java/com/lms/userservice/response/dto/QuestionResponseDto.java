package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.annotations.Type;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class QuestionResponseDto<T> {

	@ApiModelProperty(value = "Id of the question", example = "ff808181870a831f06789a8a354d0000", position = 1)
	private String questionId;

	@ApiModelProperty(value = "Id of the question type", example = "ff808181870a831f06789a8a354d0000", position = 2)
	private String questionTypeId;

	@ApiModelProperty(value = "Name of the question type", example = "MSQ", position = 3)
	private String questionType;

	@ApiModelProperty(value = "Question", example = "1. Which type of plastid stores carbohydrates in potatoes?", position = 4)
	private String question;

	@ApiModelProperty(value = "Explanation of the question", position = 5)
	private String explanation;

	@Type(type = "json")
	@ApiModelProperty(value = "List of the answer option", position = 6)
	private List<T> listOfAnswers = new ArrayList<>();

	@ApiModelProperty(value = "Question wise report", position = 7)
	private QuestionWiseReportDto report;

	@ApiModelProperty(value = "map", example = "@#$TFDGF", position = 8)
	private String map;

	@ApiModelProperty(value = "wrong answer count", example = "@#$TFDGF", position = 9)
	private int wrongAnswerCount;

	@ApiModelProperty(value = "partiallyCorrectAnswerCount", example = "7", position = 10)
	private int partiallyCorrectAnswerCount;

	@ApiModelProperty(value = "mark", example = "12", position = 11)
	private int mark;

	@ApiModelProperty(value = "match type", example = "DRAG-GROP", position = 12)
	private String matchType;

	public QuestionResponseDto(String questionId, String questionTypeId, String questionType, String question,
			String explanation, String map, int wrongAnswerCount, int partiallyCorrectAnswerCount, int mark,
			String matchType) {
		this.questionId = questionId;
		this.questionTypeId = questionTypeId;
		this.questionType = questionType;
		this.question = question;
		this.explanation = explanation;
		this.map = map;
		this.wrongAnswerCount = wrongAnswerCount;
		this.partiallyCorrectAnswerCount = partiallyCorrectAnswerCount;
		this.mark = mark;
		this.matchType = matchType;
	}
}
