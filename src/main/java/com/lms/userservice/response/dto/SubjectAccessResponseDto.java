package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubjectAccessResponseDto {
	
	@ApiModelProperty(value = "Assigned Subject id", example = "402892888697da25018697dcf3b90000", position = 1)
	private String subjectId;
	
	@ApiModelProperty(value = "Assigned subject name", example = "Science", position = 2)
	private String subject;
	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "List of subtopics", position = 3)
	private List<SubtopicAccessResponseDto> subTopics;
	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "If no sub-topic sections of subject will show", position = 4)
	private List<SectionAccessResponseDto> sectionsForSubject;
	
	public SubjectAccessResponseDto(String subjectId, String subject) {
		this.subjectId = subjectId;
		this.subject = subject;
	}
}
