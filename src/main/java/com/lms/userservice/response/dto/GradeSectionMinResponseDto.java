package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Designed for the get all API of grade section mapping to take the minimum
 * details
 * 
 * <AUTHOR> C Achari
 * @since 1.0.2
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeSectionMinResponseDto {

	// Primary key of table grade_section_mapping
	@ApiModelProperty(value = "Id of grade-section-mapping", example = "ff80818180f659540180f6b4aa250008", position = 1)
	private String id;
	
	@ApiModelProperty(value = "Section Id from grade-section mapping", example = "ff80818180f65df90180f6950a560180", position = 2)
	private String sectionId;
	
	@ApiModelProperty(value = "Section name", example = "Section A", position = 3)
	private String sectionName;

	@ApiModelProperty(value = "To indicate grade-section mapping is active or not", example = "true", position = 4)
	private boolean active;

	public GradeSectionMinResponseDto(String id, String sectionId, boolean active) {
		this.id = id;
		this.sectionId = sectionId;
		this.active = active;
	}
}
