package com.lms.userservice.response.dto;

import java.time.LocalDateTime;

import com.lms.userservice.enums.AcademicStaffProfile;
import com.lms.userservice.projection.TeachersProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 1.0.3
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class AcademicStaffResponseDto {

	@ApiModelProperty(example = "ff80818180ead9320180eb1344340017")
	private String id;	
	
	//Combination of first name and last name
	@ApiModelProperty(example = "Huma Qureshi")
	private String name;
	
	@ApiModelProperty(example = "ff80818180cc6a270180cc73e9a30000")
	private String userId;
	
	@ApiModelProperty(example = "Huma.Qureshi.MMARS_001")
	private String userName;

	@ApiModelProperty(example = "<EMAIL>")
	private String email;

	@ApiModelProperty(example = "8945658923")
	private String mobile;
	
	//UI this is showing as role
	@ApiModelProperty(example = "TEACHER")
	private AcademicStaffProfile academicStaffProfile;
	
	@ApiModelProperty(example = "ff80818180efdf230180efe98cf20000")
	private String coordinatorTypeId;
	
	@ApiModelProperty(example = "Teacher-In-Charge")
	private String coordinatorType;

	@ApiModelProperty(example = "ff80818180cce35f0180ccf1e6b30005")
	private String roleId;
	
	@ApiModelProperty(example = "Teacher")
	private String role;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String schoolId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String school;
	
	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branchId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School-CISCE")
	private String branch;

	@ApiModelProperty(example = "true")
	private boolean active;

	@ApiModelProperty(example = "2022-04-21T08:33:03")
	private LocalDateTime lastLoginTime;
	
	public AcademicStaffResponseDto(TeachersProjection projection) {
		this.id = projection.getId();	
		this.name = projection.getName();
		this.userId = projection.getUserId();
		this.userName = projection.getUserName();
		this.email = projection.getEmail();
		this.mobile = projection.getMobile();
		this.academicStaffProfile = AcademicStaffProfile.valueOf(projection.getAcademicStaffProfile());
		this.coordinatorTypeId = projection.getCoordinatorTypeId();
		this.roleId = projection.getRoleId();
		this.role = projection.getRole();
		this.schoolId = projection.getSchoolId();
		this.school = projection.getSchool();
		this.branchId = projection.getBranchId();
		this.branch = projection.getBranch();
		this.active = projection.isActive();
		this.lastLoginTime = projection.getLastLoginTime();
	}

}
