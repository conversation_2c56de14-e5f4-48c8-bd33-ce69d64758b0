package com.lms.userservice.response.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.lms.userservice.projection.TokenListProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TokenListResponseDto {
	
	@ApiModelProperty(example = "ff80818180ead9320180eb1344340017")
	private String id;
	
	@ApiModelProperty(example = "770-1798-MMARS")
	private String token;
	
	@ApiModelProperty(example = "ff80818180cce35f0180ccf1e6b30005")
	private String roleId;
	
	@ApiModelProperty(example = "Teacher")
	private String role;
	
	@ApiModelProperty(example = "<EMAIL>")
	private String email;

	/**
	 * true: Yes false: No
	 */
	@ApiModelProperty(example = "No")
	private String multiUser;

	// tokenUseCount
	@ApiModelProperty(example = "1")
	private Integer noOfUsers;
	
	@ApiModelProperty(example = "ff80818180cc6a270180cc73e9a30000")
	private String usedUserId;

	// createdAt
	@ApiModelProperty(example = "2022-04-21T08:33:03")
	private LocalDateTime dateCreated;
	
	@ApiModelProperty(example = "2022-05-31")
	private LocalDate expiaryDate;
	
	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branchId;
	
	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String branch;
	
	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String schoolId;
	
	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String school;
	
	private List<TokenDetailsResponseDto> userDetails;

	public TokenListResponseDto(TokenListProjection projection) {
		this.id = projection.getId();
		this.token = projection.getToken();
		this.roleId = projection.getRoleId();
		this.email = projection.getEmail();
		this.multiUser = projection.getMultiUser();
		this.noOfUsers = projection.getNoOfUsers();
		this.usedUserId = projection.getUsedUserId();
		this.dateCreated = projection.getDateCreated();
		this.expiaryDate = projection.getExpiaryDate();
		this.branchId = projection.getBranchId();
		this.branch = projection.getBranch();
		this.schoolId = projection.getSchoolId();
		this.school = projection.getSchool();
	}

}
