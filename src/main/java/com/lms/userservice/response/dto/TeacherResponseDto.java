package com.lms.userservice.response.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.userservice.projection.TeachersProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeacherResponseDto {

	@ApiModelProperty(example = "ff80818180ead9320180eb1344340017")
	private String id;

	@ApiModelProperty(example = "Huma")
	private String firstName;

	@ApiModelProperty(example = "Qureshi")
	private String lastName;

	@ApiModelProperty(example = "<EMAIL>")
	private String email;

	@ApiModelProperty(example = "8945658923")
	private String mobile;

	@ApiModelProperty(example = "1983-02-26")
	private LocalDate dob;

	@ApiModelProperty(example = "FEMALE")
	private String gender;

	@ApiModelProperty(example = "2000-01-14")
	private LocalDate joinDate;

	@ApiModelProperty(example = "6 yrs")
	private String previousWorkExp;

	@ApiModelProperty(example = "25PL/TVM, Trivandrum")
	private String address;

	@ApiModelProperty(example = "Maths Teacher")
	private String designation;

	@ApiModelProperty(hidden = true)
	private String documentUrl;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String school;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String schoolName;

	@ApiModelProperty(example = "MMARS_001")
	private String schoolCode;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branch;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School-CISCE")
	private String branchName;

	@ApiModelProperty(value = "This only for while viewing the profile", example = "Mathews Mar Athanasius Residential School-CISCE")
	private String boardId;

	@ApiModelProperty(example = "ff80818180cce35f0180ccf1e6b30005")
	private String roleId;

	@ApiModelProperty(example = "Teacher")
	private String role;

	@ApiModelProperty(example = "Teacher")
	private String academicStaffProfile;

	@ApiModelProperty(example = "ff80818180efdf230180efe98cf20000")
	private String coordinatorTypeId;

	@ApiModelProperty(example = "Teacher-In-Charge")
	private String coordinatorType;

	@ApiModelProperty(example = "ff80818180cc6a270180cc73e9a30000")
	private String userId;

	@ApiModelProperty(example = "Huma.Qureshi.MMARS_001")
	private String userName;

	@ApiModelProperty(example = "1653210494329")
	private LocalDateTime lastLoginTime;

	private boolean active;

	@ApiModelProperty(example = "LOCAL")
	private String lmsEnv;

	@JsonIgnore
	private String password;

	@JsonIgnore
	private String typeOfEmailSend;

	@JsonIgnore
	private String roleName;

	// first name & last name of person who editing the details.
	@JsonIgnore
	private String adminName;

	@JsonIgnore
	private String roleNameOfAdmin;

	public TeacherResponseDto(TeachersProjection projection) {
		this.id = projection.getId();
		this.firstName = projection.getFirstName();
		this.lastName = projection.getLastName();
		this.email = projection.getEmail();
		this.mobile = projection.getMobile();
		this.dob = projection.getDob();
		this.gender = projection.getGender();
		this.joinDate = projection.getJoinDate();
		this.previousWorkExp = projection.getPreviousWorkExp();
		this.address = projection.getAddress();
		this.designation = projection.getDesignation();
		this.documentUrl = projection.getDocumentUrl();
		this.school = projection.getSchoolId();
		this.schoolName = projection.getSchool();
		this.schoolCode = projection.getSchoolCode();
		this.branch = projection.getBranchId();
		this.branchName = projection.getBranch();
		this.boardId = projection.getBoardId();
		this.roleId = projection.getRoleId();
		this.role = projection.getRole();
		this.academicStaffProfile = projection.getAcademicStaffProfile();
		this.coordinatorTypeId = projection.getCoordinatorTypeId();
		this.coordinatorType = projection.getCoordinatorType();
		this.userId = projection.getUserId();
		this.userName = projection.getUserName();
		this.lastLoginTime = projection.getLastLoginTime();
		this.active = projection.isActive();
	}

}
