package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchesMinDataResponseDto {

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branchId;

	@ApiModelProperty(example = "Mathews Mar Athanesious Residential School : ICSE/ISE")
	private String branch;

	private boolean active;

}
