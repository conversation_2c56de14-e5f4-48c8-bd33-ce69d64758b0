package com.lms.userservice.response.dto;

import com.lms.userservice.feign.master.SubjectGroupsSubjectMappingsResponseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR> <PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentSubjectGroupSubjectMappingsResponseDto {
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 1)
    private String id;
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 2)
    private String subjectGroupSubjectMappingId;
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 3)
    private String subjectGroupId;
    
    @ApiModelProperty(example = "Languages", position = 4)
    private String subjectGroupTitle;
    
    @ApiModelProperty(example = "2322", position = 5)
    private String subjectGroupCode;
    
    @ApiModelProperty(example = "2c91808381043f9c018105d06f6b002a", position = 6)
    private String subjectId;
    
    @ApiModelProperty(example = "Hindi", position = 7)
    private String subject;

    public StudentSubjectGroupSubjectMappingsResponseDto(String id, SubjectGroupsSubjectMappingsResponseDto subjectGroupsSubjectMappings) {
        this.id = id;
        this.subjectGroupSubjectMappingId = subjectGroupsSubjectMappings.getId();
        this.subjectGroupId = subjectGroupsSubjectMappings.getSubjectGroupId();
        this.subjectGroupTitle = subjectGroupsSubjectMappings.getSubjectGroupTitle();
        this.subjectGroupCode = subjectGroupsSubjectMappings.getSubjectGroupCode();
        this.subjectId = subjectGroupsSubjectMappings.getSubjectId();
        this.subject = subjectGroupsSubjectMappings.getSubject();
    }
    
    public StudentSubjectGroupSubjectMappingsResponseDto(String id, String subjectGroupSubjectMappingId) {
        this.id = id;
        this.subjectGroupSubjectMappingId = subjectGroupSubjectMappingId;
    }
}
