package com.lms.userservice.response.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Set;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class InstitutionStudentCountResponseDto {

	private String schoolId;

	private String school;

	private String branchId;

	private String branch;

	private long totalStudents;

	// use this while make call to content-service
	private String boardId;

	private String gradeId;

	private String academicYearId;

	// key as subject and value as Set<Sub-topics>
	private HashMap<String, Set<String>> subjectAndSubTopic;

	public InstitutionStudentCountResponseDto(String schoolId, String school, String branchId, String branch,
			long totalStudents) {
		this.schoolId = schoolId;
		this.school = school;
		this.branchId = branchId;
		this.branch = branch;
		this.totalStudents = totalStudents;
	}

}
