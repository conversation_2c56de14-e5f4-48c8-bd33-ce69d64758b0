package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * To get the list of section id and count of students who studying in the
 * section
 * 
 * <AUTHOR>
 * 
 * @see <a href=
 *      "https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/2881197b-11e5-4ec5-9778-ddd1c9637bcd/">Grade
 *      Access Information</a>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class SectionAccessResponseDto {

	@ApiModelProperty(value = "Section assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String sectionId;
	
	@ApiModelProperty(value = "Section Name assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 2)
	private String section;

	@ApiModelProperty(value = "Student count from section", example = "32", position = 3)
	private long studentCount;
	
	public SectionAccessResponseDto(String sectionId, String section) {
		this.sectionId = sectionId;
		this.section = section;
	}
}
