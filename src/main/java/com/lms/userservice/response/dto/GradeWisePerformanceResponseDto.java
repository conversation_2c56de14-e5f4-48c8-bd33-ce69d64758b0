package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GradeWisePerformanceResponseDto {

	@ApiModelProperty(value = "Grade assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade Name assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 2)
	private String grade;

	@ApiModelProperty(value = "Sections Subjects score", position = 3)
	private List<SectionSubjectScoreResponseDto> sections;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Subjects score", position = 4)
	private List<SubjectScoreResponseDto> subjects;

	public GradeWisePerformanceResponseDto(String gradeId, String grade,
			List<SectionSubjectScoreResponseDto> sections) {
		this.gradeId = gradeId;
		this.grade = grade;
		this.sections = sections;
	}

}
