package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class QuestionWisePerformanceResponseDto {

	@ApiModelProperty(value = "Average of the class", example = "78", position = 1)
	private Long classAverage;

	@ApiModelProperty(value = "Concepts wise performance", position = 2)
	private List<ConceptQuestionResponseDto> concepts;
	
	@ApiModelProperty(value = "percentage in quiz", example = "78", position = 3)
	private double percentageInQuizForCaseStudy;

	@ApiModelProperty(value = "Concepts wise performance", position = 4)
	private List<CaseStudyReportResponseDto> caseStudy;

}
