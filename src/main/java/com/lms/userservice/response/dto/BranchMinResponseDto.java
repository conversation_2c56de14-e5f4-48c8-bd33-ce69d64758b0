package com.lms.userservice.response.dto;

import com.lms.userservice.projection.BranchesProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class BranchMinResponseDto {

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003", position = 1)
	private String id;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School-CISCE", position = 2)
	private String name;

	@ApiModelProperty(example = "ff80818180bc92e90180bc9b3c080009", position = 3)
	private String cityId;

	@ApiModelProperty(example = "Karumady", position = 4)
	private String cityName;

	@ApiModelProperty(example = "Karumady EDB.O", position = 5)
	private String locality;

	@ApiModelProperty(example = "ff80818180ead9320180eb06357f0014", position = 6)
	private String boardId;

	@ApiModelProperty(example = "CBSE", position = 7)
	private String board;

	@ApiModelProperty(example = "ff80818180ead9320180eb06357f0032", position = 8)
	private String planId;

	@ApiModelProperty(example = "Plan 17", position = 9)
	private String plan;

	@ApiModelProperty(example = "ff80818180ead9320180eb06357f0032", position = 10)
	private String schoolId;

	@ApiModelProperty(example = "Kendriy Mahavidyalay", position = 11)
	private String school;

	@ApiModelProperty(example = "1500", position = 12)
	private long numberOfStudents;

	@ApiModelProperty(example = "10", position = 13)
	private long gradeCount;

	public BranchMinResponseDto(String id, String name, String cityId, String locality, String boardId, String planId,
			long numberOfStudents) {
		this.id = id;
		this.name = name;
		this.cityId = cityId;
		this.locality = locality;
		this.boardId = boardId;
		this.planId = planId;
		this.numberOfStudents = numberOfStudents;
	}

	public BranchMinResponseDto(BranchesProjection projection) {
		this.id = projection.getId();
		this.name = projection.getName();
		this.cityId = projection.getCityId();
		this.locality = projection.getLocality();
		this.boardId = projection.getBoardId();
		this.planId = projection.getPlanId();
		this.numberOfStudents = projection.getNumberOfStudents();
		this.schoolId = projection.getSchoolId();
		this.school = projection.getSchool();
		this.gradeCount = projection.getTotalGrades();
	}
}
