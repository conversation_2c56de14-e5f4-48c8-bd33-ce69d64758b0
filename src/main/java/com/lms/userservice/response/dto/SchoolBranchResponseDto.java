package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchoolBranchResponseDto {

	@ApiModelProperty(example = "2c91808480bc79330180c20aa7b60004")
	private String schoolId;

	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String school;

	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String branchId;

	@ApiModelProperty(example = "Mathews Mar Athanesious Residential School : ICSE/ISE")
	private String branch;

	@ApiModelProperty(value = "Logo of branch", example = "9", position = 6)
	private String branchLogo;

	@ApiModelProperty(value = "City ID of branch", example = "402880e88092a7ad018092f47ef75467", position = 7)
	private String cityId;

	@ApiModelProperty(value = "City of branch", example = "HSR Layout", position = 8)
	private String city;

	public SchoolBranchResponseDto(String schoolId, String school, String branchId, String branch, String branchLogo,
			String cityId) {
		this.schoolId = schoolId;
		this.school = school;
		this.branchId = branchId;
		this.branch = branch;
		this.branchLogo = branchLogo;
		this.cityId = cityId;
	}
	
	public SchoolBranchResponseDto(String schoolId, String school, String branchId, String branch) {
		this.schoolId = schoolId;
		this.school = school;
		this.branchId = branchId;
		this.branch = branch;
	}

	public SchoolBranchResponseDto(String schoolId, String branchId) {
		this.schoolId = schoolId;
		this.branchId = branchId;
	}

}
