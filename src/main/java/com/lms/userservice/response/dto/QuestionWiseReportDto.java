package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class QuestionWiseReportDto {

	@ApiModelProperty(value = "Percentage of student who select the correct answer", example = "79", position = 1)
	private Integer correctQuestionAnswerStudentPercentage;

	@ApiModelProperty(value = "Correct answer option", example = "4", position = 2)
	private Map<String,Object> correctQuestionAnswerOption;

	@ApiModelProperty(value = "Quiz attempt rate", example = "4", position = 3)
	private Integer quizAttemptRate;

	@ApiModelProperty(value = "No of student who attend the question", example = "41", position = 4)
	private Integer studentQuestionAttemptedCount;

	@ApiModelProperty(value = "Most of the student who chose the same option", example = "3", position = 5)
	private String mostOptionChoseStudentCount;

	@ApiModelProperty(value = "total obtained mark", example = "16", position = 6)
	private Integer mark;

	@ApiModelProperty(value = "Most wrong of the student who chose the same option", example = "A", position = 7)
	private String mostWrongOptionChoseStudentCount;
}
