package com.lms.userservice.response.dto;

import com.lms.userservice.projection.BranchCommunicationProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BranchCommunicationResponseDto {

	@ApiModelProperty(example = "User Creation")
	private String communicationAction;

	@ApiModelProperty(example = "true")
	private boolean sms;

	@ApiModelProperty(example = "false")
	private boolean email;

	@ApiModelProperty(example = "true")
	private boolean active;

	public BranchCommunicationResponseDto(BranchCommunicationProjection projection) {
		this.communicationAction = projection.getCommunication();
		this.sms = projection.getSms();
		this.email = projection.getEmail();
		this.active = projection.getActive();
	}

}
