package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import com.lms.userservice.projection.BranchesProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchResponseDto {
	
	@ApiModelProperty(example = "4028818380665dd20180667ee7ee0003")
	private String id;
	
	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School-CISCE")
	private String name;
	
	@ApiModelProperty(example = "ff80818180bc92e90180bc9b3c080009")
	private String cityId;
	
	@ApiModelProperty(example = "Karumady")
	private String cityName;
	
	@ApiModelProperty(example = "Karumady EDB.O")
	private String locality;
	
	@ApiModelProperty(example = "<EMAIL>")
	private String pocEmail;
	
	@ApiModelProperty(example = "9057583852")
	private String phoneNumber;
	
	@ApiModelProperty(example = "ff80818180ead9320180eb06357f0014")
	private String boardId;

	@ApiModelProperty(example = "CBSE")
	private String board;
	
	@ApiModelProperty(example = "https://s3.ap-south-1.amazonaws.com/lmscontentupload/User-Service/b2da6fc0-b089-473b-ad23-eec27f24333e_Smiley.jpg")
	private String logoUrl;
	
	@ApiModelProperty(example = "2c91808480bc79330180c20aa7b60004")
	private String schoolId;
	
	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String school;
	
	@ApiModelProperty(example = "3")
	private Long numberOfCoordinators;
	
	@ApiModelProperty(example = "80")
	private Long numberOfTeachers;
	
	@ApiModelProperty(example = "1")
	private Long numberOfPrincipals;
	
	@ApiModelProperty(example = "1500")
	private Long numberOfStudents;
	
	@ApiModelProperty(example = "2c91808480bc79330180c20aa7b60004")
	private String branchCode;
	
	private Boolean active;
	
	private List<BranchPlanResponseDto> plans = new ArrayList<>();
	
	private Boolean testBranch;
	
	public BranchResponseDto(BranchesProjection projection) {
		this.id = projection.getId();
		this.name = projection.getName();
		this.cityId = projection.getCityId();
		this.locality = projection.getLocality();
		this.pocEmail = projection.getPocEmail();
		this.phoneNumber = projection.getPhoneNumber();
		this.boardId = projection.getBoardId();
		this.logoUrl = projection.getLogoUrl();
		this.schoolId = projection.getSchoolId();
		this.school = projection.getSchool();
		this.numberOfCoordinators = projection.getNumberOfCoordinators();
		this.numberOfTeachers = projection.getNumberOfTeachers();
		this.numberOfPrincipals = projection.getNumberOfPrincipals();
		this.numberOfStudents = projection.getNumberOfStudents();
		this.active = projection.getActive();
		//this.branchCode=projection.getBranchCode();
	}

	public BranchResponseDto(String id, String name, String cityId, String locality, String pocEmail,
			String phoneNumber, String boardId, String logoUrl, String schoolId,
			String school, Boolean active) {
		this.id = id;
		this.name = name;
		this.cityId = cityId;
		this.locality = locality;
		this.pocEmail = pocEmail;
		this.phoneNumber = phoneNumber;
		this.boardId = boardId;
		this.logoUrl = logoUrl;
		this.schoolId = schoolId;
		this.school = school;
		this.active = active;
		//this.branchCode=branchCode;
	}

}
