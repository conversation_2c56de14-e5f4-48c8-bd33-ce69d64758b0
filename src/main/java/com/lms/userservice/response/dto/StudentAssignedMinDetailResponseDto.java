package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StudentAssignedMinDetailResponseDto {

	@ApiModelProperty(value = "Id of the students", example = "Mohan", position = 1)
	private String id;

	@ApiModelProperty(value = "Id of the board", example = "Mohan", position = 2)
	private String boardId;

	@ApiModelProperty(value = "Id of the school", example = "Mohan", position = 3)
	private String schoolId;

	@ApiModelProperty(value = "Name of the school", example = "Mohan", position = 4)
	private String school;

	@ApiModelProperty(value = "Id of the branch", example = "Mohan", position = 5)
	private String branchId;

	@ApiModelProperty(value = "Name of the branch", example = "Mohan", position = 6)
	private String branch;

	@ApiModelProperty(value = "Id of the grade", example = "Mohan", position = 7)
	private String gradeId;

	@ApiModelProperty(value = "Id of the section", example = "Mohan", position = 8)
	private String sectionId;

	@ApiModelProperty(value = "Id of the plan", example = "Plan Term 1", position = 9)
	private String planId;

	public StudentAssignedMinDetailResponseDto(String id, String boardId, String schoolId, String school,
			String branchId, String branch, String gradeId, String sectionId) {
		this.id = id;
		this.boardId = boardId;
		this.schoolId = schoolId;
		this.school = school;
		this.branchId = branchId;
		this.branch = branch;
		this.gradeId = gradeId;
		this.sectionId = sectionId;
	}

}
