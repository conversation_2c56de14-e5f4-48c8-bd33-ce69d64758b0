package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SchoolBranchTeacherNameResponseDto {
	
	@ApiModelProperty(notes = "School name", example = "MMARS", position = 1)
	private String school;
	
	@ApiModelProperty(notes = "Brach name", example = "MMARS-ICSE", position = 2)
	private String branch;
	
	@ApiModelProperty(notes = "Started teacher's name", example = "Thara", position = 3)
	private String startedByName;
	
	@ApiModelProperty(notes = "Ended teacher's name", example = "<PERSON>ji", position = 4)
	private String endedByName;
}
