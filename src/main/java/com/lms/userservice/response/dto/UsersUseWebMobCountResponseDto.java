package com.lms.userservice.response.dto;

import java.util.List;

import com.lms.userservice.projection.SchoolsProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsersUseWebMobCountResponseDto {

	@ApiModelProperty(value = "Id of the School", example = "2c91808480bc79330180c20aa7b60004", position = 1)
	private String schoolId;

	@ApiModelProperty(value = "Name of the School", example = "Mathews Mar Athanasius Residential School", position = 2)
	private String school;

	@ApiModelProperty(value = "No of the Branches users count", example = "", position = 3)
	private List<BranchUsersWebMobCountResponseDto> usersCount;

	public UsersUseWebMobCountResponseDto(SchoolsProjection projection) {
		this.schoolId = projection.getId();
		this.school = projection.getName();
	}

}
