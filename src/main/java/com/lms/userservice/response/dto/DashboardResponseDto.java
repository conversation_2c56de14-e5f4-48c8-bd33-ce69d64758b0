package com.lms.userservice.response.dto;

import java.util.List;

import com.lms.userservice.projection.DashboardProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DashboardResponseDto {
	
	@ApiModelProperty(value = "Grade count from school.", example = "10", position = 1)
	private Long gradesCount;

	@ApiModelProperty(value = "Section count from grade.", example = "IV", position = 2)
	private Long sectionsCount;
 
	@ApiModelProperty(value = "Student count from grade/school.", example = "50", position = 3)
	private Long studentCount;

	@ApiModelProperty(value = "Teacher count from school.", example = "15", position = 4)
	private Long teacherCount;

	@ApiModelProperty(value = "Coordinator count from school.", example = "4", position = 5)
	private Long coordinatorCount;
	
	@ApiModelProperty(value = "Assigned subject for teacher", position = 6)
	private List<SubjectsMinResponseDto> assignedSubjects;
	
	public DashboardResponseDto(Long studentCount) {
		this.studentCount = studentCount;
	}
	
	public DashboardResponseDto(Long sectionsCount, Long studentCount) {
		this.studentCount = studentCount;
		this.sectionsCount = sectionsCount;
	}
	
	public DashboardResponseDto(DashboardProjection projection) {
		this.gradesCount = projection.getGradesCount();
		this.sectionsCount = projection.getSectionsCount();
		this.studentCount = projection.getStudentCount();
		this.teacherCount = projection.getTeacherCount();
		this.coordinatorCount = projection.getCoordinatorCount();
	}
}
