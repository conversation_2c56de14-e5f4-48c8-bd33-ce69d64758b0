package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsersRoleResponseDto {

	@ApiModelProperty(example = "ff8081818041c904018041d079c50000", position = 1)
	private String roleId;

	@ApiModelProperty(example = "SUPER_ADMIN", position = 2)
	private Integer count;

	@ApiModelProperty(example = "SUPER_ADMIN", position = 3)
	private List<UserDetailsResponseDto> userDetails;

}
