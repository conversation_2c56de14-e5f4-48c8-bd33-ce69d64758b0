package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmberStudentsResponseDto {

	@ApiModelProperty(value = "Id of the Ember student", example = "402892888697da25018697dcf3b90000", position = 1)
	private String studentId;
	
	@ApiModelProperty(value = "Ember students name", example = "Virat Kohli", position = 2)
	private String studentName;

	@ApiModelProperty(value = "Ember students percentage", example = "36%", position = 3)
	private Integer emberPercentage;

}
