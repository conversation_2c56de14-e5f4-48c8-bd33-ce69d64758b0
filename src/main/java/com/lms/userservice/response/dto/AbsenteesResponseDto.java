package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Minimum details of student who hasn't attended the exam
 * 
 * <AUTHOR> Achari | 13 Dec 2023
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class AbsenteesResponseDto {

	@ApiModelProperty(value = "The selected schoolId", example = "402892888b4e5d01018b5b65490100c1", position = 1)
	private String studentId;

	@ApiModelProperty(value = "Combination of first/last name", example = "<PERSON><PERSON>", position = 2)
	private String name;

	@ApiModelProperty(value = "Username of student", example = "Sania.Mirsa.NPS_KRM", position = 2)
	private String userName;
}
