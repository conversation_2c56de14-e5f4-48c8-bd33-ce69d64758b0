package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TokenDetailsResponseDto {

	@ApiModelProperty(example = "ff80818180cc6a270180cc73e9a30000")
	private String userId;

	@ApiModelProperty(example = "<EMAIL>")
	private String email;

	@ApiModelProperty(example = "8945658923")
	private String phone;

	@ApiModelProperty(example = "Huma.Qureshi.MMARS_001")
	private String userName;
}
