package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class PlansGradesMappingResponseDto {

	@ApiModelProperty(value = "Plan-Grade mapping ID", example = "ff80818180f659540180f6b4aa250008", position = 1)
	private String id;

	@ApiModelProperty(value = "Grade ID", example = "ff80818180ead9320180eaff5b0c0008", position = 2)
	private String gradeId;

	@ApiModelProperty(value = "Grade Name", example = "Grade 1", position = 3)
	private String grade;

	@ApiModelProperty(value = "Plan template details", position = 4)
	private List<PlanTemplateResponseDto> planTemplates = new ArrayList<>();

	@ApiModelProperty(value = "Active status of grade in plan.", example = "true", position = 5)
	private boolean active;
	
	@ApiModelProperty(value = "Insertion order of the grades", example = "1", position = 6)
    private Integer sortOrder;

	public PlansGradesMappingResponseDto(String id, String gradeId, String grade, boolean active) {
		this.id = id;
		this.gradeId = gradeId;
		this.grade = grade;
		this.active = active;
	}
	
	public PlansGradesMappingResponseDto(String id, String gradeId, String grade, Integer sortOrder, boolean active) {
		this.id = id;
		this.gradeId = gradeId;
		this.grade = grade;
		this.sortOrder = sortOrder;
		this.active = active;
	}

}
