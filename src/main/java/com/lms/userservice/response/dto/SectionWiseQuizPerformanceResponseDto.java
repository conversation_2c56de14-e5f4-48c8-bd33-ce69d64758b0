package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectionWiseQuizPerformanceResponseDto {

	@ApiModelProperty(value = "Section Id", example = "ff80818180433e890180433ef2150000", position = 1)
	private String sectionId;

	@ApiModelProperty(value = "Section", example = "Section A", position = 2)
	private String section;

	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 3)
	private Long avgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Unit Quiz", example = "60", position = 5)
	private Long avgAttemptPercentageUQ;

	@ApiModelProperty(value = "Practice Quiz", example = "85", position = 7)
	private Long avgAttemptPercentagePQ;

}
