package com.lms.userservice.response.dto;

import java.util.List;



import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class CaseStudyReportResponseDto {

	@ApiModelProperty(value = "Id of the parent question", example = "ff808181870a831f06789a8a354d0000", position = 1)
	private String parentQuestionId;

	@ApiModelProperty(value = "Case study question Percentage in quiz", example = "67", position = 2)
	private Integer percentageInQuiz;

	@ApiModelProperty(value = "Case study Question", example = "1. Which type of plastid stores carbohydrates in potatoes?", position = 3)
	private String caseStudyQuestion;

	@ApiModelProperty(value = "Explanation of the case study question", position = 4)
	private String caseStudyExplanation;

	@ApiModelProperty(value = "Explanation of the case study question", position = 5)
	private List<TaxonomyPercentageDto> taxonomyPercentageDto;

	@ApiModelProperty(value = "Question wise report", position = 6)
	private List<QuestionResponseDto> subQuestion;

	@ApiModelProperty(value = "map", example = "@#$TFDGF", position = 6)
	private String map;

	@ApiModelProperty(value = "parentClassAverage", example = "77", position = 7)
	private Integer parentClassAverage;

	public CaseStudyReportResponseDto(String parentQuestionId, Integer percentageInQuiz, String caseStudyQuestion,
			String caseStudyExplanation, String map) {
		this.parentQuestionId = parentQuestionId;
		this.percentageInQuiz = percentageInQuiz;
		this.caseStudyQuestion = caseStudyQuestion;
		this.caseStudyExplanation = caseStudyExplanation;
		this.map = map;
	}

}
