package com.lms.userservice.response.dto;

import com.lms.userservice.feign.master.SubjectGroupsSubjectMappingsMinResponseDto;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentSubjectMappingsMinResponseDto {
    
    @ApiModelProperty(example = "String", position = 1)
    private String studentId;
    
    @ApiModelProperty(example = "String", position = 2)
    private String academicYearsId;
    
    @ApiModelProperty(example = "String", position = 3)
    private String academicYears;
    
    @ApiModelProperty(position = 4)
    private List<SubjectGroupsSubjectMappingsMinResponseDto> subjects;

    public StudentSubjectMappingsMinResponseDto(StudentsResponseDto studentsResponse, String academicYearsId, String academicYears) {
        this.studentId=studentsResponse.getId();
        this.academicYearsId=academicYearsId;
        this.academicYears=academicYears;
    }
    
}
