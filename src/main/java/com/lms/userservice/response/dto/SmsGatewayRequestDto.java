package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsGatewayRequestDto {

	@ApiModelProperty(value = "firstName of the selected persona", example = "Huma", position = 1)
	private String firstName;

	@ApiModelProperty(value = "userName of the selected persona", example = "Huma.Querasi.MMARS_001", position = 2)
	private String userName;

	@ApiModelProperty(value = "Mobile number of student", example = "8547890756", position = 3)
	private String phoneNumber;

	@ApiModelProperty(value = "Email address of user", example = "<EMAIL>", position = 4)
	private String emailId;

	@ApiModelProperty(value = "last updated", example = "(dd-mm-yy hh:mm AM)", position = 5)
	private String lastModifiedAt;

	@ApiModelProperty(value = "sms action", example = "USER_CREATION", position = 6)
	private String smsAction;

	@ApiModelProperty(value = "batch operation", example = "true", position = 7)
	private boolean batchOperation = false;

	@ApiModelProperty(value = "batch receptinist", position = 8)
	private List<BatchReceptionistRequestDto> receptionist;

	public SmsGatewayRequestDto(String firstName, String userName, String phoneNumber, String emailId,
			String smsAction) {
		this.firstName = firstName;
		this.userName = userName;
		this.phoneNumber = phoneNumber;
		this.emailId = emailId;
		this.smsAction = smsAction;
	}

	public SmsGatewayRequestDto(String firstName, String userName, String phoneNumber, String emailId,
			String lastModifiedAt, String smsAction) {
		this.firstName = firstName;
		this.userName = userName;
		this.phoneNumber = phoneNumber;
		this.emailId = emailId;
		this.lastModifiedAt = lastModifiedAt;
		this.smsAction = smsAction;
	}

}
