package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchoolsBranchsResponseDto {
	
	@ApiModelProperty(example = "2c91808480d1365d0180d148ee3c0000")
	private String schoolId;
	
	@ApiModelProperty(example = "MMARS_001")
	private String schoolCode;
	
	@ApiModelProperty(example = "Mathews Mar Athanesious Residential School")
	private String school;
	
	private List<BranchesMinDataResponseDto> branches;
}
