package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UsersCountResponseDto {

	@JsonInclude(value = Include.NON_EMPTY)
	private Long schoolCount;

	@JsonInclude(value = Include.NON_EMPTY)
	private Long branchCount;

	private Long studentCount;
	private Long teacherCount;
	private Long coordinatorCount;
	private Long principalCount;

	public UsersCountResponseDto(Long studentCount, Long teacherCount, Long coordinatorCount, Long principalCount) {
		this.studentCount = studentCount;
		this.teacherCount = teacherCount;
		this.coordinatorCount = coordinatorCount;
		this.principalCount = principalCount;
	}

}
