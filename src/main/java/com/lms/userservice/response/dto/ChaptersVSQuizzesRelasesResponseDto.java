package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChaptersVSQuizzesRelasesResponseDto {

	@ApiModelProperty(value = "Grade assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade Name assigned to teacher", example = "Grade 1", position = 2)
	private String grade;
	
	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Teacher count from chapter.", example = "32", position = 3)
	private Long totalChapters;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Teacher count from chapter.", example = "16", position = 4)
	private Long completedChapters;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Teacher count from quizzes.", example = "15", position = 5)
	private Long releasedQuizzes;
	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Teacher count from quizzes.", position = 5)
	List<ChaptersVSQuizzesRelasesBySectionResponseDto> sections;

	public ChaptersVSQuizzesRelasesResponseDto(String gradeId, String grade) {
		this.gradeId = gradeId;
		this.grade = grade;
	}

}
