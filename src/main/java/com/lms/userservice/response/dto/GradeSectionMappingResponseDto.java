package com.lms.userservice.response.dto;

import com.lms.userservice.enums.SectionData;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Designed to get all the grade-section mapping
 * 
 * <AUTHOR>
 * @since 2.0.0
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeSectionMappingResponseDto {
	
    @ApiModelProperty(value = "Mapping Id", example = "String", position = 1)
    private String id;

    @ApiModelProperty(value = "Grade Id", example = "String", position = 2)
    private String gradeId;

    @ApiModelProperty(value = "Grade Name", example = "String", position = 3)
    private String grade;

    @ApiModelProperty(value = "Section Id", example = "String", position = 4)
    private String sectionId;

    @ApiModelProperty(value = "Section Name", example = "String", position = 5)
    private String section;
    
    @ApiModelProperty(value = "Section type", example = "String", position = 6)
    private SectionData sectionData;

    @ApiModelProperty(value = "Active", example = "true", position = 7)
    private boolean active = false;
    
    @ApiModelProperty(value = "Sorting Order", example = "true", position = 8)
    private Integer sortOrder;
	
}
