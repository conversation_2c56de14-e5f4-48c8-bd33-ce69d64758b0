package com.lms.userservice.response.dto;

import java.util.List;

import com.lms.userservice.projection.SchoolsProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SchoolBranchDetailsResponseDto {

	@ApiModelProperty(value = "Id of the school for dashboard", example = "4028818380665dd20180667ee7ee0003", position = 1)
	private String schoolId;

	@ApiModelProperty(value = "Name of the school for dashboard", example = "Navaudaya", position = 2)
	private String schoolName;

	@ApiModelProperty(value = "Code of the school for dashboard", example = "LAW123", position = 3)
	private String schoolCode;

	@ApiModelProperty(value = "count of the brances for the school", example = "12", position = 4)
	private Integer branchCount;

	@ApiModelProperty(value = "list of branches", position = 5)
	private List<BranchMinResponseDto> branches;

	public SchoolBranchDetailsResponseDto(String schoolId, String schoolName, String schoolCode) {
		this.schoolId = schoolId;
		this.schoolName = schoolName;
		this.schoolCode = schoolCode;
	}

	public SchoolBranchDetailsResponseDto(SchoolsProjection projection) {
		this.schoolId = projection.getId();
		this.schoolName = projection.getName();
		this.schoolCode = projection.getCode();
	}

}
