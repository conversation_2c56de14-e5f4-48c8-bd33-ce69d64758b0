package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class BlueprintLevelResponseDto {
	
	@ApiModelProperty(value = "Id of blueprint level", example = "ff80818181d1bf4d0181d1c189440000", position = 1)
	private String id;

	@ApiModelProperty(value = "Level numbers, only numbers allowed", example = "1", required = true, position = 2)
	private int levelNumber;

	@ApiModelProperty(value = "Level name", example = "Level 1", position = 3)
	private String levelName;

	@ApiModelProperty(value = "Level description", example = "U-30|A-50|S-20", position = 4)
	private String description;
	
	@ApiModelProperty(value = "Active status", example = "true", position = 5)
	private boolean active;
}
