package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GradeSectionSubjectsResponseDto {

	@ApiModelProperty(value = "Grade assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Section assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 2)
	private String sectionId;

	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 3)
	private String subjectId;

	@ApiModelProperty(value = "Subtopic assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 4)
	private String subTopicId;

}
