package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SyllabusSectionAccessResponseDto {

	@ApiModelProperty(value = "Section assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String sectionId;

	@ApiModelProperty(value = "Section Name assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 2)
	private String section;
	
	@ApiModelProperty(value = "Student count from grade.", example = "32", position = 3)
	private long totalPercentage;

	@ApiModelProperty(value = "Student count from section", example = "32", position = 4)
	private long completedPercentage;

}
