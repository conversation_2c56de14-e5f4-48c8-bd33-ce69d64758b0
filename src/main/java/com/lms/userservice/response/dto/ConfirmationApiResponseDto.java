package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Common to all the confirmation API
 * 
 * <AUTHOR> | Wed 30-Nov-2022
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class ConfirmationApiResponseDto {

	@ApiModelProperty(value = "Indicating the mapping existance", example = "true", position = 1)
	private boolean mappingExists;

	@ApiModelProperty(value = "Return message for the data", example = "No mapping found in the workflow.", position = 2)
	private String message;
}
