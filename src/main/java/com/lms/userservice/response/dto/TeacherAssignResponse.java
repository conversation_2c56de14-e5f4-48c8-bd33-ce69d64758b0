package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TeacherAssignResponse {
    @ApiModelProperty(value = "Primary key", example = "402880e78262feb901826304a0350001")
    private String id;

    @ApiModelProperty(value = "Id of subject mapped", example = "402880e78262feb901826304a0350001")
    private String subjectId;

    @ApiModelProperty(value = "Title of the subject", example = "Science")
    private String subject;

    @ApiModelProperty(value = "Id of subtopic mapped", example = "2c918085826c223101826daed1a50008")
    private String subtopicId;

    @ApiModelProperty(value = "Title of the subtopic", example = "Physics")
    private String subtopic;

    @ApiModelProperty(value = "Id of grade mapped", example = "2c918085826c223101826daed1a50008")
    private String gradeId;

    @ApiModelProperty(value = "Title of the grade", example = "X")
    private String grade;

    @ApiModelProperty(value = "Id of section", example = "2c918085826c223101826daed1a50008")
    private String sectionId;

    @ApiModelProperty(value = "Title of the section", example = "A")
    private String section;

    @ApiModelProperty(value = "Active field", example = "true")
    private boolean active;
    
    

}
