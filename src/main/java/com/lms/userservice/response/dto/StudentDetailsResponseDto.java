package com.lms.userservice.response.dto;

import com.lms.userservice.projection.StudentsProjection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StudentDetailsResponseDto {

	private String studentId;

	private String boardId;

	private String gradeId;

	private String sectionId;

	private String branchId;

	private String schoolId;

	public StudentDetailsResponseDto(StudentsProjection projection) {
		this.studentId = projection.getId();
		this.boardId = projection.getBoardId();
		this.gradeId = projection.getGradeId();
		this.sectionId = projection.getSectionId();
		this.branchId = projection.getBranchId();
		this.schoolId = projection.getSchoolId();
	}

}
