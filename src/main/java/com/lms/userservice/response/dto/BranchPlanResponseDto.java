package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchPlanResponseDto {
	
	@ApiModelProperty(example = "4028818380665dd20180668a864c0009")
	private String id;
	
	@ApiModelProperty(example = "4028818580b2dcf40180b2e8f2e20000")
	private String planId;
	
	@ApiModelProperty(example = "Pre-Board Plan 01")
	private String planName;
	
	@ApiModelProperty(example = "true")
	private boolean active;
}
