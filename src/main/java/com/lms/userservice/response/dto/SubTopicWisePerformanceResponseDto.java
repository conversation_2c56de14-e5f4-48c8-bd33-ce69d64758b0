package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubTopicWisePerformanceResponseDto {

	@ApiModelProperty(value = "SubTopic Id assigned to teacher", example = "ff80818180ead9320180eafe48800004", position = 1)
	@JsonInclude(value = Include.NON_NULL)
	private String subTopicId;

	@ApiModelProperty(value = "SubTopic Name assigned to teacher", example = "Physics", position = 2)
	@JsonInclude(value = Include.NON_NULL)
	private String subTopic;

	@ApiModelProperty(value = "Unit Quiz", example = "70", position = 3)
	@JsonInclude(value = Include.NON_NULL)
	private Long avgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Unit Quiz", example = "80", position = 4)
	@JsonInclude(value = Include.NON_NULL)
	private Long globalAvgScorePercentageUQ; // only unit quiz

	@ApiModelProperty(value = "Unit Quiz", example = "60", position = 5)
	@JsonInclude(value = Include.NON_NULL)
	private Long avgAttemptPercentageUQ;

	@ApiModelProperty(value = "Unit Quiz", example = "80", position = 6)
	@JsonInclude(value = Include.NON_NULL)
	private Long globalAvgAttemptPercentageUQ;

	@ApiModelProperty(value = "Practice Quiz", example = "85", position = 7)
	@JsonInclude(value = Include.NON_NULL)
	private Long avgAttemptPercentagePQ;

	@ApiModelProperty(value = "Practice Quiz", example = "70", position = 8)
	@JsonInclude(value = Include.NON_NULL)
	private Long globalAvgAttemptPercentagePQ;

	@ApiModelProperty(value = "Unit Quiz", example = "Good work!", position = 9)
	@JsonInclude(value = Include.NON_NULL)
	private String unitAttemptRateMessage;

	@ApiModelProperty(value = "Practice Quiz", example = "Good work!", position = 10)
	@JsonInclude(value = Include.NON_NULL)
	private String practiceAttemptRateMessage;

	public SubTopicWisePerformanceResponseDto(String subTopicId, String subTopic) {
		this.subTopicId = subTopicId;
		this.subTopic = subTopic;
	}

}
