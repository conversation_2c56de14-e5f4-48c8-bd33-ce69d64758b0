package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfileResponseDto {

	@ApiModelProperty(example = "ZSC_ER45")
	private String userName;
	
	@JsonInclude(value = Include.NON_NULL)
	private TeacherResponseDto teacher;
	
	@JsonInclude(value = Include.NON_NULL)
	private StudentsResponseDto student;
	
	@JsonInclude(value = Include.NON_NULL)
	private AdministrationResponseDto administration;
	
	@JsonInclude(value = Include.NON_NULL)
	private AdminUsersResponseDto adminUsers;
	
}
