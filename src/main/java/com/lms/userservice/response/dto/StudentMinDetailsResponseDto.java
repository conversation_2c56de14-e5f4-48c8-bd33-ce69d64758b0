package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentMinDetailsResponseDto {
	
	@ApiModelProperty(value = "Id of the Student.", example = "ff80818180f65df90180f67e409a017e", position = 1)
	private String id;
	
	@ApiModelProperty(value = "First name", example = "Zara", required = true, position = 2)
    private String firstName;
    
	@ApiModelProperty(value = "Last name", example = "<PERSON>akir", required = true, position = 3)
    private String lastName;
		
	@ApiModelProperty(value = "Username", example = "ZKR_0845", required = true, position = 4)
    private String userName;
	
	@ApiModelProperty(value = "Active status of change profile student", example = "true", required = true, position = 5)
    private boolean active;
	
}
