package com.lms.userservice.response.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.lms.userservice.projection.UsersProjection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The {@code UsersFeignDto} class is used to retrieve from the DB. <br>
 * {@code @Data} annotation is used to generate <br>
 * <i>Get<PERSON>, Setters, Parameterized Constructor, toString, equals and HashCode
 * methods</i>
 * 
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsersFeignDto {

	private String id;
	private String userName;
	private String email;
	private String phoneNumber;
	private String password;
	private String fullName;
	private LocalDateTime lastLoginTime;
	private List<UserRolesResponseDto> userRoles;

	public UsersFeignDto(String id, String userName, String email, String phoneNumber, String password, String firstName, String lastName) {
		this.id = id;
		this.userName = userName;
		this.email = email;
		this.phoneNumber = phoneNumber;
                this.password=password;
		this.fullName = firstName.concat(" ").concat(lastName);
	}

	public UsersFeignDto(UsersProjection projection) {
		this.id = projection.getId();
		this.userName = projection.getUserName();
		this.email = projection.getEmail();
		this.phoneNumber = projection.getPhoneNumber();
		this.lastLoginTime = projection.getLastLoginTime();
                this.fullName=projection.getFirstName().concat(" ").concat(projection.getLastName());
	}

}
