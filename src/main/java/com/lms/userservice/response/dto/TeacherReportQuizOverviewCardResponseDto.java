package com.lms.userservice.response.dto;

import java.util.List;



import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherReportQuizOverviewCardResponseDto {

	@ApiModelProperty(value = "Id of the chapter", example = "ff808181870a831f06789a8a354d0000", position = 1)
	private String chapterId;

	@ApiModelProperty(value = "Name of the Chapter", example = "Biology", position = 2)
	private String chapterName;

	@ApiModelProperty(value = "No of student in section", example = "50", position = 3)
	private Integer studentCountInSection;

	@ApiModelProperty(value = "No of students completed quiz", example = "40", position = 4)
	private Long studentCompletedQuiz;

	@ApiModelProperty(value = "No of student attended the quiz in percentage", example = "80", position = 5)
	private Long quizAttendance;

	@ApiModelProperty(value = "No of times quiz released", example = "4", position = 6)
	private Integer noOfTimesQuizReleased;

	@ApiModelProperty(value = "No of questions in the quiz", example = "40", position = 7)
	private Integer noOfQuestions;

	@ApiModelProperty(value = "No of case studies in the quiz", example = "2", position = 8)
	private Long noOfCaseStudies;

	@ApiModelProperty(value = "First quiz release date", example = "08-08-2022", position = 9)
	private String firstReleaseDate;

	@ApiModelProperty(value = "latest quiz release date", example = "23-12-2022", position = 10)
	private String latestReleaseDate;

	@ApiModelProperty(value = "Average time taken to complte the exam ", example = "00Hrs 24Mins 10Sec", position = 11)
	private String averageTimeTaken;

	@ApiModelProperty(value = "Average time taken to complete the exam ", example = "00Hrs 4Mins 12Sec", position = 12)
	private String averageTimeTakenQuestion;

//	private List<TaxonomyCalculationMinResponseDto> TaxonomyCalculations;

}
