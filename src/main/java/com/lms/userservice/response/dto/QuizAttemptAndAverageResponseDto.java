package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuizAttemptAndAverageResponseDto {

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 1)
	private Double unitQuizAttemptRate;

	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 2)
	private Double unitGlobalQuizAttemptRate;

	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 3)
	private String unitAttemptRateMessage;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 4)
	private Double practiceQuizAttemptRate;

	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 5)
	private Double practiceGlobalQuizAttemptRate;

	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 6)
	private String practiceAttemptRateMessage;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 7)
	private Double quizAverage;

	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 8)
	private Double globalQuizAverage;

	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 9)
	private String averageMessage;

}
