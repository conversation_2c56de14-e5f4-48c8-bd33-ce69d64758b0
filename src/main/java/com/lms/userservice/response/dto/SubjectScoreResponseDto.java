package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubjectScoreResponseDto {

	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String subjectId;

	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 2)
	private String subject;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Subtopics score", position = 3)
	private List<SubtopicScoreResponseDto> subtopics;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 4)
	private Long unitQuizAttemptRate;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 5)
	private Long unitGlobalQuizAttemptRate;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 6)
	private String unitAttemptRateMessage;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 7)
	private Long practiceQuizAttemptRate;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 8)
	private Long practiceGlobalQuizAttemptRate;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 9)
	private String practiceAttemptRateMessage;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 10)
	private Long quizAverageScorePercentage;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 11)
	private Long globalQuizAverageScorePercentage;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 12)
	private String averageMessage;
	
	@ApiModelProperty(example = "Yes", position = 13)
	private boolean skilledSubject;

	public SubjectScoreResponseDto(String subjectId, String subject, List<SubtopicScoreResponseDto> subtopics) {
		this.subjectId = subjectId;
		this.subject = subject;
		this.subtopics = subtopics;
	}

	public SubjectScoreResponseDto(String subjectId, String subject, Long unitQuizAttemptRate,
			Long unitGlobalQuizAttemptRate, String unitAttemptRateMessage, Long practiceQuizAttemptRate,
			Long practiceGlobalQuizAttemptRate, String practiceAttemptRateMessage, Long quizAverageScorePercentage,
			Long globalQuizAverageScorePercentage, String averageMessage) {
		this.subjectId = subjectId;
		this.subject = subject;
		this.unitQuizAttemptRate = unitQuizAttemptRate;
		this.unitGlobalQuizAttemptRate = unitGlobalQuizAttemptRate;
		this.unitAttemptRateMessage = unitAttemptRateMessage;
		this.practiceQuizAttemptRate = practiceQuizAttemptRate;
		this.practiceGlobalQuizAttemptRate = practiceGlobalQuizAttemptRate;
		this.practiceAttemptRateMessage = practiceAttemptRateMessage;
		this.quizAverageScorePercentage = quizAverageScorePercentage;
		this.globalQuizAverageScorePercentage = globalQuizAverageScorePercentage;
		this.averageMessage = averageMessage;

	}

}
