package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubTopicsMinResponseDto {

	@ApiModelProperty(notes = "Id of sub_topic", example = "2c9180878471c1fd0184743576d5009a", position = 1)
	private String id;
	
	@ApiModelProperty(notes = "Sub_Topic name", example = "Physics", position = 2)
	private String subTopic;
	
	@ApiModelProperty(notes = "Active stage", example = "true", allowableValues = "true, false", position = 3)
	private boolean active;

	@ApiModelProperty(example = "false", position = 4)
	private boolean hideSubtopics;
}
