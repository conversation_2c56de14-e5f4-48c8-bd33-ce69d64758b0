package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SubTopicBulkInnerResponseDto {
	@ApiModelProperty(value = "Id of sub-topic", example = "ff80818180ead9320180eadd0a6a0001", position = 1)
	private String id;

	@ApiModelProperty(value = "Sub-topic name", example = "Biology", position = 2)
	private String subTopic;

	@ApiModelProperty(value = "Chapter Count", position = 3)
	private Integer chapterCount;

	@ApiModelProperty(value = "Quiz count", position = 4)
	private Integer quizCount;

	@ApiModelProperty(value = "Score", position = 5)
	private Integer score;

	@ApiModelProperty(value = "Active status", example = "true", position = 6)
	private boolean active;
}
