package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChangeProfileResponseDto {
		
	@ApiModelProperty(value = "List of students from school.", position = 1)
	private List<StudentMinDetailsResponseDto> students = new ArrayList<>();
			
	@ApiModelProperty(value = "To Section Id", example = "ff80818180433e890180433ef2150000", position = 2)
	private String toSectionId;
	
	@ApiModelProperty(value = "to Section Name",example = "section name A,B,C etc", position = 3)
	private String toSectionName;
				
	@ApiModelProperty(value = "To Grade Id" ,example = "2ff80818180ead9320180eaff5b0c0008", required = true, position = 4)
	private String toGradeId;
				
	@ApiModelProperty(value = "To Grade Name", example = "2c91808480bc79330180c20aa7b60004", required = true, position = 5)
	private String toGradeName;
	
	@ApiModelProperty(value = "Affilicated board of the branch", example = "ff80818180ead9320180eb06357f0014", required = true,  position = 6)
	private String branchId;
	
	@ApiModelProperty(value = "Branch Name", example = "Mathews Mar Athanesious Residential School : ICSE/ISE" , required = true, position = 7)
	private String branchName;
	
	@ApiModelProperty(value = "School Id", example = "2c91808480bc79330180c20aa7b60004", required = true, position = 8)
	private String schoolId;
	
	@ApiModelProperty(value = "School Name", example = "Mathews Mar Athanasius Residential School", required = true, position = 9)
	private String schoolName;
	
	@ApiModelProperty(value = "Action taken same year", example = "true", position = 10)
	private boolean sameYear;	
	
	@ApiModelProperty(value = "Action taken on current year's end", example = "true", position = 11)
	private boolean yearEndProcess;
			
}
