package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlanTemplateImplResponseDto {
	
	@ApiModelProperty(value = "Name of the menu, sub-menu, document-category or button", example = "Assign Quiz", position = 1)
	private String name;
	
	@ApiModelProperty(value = "Type of the toggle item.", example = "MENU", allowableValues = "MENU, SUB-MENU, DOCUMENT-CATEGORY, BUTTON", position = 2)
	private String type;
	
	@ApiModelProperty(value = "ToggleStatus=true Show | ToggleStatus=false Hide ", example = "true", allowableValues = "true, false", position = 3)
	private boolean toggleStatus;
	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "For sub-menus only", position = 4)
	private List<PlanTemplateImplResponseDto> subMenus;
}
