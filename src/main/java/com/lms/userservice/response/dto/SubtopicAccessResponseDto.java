package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubtopicAccessResponseDto {
	
	@ApiModelProperty(notes = "Id of sub_topic", example = "2c9180878471c1fd0184743576d5009a", position = 1)
	private String subTopicId;
	
	@ApiModelProperty(notes = "Sub_Topic name", example = "Physics", position = 2)
	private String subTopic;
	
	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(notes = "Section listing", position = 3)
	private List<SectionAccessResponseDto> sectionsForSubTopic;
	
	public SubtopicAccessResponseDto(String subTopicId, String subTopic) {
		this.subTopicId = subTopicId;
		this.subTopic = subTopic;
	}
}
