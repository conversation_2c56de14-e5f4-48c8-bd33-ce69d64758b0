package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.userservice.enums.Gender;
import com.lms.userservice.projection.AdministrationProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdministrationResponseDto {

	@ApiModelProperty(example = "ff80818180cc6a270180cc73e9a30000")
	private String id;

	@ApiModelProperty(example = "David")
	private String firstName;

	@ApiModelProperty(example = "Beckham")
	private String lastName;

	@ApiModelProperty(example = "<EMAIL>")
	private String email;

	@ApiModelProperty(example = "7025785634")
	private String mobile;

	@ApiModelProperty(example = "MALE")
	private Gender gender;

	private Boolean active;

	@ApiModelProperty(example = "false")
	private Boolean deleted;

	@ApiModelProperty(example = "30")
	private Long numberOfSchools;

	@ApiModelProperty(example = "60")
	private Long numberOfBranches;

	@ApiModelProperty(example = "Mathews Mar Athanesious Residential School")
	private String schoolName;

	@ApiModelProperty(example = "ff80818180cc6a270180cc73e9a56025")
	private String userId;

	@ApiModelProperty(example = "David-Beckham-MMARS_001")
	private String userName;

	private List<SchoolsBranchsResponseDto> institutions = new ArrayList<>();

	@ApiModelProperty(example = "LOCAL")
	private String lmsEnv;

	@JsonIgnore
	private String password;

	@JsonIgnore
	private String typeOfEmailSend;

	@JsonIgnore
	private String roleName;

	// first name & last name of person who editing the details.
	@JsonIgnore
	private String adminName;

	@JsonIgnore
	private String roleNameOfAdmin;

	public AdministrationResponseDto(AdministrationProjection projection) {
		this.id = projection.getId();
		this.firstName = projection.getFirstName();
		this.lastName = projection.getLastName();
		this.email = projection.getEmail();
		this.mobile = projection.getMobile();
		this.gender = Gender.valueOf(projection.getGender());
		this.active = projection.getActive();
		this.deleted = projection.getDeleted();
		this.numberOfSchools = projection.getNumberOfSchools();
		this.numberOfBranches = projection.getNumberOfBranches();
		this.schoolName = projection.getSchoolName();
		this.userId = projection.getUserId();
		this.userName = projection.getUserName();
	}

	public AdministrationResponseDto(AdministrationProjection projection,
			List<SchoolsBranchsResponseDto> institutions) {
		this.id = projection.getId();
		this.firstName = projection.getFirstName();
		this.lastName = projection.getLastName();
		this.email = projection.getEmail();
		this.mobile = projection.getMobile();
		this.gender = Gender.valueOf(projection.getGender());
		this.active = projection.getActive();
		this.deleted = projection.getDeleted();
		this.numberOfSchools = projection.getNumberOfSchools();
		this.numberOfBranches = projection.getNumberOfBranches();
		this.schoolName = projection.getSchoolName();
		this.userId = projection.getUserId();
		this.userName = projection.getUserName();
		this.institutions = institutions;
	}

}
