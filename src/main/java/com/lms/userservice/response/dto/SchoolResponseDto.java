package com.lms.userservice.response.dto;

import java.util.ArrayList;
import java.util.List;

import com.lms.userservice.feign.master.BoardsResponseDto;
import com.lms.userservice.projection.SchoolsProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SchoolResponseDto {
	
	@ApiModelProperty(example = "2c91808480bc79330180c20aa7b60004")
	private String id;
	
	@ApiModelProperty(example = "Mathews Mar Athanasius Residential School")
	private String name;
	
	@ApiModelProperty(example = "MMARS_001")
	private String code;
	
	@ApiModelProperty(example = "ff80818180bc92e90180bc9b3c080009")
	private String cityId;
	
	@ApiModelProperty(example = "Karumady EDB.O")
	private String city;
	
	@ApiModelProperty(example = "<EMAIL>")
	private String pocEmail;
	
	@ApiModelProperty(example = "8906754376")
	private String phoneNumber;
	
	@ApiModelProperty(example = "http://mmarschoolise.com")
	private String website;
	
	@ApiModelProperty(example = "Abraham Joy")
	private String signatoryName;
	
	@ApiModelProperty(example = "President")
	private String signatoryRole;
	
	@ApiModelProperty(example = "https://s3.ap-south-1.amazonaws.com/lmscontentupload/User-Service/b2da6fc0-b089-473b-ad23-eec27f24333e_Smiley.jpg")
	private String logoUrl;
	
	//@ApiModelProperty(example = "ff80818180ead9320180eb06357f0014")
	//private String boardId;
	
	//@ApiModelProperty(example = "CISCE(ICSE/ISC/CVE)")
	//private String board;
	
	@ApiModelProperty(example = "10")
	private Long numberOfBranches;
	
	@ApiModelProperty(example = "20")
	private Long numberOfCoordinators;
	
	@ApiModelProperty(example = "200")
	private Long numberOfTeachers;
	
	@ApiModelProperty(example = "10")
	private Long numberOfPrincipals;
	
	@ApiModelProperty(example = "15000")
	private Long numberOfStudents;
	
	@ApiModelProperty(value = "List of boards from the branches of school.")
	private List<BoardsResponseDto> boardList = new ArrayList<>();
	
	private Boolean active;
	
	@ApiModelProperty(value = "Environment of application", example = "LOCAL", allowableValues = "LOCAL, DEV, TEST, UAT, PRE_PROD, PROD", required = true)
	private String lmsEnv;

	public SchoolResponseDto(SchoolsProjection projection) {
		this.id = projection.getId();
		this.name = projection.getName();
		this.code = projection.getCode();
		this.cityId = projection.getCityId();
		this.pocEmail = projection.getPocEmail();
		this.phoneNumber = projection.getPhoneNumber();
		this.website = projection.getWebsite();
		this.signatoryName = projection.getSignatoryName();
		this.signatoryRole = projection.getSignatoryRole();
		this.logoUrl = projection.getLogoUrl();
		this.numberOfBranches = projection.getNumberOfBranches();
		this.numberOfCoordinators = projection.getNumberOfCoordinators();
		this.numberOfTeachers = projection.getNumberOfTeachers();
		this.numberOfPrincipals = projection.getNumberOfPrincipals();
		this.numberOfStudents = projection.getNumberOfStudents();
		this.active = projection.getActive();
	}

	public SchoolResponseDto(String id, String name, String code, String cityId, String pocEmail, String phoneNumber,
			String website, String signatoryName, String signatoryRole, String logoUrl,	Boolean active) {
		this.id = id;
		this.name = name;
		this.code = code;
		this.cityId = cityId;
		this.pocEmail = pocEmail;
		this.phoneNumber = phoneNumber;
		this.website = website;
		this.signatoryName = signatoryName;
		this.signatoryRole = signatoryRole;
		this.logoUrl = logoUrl;
		this.active = active;
	}

}