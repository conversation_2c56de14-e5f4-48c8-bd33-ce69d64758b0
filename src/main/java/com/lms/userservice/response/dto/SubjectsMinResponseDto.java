package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubjectsMinResponseDto {
	
	@ApiModelProperty(notes = "Id of subject", example = "2c9180878471c1fd0184743576c10099", position = 1)
	private String subjectId;
	
	@ApiModelProperty(notes = "Subject name", example = "Science", position = 2)
	private String subject;
	
	@ApiModelProperty(notes = "Active stage", example = "true", allowableValues = "true, false", position = 3)
	private boolean active;
	
	@ApiModelProperty(notes = "List of subtopics", position = 4)
	private List<SubTopicsMinResponseDto> subTopics;

	@ApiModelProperty(example = "false", position = 3)
	private boolean hideSubtopics;
	
	@ApiModelProperty(example = "true", position = 5)
	private boolean skilledSubject;
	
	public SubjectsMinResponseDto(String id, String subject, boolean active, boolean hideSubtopics, boolean skilledSubject) {
		this.subjectId = id;
		this.subject = subject;
		this.active = active;
		this.hideSubtopics = hideSubtopics;
		this.skilledSubject = skilledSubject;
	}
	

//	public SubjectsMinResponseDto(SubjectsProjection projection) {
//		this.subjectId = projection.getId();
//		this.subject = projection.getSubject();
//		this.active = projection.isActive();
//		this.hideSubtopics = projection.isHideSubtopics();
//
//	}
}
