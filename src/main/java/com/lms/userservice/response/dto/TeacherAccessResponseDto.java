package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherAccessResponseDto {

	@ApiModelProperty(value = "Assigned grade for teacher", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Assigned grade for teacher", position = 2)
	private String grade;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Subject list", position = 3)
	private List<SubjectAccessResponseDto> subjects;

	public TeacherAccessResponseDto(String gradeId, String grade) {
		this.gradeId = gradeId;
		this.grade = grade;
	}
}
