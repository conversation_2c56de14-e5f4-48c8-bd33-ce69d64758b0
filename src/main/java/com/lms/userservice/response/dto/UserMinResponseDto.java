package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserMinResponseDto {

	@ApiModelProperty(value = "user id", example = "ff80818181293fa2018129458e000001", position = 1)
	@JsonInclude(value = Include.NON_NULL)
	private String id;

	@ApiModelProperty(value = "user_name", example = "Renu.David.MMARS_001", position = 2)
	@JsonInclude(value = Include.NON_NULL)
	private String userName;

	@ApiModelProperty(value = "Email", example = "<EMAIL>", position = 3)
	@JsonInclude(value = Include.NON_NULL)
	private String email;

	@ApiModelProperty(value = "first/last name combination", example = "Renu David", position = 4)
	@JsonInclude(value = Include.NON_NULL)
	private String name;

	public UserMinResponseDto(String id, String userName, String email) {
		this.id = id;
		this.userName = userName;
		this.email = email;
	}

	public UserMinResponseDto(String userName, String name) {
		this.userName = userName;
		this.name = name;
	}

}
