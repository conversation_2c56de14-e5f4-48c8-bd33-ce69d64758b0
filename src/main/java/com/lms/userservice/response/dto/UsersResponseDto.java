package com.lms.userservice.response.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.projection.UsersProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The {@code UsersResponseDto} class is used to retrieve from the DB. <br>
 * {@code @Data} annotation is used to generate <br>
 * <i>Getters, Setters, Parameterized Constructor, toString, equals and HashCode
 * methods</i>
 * 
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsersResponseDto {

	@ApiModelProperty(example = "ff8081818008e999018008f1c4730000")
	private String id;

	@ApiModelProperty(example = "ZSC_ER45")
	private String userName;

	@ApiModelProperty(example = "<EMAIL>")
	private String email;

	@ApiModelProperty(example = "9856784590")
	private String phoneNumber;

	@ApiModelProperty(example = "2022-04-21T08:33:03")
	private LocalDateTime lastLoginTime;

	@ApiModelProperty(example = "2022-04-21T08:33:03")
	private List<UserRolesResponseDto> userRoles;
	
	@ApiModelProperty(example = "2022-04-21T08:33:03")
	private List<RolesFeignDto> roles;

	public UsersResponseDto(String id, String userName, String email, String phoneNumber) {
		this.id = id;
		this.userName = userName;
		this.email = email;
		this.phoneNumber = phoneNumber;
	}

	public UsersResponseDto(UsersProjection projection) {
		this.id = projection.getId();
		this.userName = projection.getUserName();
		this.email = projection.getEmail();
		this.phoneNumber = projection.getPhoneNumber();
		this.lastLoginTime = projection.getLastLoginTime();
	}

}
