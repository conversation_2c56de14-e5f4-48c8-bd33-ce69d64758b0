package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrincipalGradeWiseQuizPerformanceResponseDto {

	@ApiModelProperty(value = "Id of the board for dashboard", example = "4028928883193d8701831d218cff0001", position = 1)
	private String boardId;

	@ApiModelProperty(value = "Name of the board for dashboard", example = "CBSE", position = 2)
	private String board;

	@ApiModelProperty(value = "Id of the school for dashboard", example = "4028818380665dd20180667ee7ee0003", position = 3)
	private String schoolId;

	@ApiModelProperty(value = "Name of the school for dashboard", example = "Navaudaya", position = 4)
	private String school;

	@ApiModelProperty(value = "Id of the branch for dashboard", example = "402892888318a2c801833c0f003b0068", position = 5)
	private String branchId;

	@ApiModelProperty(value = "Name of the branch for dashboard", example = "LMS branch", position = 6)
	private String branch;

	@ApiModelProperty(value = "List of grade details", position = 7)
	private List<GradeWiseQuizPerformanceResponseDto> gradeDetails;

	public PrincipalGradeWiseQuizPerformanceResponseDto(String boardId, String board, String schoolId, String school,
			String branchId, String branch) {
		this.boardId = boardId;
		this.board = board;
		this.schoolId = schoolId;
		this.school = school;
		this.branchId = branchId;
		this.branch = branch;
	}

}
