package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SectionSubjectScoreResponseDto {

	@ApiModelProperty(value = "Section assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String sectionId;

	@ApiModelProperty(value = "Section Name assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 2)
	private String section;
	
	@ApiModelProperty(value = "Subjects score", position = 3)
	private List<SubjectScoreResponseDto> subjects;

}
