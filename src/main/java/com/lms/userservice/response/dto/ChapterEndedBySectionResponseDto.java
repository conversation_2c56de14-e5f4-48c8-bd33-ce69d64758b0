package com.lms.userservice.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChapterEndedBySectionResponseDto {

	@ApiModelProperty(value = "Section based count of end chapter and released quizzes", example = "40289288840fe4440184142921600011", position = 1)
	private String sectionId;

	@ApiModelProperty(value = "Count of ended chapter", example = "10", position = 2)
	private Long chapterCount;

	@ApiModelProperty(value = "Count of released quizzes", example = "5", position = 3)
	private Long quizReleaseCount;
}
