package com.lms.userservice.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class PlanTemplateResponseDto {

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(example = "ff80818180ead9320180eaff5b0c0008", position = 1)
	private String id;

	@ApiModelProperty(example = "ff80818180ead9320180eadd0a220000", position = 2)
	private String subjectId;

	@ApiModelProperty(example = "Biology", position = 3)
	private String subject;

	@ApiModelProperty(example = "true", position = 4)
	private boolean lessonPlan;

	@ApiModelProperty(example = "true", position = 5)
	private boolean teacherRevisionModule;

	@ApiModelProperty(example = "true", position = 6)
	private boolean rmLock;

	@ApiModelProperty(example = "true", position = 7)
	private boolean assessmentModule;

	@ApiModelProperty(example = "true", position = 8)
	private boolean activities;

	@ApiModelProperty(example = "true", position = 9)
	private boolean wsDownload;

	@ApiModelProperty(example = "true", position = 10)
	private boolean wbDownload;

	@ApiModelProperty(example = "true", position = 11)
	private boolean newsStories;

	@ApiModelProperty(example = "true", position = 12)
	private boolean studentBooklet;

	@ApiModelProperty(example = "true", position = 13)
	private boolean studentRevisionModule;

	@ApiModelProperty(example = "true", position = 14)
	private boolean active;

	public PlanTemplateResponseDto(String id, String subjectId, String subject, boolean active) {
		this.id = id;
		this.subjectId = subjectId;
		this.subject = subject;
		this.active = active;
	}
}
