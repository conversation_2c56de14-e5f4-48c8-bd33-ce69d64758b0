package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GradeAccessInfoStudentsDetailsResponseDto {

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Grade count from principal access.", example = "10", position = 1)
	private Integer totalGradesPL;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Student count from principal access.", example = "85", position = 2)
	private Integer totalStudentsPL;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Grade count from teacher access.", example = "10", position = 3)
	private Integer totalGradesTR;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Student count from teacher access.", example = "85", position = 4)
	private Integer totalStudentsTR;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Teacher count from grade", example = "32", position = 5)
	private Integer totalTeachers;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Co-ordinator count from grade", example = "32", position = 6)
	private Integer totalCoordinators;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Grade list", position = 7)
	private List<GradeAccessStudentsDetailsResponseDto> gradeDetails;

}
