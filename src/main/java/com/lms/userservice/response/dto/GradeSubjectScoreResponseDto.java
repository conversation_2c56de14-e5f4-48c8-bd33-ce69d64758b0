package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GradeSubjectScoreResponseDto {

	@ApiModelProperty(value = "Id of the Board", example = "402880e78262feb901826304a0350001", position = 1)
	private String boardId;

	@ApiModelProperty(value = "Name of the Board", example = "ICC", position = 2)
	private String board;

	@ApiModelProperty(value = "Id of the School", example = "2c91808480bc79330180c20aa7b60004", position = 3)
	private String schoolId;

	@ApiModelProperty(value = "Name of the School", example = "Mathews Mar Athanasius Residential School", position = 4)
	private String school;

	@ApiModelProperty(value = "Id of the Branch", example = "4028818380665dd20180667ee7ee0003", position = 5)
	private String branchId;

	@ApiModelProperty(value = "Name of the Branch", example = "Mathews Mar Athanasius Residential School-CISCE", position = 6)
	private String branch;

	@ApiModelProperty(value = "Grade assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 7)
	private String gradeId;

	@ApiModelProperty(value = "Grade Name assigned to teacher", example = "X", position = 8)
	private String grade;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Section assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 9)
	private String sectionId;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Section Name assigned to teacher", example = "A", position = 10)
	private String section;

	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 11)
	private String subjectId;

	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 12)
	private String subject;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 13)
	private String subtopicId;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Biology", position = 14)
	private String subtopic;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Chapter details quiz perfromance", position = 15)
	private List<ChapterScoreResponseDto> chapters;
	
	@ApiModelProperty(value = "subTopics list details", position = 16)
	List<SubTopicBulkInnerResponseDto> subTopicsDetails;

}
