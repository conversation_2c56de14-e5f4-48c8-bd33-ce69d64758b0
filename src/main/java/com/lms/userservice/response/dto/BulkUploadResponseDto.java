package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response dto will hold the data of success and failed CSV upload data for
 * both teacher and student. This is a generic class can hold any type of
 * object.
 * 
 * <AUTHOR> <PERSON>ri
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class BulkUploadResponseDto<T> {

	@ApiModelProperty(value = "Save data from uploaded CSV", position = 1)
	private List<T> passedCSV;

	@ApiModelProperty(value = "Corrupted data from CSV", position = 2)
	private List<T> failedCSV;

	@ApiModelProperty(value = "Message of CSV upload", example = "Out of 100 50 data have been saved, 50 failed to save.", position = 2)
	private String message;
}
