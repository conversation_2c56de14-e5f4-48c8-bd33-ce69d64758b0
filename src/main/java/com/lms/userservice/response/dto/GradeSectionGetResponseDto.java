package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Designed to get all the grade-section mapping
 * 
 * <AUTHOR> C Achari
 * @since 1.0.2
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeSectionGetResponseDto {
	
	@ApiModelProperty(value = "Grade Id", example = "ff80818180ead9320180eaff5b0c0008", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade", example = "Grade 1", position = 2)
	private String gradeName;
        
	@ApiModelProperty(value = "Active", example = "true", position = 3)
	private boolean active=false;
	
	@ApiModelProperty(value = "Section list from mapping", position = 4)
	private List<GradeSectionMinResponseDto> gradeSectionMapping;
}
