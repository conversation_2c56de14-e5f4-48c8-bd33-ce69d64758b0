package com.lms.userservice.response.dto;

import com.lms.userservice.projection.BranchesProjection;
import com.lms.userservice.projection.UsersWebMobCountProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class BranchUsersWebMobCountResponseDto {

	@ApiModelProperty(value = "Id of the Branch", example = "4028818380665dd20180667ee7ee0003", position = 1)
	private String branchId;

	@ApiModelProperty(value = "Id of the Branch", example = "Mathews Mar Athanasius Residential School-CISCE", position = 2)
	private String branch;

	@ApiModelProperty(value = "User STUDENT or TEACHER", example = "STUDENT", position = 3)
	private String persona;

	@ApiModelProperty(value = "Only Web user count", example = "35", position = 4)
	private Long onlyWebUserCount;

	@ApiModelProperty(value = "Only Mob user count", example = "30", position = 5)
	private Long onlyMobUserCount;

	@ApiModelProperty(value = "Both Web and Mob user but High Web User", example = "45", position = 6)
	public Long bothUsersWebAndMobWebFavoredCount;

	@ApiModelProperty(value = "Both Web and Mob user but High Mob User", example = "25", position = 7)
	public Long bothUsersWebAndMobMobFavoredCount;

	@ApiModelProperty(value = "Total Users", example = "125", position = 8)
	public Long totalUsersCount;

	public BranchUsersWebMobCountResponseDto(UsersWebMobCountProjection projection) {
		this.branchId = projection.getBranchId();
		this.branch = projection.getBranch();
		this.onlyWebUserCount = projection.getOnlyWebUsersCount();
		this.onlyMobUserCount = projection.getOnlyMobUsersCount();
		this.bothUsersWebAndMobWebFavoredCount = projection.getBothUsersWebAndMobWebFavored();
		this.bothUsersWebAndMobMobFavoredCount = projection.getBothUsersWebAndMobMobFavored();
		this.totalUsersCount = projection.getTotalUsersCount();
		
	}
}
