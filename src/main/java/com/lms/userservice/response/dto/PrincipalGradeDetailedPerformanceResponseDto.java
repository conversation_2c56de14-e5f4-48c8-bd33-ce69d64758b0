package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrincipalGradeDetailedPerformanceResponseDto {

	@ApiModelProperty(value = "Grade Id", example = "ff80818180ead9320180eafe48800004", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade", example = "Grade 1", position = 2)
	private String grade;

	@ApiModelProperty(value = "Section Id", example = "ff80818180433e890180433ef2150000", position = 3)
	@JsonInclude(value = Include.NON_NULL)
	private String sectionId;

	@ApiModelProperty(value = "Section", example = "Section A", position = 4)
	@JsonInclude(value = Include.NON_NULL)
	private String section;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "List of subject details", position = 5)
	public List<SubjectWisePerformanceResponseDto> subjectDetails;

	public PrincipalGradeDetailedPerformanceResponseDto(String gradeId, String grade) {
		this.gradeId = gradeId;
		this.grade = grade;
	}

}
