package com.lms.userservice.response.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GradeAccessStudentsDetailsResponseDto {

	@ApiModelProperty(value = "Grade assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade Name assigned to teacher", example = "Grade 5", position = 2)
	private String grade;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Sections count from grade.", example = "5", position = 3)
	private long totalSections;

	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Student count from grade", example = "32", position = 4)
	private long totalStudentsUnderGrade;

	@JsonInclude(value = Include.NON_EMPTY)
	@ApiModelProperty(value = "Section list", position = 5)
	private List<SectionAccessStudentsDetailsResponseDto> sectionDetails;

	public GradeAccessStudentsDetailsResponseDto(String gradeId, String grade) {
		this.gradeId = gradeId;
		this.grade = grade;
	}

}
