package com.lms.userservice.response.dto;

import com.lms.userservice.enums.SectionData;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> C Achari
 * @since 1.0.2
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeSectionMapResponseDto {
	
	@ApiModelProperty(value = "Mapping id", example = "2c91808480aed8e60180bbcef8040001", position = 1)
	private String id;
	
	@ApiModelProperty(value = "School Id", example = "2c91808480bc79330180c20aa7b60004", position = 2)
	private String schoolId;
	
	@ApiModelProperty(value = "School", example = "Mathews Mar Athanasius Residential School", position = 3)
	private String school;
	
	@ApiModelProperty(value = "School code", example = "MMARS_001", position = 4)
	private String schoolCode;
	
	@ApiModelProperty(value = "Branch Id", example = "4028818380665dd20180667ee7ee0003", position = 5)
	private String branchId;
	
	@ApiModelProperty(value = "Branch", example = "Mathews Mar Athanasius Residential School-CISCE", position = 6)
	private String branch;
	
	@ApiModelProperty(value = "Section type", example = "SAME_SECTION", position = 7)
	private SectionData sectionData;
	
	@ApiModelProperty(value = "Grade Id", example = "ff80818180ead9320180eafe48800004", position = 8)
	private String gradeId;
	
	@ApiModelProperty(value = "Grade", example = "Grade 1", position = 9)
	private String gradeName;
	
	@ApiModelProperty(value = "Section Id", example = "ff80818180433e890180433ef2150000", position = 10)
	private String sectionId;
	
	@ApiModelProperty(value = "Section", example = "Section A", position = 11)
	private String sectionName;
	
	@ApiModelProperty(value = "Academic year id", example = "2c91808582fc425e0182fd9de57f000a", position = 12)
	private String academicYearId;
	
	@ApiModelProperty(value = "Academic year", example = "2005-2007", position = 13)
	private String academicYear;
		
	@ApiModelProperty(value = "active status indicator of mapping", example = "true", position = 14)
	private boolean active;
}
