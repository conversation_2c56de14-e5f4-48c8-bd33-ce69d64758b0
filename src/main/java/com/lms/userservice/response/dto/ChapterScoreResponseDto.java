package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChapterScoreResponseDto {

	@ApiModelProperty(value = "Subject assigned to teacher", example = "402892888697da25018697dcf3b90000", position = 1)
	private String chapterId;

	@ApiModelProperty(value = "Subject Name assigned to teacher", example = "Science", position = 2)
	private String chapter;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 3)
	private Long quizAverage;

	@ApiModelProperty(value = "Unit Global Quiz attempt rate", example = "70", position = 4)
	private Long globalQuizAverage;

	@ApiModelProperty(value = "Quiz Attempt Rate Message", example = "78", position = 5)
	private String averageMessage;

	@ApiModelProperty(value = "EmberStudent and their percentage", position = 6)
	private List<EmberStudentsResponseDto> emberStudents;

	@ApiModelProperty(value = "Percentage of students in Ember Category ", example = "67", position = 7)
	private Long studentPercentageInEmber;
	
	@ApiModelProperty(value = "chapter assosicated section", example = "Section A", position = 8)
	private String section;
	
	@ApiModelProperty(value = "chapter startDate", example = "02_Oct_2024", position = 9)
	private String startDate;
	
	@ApiModelProperty(value = "chapter endDate", example = "02_Oct_2024", position = 10)
	private String endDate;
	
	@ApiModelProperty(value = "quiz release", example = "02_Oct_2024", position = 11)
	private String quizRelease;
	
	@ApiModelProperty(value = "attendance", example = "90%", position = 12)
	private Long attendance;
	
	@ApiModelProperty(value = "Teacher name", example = "SIVA", position = 13)
	private String teacherName;
	
	@ApiModelProperty(value = "sectionId", example = "402892888697da25018697dcf3b90000", position = 14)
	private String sectionId;
	
	@ApiModelProperty(value = "teacherId", example = "402892888697da25018697dcf3b90000", position = 15)
	private String teacherId;
	
}
