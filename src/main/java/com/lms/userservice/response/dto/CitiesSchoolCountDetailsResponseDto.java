package com.lms.userservice.response.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CitiesSchoolCountDetailsResponseDto {

	@ApiModelProperty(value = "City Id", example = "2c91808480bc79330180c20aa7b60004", position = 1)
	private String id;

	@ApiModelProperty(value = "Count of the schools", example = "50", position = 2)
	private Integer schoolCount;

	@ApiModelProperty(value = "List of schools by the cities.", position = 3)
	private List<SchoolDetailsResponseDto> schoolDetails;

}
