package com.lms.userservice.feign.master;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradesSectionFeignResponseDto {
	
	@ApiModelProperty(value = "Id of the grade", example = "ff80818180ead9320180eafe99310005", position = 1)
	private String id;

	@ApiModelProperty(value = "Grade name", example = "Nursery", position = 2)
	private String grade;

	@ApiModelProperty(value = "Section list", position = 3)
	private List<SectionsResponseDto> sections;
}
