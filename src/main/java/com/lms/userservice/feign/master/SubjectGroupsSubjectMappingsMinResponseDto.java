package com.lms.userservice.feign.master;

import com.lms.userservice.response.dto.SubTopicCountersResponseDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubjectGroupsSubjectMappingsMinResponseDto {
//    private String id;

    private String subjectId;
    private String subject;
    private List<SubTopicCountersResponseDto> subTopics;
    private Integer chapterCount;
    private Integer quizCount;
    private Integer score;
    private String lastUpadatedAt;
}
