package com.lms.userservice.feign.master;

import java.util.List;

import com.lms.userservice.response.dto.SubTopicBulkInnerResponseDto;

import lombok.Data;

@Data
public class SubjectsResponseDto {

	private String id;

	private String subject;

	private Integer chapterCount;

	private Integer quizCount;

	private Integer score;

	private String subjectTypeId;

	private String subjectType;

	private String discription;

	private boolean active;

	private List<SubTopicBulkInnerResponseDto> subTopics;
}
