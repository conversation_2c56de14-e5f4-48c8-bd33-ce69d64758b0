package com.lms.userservice.feign.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class CoordinatorTypeResponseDto {
	
	@ApiModelProperty(example = "ff80818180ead9320180eaff806e000a", position = 1)
	private String id;
	
	@ApiModelProperty(example = "Teacher-In-Charge", position = 2)
	private String coordinatorType;
	
	@ApiModelProperty(example = "true", position = 3)
	private boolean active;
}
