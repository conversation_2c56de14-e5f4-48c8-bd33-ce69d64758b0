package com.lms.userservice.feign.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SectionsResponseDto {
	
	@ApiModelProperty(value = "Id of the section", example = "ff80818180f65df90180f6950a560180", position = 1)
	private String id;
	
	@ApiModelProperty(value = "Section name", example = "Section A", position = 2)
	private String section;
}
