package com.lms.userservice.feign.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradesResponseDto {

	@ApiModelProperty(value = "Id of the grade", example = "ff80818180ead9320180eafe99310005", position = 1)
	private String id;

	@ApiModelProperty(value = "Grade name", example = "Nursery", position = 2)
	private String grade;

	@ApiModelProperty(value = "Order of grades", example = "1", position = 3)
	private Integer sortOrder;
	
}
