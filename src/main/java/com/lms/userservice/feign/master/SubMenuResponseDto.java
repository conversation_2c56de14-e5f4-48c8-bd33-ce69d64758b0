package com.lms.userservice.feign.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubMenuResponseDto {

	@ApiModelProperty(value = "Sub-Menu Id", example = "ff80818182daf3150182db0784a70008", position = 1)
	private String subMenuId;

	@ApiModelProperty(value = "Sub-Menu name", example = "Admin Roles", position = 2)
	private String subMenuName;

	@ApiModelProperty(value = "Indux No. of Sub-Menu", example = "1", position = 3)
	private int sequenceNo;
	
	@ApiModelProperty(value = "Menu Id", example = "ff80818180cce30c0180cd26560d0000", position = 4)
	private String menuId;
	
	@ApiModelProperty(value = "application", position = 5)
	private String application;
	
	@ApiModelProperty(value = "iconClass", position = 6)
	private String iconClass;
	
	@ApiModelProperty(value = "route", position = 7)
	private String route;

}