package com.lms.userservice.feign.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Users details from the USER-SERVICE.
 * 
 * <AUTHOR> <PERSON>
 * @since 1.0.0
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RolesFeignDto {
	
	@ApiModelProperty(value = "Role id", example = "ff80818180cce35f0180ccf1e6b30005", required = true, position = 1)
	private String id;
	
	@ApiModelProperty(value = "Role name", example = "Super Admin", required = true, position = 2)
	private String role;
	
	@ApiModelProperty(value = "Discription of role.", example = "Super Admin", required = true, position = 3)
	private String discription;
}
