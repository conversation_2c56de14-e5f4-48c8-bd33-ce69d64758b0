package com.lms.userservice.feign.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class IrMenuResponseDto {
	
//	@ApiModelProperty(value = "Menu Id", example = "ff80818180cce30c0180cd26560d0000", position = 1)
//	private String menuId;
//	
//	@ApiModelProperty(value = "Menu name", example = "Master", position = 2)
//	private String menu;
//	
//	@ApiModelProperty(value = "application", position = 3)
//	private String application;
//	
//	@ApiModelProperty(value = "iconClass", position = 4)
//	private String iconClass;
//	
//	@ApiModelProperty(value = "routeu", position = 5)
//	private String route;
//	
//	@ApiModelProperty(value = "Sub-menu associate with a Men<PERSON>", position = 6)
//	private List<SubMenuResponseDto> subMenus;
	
	@ApiModelProperty(value = "order", example = "ff80818180cce30c0180cd26560d0000", position = 1)
	private int order;
	
	@ApiModelProperty(value = "key", example = "Master", position = 2)
	private String key;
	
	@ApiModelProperty(value = "title", position = 3)
	private String title;
	
	@ApiModelProperty(value = "route", position = 4)
	private String route;
	
	@ApiModelProperty(value = "icon", position = 5)
	private String icon;
	
	@ApiModelProperty(value = "icon", position = 6)
	private String roles;
	
//	@ApiModelProperty(value = "Sub-menu associate with a Menu", position = 6)
//	private List<SubMenuResponseDto> subMenus;
	
	
}