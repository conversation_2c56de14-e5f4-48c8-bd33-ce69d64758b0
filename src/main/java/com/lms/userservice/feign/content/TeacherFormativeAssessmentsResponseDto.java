package com.lms.userservice.feign.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherFormativeAssessmentsResponseDto {

	@ApiModelProperty(value = "Id of the quiz", example = "ff8081818358bd6c018358f3a7f00009", position = 1)
	private String quizId;

	@ApiModelProperty(value = "Name of the quiz", example = "Unit Quiz- chapter-1", position = 2)
	private String name;

	@ApiModelProperty(value = "Id of the quiz type", example = "ff8081818358bd6c018358f3a7f00008", position = 3)
	private String quizTypeId;

	@ApiModelProperty(value = "Type of the quiz", example = "Unit Quiz", position = 4)
	private String quizType;

}
