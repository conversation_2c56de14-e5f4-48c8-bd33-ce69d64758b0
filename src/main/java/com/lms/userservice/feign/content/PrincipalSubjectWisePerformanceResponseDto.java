package com.lms.userservice.feign.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrincipalSubjectWisePerformanceResponseDto {

	@ApiModelProperty(value = "Id of the subject", example = "402880e781d699750181d6c59fec0003", position = 1)
	private String subject;

	@ApiModelProperty(value = "Id of the Quiz Release", example = "402880e781d699750181d6c59fec0003", position = 2)
	private String quizId;

	@ApiModelProperty(value = "Quiz Type", example = "Unit Quiz/Practice Quiz", position = 3)
	private String quizType;

	@ApiModelProperty(value = "Quiz marks", example = "Unit Quiz/Practice Quiz", position = 4)
	private Integer totalMarks;

	@ApiModelProperty(value = "Chapters's chapterId", example = "Unit Quiz/Practice Quiz", position = 4)
	private String chapterId;
}
