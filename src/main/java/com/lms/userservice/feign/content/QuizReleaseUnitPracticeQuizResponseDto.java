package com.lms.userservice.feign.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuizReleaseUnitPracticeQuizResponseDto {

	@ApiModelProperty(value = "Id of the subject", example = "402880e781d699750181d6c59fec0003", position = 1)
	private String subjectId;

//	@ApiModelProperty(value = "Id of the chapter", example = "402880e781d699750181d6c59fec0003", position = 2)
//	private String chapterId;

	@ApiModelProperty(value = "Quiz Type", example = "Unit Quiz/Practice Quiz", position = 2)
	private String quizType;

	@ApiModelProperty(value = "Id of the Quiz Release", example = "402880e781d699750181d6c59fec0003", position = 3)
	private String releasedId;

	@ApiModelProperty(value = "total quiz marks", example = "100", position = 5)
	private Integer totalMarks;

}
