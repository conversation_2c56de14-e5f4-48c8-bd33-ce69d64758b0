package com.lms.userservice.feign.content;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuizChapterTotalMarksResponseDto {

	private String subjectId;
	private String chapterId;
	private String quizId;
	private String quizType;
	private Integer totalMarks;

	public QuizChapterTotalMarksResponseDto(String subjectId, String chapterId, String quizId, Integer totalMarks) {
		this.subjectId = subjectId;
		this.chapterId = chapterId;
		this.quizId = quizId;
		this.totalMarks = totalMarks;
	}

}
