package com.lms.userservice.feign.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReleasedUnitPracticeQuizResponseDto {

	@ApiModelProperty(value = "total quiz marks", example = "100", position = 1)
	private Integer totalMarks;

	@ApiModelProperty(value = "Grade Id", example = "ff80818180ead9320180eafe48800004", position = 2)
	private String gradeId;

	@ApiModelProperty(value = "Section Id", example = "ff80818180433e890180433ef2150000", position = 3)
	private String sectionId;

	@ApiModelProperty(value = "Quiz Type", example = "Unit Quiz/Practice Quiz", position = 4)
	private String quizType;

	@ApiModelProperty(value = "Quiz TypeId", example = "40289288870ab8f701870da41066001a", position = 5)
	private String quizTypeId;

	@ApiModelProperty(value = "Id of the Quiz", example = "402880e781d699750181d6c59fec0003", position = 6)
	private String quizId;
}
