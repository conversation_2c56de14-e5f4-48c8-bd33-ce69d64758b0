package com.lms.userservice.feign.content;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherReportQuizResponseDto {
	
	@ApiModelProperty(value = "Name of the Chapter", example = "Biology", position = 1)
	private String chapterName;
	
	@ApiModelProperty(value = "Id of the quiz", example = "ff808181870a831f01870a8a354d0000", position = 2)
	private String quizId;

	@ApiModelProperty(value = "No of questions in the quiz", example = "40", position = 3)
	private Integer noOfQuestions;
	
	@ApiModelProperty(value = "No of case studies in the quiz", example = "2", position = 4)
	private Long noOfCaseStudies;
	
	@ApiModelProperty(value = "First quiz release date", example = "08-08-2022", position = 5)
	private String firstReleaseDate;
	
	@ApiModelProperty(value = "latest quiz release date", example = "23-12-2022", position = 6)
	private String latestReleaseDate;
	
	@ApiModelProperty(value = "No of times quiz released", example = "4", position = 7)
	private Integer noOfTimesQuizRelease;

	@ApiModelProperty(value = "Start Date of the quiz", example = "12/04/2023", position = 8)
	private String startDate;

	@ApiModelProperty(value = "Start time of the quiz", example = "09:30 AM", position = 9)
	private String startTime;

	@ApiModelProperty(value = "End Date of the quiz", example = "12/04/2023", position = 10)
	private String endDate;

	@ApiModelProperty(value = "End time of the quiz", example = "12:30 PM", position = 11)
	private String endTime;
	
	private List<TaxonomyCalculationMinResponseDto> TaxonomyCalculations;

}
