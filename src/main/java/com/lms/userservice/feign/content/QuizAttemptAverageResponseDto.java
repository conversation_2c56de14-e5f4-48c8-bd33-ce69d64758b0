package com.lms.userservice.feign.content;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuizAttemptAverageResponseDto {

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 1)
	private BigDecimal unitQuizAttemptRate;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 2)
	private BigDecimal practiceQuizAttemptRate;

	@ApiModelProperty(value = "Unit Quiz attempt rate", example = "78", position = 3)
	private BigDecimal quizAverageScorePercentage;

}
