package com.lms.userservice.feign.content;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.lms.userservice.config.FiegnConfiguration;
import com.lms.userservice.dto.UpdateSectionRequestDTO;
import com.lms.userservice.model.GradeWiseCover;
import com.lms.userservice.model.InstituteQuizzesModel;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.SubjectWisePrincipalCoordinator;
import com.lms.userservice.response.dto.InstitutionStudentCountResponseDto;

/**
 * Call the API's from the service CONTENT-SERVICE, This interface only designed
 * for the CONTENT-SERVICE.
 *
 * <AUTHOR>
 * @since 1.0.0
 *
 */
@FeignClient(name = "CONTENT-SERVICE", configuration = FiegnConfiguration.class, url="${api.content.service.url}")
public interface ContentFeignClient {

	@GetMapping("/v1/api/content/teacher-chapter/count")
	public LMSResponse<Integer> getCountOfTeacherUploads(@RequestParam(value = "chapterIds") List<String> chapterIds);

	// quiz release
	@GetMapping("/v1/api/content/quiz-release/count")
	public LMSResponse<Integer> getQuizReleaseCount(@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "quizTypeId", required = false) String quizTypeId);

	@GetMapping("/v1/api/content/quiz-release/unit-practice/total-marks")
	public LMSResponse<List<QuizReleaseUnitPracticeQuizResponseDto>> getUnitPracticeQuizTotalMark(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectIds", required = false) List<String> subjectIds);

	@GetMapping("/v1/api/content/quiz-release/unit-practice/total-marks/teacher")
	public LMSResponse<List<QuizReleaseUnitPracticeQuizMinResponseDto>> getUnitPracticeQuizTotalMark(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectIds", required = false) List<String> subjectIds,
			@RequestParam(value = "subtopicIds", required = false) List<String> subtopicIds);

	@GetMapping("/v1/api/content/quiz-release/unit-practice-quizzes/teacher")
	public LMSResponse<List<QuizChapterTotalMarksResponseDto>> getQuizReleaseTotalMarksCompletedChapter(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapter", required = false) List<String> chapterIdList);

	@GetMapping("/v1/api/content/quiz-release/unit-quizzes/student-teacher")
	public LMSResponse<List<QuizChapterTotalMarksResponseDto>> getQuizzesTotalMarksCompletedChapter(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapter", required = false) List<String> chapterIdList,
			@RequestParam(value = "quizType", required = false) String quizType);

	@PutMapping("/v1/api/content/quiz-release/for/selected/institutes")
	public LMSResponse<List<InstituteQuizzesModel>> forSelectedInstitutes(
			@RequestBody List<InstituteQuizzesModel> request);

	@PutMapping("/v1/api/content/quiz-release/subject-wise/for/academic-heads")
	public LMSResponse<List<SubjectWisePrincipalCoordinator>> getTheGlobalScoreAverageAndAttempRate(
			@RequestBody List<InstitutionStudentCountResponseDto> request);

	@PutMapping("/v1/api/content/quiz-release/grade-wise/for/academic-heads")
	public LMSResponse<List<GradeWiseCover>> gradeWiseUqPqReleases(@RequestBody List<GradeWiseCover> request);

	// counters
	@GetMapping("/v1/api/content/counters/branch")
	public LMSResponse<List<Long>> branchIds(@RequestParam(value = "id", required = true) String id);

	@GetMapping("/v1/api/content/counters/school")
	public LMSResponse<List<Long>> schoolIds(@RequestParam(value = "id", required = true) String id);

	@GetMapping("/v1/api/content/quiz/total-quiz-count")
	public LMSResponse<Integer> totalQuizCount(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/content/quiz-release/released-quiz-count")
	public LMSResponse<Integer> releasedQuizCount(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	// quiz
	@GetMapping("/v1/api/content/quiz/filter-by-quiz-types")
	public LMSResponse<List<QuizTotalMarksMinResponseDto>> getAllUnitQuizzes(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/content/quiz/quiz-totalmarks")
	public LMSResponse<QuizTotalMarkMinResponseDto> getUnitQuizTotalMark(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "chapterId", required = true) String chapterId);

	@GetMapping("/v1/api/content/quiz/teacher-formative-assessment")
	public LMSResponse<List<TeacherFormativeAssessmentsResponseDto>> getTeacherFormativeAssessment(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "chapterId") List<String> chapterList);

	@GetMapping("/v1/api/content/quiz-release/released-global-quiz/principal")
	public LMSResponse<List<ReleasedUnitPracticeQuizResponseDto>> findGlobalReleasedUnitPracticeQuizzes(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/content/quiz-release/unit-practice-quiz/subject-wise")
	public LMSResponse<List<PrincipalSubjectWisePerformanceResponseDto>> getSubjectWiseUnitPracticeQuizzes(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "chapterIds", required = false) List<String> chapterIds);
	
	@GetMapping("/v1/api/content/quiz-release/last-modified-at")
	public LMSResponse<Long> getQuizReleaseLastModifiedAt(
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "quizTypeId", required = false) String quizTypeId);
	
	
	@GetMapping("/v1/api/content/quiz/quiz-overview-report")
	public LMSResponse<TeacherReportQuizResponseDto> getTeacherQuizReportDetails(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subtopicId,
			@RequestParam(value = "chapterId", required = true) String chapterId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "sectionId", required = false) String sectionId);

	@PutMapping("/v1/api/content/quiz/update/section")
	Boolean updateSection(@RequestBody UpdateSectionRequestDTO request);
}
