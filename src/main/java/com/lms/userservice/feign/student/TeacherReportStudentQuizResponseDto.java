package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherReportStudentQuizResponseDto {

	@ApiModelProperty(value = "No of students completed quiz ", example = "40", position = 1)
	private Long studentCompletedQuiz;

	@ApiModelProperty(value = "No of student attended the quiz ", example = "35", position = 2)
	private Long studentAttendedQuiz;
	
	@ApiModelProperty(value = "Average time taken to complte the exam ", example = "00Hrs 24Mins 10Sec", position = 3)
	private String averageTimeTaken;

	@ApiModelProperty(value = "Average time taken to complete the exam ", example = "00Hrs 4Mins 12Sec", position = 4)
	private String averageTimeTakenQuestion;
	
	public TeacherReportStudentQuizResponseDto(Long studentCompletedQuiz, Long studentAttendedQuiz) {
		this.studentCompletedQuiz = studentCompletedQuiz;
		this.studentAttendedQuiz = studentAttendedQuiz;
	}
}
