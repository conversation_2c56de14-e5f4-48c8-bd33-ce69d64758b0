package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class StudentLevelMinResponseDto {

	@ApiModelProperty(value = "Name of name of student",example = "ff808181857c6f1b01857cb6f82c0000", position = 1)
	private String studentId;
	
	@ApiModelProperty(value = "Id of the quiz", example = "ff8081818704dcfa0187053d89f70004", position = 2)
	private String quizId;

	@ApiModelProperty(value = "Obtained Marks", example = "70", position = 3)
	private Integer totalObtainedMark;
}
