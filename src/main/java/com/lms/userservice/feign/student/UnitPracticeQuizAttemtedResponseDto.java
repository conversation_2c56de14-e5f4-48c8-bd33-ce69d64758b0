package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnitPracticeQuizAttemtedResponseDto {

	@ApiModelProperty(value = "Quiz attended student count", example = "50", position = 1)
	private long studentCount;

	@ApiModelProperty(value = "Quiz attended students obtained marks", example = "500", position = 2)
	private long totalObtainedMark;
}
