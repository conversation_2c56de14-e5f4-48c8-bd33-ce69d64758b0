package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ScoreRangeResponseDto {

	@ApiModelProperty(value = "Score range id", position = 1)
	private String id;

	@ApiModelProperty(value = "Start range", example = "0", position = 2)
	private int startRange;

	@ApiModelProperty(value = "End range", example = "39", position = 3)
	private int endRange;

	@ApiModelProperty(value = "Name of the score range", example = "Ember", position = 4)
	private String name;

	@ApiModelProperty(value = "Name of score range with range", example = "Ember (0 - 39 %)", position = 5)
	private String rangeName;

	@ApiModelProperty(value = "Order of the score range", example = "1", position = 6)
	private int scoreOrder;

	@ApiModelProperty(value = "Score range status, true or false", example = "true", position = 7)
	private boolean active;
	
	public ScoreRangeResponseDto(int startRange, int endRange, String name) {
		this.startRange = startRange;
		this.endRange = endRange;
		this.name = name;
	}

}
