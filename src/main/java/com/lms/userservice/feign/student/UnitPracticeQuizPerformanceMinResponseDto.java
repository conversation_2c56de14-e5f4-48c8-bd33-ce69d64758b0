package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnitPracticeQuizPerformanceMinResponseDto {

	@ApiModelProperty(value = "Id of quiz", example = "50", position = 1)
	private String quizId;

	@ApiModelProperty(value = "Quiz attended students obtained marks", example = "500", position = 3)
	private long totalObtainedMark;

	@ApiModelProperty(value = "Quiz attended student count", example = "50", position = 2)
	private long studentCount;
}
