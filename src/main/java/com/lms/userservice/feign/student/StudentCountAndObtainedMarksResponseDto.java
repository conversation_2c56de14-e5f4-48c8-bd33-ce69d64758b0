package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentCountAndObtainedMarksResponseDto {

	@ApiModelProperty(value = "Quiz attended student count", example = "50", position = 1)
	private String studentId;
	
	@ApiModelProperty(value = "Quiz attended student count", example = "50", position = 2)
	private String releasedId;

	@ApiModelProperty(value = "Quiz attended students obtained marks", example = "500", position = 3)
	private Integer totalObtainedMark;

}
