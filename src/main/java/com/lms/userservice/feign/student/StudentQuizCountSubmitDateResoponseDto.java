package com.lms.userservice.feign.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> | 21-03-2023
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class StudentQuizCountSubmitDateResoponseDto {
	
	@ApiModelProperty(value = "Student Id", example = "ff8081818358bd6c018358f3a7f00009", position = 1)
	private String studentId;
	
	@ApiModelProperty(value = "Numbe of Quiz attended by student", example = "4", position = 2)
	private long numberOfQuizAttempted;
	
	@ApiModelProperty(value = "Last submission date and time of Unit-Quiz", example = "30-12-2022 11:39:35 AM", position = 3)
	private String lastUnitQuizSubmission;
}
