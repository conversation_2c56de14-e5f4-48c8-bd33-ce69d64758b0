package com.lms.userservice.feign.notification;

import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.lms.userservice.config.FiegnConfiguration;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.EmailRequestDto;
import com.lms.userservice.request.dto.SmsAlertBody;
import com.lms.userservice.response.dto.EmailResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.response.dto.SmsGatewayRequestDto;
import com.lms.userservice.response.dto.SmsSendResponseDTO;

@FeignClient(name = "NOTIFICATION-SERVICE", configuration = FiegnConfiguration.class, url="${api.notification.url}")
public interface NotificationFeignClient {

	// EMAIL
	@PostMapping("/v1/api/notification/emails/createUser")
	public LMSResponse<EmailResponseDto> userCreated(@RequestBody CreateUserEmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/edit-user")
	public LMSResponse<EmailResponseDto> editUser(@RequestBody CreateUserEmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/forgot-password")
	public LMSResponse<EmailResponseDto> forgotPassword(@Valid @RequestBody EmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/share-details")
	public LMSResponse<EmailResponseDto> shareDetails(@RequestBody ShareDetailsResponseDto request);

	@PostMapping("/v1/api/notification/emails/update-password")
	public LMSResponse<EmailResponseDto> updatePassword(@RequestBody CreateUserEmailRequestDto request);

	@PostMapping("/v1/api/notification/emails/school-created-or-update")
	public LMSResponse<EmailResponseDto> afterSchoolCreatedOrEdited(@RequestBody SchoolNotificationResponseDto request);

	// SMS
	@PostMapping("/v1/api/notification/sms")
	public LMSResponse<SmsAlertBody> sendSms(@RequestBody SmsAlertBody smsAlertBody);
	
	@PutMapping("/v1/api/notification/sms_configurations")
	public LMSResponse<Boolean> sendSMSToUser(@Valid @RequestBody SmsGatewayRequestDto request);
	
	@PutMapping("/v1/api/notification/sms/send")
	public LMSResponse<String> sendSmsUser(@RequestBody SmsSendResponseDTO smsSendResponseDTO);

	@PostMapping("/v1/api/notification/emails/createSMS")
	public void sendEmailUser(SmsSendResponseDTO smsSendResponseDTO);

}
