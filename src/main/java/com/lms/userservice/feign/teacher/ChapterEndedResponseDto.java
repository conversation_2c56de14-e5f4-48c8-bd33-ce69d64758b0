package com.lms.userservice.feign.teacher;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.lms.userservice.response.dto.ChapterEndedBySectionResponseDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChapterEndedResponseDto {

	@ApiModelProperty(value = "Grade based count of end chapter and released quizzes", example = "4028928883193d8701831d2d8b850010", position = 1)
	private String gradeId;
	
	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Count of ended chapter", example = "10", position = 2)
	private Long chapterCount;
	
	@JsonInclude(value = Include.NON_NULL)
	@ApiModelProperty(value = "Count of released quizzes", example = "5", position = 1)
	private Long quizReleaseCount;
	
	@JsonInclude(value = Include.NON_EMPTY)
	private List<ChapterEndedBySectionResponseDto> sections;

}
