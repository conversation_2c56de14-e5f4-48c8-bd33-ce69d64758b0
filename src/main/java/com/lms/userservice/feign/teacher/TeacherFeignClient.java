package com.lms.userservice.feign.teacher;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.lms.userservice.assignedTeacher.ChapterTrackingResponseDto;
import com.lms.userservice.assignedTeacher.PaginatedResponse;
import com.lms.userservice.config.FiegnConfiguration;
import com.lms.userservice.dto.UpdateSectionRequestDTO;
import com.lms.userservice.model.InstituteQuizzesModel;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.response.dto.QuestionWisePerformanceResponseDto;
import com.lms.userservice.response.dto.TeacherReportQuizOverviewCardResponseDto;

import io.swagger.annotations.ApiOperation;

@FeignClient(name = "TEACHER-SERVICE", configuration = FiegnConfiguration.class, url="${api.teacher.service.url}")
public interface TeacherFeignClient {

	// counters
	@GetMapping("/v1/api/teacher/counters/branch")
	public LMSResponse<List<Long>> branchIds(@RequestParam(value = "branchId", required = true) String branchId);

	@GetMapping("/v1/api/teacher/teach/chapter/ended-chapter-count")
	public LMSResponse<Integer> endedChapterCount(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/teacher/teach/chapter/ended-count-principal")
	public LMSResponse<Long> endedChapterCountForPrincipal(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/teacher/teach/chapter/ended-count-for-principal")
	public LMSResponse<Long> endedChapterCountSectionForPrincipal(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionIds", required = false) List<String> sectionIds,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId);

	@GetMapping("/v1/api/teacher/teach/chapter/count/end-teaching")
	public LMSResponse<ChapterEndedResponseDto> getEndedChpterCountForTeacher(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "sectionIds", required = false) List<String> sectionIds,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/teacher/teach/chapter/ended-chapter-list")
	public LMSResponse<List<String>> endedChapterList(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "sectionId", required = false) List<String> sectionIdList,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@GetMapping("/v1/api/teacher/teach/chapter/completed-chapters/student-teacher")
	public LMSResponse<List<String>> completedChapterLists(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@PutMapping("/v1/api/teacher/teach/chapter/completed-chapters/chapter-wise-performance")
	public LMSResponse<List<InstituteQuizzesModel>> getTheTeachingCompletedChapter(
			@RequestBody List<InstituteQuizzesModel> request);
	
	@GetMapping("/v1/api/teacher/teach/chapter/chapter_start_end_date")
	public LMSResponse<List<com.lms.userservice.response.dto.TeachStatusResponseVO>> getStartandEndDateDetails(
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "chapterId", required = true) String chapterId);

	@GetMapping("/v1/api/teacher/report/quiz-overview-report")
	public LMSResponse<TeacherReportQuizOverviewCardResponseDto> getTeacherReportCardQuizOverview(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "chapterId", required = true) String chapterId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId); 
	
	@GetMapping("/v1/api/teacher/teach/chapter/tracking")
	public LMSResponse<PaginatedResponse<ChapterTrackingResponseDto>> getChapterTracking(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "100") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId,
			@RequestParam(value = "active", required = false) Boolean active);
	
	@GetMapping("/v1/api/teacher/report/questionwise-quiz-performance")
	public LMSResponse<QuestionWisePerformanceResponseDto> getQustionWiseQuizPerformance(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "chapterId", required = true) String chapterId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId);

	@PutMapping("/v1/api/teacher/teach/chapter/update/section")
	Boolean updateSection(@RequestBody UpdateSectionRequestDTO request);
}
