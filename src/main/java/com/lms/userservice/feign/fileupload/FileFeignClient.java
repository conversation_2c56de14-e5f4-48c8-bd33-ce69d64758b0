package com.lms.userservice.feign.fileupload;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.lms.userservice.config.FiegnConfiguration;
import com.lms.userservice.model.LMSResponse;



/**
 * Call the API's from the service FILE-UPLOAD, This interface only designed for the FILE-UPLOAD.
 * 
 * <AUTHOR> <PERSON>
 * @since 1.0.0
 *
 */
@FeignClient(name = "FILE-UPLOAD", configuration = FiegnConfiguration.class, url="${api.fileupload.url}")
public interface FileFeignClient {

	@GetMapping("/v1/api/file/roles/upload")
	public LMSResponse<String> upload(@RequestParam(name = "fileCategory") FileCategories category,
			@RequestParam(name = "file") MultipartFile file);
	
}
