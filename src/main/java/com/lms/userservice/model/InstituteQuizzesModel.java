package com.lms.userservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This model is used to as both input as well as for output. Based on an
 * institutes parameter find the released quizzes and it's total mark.
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class InstituteQuizzesModel {

	private String boardId;
	private String schoolId;
	private String branchId;
	private String gradeId;
	private String sectionId;
	private String subjectId;
	private String subTopicId;
	private String quizType;
	private String academicYearId;

	// below this is for chapter-wise dashboard api
	private String chapterId;

	// below attribute is the part of output only
	private String quizId;
	private Integer totalMark;

	public InstituteQuizzesModel(String boardId, String schoolId, String branchId, String gradeId, String sectionId,
			String subjectId, String subTopicId, String quizType, String academicYearId) {
		this.boardId = boardId;
		this.schoolId = schoolId;
		this.branchId = branchId;
		this.gradeId = gradeId;
		this.sectionId = sectionId;
		this.subjectId = subjectId;
		this.subTopicId = subTopicId;
		this.quizType = quizType;
		this.academicYearId = academicYearId;
	}
}
