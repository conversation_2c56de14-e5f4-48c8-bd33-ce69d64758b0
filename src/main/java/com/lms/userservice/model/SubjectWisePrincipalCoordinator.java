package com.lms.userservice.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubjectWisePrincipalCoordinator {

	private String gradeId;

	private String subjectId;

	private String subTopicId;

	// cumulative of UQ and PQ
	private BigDecimal uqAttempRate;

	private BigDecimal uqCumulative;

	private BigDecimal pqAttempRate;

	private BigDecimal pqCumulative;

	public SubjectWisePrincipalCoordinator(String gradeId, String subjectId, String subTopicId) {
		this.gradeId = gradeId;
		this.subjectId = subjectId;
		this.subTopicId = subTopicId;
	}
}
