package com.lms.userservice.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Any users details can be fetched by this DTO
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class UserMinDetails {

	@ApiModelProperty(value = "PK of the user", example = "ff80818185f439d50185f48a1c8d001c")
	private String id;

	@ApiModelProperty(value = "Full name of a user", example = "<PERSON>")
	private String name;

	@ApiModelProperty(value = "Username of the user", example = "Ravi.Das")
	private String userName;
}
