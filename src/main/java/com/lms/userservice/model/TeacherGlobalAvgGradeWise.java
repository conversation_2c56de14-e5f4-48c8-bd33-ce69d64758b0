package com.lms.userservice.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Global average score for teacher dashboard (grade wise performance)
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class TeacherGlobalAvgGradeWise {

	private String gradeId;

	private String subjectId;

	private String subTopicId;

	private BigDecimal globalAvg;

	private BigDecimal uqGlobalAvgAttempt;
	
	private BigDecimal pqGlobalAvgAttempt;

	public TeacherGlobalAvgGradeWise(String gradeId, String subjectId, String subTopicId) {
		this.gradeId = gradeId;
		this.subjectId = subjectId;
		this.subTopicId = subTopicId;
	}

}
