package com.lms.userservice.model;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubjectsSubtopicUnderGradeModel {

	private String gradeId;

	// plan of current user's school(branch)
	private String currentPlanId;

	// key as subject and value as Set<Sub-topics>
	private HashMap<String, Set<String>> subjectAndSubTopic;

	// similar plans which has above combination
	private List<String> similarPlans;
}
