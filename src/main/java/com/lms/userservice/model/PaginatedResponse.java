package com.lms.userservice.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Common pagination response for all classes
 * 
 * <AUTHOR>
 * @since 0.0.1
 *
 * @param <T>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginatedResponse<T> {

	private long totalElements;

	private long totalPages;

	private long pageSize;

	private long pageNumber;

	private long numberOfElements;

	private List<T> data;
}
