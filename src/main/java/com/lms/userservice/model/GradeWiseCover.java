package com.lms.userservice.model;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * For Grade-wise performance this is the result-set body
 * 
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class GradeWiseCover {

	private String gradeId;

	private String boardId;

	private String academicYearId;

	private List<InstitutionList> institutes;

	// key as subject and value as Set<Sub-topics>
	private HashMap<String, Set<String>> subjectAndSubTopic;

	// Last response body
	private BigDecimal uqGlobalScoreAvg;

	private BigDecimal uqGlobalAttemptRate;

	private BigDecimal pqGlobalAttemptRate;

	public GradeWiseCover(String gradeId, String boardId, String academicYearId) {
		this.gradeId = gradeId;
		this.boardId = boardId;
		this.academicYearId = academicYearId;
	}

	public GradeWiseCover(String gradeId, String boardId, String academicYearId, List<InstitutionList> institutes) {
		this.gradeId = gradeId;
		this.boardId = boardId;
		this.academicYearId = academicYearId;
		this.institutes = institutes;
	}

}
