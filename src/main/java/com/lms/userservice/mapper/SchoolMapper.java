package com.lms.userservice.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.lms.userservice.entity.Schools;
import com.lms.userservice.projection.SchoolBranchesProjection;
import com.lms.userservice.response.dto.SchoolBranchesResponseDto;
import com.lms.userservice.response.dto.SchoolResponseDto;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SchoolMapper {

	SchoolMapper INSTANCE = Mappers.getMapper(SchoolMapper.class);

	SchoolResponseDto mapSchoolModelToResponseDto(Schools schools);
	
	@Mapping(source = "schoolActive", target = "active")
	SchoolBranchesResponseDto mapSchoolBranchesProjectionToDto(SchoolBranchesProjection response);
}
