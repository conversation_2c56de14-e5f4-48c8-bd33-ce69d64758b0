package com.lms.userservice.mapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.lms.userservice.component.Translator;
import com.lms.userservice.config.StaticContextAccessor;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.SchoolRepository;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MappingHelper {
	
	/**
	 * Get the Schools entity
	 * @param id
	 * @return
	 */
	public static Schools getSchoolById(String id) {
		if(StringUtils.isEmpty(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("school.id.not.found", null));
		
		try {
			return StaticContextAccessor.getBean(SchoolRepository.class).getById(id);
		}catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));
		}
	}
	
	/**
	 * Get the Branches entity
	 * @param id
	 * @return
	 */
	public static Branches getBranchById(String id) {
		if(StringUtils.isEmpty(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.id.not.found", null));
		
		try {
			return StaticContextAccessor.getBean(BranchRepository.class).getById(id);
		}catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));
		}
	}
}
