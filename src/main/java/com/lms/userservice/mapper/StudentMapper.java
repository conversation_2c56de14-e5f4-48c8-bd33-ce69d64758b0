package com.lms.userservice.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.lms.userservice.entity.Students;
import com.lms.userservice.projection.StudentsProjection;
import com.lms.userservice.response.dto.StudentsResponseDto;

/**
 * <AUTHOR> | April 9 2022
 *
 * @implNote Interface for mapping of various response, request models to and
 *           from Students entity
 *
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StudentMapper {

	StudentMapper INSTANCE =  Mappers.getMapper(StudentMapper.class);
	
		 @Mapping(source ="schools.id", target = "schoolId" )
		 @Mapping(source ="schools.name", target = "schoolName" )
		 @Mapping(source ="schools.code", target = "schoolCode" )
		 @Mapping(source = "branches.id", target = "branchId")
		 @Mapping(source = "branches.name", target = "branchName")
	StudentsResponseDto mapStudentToResponseDto(Students student);
	
	List<StudentsResponseDto> mapStudentToResponseDto(List<Students> students);

	//List<StudentsResponseDto> mapStudentProjectionToResponseDto(List<StudentsProjection> studentProjectionList);
	
	StudentsResponseDto mapStudentProjectionToResponseDto(StudentsProjection studentProjection);

}
