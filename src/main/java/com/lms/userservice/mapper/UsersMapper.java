package com.lms.userservice.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.lms.userservice.entity.Users;
import com.lms.userservice.request.dto.AdminUsersRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UsersMapper {

	UsersMapper INSTANCE = Mappers.getMapper(UsersMapper.class);
	
	Users fromAdminUserRequestToEntity(AdminUsersRequestDto request);
	
	AdminUsersResponseDto fromEntityToAdminUsersResponse(Users users);
	
}
