package com.lms.userservice.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.request.dto.SchoolRequestDto;
import com.lms.userservice.response.dto.BranchResponseDto;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BranchMapper {

	BranchMapper INSTANCE = Mappers.getMapper(BranchMapper.class);

	BranchResponseDto mapBranchModelToResponseDto(Branches branches);

	Branches mapFromSchoolDtoToBranches(SchoolRequestDto source);
	
	@Mapping(target = "branches.id", ignore = true)
	Branches mapFromSchoolsToBranches(Schools schools, @MappingTarget Branches branches);
	
}
