package com.lms.userservice.mapper;

import java.util.List;

import com.lms.userservice.entity.AssignTeacher;
import com.lms.userservice.projection.TeacherAssignProjection;
import com.lms.userservice.request.dto.TeacherAssignRequest;
import com.lms.userservice.response.dto.TeacherAssignResponse;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import com.lms.userservice.entity.Teachers;
import com.lms.userservice.request.dto.TeacherRequestDto;
import com.lms.userservice.request.dto.TeachersSelfRegRequestDto;
import com.lms.userservice.response.dto.TeacherResponseDto;

/**
 * <AUTHOR> | April 9 2022
 *
 * @implNote Interface for mapping of various response, request models to and
 *           from Teachers entity
 *
 */
@Mapper(componentModel = "spring", imports = MappingHelper.class, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface TeacherMapper {

	TeacherMapper INSTANCE = Mappers.getMapper(TeacherMapper.class);

	@Mapping(source = "schools.id", target = "school")
	@Mapping(source = "schools.name", target = "schoolName")
	@Mapping(source = "schools.code", target = "schoolCode")
	@Mapping(source = "branches.id", target = "branch")
	@Mapping(source = "branches.name", target = "branchName")
	TeacherResponseDto mapTeacherEntityToResponseDto(Teachers teacher);

	List<TeacherResponseDto> mapTeacherEntityToResponseDto(List<Teachers> teachers);

	// List<TeacherResponseDto>
	// mapTeacherProjectionToResponseDto(List<TeachersProjection> teacherList);

	@Mapping(source = "request.firstName", target = "firstName")
	@Mapping(source = "request.lastName", target = "lastName")
	@Mapping(source = "request.email", target = "email")
	@Mapping(source = "request.mobile", target = "mobile")
	@Mapping(source = "request.gender", target = "gender")
	@Mapping(source = "request.dob", target = "dob")
	@Mapping(source = "request.joinDate", target = "joinDate")
	@Mapping(source = "request.previousWorkExp", target = "previousWorkExp")
	@Mapping(source = "request.designation", target = "designation")
	@Mapping(source = "request.documentUrl", target = "documentUrl")
	@Mapping(source = "request.academicStaffProfile", target = "academicStaffProfile")
	@Mapping(source = "request.coordinatorTypeId", target = "coordinatorTypeId")
	@Mapping(source = "request.address", target = "address")
	@Mapping(target = "schools", expression = "java(MappingHelper.getSchoolById(request.getSchool()))")
	@Mapping(target = "branches", expression = "java(MappingHelper.getBranchById(request.getBranch()))")
	Teachers toEntityFromRequest(TeacherRequestDto request);

	@Mapping(source = "id", target = "id")
	@Mapping(source = "request.firstName", target = "firstName")
	@Mapping(source = "request.lastName", target = "lastName")
	@Mapping(source = "request.email", target = "email")
	@Mapping(source = "request.mobile", target = "mobile")
	@Mapping(source = "request.gender", target = "gender")
	@Mapping(source = "request.dob", target = "dob")
	@Mapping(source = "request.joinDate", target = "joinDate")
	@Mapping(source = "request.previousWorkExp", target = "previousWorkExp")
	@Mapping(source = "request.designation", target = "designation")
	@Mapping(source = "request.documentUrl", target = "documentUrl")
	@Mapping(source = "request.academicStaffProfile", target = "academicStaffProfile")
	@Mapping(source = "request.coordinatorTypeId", target = "coordinatorTypeId")
	@Mapping(source = "request.address", target = "address")
	@Mapping(target = "schools", expression = "java(MappingHelper.getSchoolById(request.getSchool()))")
	@Mapping(target = "branches", expression = "java(MappingHelper.getBranchById(request.getBranch()))")
	Teachers forUpdateToEntityFromRequest(TeacherRequestDto request, String id);
	
	@Mapping(target = "schools", expression = "java(MappingHelper.getSchoolById(request.getSchool()))")
	@Mapping(target = "branches", expression = "java(MappingHelper.getBranchById(request.getBranch()))")
	Teachers selfRegistrationToEntityFromRequest(TeachersSelfRegRequestDto request);

	AssignTeacher mapAssignTeacherRequestToEntity(TeacherAssignRequest request, @MappingTarget AssignTeacher assignTeacher);

	TeacherAssignResponse mapAssignTeacherEntityToResponse(AssignTeacher assignTeacher);

	List<TeacherAssignResponse> mapAssignTeacherEntityToResponse(List<AssignTeacher> assignTeacher);

	List<TeacherAssignResponse> mapTeacherAssignProjectionToResponse(List<TeacherAssignProjection> projectionList);

	TeacherAssignResponse mapTeacherAssignProjectionToResponse(TeacherAssignProjection projection);

}
