package com.lms.userservice.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Using to get the list of users, while do the self registration by the user.
 * User_Token combination should not be duplicate.
 * 
 * <AUTHOR> C <PERSON>
 * @since 0.0.1
 * @see {@link com.lms.userservice.entity.Tokens Tokens}
 *
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "user_token_mapping")
public class UsersTokenMapping extends AuditMetadata {

	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_id")
	private Users users;
	
	@NotNull(message = Constants.MANDATORY_FIELD)
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "token_id")
	private Tokens tokens;
}
