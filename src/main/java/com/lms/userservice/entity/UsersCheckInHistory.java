package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "users_check_in_history")
@EqualsAndHashCode(callSuper = false)
public class UsersCheckInHistory extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_id")
	private Users users;

	@NotBlank(message = Constants.IS_EMPTY)
	@Column(name = "user_name")
	private String userName;

	@Column(name = "web_in", columnDefinition = "boolean default false")
	private boolean webIn;

	@Column(name = "mob_in", columnDefinition = "boolean default false")
	private boolean mobIn;

	@Column(name = "web_login_at")
	private Long webLoginAt;

	@Column(name = "mob_login_at")
	private Long mobLoginAt;
	
	@Column(name = "mac_address")
	private String macAddress;
	
	@Column(name = "login_success", columnDefinition = "boolean default true")
	private boolean loginSuccess;

}
