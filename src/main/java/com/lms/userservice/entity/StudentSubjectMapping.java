package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import com.lms.userservice.util.Constants;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "student_subject_mapping")
public class StudentSubjectMapping extends AuditMetadata {

    @NotNull(message = Constants.MANDATORY_FIELD)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id")
    private Students student;

    /**
     * PK from master table Grades
     */
    @NotBlank(message = Constants.MANDATORY_FIELD)
    @Column(name = "subject_group_subject_mappings_id")
    private String subjectGroupSubjectMappingsId;

    /**
     * PK from master table Grades
     */
    @NotNull(message = Constants.MANDATORY_FIELD)
    @Column(name = "academic_years_id")
    private String academicYearsId;
}
