package com.lms.userservice.entity;

import java.beans.Transient;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "users_role_mapping", uniqueConstraints = { @UniqueConstraint(columnNames = { "user_id", "role_id" }) })
public class UsersRoleMapping extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_id")
	private Users users;

	/**
	 * roleId from master table roles
	 */
	@Column(name = "role_id")
	private String roleId;


	/**
     * Transient getter for userId from the associated Users entity.
     */
    @Transient
    public String getUserId() {
        return users != null ? users.getId() : null;
    }
}
