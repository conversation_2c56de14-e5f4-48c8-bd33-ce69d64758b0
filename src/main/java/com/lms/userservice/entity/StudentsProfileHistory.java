package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "student_profile_history")
public class StudentsProfileHistory extends AuditMetadata {
	
	@NotNull(message = Constants.MANDATORY_FIELD)	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "student_id")
	private Students students;
		
	@NotBlank(message = Constants.IS_EMPTY)
	@Column(name = "from_grade_id")	
	private String fromGradeId;
	
	@Column(name = "from_section_id")
	private String fromSectionId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "to_grade_id")
	private String toGradeId;
	
	@Column(name = "to_section_id")
	private String toSectionId;
	
	@Column(name = "same_year")
	private boolean sameYear;
	
	@Column(name = "year_end_process")
	private boolean yearEndProcess;
	
	/**
	 * academicYearId from master 
	 */
	@Column(name="academic_year_id")
	private String academicYearId;	
	
}
