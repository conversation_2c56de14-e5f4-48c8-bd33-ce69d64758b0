package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "assign_teacher")
@EqualsAndHashCode(callSuper = false)
public class AssignTeacher extends AuditMetadata {

    @NotBlank(message = Constants.IS_EMPTY)
    @Column(name = "teacher_id")
    private String teacherId;

    @NotBlank(message = Constants.IS_EMPTY)
    @Column(name = "grade_id")
    private String gradeId;

    @Column(name = "section_id")
    private String sectionId;

    @NotBlank(message = Constants.IS_EMPTY)
    @Column(name = "subject_id")
    private String subjectId;

    @Column(name = "subtopic_id")
    private String subtopicId;

}
