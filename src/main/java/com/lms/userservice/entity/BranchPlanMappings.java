package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity(name ="branch_plan_mappings")
public class BranchPlanMappings extends AuditMetadata {


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;
	
	@Column(name ="plan_id")
	private String planId;
	
	@Column(name ="plan_name")
	private String planName;
}
