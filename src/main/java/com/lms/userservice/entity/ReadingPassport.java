package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.ColumnTransformer;

import com.lms.userservice.request.dto.Access;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "reading_passport_access")
@EqualsAndHashCode(callSuper = false)
public class ReadingPassport extends AuditMetadata{
	
	@Column(name = "user_id")
	private String userId;
	
	@Column(name = "user_type")
	private String userType;
	
	@Column(name = "librarian")
	private boolean librarian;
	
	@Column(columnDefinition = "jsonb", name = "access")
	@ColumnTransformer(write = "?::jsonb")
	private String access;

	@Column(name = "grade_access")
	private String gradeAccess;
	
	@Column(name = "school_id")
	private String schoolId;
	
	@Column(name = "branch_id")
	private String branchId;
	
}
