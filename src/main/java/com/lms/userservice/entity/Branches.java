package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.lms.userservice.util.Constants;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "branches")
@EqualsAndHashCode(callSuper = false)
public class Branches extends AuditMetadata {

	@NotBlank(message = Constants.IS_EMPTY)
	@Column(name = "name")
	private String name;

	/**
	 * cityId from master table cities
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "city_id")
	private String cityId;

	@Column(name = "locality")
	private String locality;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "poc_email")
	private String pocEmail;

	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Size(min = 10, max = 10, message = Constants.PHONE_NUMBER_LENGTH)
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "phone_number")
	private String phoneNumber;

	/**
	 * boardId from master table boards
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "board_id")
	private String boardId;

	/**
	 * The upload logo url of school
	 */
	@Column(name = "logo_url")
	private String logoUrl;
        
	/**
	 *  Configuration option to restrict the number of re-releases
	 */
	@Column(name = "max_quiz_releases")
	private Integer maxQuizReleases=10;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "school_id")
	private Schools schools;
	
	@Column(name = "branch_code")
	private String branchCode;
	
	@Column(name = "test_branch")
	private Boolean testBranch;

}
