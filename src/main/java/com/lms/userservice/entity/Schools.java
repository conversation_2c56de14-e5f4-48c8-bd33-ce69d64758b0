package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "schools")
@EqualsAndHashCode(callSuper = false)
public class Schools extends AuditMetadata {

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "name")
	private String name;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "code", unique = true)
	private String code;

	/**
	 * cityId from master table cities
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "city_id")
	private String cityId;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Email(regexp = Constants.EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "poc_email")
	private String pocEmail;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.PHONE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "phone_number")
	private String phoneNumber;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Pattern(regexp = Constants.WEBSITE_REGEX, message = Constants.GIVE_VALID_VALUE)
	@Column(name = "website")
	private String website;

	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "signatory_name")
	private String signatoryName;

	/**
	 * digital signature
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "signatory_role")
	private String signatoryRole;

	/**
	 * The upload logo url of school
	 */
	@NotBlank(message = Constants.MANDATORY_FIELD)
	@Column(name = "logo_url")
	private String logoUrl;

	@Column(name = "board_id")
	private String boardId;

	private String locality;

	private String planId;

	/**
	 * Check Branch existence , columnDefinition = "boolean default true"
	 */
	@Column(name = "has_branch")
	private boolean hasBranch;

}
