package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.lms.userservice.enums.CommunicationAction;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "branch_communication")
public class BranchCommunication extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;

	@Enumerated(EnumType.STRING)
	@Column(name = "communication_action", length = 32, columnDefinition = "varchar(32)")
	private CommunicationAction communicationAction;

	@Column(name = "sms", columnDefinition = "boolean default false")
	private boolean sms;

	@Column(name = "email", columnDefinition = "boolean default false")
	private boolean email;
}
