package com.lms.userservice.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import org.hibernate.annotations.GenericGenerator;

import lombok.Data;
import lombok.ToString;

/**
 * Used to update the common fields
 * 
 * <AUTHOR> <PERSON>
 * @since 11-Mar-2022
 *
 */
@Data
@ToString
@MappedSuperclass
public abstract class AuditMetadata {

	@Id
	@GeneratedValue(generator = "uuid")
	@GenericGenerator(name = "uuid", strategy = "uuid")
	@Column(columnDefinition = "CHAR(32)")
	private String id;

	private Long createdAt;

	private Long modifiedAt;

	private String createdBy;

	private String lastModifiedBy;

	private boolean active;

	private boolean deleted;

	protected AuditMetadata() {
		this.setActive(true);
		this.setDeleted(false);
		this.createdAt = new Date().getTime();
		this.modifiedAt = this.createdAt;
	}

}
