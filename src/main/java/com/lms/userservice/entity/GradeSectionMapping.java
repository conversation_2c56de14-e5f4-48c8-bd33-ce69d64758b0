package com.lms.userservice.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import org.hibernate.annotations.Where;

import com.lms.userservice.enums.SectionData;
import com.lms.userservice.util.Constants;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "grade_section_mapping")
@Where(clause = "deleted=false")
public class GradeSectionMapping extends AuditMetadata {

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "school_id")
	private Schools schools;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "branch_id")
	private Branches branches;

	/**
	 * PK from master table Grades
	 */
	@Column(name = "grade_id")
	private String gradeId;

	/**
	 * PK from master table Sections
	 */
	@Column(name = "section_id")
	private String sectionId;

	@NotNull(message = Constants.MANDATORY_FIELD)
	@Enumerated(EnumType.STRING)
	@Column(name = "section_data")
	private SectionData sectionData;
	
	/**
	 * PK from master table AcademicYears
	 */
	@Column(name = "academic_year_id")
	private String academicYearId;

}
