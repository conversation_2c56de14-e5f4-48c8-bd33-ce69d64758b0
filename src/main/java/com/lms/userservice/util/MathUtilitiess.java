package com.lms.userservice.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.lms.userservice.component.Translator;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MathUtilitiess {

	public static Integer calcPercentage(Integer total) {
		try {
			float fTotal = total.floatValue();
			float percentage = (fTotal / 100) * 100;
			return Math.round(percentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("percentage.calulation.failed", null));
		}

	}

	/**
	 * Converting the params into float for the accurate precision.
	 * 
	 * @param obtained
	 * @param total
	 * @return
	 */
	public static Integer calculatePercentage(Long obtained, Integer total) {
		try {
			float fObtained = obtained.floatValue();
			float fTotal = total.floatValue();
			float percentage = (fObtained / fTotal) * 100;
			return Math.round(percentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("percentage.calulation.failed", null));
		}
	}

	public static Integer calculatePercentage(Integer obtained, Integer total) {
		try {
			float fObtained = obtained.floatValue();
			float fTotal = total.floatValue();
			float percentage = (fObtained / fTotal) * 100;
			return Math.round(percentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("percentage.calulation.failed", null));
		}
	}

	/**
	 * Converting the params into float for the accurate precision.
	 * 
	 * @param obtained
	 * @param total
	 * @return
	 */
	public static long calculatePercentage(Long obtained, Long total) {
		try {
			float fObtained = obtained.floatValue();
			float fTotal = total.floatValue();
			float percentage = (fObtained / fTotal) * 100;
			return Math.round(percentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("percentage.calulation.failed", null));
		}
	}

	/**
	 * Converting the params into float for the accurate precision.
	 * 
	 * @param obtained
	 * @param total
	 * @return
	 */
	public static long calculatePercentage(Double obtained, Double total) {
		try {
			float fObtained = obtained.floatValue();
			float fTotal = total.floatValue();
			float percentage = (fObtained / fTotal) * 100;
			return Math.round(percentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("percentage.calulation.failed", null));
		}
	}

	/**
	 * Calculate the percentage without scaling the decimal part
	 * 
	 * @param totalObtained
	 * @param quizTotal
	 * @return
	 */
	public static double percentageWithoutRounding(Integer totalObtained, Integer quizTotal) {
		try {
			double division = (double) totalObtained / quizTotal;
			return division * 100;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("pecentage.without.scaling.failed", null));
		}
	}

	/**
	 * Round up the decimal part. Range of the decimal is 2
	 * 
	 * @param roundUpValue
	 * @return
	 */
	public static BigDecimal roundUpHalfUsingBigDecimal(double roundUpValue, int range) {
		try {
			return BigDecimal.valueOf(roundUpValue).setScale(range, RoundingMode.HALF_UP);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("round.up.half.failed", null));
		}
	}
	
	/**
	 * Convert the BigDecimal to double. Then round the value.
	 * 
	 * @param bigDecimal
	 * @return
	 */
	public static Long bigDecimalConvertion(BigDecimal bigDecimal) {
		try {
			 if (bigDecimal == null) {
				 return null;
			 }
			double decimal = bigDecimal.doubleValue();
			return Math.round(decimal);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("bigdecimal.convertion", null));
		}
	}

}
