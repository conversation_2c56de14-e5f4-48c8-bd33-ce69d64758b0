package com.lms.userservice.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> C Acahri
 * @since 0.0.1
 *
 */
@Slf4j
public class DateUtilities {

	/**
	 * Converting the String (which comes in the format dd-MM-yyyy) to the LocalDate
	 * eg., 02-12-2004 will return 2004-12-02.
	 * 
	 * @param value
	 * @return
	 */
	public static LocalDate convertStringToLocalDate(String value) {
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
			return LocalDate.parse(value, formatter);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, "Date convertion failed");
		}
	}

	/**
	 * Converting the long to String (which comes in the format dd-MM-yyyy)
	 * 
	 * @param value
	 * @return
	 */
	public static String fromLongMilliToString(long modifiedAtMillis) {
		try {
			Date date = new Date(modifiedAtMillis);
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy | hh:mm:ss a");

			// Format the date into the desired "DD MON YYYY | HH12:MI:SS AM" string format
			String formattedTime = dateFormat.format(date);
			return formattedTime;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					"Converting Long to String in HH:mm:SS format is failed.");
		}
	}

	/**
	 * Check the given date not current or future date. This is mainly using for the
	 * checking the Date of birth during the CSV file upload.
	 * 
	 * @param value
	 * @return past date return {@code true} else {@code false}
	 */
	public static boolean checkIfTheDateHasPassed(String value) {
		try {
			Date date = new SimpleDateFormat("dd-MM-yyyy").parse(value);
			return date.before(new Date());
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, "Not an expired date!");
		}
	}
	
	public static String fromLongDateToString(long modifiedAtMillis) {
		try {
			Date date = new Date(modifiedAtMillis);
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");

			// Format the date into the desired "DD-MM-YYYY | HH12:MI:SS AM" string format
			String formattedTime = dateFormat.format(date);
			return formattedTime;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					"Converting Long to String in HH:mm:SS format is failed.");
		}
	}
}
