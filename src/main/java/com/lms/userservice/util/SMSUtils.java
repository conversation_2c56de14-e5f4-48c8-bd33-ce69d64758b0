package com.lms.userservice.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;

import com.lms.userservice.response.dto.SmsSendResponseDTO;

public class SMSUtils {

	@SuppressWarnings("all")
	public static String sendSms(SmsSendResponseDTO smsSendResponseDTO) {
		String sResult = null;
		try {
			// Construct data
			String data = "user=" + URLEncoder.encode("Azvasa", "UTF-8");
			data += "&password=" + URLEncoder.encode("Azvasa@123", "UTF-8");
			data += "&message=" + URLEncoder.encode("Dear" + smsSendResponseDTO.getUserName()
					+ ", OTP to change password for LMS is " + smsSendResponseDTO.getOtpCode() + "Regards, AZVASA");
			data += "&sender=" + URLEncoder.encode("AZVASA", "UTF-8");
			data += "&mobile=" + URLEncoder.encode(smsSendResponseDTO.getMobileNumber(), "UTF-8");
			data += "&type=" + URLEncoder.encode("3", "UTF-8");
			data += "&template_id=" + URLEncoder.encode("1007951310759545888", "UTF-8");
			// Send data
			URL url = new URL("http://api.bulksmsgateway.in/sendmessage.php?" + data);
			URLConnection conn = url.openConnection();
			conn.setDoOutput(true);
			OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
			wr.write(data);
			wr.flush();
			// Get the response
			BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line;
			String sResult1 = "";
			while ((line = rd.readLine()) != null) {
				// Process line...
				sResult1 = sResult1 + line + " ";
			}
			wr.close();
			rd.close();
			return sResult1;
		} catch (Exception e) {
			System.out.println("Error SMS " + e);
			return "Error " + e;
		}
	}
}
