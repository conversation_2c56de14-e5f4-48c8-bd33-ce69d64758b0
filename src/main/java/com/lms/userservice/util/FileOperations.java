package com.lms.userservice.util;

import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.CollectionUtils;

import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.FileExtensions;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.model.ImportedFiles;
import com.lms.userservice.model.StudentsFile;
import com.lms.userservice.model.TeachersFile;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvBindByPosition;

import au.com.bytecode.opencsv.CSVReader;
import au.com.bytecode.opencsv.bean.ColumnPositionMappingStrategy;
import au.com.bytecode.opencsv.bean.CsvToBean;
import lombok.extern.slf4j.Slf4j;

/**
 * {@code FileReaderOrWriter} will read from any type of file.
 * 
 * <p>
 * {@code readFromFile} is the opening method, where will decide which file
 * method is used to read the file.
 * 
 * <AUTHOR> C Achari
 * @since 0.0.1
 *
 */
@Slf4j
public class FileOperations {

	/**
	 * {@code readFromFile} is the opening method,
	 * {@link com.lms.userservice.enums.FileExtensions FileExtensions} redirect to
	 * file reader.
	 * 
	 * @param filePath
	 * @param fileType
	 * @param extension
	 * @return
	 */
	public static ImportedFiles readFromFile(String filePath, FileTypes fileType, FileExtensions extension) {
		try {
			log.info("Reading file, opening method");
			ImportedFiles importedFiles = new ImportedFiles();
			if (extension.equals(FileExtensions.CSV)) {
				log.info("CSV file is going to read");
				importedFiles = readFromCSV(filePath, fileType);
			}
			log.info("Reading file completed");
			return importedFiles;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.FILE_READING_FAILED);
		}
	}

	/**
	 * {@code readFromCSV} read the temporarily save CSV file from the location and
	 * return {@link com.lms.userservice.model.ImportedFiles ImportedFiles}.
	 * <p>
	 * {@link com.lms.userservice.enums.FileTypes FileTypes} will decide which class
	 * has to mapped with the generic methods
	 * 
	 * @param filePath
	 * @param fileType
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static ImportedFiles readFromCSV(String filePath, FileTypes fileType) {
		try {
			log.info("Reading CSV file started");
			ImportedFiles importedFiles = new ImportedFiles();
			CsvToBean csvToBean = new CsvToBean();
			log.info("Reading the CSV file by skipping the header");
			CSVReader csvReader = new CSVReader(new FileReader(filePath), ',', '\'', 1);

			List list = null;
			List<String> filedsList = null;

			log.info("Mapping class is deciding");
			if (fileType.equals(FileTypes.STUDENTS)) {
				filedsList = getFieldsInOrder(StudentsFile.class);
				list = csvToBean.parse(setColumMapping(StudentsFile.class, filedsList), csvReader);
				List<StudentsFile> studentList = new ArrayList<>();
				for (Object object : list) {
					studentList.add((StudentsFile) object);
				}
				log.info("students list: {}", studentList);
				if (!CollectionUtils.isEmpty(studentList)) {
					log.info("Data mapped to the StudentsFile.class");
					importedFiles.setStudentsFile(studentList);
				}
			}

			if (fileType.equals(FileTypes.TEACHERS)) {
				filedsList = getFieldsInOrder(TeachersFile.class);
				list = csvToBean.parse(setColumMapping(TeachersFile.class, filedsList), csvReader);
				List<TeachersFile> teachersList = new ArrayList<>();
				for (Object object : list) {
					teachersList.add((TeachersFile) object);
				}
				log.info("teachers list: " + teachersList);
				if (!CollectionUtils.isEmpty(teachersList)) {
					log.info("Data has been mapped to the Teachers");
					importedFiles.setTeachersFile(teachersList);
				}
			}
			log.info("Mapping completed");
			csvReader.close();
			return importedFiles;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.READING_FROM_CSV_FAILED);
		}
	}

	/**
	 * Generic method to read the field names from the Beans
	 * 
	 * @param clazz
	 * @return
	 */
	private static List<String> getFieldsInOrder(Class<?> clazz) {
		return Arrays.stream(clazz.getDeclaredFields())
				.filter(f -> f.getAnnotation(CsvBindByPosition.class) != null
						&& f.getAnnotation(CsvBindByName.class) != null)
				.sorted(Comparator.comparing(f -> f.getAnnotation(CsvBindByPosition.class).position()))
				.map(f -> f.getAnnotation(CsvBindByName.class).column()).collect(Collectors.toList());
	}

	/**
	 * Generic class for CSV file mapped with Beans
	 * 
	 * @param clazz
	 * @param fieldsList
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private static ColumnPositionMappingStrategy setColumMapping(Class<?> clazz, List<String> fieldsList) {
		try {
			ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
			strategy.setType(clazz);
			// Convert to string array
			String[] columns = fieldsList.toArray(new String[fieldsList.size()]);
			strategy.setColumnMapping(columns);
			return strategy;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.COLUMN_MAPPING_FAILED);
		}
	}

}
