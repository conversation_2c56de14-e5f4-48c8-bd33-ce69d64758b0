package com.lms.userservice.util;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.lms.userservice.component.Translator;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;

import lombok.extern.slf4j.Slf4j;

/**
 * During the pagination field {@code sortedBy} will receive field name which is
 * not related with the database fields. To map with different field use this
 * class. According the tables create different methods
 * 
 * <AUTHOR> C Achari
 * @since 0.0.1
 *
 */
@Slf4j
public class FieldMappers {

	/**
	 * Based on Student pagination
	 * 
	 * @param value
	 * @return
	 */
	@SuppressWarnings("all")
	public static String studentsApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "name", "email", "mobile", "sectionId", "gradeId", "userId", "userName",
					"lastLoginTime", "active", "createdAt" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "first_name", "email", "mobile", "section_id", "grade_id", "u.id",
					"u.user_name", "u.last_login_time", "active", "created_at" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

	/**
	 * Based on Teacher pagination
	 * 
	 * @param value
	 * @return
	 */
	public static String teacherApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "name", "email", "userName", "mobile", "schoolId", "role", "schoolName",
					"branch", "branchName", "lastLoginTime", "active", "createdAt" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "first_name", "email", "u.user_name", "mobile", "s.id",
					"academic_staff_profile", "s.name", "b.id", "b.name", "u.last_login_time", "active", "created_at" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

	/**
	 * Based on Users pagination. This can also use for the Admin-User pagination.
	 * 
	 * @param value
	 * @return
	 */
	public static String usersApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "firstName", "lastName", "userName", "email", "phoneNumber",
					"lastLoginTime", "roleId", "createdAt" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "first_name", "last_name", "user_name", "email", "phone_number",
					"last_login_time", "urm.role_id", "created_at" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

	/**
	 * Based on Administration Projection
	 * 
	 * @param value
	 * @return
	 */
	public static String administrationApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "firstName", "lastName", "email", "mobile", "numberOfSchools",
					"numberOfBranches", "schoolName", "createdAt" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "first_name", "last_name", "email", "mobile", "uim_2.schoolCount",
					"uim_2.branchCount", "s.name", "created_at" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

	/**
	 * Based on School pagination
	 * 
	 * @param value
	 * @return
	 */
	@SuppressWarnings("all")
	public static String schoolApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "name", "code", "cityId", "pocEmail", "phoneNumber", "website",
					"signatoryName", "signatoryRole", "logoUrl", "boardId", "createdAt", "numberOfBranches",
					"numberOfCoordinators", "numberOfTeachers", "numberOfPrincipals", "numberOfStudents" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "name", "code", "cityId", "pocEmail", "phoneNumber", "website",
					"signatoryName", "signatoryRole", "logoUrl", "boardId", "createdAt", "numberOfBranches",
					"numberOfCoordinators", "numberOfTeachers", "numberOfPrincipals", "numberOfStudents" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

	/**
	 * Based on Branches pagination
	 * 
	 * @param value
	 * @return
	 */
	@SuppressWarnings("all")
	public static String branchApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "branch", "cityId", "locality", "boardId", "numberOfTeachers",
					"numberOfCoordinators", "numberOfPrincipals", "numberOfStudents", "createdAt" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "name", "city_id", "locality", "board_id", "numberOfTeachers",
					"numberOfCoordinators", "numberOfPrincipals", "numberOfStudents", "createdAt" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

	/**
	 * Based on token pagination
	 * 
	 * @param value
	 * @return
	 */
	@SuppressWarnings("all")
	public static String tokenApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "token", "roleId", "email", "multiUser", "noOfUsers", "usedUserId",
					"dateCreated", "expiaryDate", "createdAt" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "token", "role_id", "u.email", "multi_user", "token_use_count", "u.id",
					"created_at", "expiary_date", "created_at" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

}