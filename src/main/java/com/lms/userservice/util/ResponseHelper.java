package com.lms.userservice.util;

import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.model.LMSResponse;

/**
 * Create the common response format for all the API
 * 
 * <AUTHOR> C <PERSON>chari
 * @since 0.0.1
 *
 */
public class ResponseHelper {

	/**
	 * Use when the API return some response class.
	 * 
	 * @param response
	 * @param data
	 * @param successMessage
	 * @param errorMessage
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static LMSResponse createResponse(LMSResponse response, Object data, String successMessage,
			String errorMessage) {

		if (data != null) {
			response.setSuccess(true);
			response.setData(data);
			response.setMessage(successMessage);
		} else {
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, errorMessage);
		}
		return response;
	}

	/**
	 * Use when the API return some response class.
	 * <p>
	 * Use this while do GET APIs
	 * 
	 * @param response
	 * @param data
	 * @param successMessage
	 * @param errorMessage
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static LMSResponse responseForGetOrFeign(LMSResponse response, Object data, String successMessage,
			String errorMessage) {
		if (data != null) {
			response.setSuccess(true);
			response.setData(data);
			response.setMessage(successMessage);
		} else {
			throw new USException(ErrorCodes.NO_CONTENT, errorMessage);
		}
		return response;
	}

	/**
	 * Use this format when the API returns only boolean
	 * 
	 * @param response
	 * @param flag
	 * @param successMessage
	 * @param errorMessage
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static LMSResponse createResponseForFlags(LMSResponse response, boolean flag, String successMessage,
			String errorMessage) {
		if (flag) {
			response.setSuccess(flag);
			response.setData(flag);
			response.setMessage(successMessage);
		} else {
			throw new USException(ErrorCodes.OK, errorMessage);
		}
		return response;
	}
}
