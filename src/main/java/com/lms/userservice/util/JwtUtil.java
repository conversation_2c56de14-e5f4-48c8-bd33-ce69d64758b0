
package com.lms.userservice.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
/**
 * Create the JwtUtil for JWT Token Generation,Expire,Claims
 * 
 * <AUTHOR>
 * @since 0.0.1
 *
 */
@Slf4j
@Service
public class JwtUtil {

	private String secret;
	private Long jwtExpirationInMs;
	private Long refreshExpirationDateInMs;
	private Long maxIdleTimeoutInMs;

	@Value("${jwt.secret}")
	public void setSecret(String secret) {
		this.secret = secret;
	}

	@Value("${jwt.expirationDateInMs}")
	public void setJwtExpirationInMs(Long jwtExpirationInMs) {
		this.jwtExpirationInMs = jwtExpirationInMs;
	}
	
	@Value("${jwt.maxIdleTimeoutInMs}")
	public void setMaxIdleTimeoutInMs(Long maxIdleTimeoutInMs) {
		this.maxIdleTimeoutInMs = maxIdleTimeoutInMs;
	}

	@Value("${jwt.refreshExpirationDateInMs}")
	public void setRefreshExpirationDateInMs(Long refreshExpirationDateInMs) {
		this.refreshExpirationDateInMs = refreshExpirationDateInMs;
	}

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }
    
    public <T> T extractClaim(Claims claims, Function<Claims, T> claimsResolver) {
        return claimsResolver.apply(claims);
    }
    
    private Claims extractAllClaims(String token) {
    	//try {
    		return Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
//    	}catch (Exception e) {
//    		log.error(ExceptionUtils.getStackTrace(e));
//    		throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("malformed.jwt.signature", null));
//		}
    }

    public Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }
    
    public Boolean canTokenBeExtended(Claims claims) {
    	Date dt=new Date(System.currentTimeMillis()-maxIdleTimeoutInMs);
    	Boolean bl=extractClaim(claims, Claims::getExpiration).after(dt);
        return bl;
    }

    public String generateToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        return createToken(claims, username);
    }
    
    public String doGenerateRefreshToken(Map<String, Object> claims, String subject) {
    	
		return Jwts.builder().setClaims(claims).setSubject(subject).setIssuedAt(new Date(System.currentTimeMillis()))
				.setExpiration(new Date(System.currentTimeMillis() + refreshExpirationDateInMs))
				.signWith(SignatureAlgorithm.HS256, secret).compact();

	}
    
public String reGenerateToken(Map<String, Object> claims, String subject) {
    	
		return Jwts.builder().setClaims(claims).setSubject(subject).setIssuedAt(new Date(System.currentTimeMillis()))
				.setExpiration(new Date(System.currentTimeMillis() + jwtExpirationInMs))
				.signWith(SignatureAlgorithm.HS256, secret).compact();

	}

    private String createToken(Map<String, Object> claims, String subject) {

        return Jwts.builder().setClaims(claims).setSubject(subject).setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis()+jwtExpirationInMs))
                .signWith(SignatureAlgorithm.HS256, secret).compact();
    }

    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }
    
    public static String getUsername() {
    UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().getAuthentication()
            .getPrincipal();
    String username = userDetails.getUsername();
	return username;
    }
    
    public String currentLoginUser() {
    	return SecurityContextHolder.getContext().getAuthentication().getName();
    }
}

