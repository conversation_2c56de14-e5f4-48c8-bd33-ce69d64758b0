package com.lms.userservice.util;

import java.nio.charset.StandardCharsets;
import java.security.spec.KeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.lms.userservice.component.Translator;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EncryptionAndDecryption {
	
	private static final String SECRET_KEY = "943BE2C1B43D6AE13B8DBE78D496F";
	private static final String SALT = "A611C228DC5564E58CE15AB2D9ED5";
	
	/**
	 * @param strToEncrypt
	 * @return
	 */
	@SuppressWarnings("all")
	public static String encrypt(String strToEncrypt) {
		try {
			String encrypted = null;
			if(!StringUtils.isBlank(strToEncrypt)) {
				byte[] iv = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
				IvParameterSpec ivspec = new IvParameterSpec(iv);

				SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
				KeySpec spec = new PBEKeySpec(SECRET_KEY.toCharArray(), SALT.getBytes(), 65536, 256);
				SecretKey tmp = factory.generateSecret(spec);
				SecretKeySpec secretKey = new SecretKeySpec(tmp.getEncoded(), "AES");

				Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivspec);
				encrypted = Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes(StandardCharsets.UTF_8)));
			}
			return encrypted;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("encrypt.failed", null));
		}
	}

	/**
	 * @param strToDecrypt
	 * @return
	 */
	@SuppressWarnings("all")
	public static String decrypt(String strToDecrypt) {
		try {
			byte[] iv = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
			IvParameterSpec ivspec = new IvParameterSpec(iv);

			SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
			KeySpec spec = new PBEKeySpec(SECRET_KEY.toCharArray(), SALT.getBytes(), 65536, 256);
			SecretKey tmp = factory.generateSecret(spec);
			SecretKeySpec secretKey = new SecretKeySpec(tmp.getEncoded(), "AES");

			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
			cipher.init(Cipher.DECRYPT_MODE, secretKey, ivspec);
			return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("decrypt.failed", null));
		}
	}
}
