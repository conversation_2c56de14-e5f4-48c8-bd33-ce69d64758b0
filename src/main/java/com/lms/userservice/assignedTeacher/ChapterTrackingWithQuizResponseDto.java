package com.lms.userservice.assignedTeacher;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ChapterTrackingWithQuizResponseDto {

	@ApiModelProperty(value = "Grade id", example = "2c9180878471c1fd01847426d753008c")
	private String gradeId;

	@ApiModelProperty(value = "Grade", example = "Grade 10")
	private String grade;

	@ApiModelProperty(value = "Section id", example = "2c9180878471c1fd018475210afd0135")
	private String sectionId;

	@ApiModelProperty(value = "Section", example = "Section A")
	private String section;

	@ApiModelProperty(value = "Subject Id", example = "2c9180878471c1fd018474c592c000d5")
	private String subjectId;

	@ApiModelProperty(value = "Subject", example = "Science")
	private String subject;

	@ApiModelProperty(value = "Subtopic id", example = "40289288833c3171018341910ed6001d")
	private String subTopicId;

	@ApiModelProperty(value = "Subtopic", example = "Physics")
	private String subTopic;

	@ApiModelProperty(value = "Chapter id", example = "2c918083856331d7018564a5a35e0006")
	private String chapterId;

	@ApiModelProperty(value = "Chapter", example = "Astrophysics")
	private String chapter;

	@ApiModelProperty(value = "When the chapter start", example = "2022-08-12")
	private LocalDate startDate;

	@ApiModelProperty(value = "When the chapter end", example = "2022-08-20")
	private LocalDate endDate;

	@ApiModelProperty(value = "Number of days required to complete the chapter", example = "70")
	private Integer numberOfDays;

	@ApiModelProperty(value = "Number of days required to complete the chapter", example = "70")
	private String teacherName;

	@ApiModelProperty(value = "Number of days required to complete the chapter", example = "70")
	private String avgQuizScore;

	@ApiModelProperty(value = "Number of days required to complete the chapter", example = "70")
	private Long attendance;

	@ApiModelProperty(value = "When the chapter start", example = "2022-08-12")
	private String releaseDate;



}
