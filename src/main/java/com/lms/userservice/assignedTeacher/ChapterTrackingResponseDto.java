package com.lms.userservice.assignedTeacher;

import java.time.LocalDate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ChapterTrackingResponseDto {

	@ApiModelProperty(value = "Grade id", example = "2c9180878471c1fd01847426d753008c", position = 1)
	private String gradeId;

	@ApiModelProperty(value = "Grade", example = "Grade 10", position = 2)
	private String grade;

	@ApiModelProperty(value = "Section id", example = "2c9180878471c1fd018475210afd0135", position = 3)
	private String sectionId;

	@ApiModelProperty(value = "Section", example = "Section A", position = 4)
	private String section;

	@ApiModelProperty(value = "Subject Id", example = "2c9180878471c1fd018474c592c000d5", position = 5)
	private String subjectId;

	@ApiModelProperty(value = "Subject", example = "Science", position = 6)
	private String subject;

	@ApiModelProperty(value = "Subtopic id", example = "40289288833c3171018341910ed6001d", position = 7)
	private String subTopicId;

	@ApiModelProperty(value = "Subtopic", example = "Physics", position = 8)
	private String subTopic;

	@ApiModelProperty(value = "Chapter id", example = "2c918083856331d7018564a5a35e0006", position = 9)
	private String chapterId;

	@ApiModelProperty(value = "Chapter", example = "Astrophysics", position = 10)
	private String chapter;

	@ApiModelProperty(value = "When the chapter start", example = "2022-08-12", position = 11)
	private LocalDate startDate;

	@ApiModelProperty(value = "When the chapter end", example = "2022-08-20", position = 12)
	private LocalDate endDate;

	@ApiModelProperty(value = "Number of days required to complete the chapter", example = "70", position = 13)
	private Integer numberOfDays;
}
