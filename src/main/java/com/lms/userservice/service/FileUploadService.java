package com.lms.userservice.service;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.response.dto.BulkUploadResponseDto;

@Service
public interface FileUploadService {

	/**
	 * Upload and save the file temporarily, then from the file according to the
	 * {@link com.lms.userservice.enums.FileTypes FileTypes} and
	 * {@link com.lms.userservice.enums.FileExtensions FileExtensions}
	 * <p>
	 * If the file read completely to the database then file and folder will be
	 * deleted.
	 * 
	 * @param file
	 * @param fileTypes
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	BulkUploadResponseDto uploadFile(MultipartFile file, FileTypes fileTypes);


}
