package com.lms.userservice.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.SchoolRequestDto;
import com.lms.userservice.response.dto.CitiesSchoolCountDetailsResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SchoolBranchDetailsResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;
import com.lms.userservice.response.dto.SchoolBranchesResponseDto;
import com.lms.userservice.response.dto.SchoolMinResponseDto;
import com.lms.userservice.response.dto.SchoolResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;

@Service
public interface SchoolService {

	/**
	 * Register the school details
	 * 
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	SchoolResponseDto createSchool(SchoolRequestDto request);

	/**
	 * Get School details by id
	 * 
	 * @param id
	 * @return
	 * @since 0.0.1
	 */
	SchoolResponseDto getSchoolById(String id);

	/**
	 * Update a school details by id, To-Do other checking will do later
	 * 
	 * @param id
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	SchoolResponseDto updateSchool(String id, SchoolRequestDto request);

	/**
	 * Mark School as deleted
	 * 
	 * @param id
	 * @return boolean
	 * @since 0.0.1
	 */
	Boolean deleteSchoolById(String id);

	/**
	 * Get all the school details by Page
	 * 
	 * @return
	 * @since 0.0.1
	 */
	public PaginatedResponse<SchoolResponseDto> getPageSchools(int pageNumber, int pageSize, String sortBy,
			boolean order, String search, String schoolId, List<String> boardId, Boolean active);

	/**
	 * Get all counts
	 * 
	 * @param schoolId
	 * @param boardIds
	 * @return
	 */
	UsersCountResponseDto getAllCounts(String schoolId, List<String> boardIds, boolean administrator, String userName);

	/**
	 * Get all the schools
	 * 
	 * @return
	 */
	List<SchoolMinResponseDto> getAllSchools(String userId, String search);

	/**
	 * Get all the schools with branches list
	 * 
	 * @return
	 */
	List<SchoolBranchesResponseDto> getAllSchoolsWithBranches(String search);

	/**
	 * Update active field to activate/deactivate school
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	Boolean updateActiveField(String id, boolean active);

	/**
	 * Return the mapping tables related to the school, if the school's column
	 * has_branch==false return the default message. Or check the count if the
	 * count==0 return the default message or show the school has mapping as in a
	 * message format.
	 * 
	 * @param id
	 * @return
	 */
	String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType);

	/**
	 * Get the Schools and Branches Mapped data by City Id before marking the record
	 * as deleted
	 * 
	 * @param cityId
	 * @return
	 */
	Boolean getCountOfSchoolAndBranchByCityId(String cityId);

	/**
	 * Get the Schools and Branches Mapped data by Board Id before marking the
	 * record as deleted
	 * 
	 * @param boardId
	 * @return
	 */
	Boolean getCountOfSchoolAndBranchByBoardId(String boardId);

	/**
	 * Get the last modification done on the table schools return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();

	/**
	 * Confirmation API before active/de-active and delete
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	ConfirmationApiResponseDto confirmationApiForSchool(String id, String operationType);

	/**
	 * This feign call for content service to get schoolName and branchName
	 * 
	 * @param schoolId
	 * @param branchId
	 * @return
	 */
	SchoolBranchResponseDto getSchoolAndBranchById(String schoolId, String branchId);

	/**
	 * Feign call to master-service for Dash board, get schools by city
	 *
	 */
	List<CitiesSchoolCountDetailsResponseDto> getCitiesSchoolDetails(List<String> cityIds);

	/**
	 * This API is used for management/super_admin/school_admin dash_board for
	 * getting school's branches details based on user name
	 * 
	 * @return
	 *
	 */
	PaginatedResponse<SchoolBranchDetailsResponseDto> getAllSchoolDetailsWithUserName(int pageNumber, int pageSize,
			String sortBy, boolean order, String search, String schoolId, Boolean active);

	/**
	 * By plan find the school/branches to content-service.
	 * 
	 * @param planIds
	 * @return
	 */
	List<SchoolBranchResponseDto> schoolAndBranchForAssignAssessment(List<String> planIds);

	/**
	 * Get the school/branch name for the assessment papers(if the assessment paper
	 * has assigned to any school/branch). Fiegn to content-service
	 * 
	 * @param schoolIds
	 * @param branchIds
	 * @return
	 */
	List<SchoolBranchResponseDto> assignedInstitutesDetails(List<String> schoolIds, List<String> branchIds);

}