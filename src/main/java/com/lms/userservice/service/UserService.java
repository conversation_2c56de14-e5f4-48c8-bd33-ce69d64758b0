package com.lms.userservice.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.lms.userservice.entity.Users;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AuthRequestDto;
import com.lms.userservice.request.dto.ChangePasswordRequestDto;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.EmailRequestDto;
import com.lms.userservice.request.dto.ForgetPasswordNumberOtpRequestDto;
import com.lms.userservice.request.dto.ForgetPasswordOtpRequestDto;
import com.lms.userservice.request.dto.ResetPasswordWithoutTokenRequestDto;
import com.lms.userservice.request.dto.SchoolsBranchsRequestDto;
import com.lms.userservice.request.dto.ShareDetailsRequestDto;
import com.lms.userservice.request.dto.UpdateMobileNumberRequestDto;
import com.lms.userservice.request.dto.UserRolesRequestDto;
import com.lms.userservice.request.dto.UsersRequestDto;
import com.lms.userservice.request.dto.UsersResetPasswordDto;
import com.lms.userservice.request.dto.ValidateTokenRequestDto;
import com.lms.userservice.request.dto.VerifyOtpRequestDto;
import com.lms.userservice.response.dto.AuthResponseDto;
import com.lms.userservice.response.dto.ProfileResponseDto;
import com.lms.userservice.response.dto.SchoolsBranchsResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.response.dto.UserMinResponseDto;
import com.lms.userservice.response.dto.UserRolesResponseDto;
import com.lms.userservice.response.dto.UsersFeignDto;
import com.lms.userservice.response.dto.UsersResponseDto;
import com.lms.userservice.response.dto.UsersRoleResponseDto;
import com.lms.userservice.response.dto.UsersUseWebMobCountResponseDto;

/**
 * <AUTHOR> The UserService interface
 */

public interface UserService {

	/**
	 * Register the user details
	 * 
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	UsersResponseDto createuser(UsersRequestDto request);

	/**
	 * Get user details by id
	 * 
	 * @param id
	 * @return
	 * @since 0.0.1
	 */
	UsersResponseDto getuserById(String id);

	/**
	 * Get all the user details
	 * 
	 * @return
	 * @since 0.0.1
	 */
	List<UsersResponseDto> getAllUsers();

	/**
	 * Update a user details by id, To-Do other checking will do later
	 * 
	 * @param id
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	UsersResponseDto updateUser(String id, UsersRequestDto request);

	/**
	 * Get all the user details by Page
	 * 
	 * @return
	 * @since 0.0.1
	 */
	PaginatedResponse<UsersResponseDto> getUsersByPagniation(int pageNumber, int pageSize, boolean sortOrder,
			String sortBy, String search, String roleId, Boolean active);

	/**
	 * Authentication
	 * 
	 * @return
	 * @since 0.0.1
	 */

	public AuthResponseDto authentication(AuthRequestDto authRequestDto);

	/**
	 * Authentication using refresh token
	 * 
	 * @return
	 * @since 0.0.1
	 */

	public AuthResponseDto authenticationByRefreshToken(HttpServletRequest request);

	/**
	 * Get user details by email
	 * 
	 * @param id
	 * @return
	 * @since 0.0.1
	 */
	public UsersResponseDto getuserByEmail(String email);

	public UsersResponseDto getuserByPhoneNumber(String phoneNumber);

	/**
	 * Get user details by user name This method wrote for master-service token
	 * validation.
	 * 
	 * @param username
	 * @return
	 * @since 0.0.1
	 */
	public UsersFeignDto getUsersByUsernameForFeign(String username);

	public UsersResponseDto resetPassword(String id, UsersResetPasswordDto request);

	/**
	 * @param id
	 * @return
	 */
	Boolean deleteUserById(String id);

	/**
	 * Add the roles to user
	 * 
	 * @param users
	 * @param userRoles
	 * @return
	 */
	List<UserRolesResponseDto> addRoles(Users users, List<UserRolesRequestDto> userRoles);

	/**
	 * To map the Users with Schools and Branches
	 * 
	 * @param users
	 * @param institutions
	 * @return
	 */
	List<SchoolsBranchsResponseDto> addInstitution(Users users, List<SchoolsBranchsRequestDto> institutions);

	/**
	 * Once the user/admin/teacher or student deleted, then remove user-role mapping
	 * too.
	 * 
	 * @param userId
	 * @return
	 */
	Long deleteUsersRoleMapping(String userId);

	/**
	 * Once the user/admin/teacher or student deleted, then remove user-institution
	 * mapping too.
	 * 
	 * @param userId
	 * @return
	 */
	Long deleteUsersInstitutionMapping(String userId);

	/**
	 * Token will get validate before each and every request, which needed the
	 * token.
	 * 
	 * @param token
	 * @return
	 */
	UsersResponseDto tokenValidation(ValidateTokenRequestDto request);

	/**
	 * Sends a reset link to the email with the instructions for resetting password
	 * 
	 * @param email
	 * @return
	 */
	EmailRequestDto forgotPassword(String email, String lmsEnv, String userName);

	String forgotPasswordMobile(String phoneNumber, String userName);

	/**
	 * @param id
	 * @param operationType
	 * @return
	 */
	String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType);

	/**
	 * Followed by the forgot-password from the UI
	 * 
	 * @param userId
	 * @param request
	 * @return
	 */
	UsersResponseDto withoutTokenResetPassword(ResetPasswordWithoutTokenRequestDto request);

	/**
	 * Check verify the user with phone number and the otp, then resetting the
	 * password
	 * 
	 * @param request
	 * @return
	 */
	UsersResponseDto verifyOtp(VerifyOtpRequestDto request);

	/**
	 * After verify the OTP , resetting the password. This API no need of token
	 * 
	 * @param request
	 * @return
	 */
	UsersResponseDto mobileResetPasswordWithoutToken(ResetPasswordWithoutTokenRequestDto request);

	/**
	 * Get the last modification done on the table users return format example will
	 * be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();

	/**
	 * List all user responses matching the particular role
	 * 
	 * @param roleId
	 * @return
	 */
	List<UserMinResponseDto> getAllUsersMappedToRole(String roleId);

	/*
	 * Generate user name for persons like school-admin, management, admin-uers
	 * [master/super/content/revision-Admin or reviewer]
	 * 
	 * @param firstName
	 * 
	 * @param lastName
	 * 
	 * @return
	 */
	String generateUserName(String firstName, String lastName);

	/**
	 * Get user details by user name on the basis of role
	 * 
	 * @param username
	 * @param roles
	 * @return
	 */
	ProfileResponseDto getUsersByUserName(String username, List<String> roles);

	/**
	 * count roles used in user-service
	 * 
	 * @param roleId
	 * @return
	 */
	Long countRoleIdForDeletion(String roleId);

	/**
	 * Get all the user ids mapped with role id for notification
	 * 
	 * @param roleId
	 * @return
	 */
	List<String> getUserIdsByRoleId(String roleId);

	/**
	 * Share the user details via email, WhatsApp or SMS. Currently only Email is
	 * integrating WhatsApp and SMS are paid.
	 * 
	 * @param request
	 * @return
	 */
	ShareDetailsResponseDto shareUserDetails(ShareDetailsRequestDto request);

	/**
	 * Update password by admin to entire application.
	 * <ol>
	 * <li>Check with persona and bring the username, email, firstName</li>
	 * <li>Update the password by username to users table</li>
	 * <li>Set the baseUrl, password reset url and password to response body</li>
	 * </ol>
	 * 
	 * @param request
	 * @return
	 */
	CreateUserEmailRequestDto changePasswordBySuperAdmin(ChangePasswordRequestDto request);

	/**
	 * Generate otp to change Mobile number
	 * 
	 * @param request
	 * @return
	 */
	String generateOtp(UpdateMobileNumberRequestDto request);

	/**
	 * Verify otp and change the Mobile number.
	 * 
	 * @param request
	 * @return
	 */
	UsersResponseDto verifyOtpToChangeMobileNumber(UpdateMobileNumberRequestDto request);
	
	/**
	 * Extract the user_name which is created based on either phone_number or email.
	 * The {@code extractor} can hold either phone number or email.
	 */
	List<String> getUserNameExtractingList(String extractor);

	/**
	 * This Is feign call for master service to get users details.
	 * @param roleIds
	 * @return
	 */
	List<UsersRoleResponseDto> getAllAdminUsersRole(List<String> roleIds);

	/**
	 * This api is used for count Web User, Mob User, both users with most use.
	 * @param userId
	 * @param userName
	 * @return
	 */
	List<UsersUseWebMobCountResponseDto> getUserWebMobCount();

	/**
	 * Feign call to content service to get the first/last name by username
	 * 
	 * @param userNames
	 * @return
	 */
	List<UserMinResponseDto> getAllUsersByUserNames(List<String> userNames);

	UsersResponseDto getUserDetailsForFeign(String username);

	String userSendOtp(@Valid ForgetPasswordNumberOtpRequestDto request);

	String userVerifyOtp(@Valid ForgetPasswordOtpRequestDto request);

	List<String> getUserNameExtractingListWithUserType(String extractor, String userType);
	
}
