package com.lms.userservice.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AdministrationRequestDto;
import com.lms.userservice.response.dto.AdministrationResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;

@Service
public interface AdministrationService {

	/**
	 * Create either School_Admin or Management persona
	 * 
	 * School admin can only handle 1 School and N number of Branches of the school.
	 * Management can hadle N number of schools and N number of branches under the
	 * schools.
	 * 
	 * @param request
	 * @return
	 */
	AdministrationResponseDto createAdministrationAndSendEmail(AdministrationRequestDto request);

	/**
	 * Paginated response for both management and admin
	 * 
	 * search by: firstName, lastName, email and mobile filter by: schoolId,
	 * branchId and roleId
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortOrder
	 * @param sortBy
	 * @param schoolId
	 * @param branchId
	 * @param roleId
	 * @param search
	 * @return
	 */
	PaginatedResponse<AdministrationResponseDto> getAdministrationByPagniation(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String schoolId, String branchId, String roleId, String search,
			String adminType, Boolean active);

	/**
	 * @param id
	 * @param request
	 * @return
	 */
	AdministrationResponseDto updateAdministrationById(String id, AdministrationRequestDto request);

	/**
	 * @param id
	 * @return
	 */
	AdministrationResponseDto getAdministrationById(String id);

	/**
	 * Mark Administration as Deleted
	 * 
	 * @param id The Administration ID String
	 * @return boolean
	 */
	Boolean deleteAdministration(String id);

	/**
	 * Update active field
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	Boolean updateActiveField(String id, boolean active);

	/**
	 * Get the last modification done on the table administration return format
	 * example will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt(String adminType);

	/**
	 * List all administration details for a particular role
	 * 
	 * @param roleId
	 * @return
	 */
	List<NameCommonResponseDto> getAllAdministrationDetailsByRoleMapping(String roleId);

	/**
	 * Confirmation API before active/de-active and delete
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	ConfirmationApiResponseDto confirmationAPI(String id, String operationType);

}
