package com.lms.userservice.service;

import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.lms.userservice.enums.FileExtensions;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.feign.master.GradesSectionFeignResponseDto;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.model.UserMinDetails;
import com.lms.userservice.request.dto.TeacherAssignRequest;
import com.lms.userservice.request.dto.TeacherRequestDto;
import com.lms.userservice.request.dto.TeachersSelfRegRequestDto;
import com.lms.userservice.response.dto.AcademicStaffResponseDto;
import com.lms.userservice.response.dto.AllTypeUserMinResponseDto;
import com.lms.userservice.response.dto.BulkUploadResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.GradeAccessInfoResponseDto;
import com.lms.userservice.response.dto.GradeSectionResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.StudentFormativeMinResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentNameDetails;
import com.lms.userservice.response.dto.SubjectsMinResponseDto;
import com.lms.userservice.response.dto.TeacherAccessResponseDto;
import com.lms.userservice.response.dto.TeacherAssignResponse;
import com.lms.userservice.response.dto.TeacherAssignmentResponse;
import com.lms.userservice.response.dto.TeacherDetailsForFeignResponseDto;
import com.lms.userservice.response.dto.TeacherNameResponse;
import com.lms.userservice.response.dto.TeacherResponseDto;
import com.lms.userservice.response.dto.TeacherSubjectMappingFeignResponse;

public interface TeacherService {

	/**
	 * Get Teachers by Teacher ID
	 * 
	 * @param teacherId
	 * @return Teachers of particular ID
	 */
	public TeacherResponseDto getTeacherById(String teacherId);

	/**
	 * Get Teachers by Pagination
	 * 
	 * @return Teachers using Pagination
	 */
	public PaginatedResponse<AcademicStaffResponseDto> getTeachersByPage(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String search, String schoolId, String branchId, String profile,
			Boolean active);

	/**
	 * Add Teachers
	 * 
	 * @param teacherRequest the Teacher DTO Request
	 * @return Teachers
	 */
	public TeacherResponseDto addTeachers(TeacherRequestDto teacherRequest);

	/**
	 * Update Teachers by ID
	 * 
	 * @param teacherId
	 * @param teacherRequest
	 * @return Teachers
	 */
	public TeacherResponseDto updateTeacher(String teacherId, TeacherRequestDto teacherRequest);

	/**
	 * Mark Teacher as Deleted
	 * 
	 * @param teacherId
	 * @return boolean value
	 */
	public Boolean removeTeacher(String teacherId);

	/**
	 * Read Teachers from .csv file and save the data in the database
	 * 
	 * @param filePath  the file path
	 * @param extension the file extension
	 * @param fileTypes the file type
	 * @return List of Teachers fields
	 */
	@SuppressWarnings("rawtypes")
	BulkUploadResponseDto readAndSaveTeachersFromFile(String filePath, FileExtensions extension,
			FileTypes fileTypes);

	/**
	 * Teacher self registration using token
	 * 
	 * @param request The Teacher request DTO
	 * @return The Teacher response fields
	 */
	TeacherResponseDto teacherSelfRegistration(TeachersSelfRegRequestDto request);

	/**
	 * Update active field for activating/deactivating teacher
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	public Boolean updateActiveField(String id, boolean active);

	/**
	 * Find the academic_staff and then find the user class by the user_name
	 * 
	 * @param id
	 * @param operationType
	 * @return
	 */
	String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType);

	/**
	 * Get the last modification done on the table teachers return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();

	/**
	 * Get all teacher mappings based on coordinator type
	 * 
	 * @param coordinatorTypeId
	 * @return
	 */
	List<NameCommonResponseDto> getAllTeacherMappingsForCoordinatorType(String coordinatorTypeId);

	/**
	 * Get all teacher responses based on role mapping
	 * 
	 * @param roleId
	 * @return
	 */
	List<NameCommonResponseDto> getAllTeacherMappingsForRole(String roleId);

	/**
	 * Assign subject(s) to the teacher
	 *
	 * @param teacherId
	 * @param request
	 * @return
	 */
	List<TeacherAssignResponse> assignSubjectToTeacher(String teacherId, TeacherAssignRequest request);

	/**
	 * List all subjects mapped to teacher
	 * 
	 * @param teacherId
	 * @return
	 */
	List<GradeAccessInfoResponseDto> gradeAccessInformation(String teacherId);

	/**
	 * Toggle active subjects mapped to teacher for a particular primary key id
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	Boolean toggleActiveMappedSubjects(String id, boolean active);

	/**
	 * Fetch all subjects assigned to a teacher/coordinator/principal
	 *
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	List<SubjectsMinResponseDto> listAllSubjectsByTeacher(String teacherId, String gradeId, String sectionId,
			String menuName);

	/**
	 * Count of uploads for a teacher
	 * 
	 * @param teacherId
	 * @param subjectId
	 * @return
	 */
	Integer getCountOfTeacherUploads(String teacherId, String subjectId);

	/**
	 * Fetch count of all students under a teacher
	 *
	 * @param teacherId
	 * @param subjectId
	 * @return
	 */
	Integer getCountOfStudents(String teacherId, String subjectId);

	/**
	 * Fetch count of all quizzes under a teacher
	 *
	 * @param teacherId
	 * @param subjectId
	 * @return
	 */
	TeacherSubjectMappingFeignResponse getTeacherSubjectMappingMinResponse(String teacherId, String subjectId);

	/**
	 * Get teacher name responses
	 * 
	 * @param teacherIds
	 * @return
	 */
	List<TeacherNameResponse> getTeacherNameResponseByIds(List<String> teacherIds);

	/**
	 * Check if the teacher exists by given Id
	 * 
	 * @param teacherId
	 * @return
	 */
	Boolean checkIfTeacherExistsById(String teacherId);

	/**
	 * Confirmation API before active/de-active and delete
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	ConfirmationApiResponseDto checkTheMappingForConcept(String id, String operationType);

	/**
	 * Checking the mapping of coordinator type.
	 * 
	 * @param coordinatorTypeId
	 * @return
	 */
	boolean checkTheMappingForCoordinatoryType(String coordinatorTypeId);

	/**
	 * Find the mapping for subject, this is the feign API method.
	 * 
	 * @param subjectId
	 * @return
	 */
	boolean checkTheMappingForSubject(String subjectId);

	/**
	 * Find the mapping for sub-topic, this is the feign API method.
	 * 
	 * @param subTopicId
	 * @return
	 */
	boolean checkTheMappingForSubTopic(String subTopicId);

	/**
	 * Fetching the grade-section combo from assign-teacher table.
	 * assignedGradesFromPlan
	 * 
	 * @param teacherId
	 * @return
	 */
	List<GradesSectionFeignResponseDto> gradesFromTeacherAccessEntity(String teacherId, String menuName);

	/**
	 * This is feign call for finding section or grade for a particular teacher to
	 * content-service. return only assigned section
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @param subtopicId
	 * @param teacherId
	 * @return
	 */
	GradeSectionResponseDto getGradeOrSectionToTeacherForQuizRelease(String schoolId, String branchId, String gradeId,
			String subjectId, String subtopicId, String teacherId);

	/**
	 * Teacher access for coordinator and principal
	 * <p>
	 * 
	 * @param teacherName
	 * @param teacherEmail
	 * @param teacherBranch
	 * @return
	 */
	PaginatedResponse<AcademicStaffResponseDto> currentTeacherAccessForCoordinatorORPrincipal(int pageNumber,
			int pageSize, String schoolId, String branchId, String teacherId, String search, Boolean active);

	/**
	 * Fetching the grade-section based students-count from assign-teacher and
	 * grade-section-mapping tables * assignedGrades or not assigned for principal
	 * and coordinator
	 * 
	 * @param teacherId
	 * @return
	 */
	List<GradeAccessInfoResponseDto> schoolStrength(String teacherId);

	/**
	 * Return the response of teacher access.
	 * 
	 * @param teacherId
	 * @return
	 */
	List<TeacherAccessResponseDto> getAllAssignedAccessOfTeacher(String teacherId);

	/**
	 * This is a feign call for teacher service. Get count of student.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param subjectId
	 * @param gradeId
	 * @param sectionId
	 * @param subtopicId
	 * @return
	 */
	Integer getStudentsCount(String teacherId, String schoolId, String branchId, String subjectId, String gradeId,
			String sectionId, String subtopicId);

	/**
	 * This is a feign call for teacher service to get assigned students
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param subjectId
	 * @param gradeId
	 * @param sectionId
	 * @param subtopicId
	 * @return
	 */
	List<StudentMinResponseDto> getStudentDetails(String teacherId, String schoolId, String branchId, String subjectId,
			String gradeId, String sectionId, String subtopicId);

	/**
	 * Editing teacher access based on grade, subject and section, subTopic. This is
	 * mainlly used for Principal dashboard Teacher Access
	 */
	List<TeacherAssignResponse> updateTeacherAccess(String teacherId, TeacherAssignRequest request);

	/**
	 * This is a feign call for teacher formal assessment service to get student
	 * Details
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	List<StudentFormativeMinResponseDto> getFormativeStudentDetailsFilterBySection(String schoolId, String branchId,
			String gradeId, String sectionId);

	/**
	 * This is a feign call for teacher service to get assigned students
	 *
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param subjectId
	 * @param gradeId
	 * @param sectionId
	 * @param subtopicId
	 * @return
	 */
	List<StudentNameDetails> getStudentNameDetails(String teacherId, String schoolId, String branchId, String subjectId,
			String gradeId, String sectionId, String subtopicId);

	/**
	 * Teacher list for Principal's or coordinator's Dashboard
	 * 
	 * @param gradeId
	 * @return
	 */
	List<AllTypeUserMinResponseDto> getAllTeachersForPrincipalOrCoodinator(String gradeId);

	/**
	 * This is feign call for teacher service for homework assignment
	 * 
	 * @param teacherId
	 */
	TeacherDetailsForFeignResponseDto getTeacherDetailsForFeign(String teacherId);

	/**
	 * feign call for teacher service
	 * 
	 * @param teacherIds
	 * @return
	 */
	List<TeacherDetailsForFeignResponseDto> getAllTeacherDetailsForFeign(List<String> teacherIds);

	/**
	 * Find the id, name and username for subjective paper. Feign call to
	 * content-service
	 * 
	 * @param id
	 * @return
	 */
	UserMinDetails getUserMiniDetails(String id);

	/**
	 * For pagination response in subjective papers. Feign to content-service.
	 * 
	 * @param ids
	 * @return
	 */
	List<UserMinDetails> getAllUserMiniDetails(List<String> ids);
	
	
	public List<String> getteacherIdsListDetails(String schoolId, String gradeId, String sectionId);


/**
 * This api is update the sectionIds
 * @param oldSectionId
 * @param newSectionId
 * @return
 */
Boolean updateSectionIds(String oldSectionId, String newSectionId);

public List<TeacherAssignmentResponse> getTeachersAndAssignedSections(String gradeId, String subjectId);

public TeacherResponseDto getTeacherByUserName(String username);

public List<TeacherResponseDto> getAssignedTeacherList(String gradeId);

public JsonNode getAssignGrades(String teacherId, String menuName);

//public List<String> getTeachersAndAssignedSections();

//public List<String> getTeachersAndAssignedSections(String gradeId, String subjectId);


}
