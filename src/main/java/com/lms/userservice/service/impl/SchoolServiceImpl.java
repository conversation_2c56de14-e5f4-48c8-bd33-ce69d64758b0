package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.BranchPlanMappings;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.content.ContentFeignClient;
import com.lms.userservice.feign.master.BoardsResponseDto;
import com.lms.userservice.feign.master.CitiesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.PlansResponseDto;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.feign.notification.SchoolNotificationResponseDto;
import com.lms.userservice.mapper.BranchMapper;
import com.lms.userservice.mapper.SchoolMapper;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.projection.BranchesProjection;
import com.lms.userservice.projection.SchoolBranchesProjection;
import com.lms.userservice.projection.SchoolsProjection;
import com.lms.userservice.projection.TeacherCountProjection;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.repository.UsersInstitutionMappingRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.request.dto.BranchPlansRequestDto;
import com.lms.userservice.request.dto.SchoolRequestDto;
import com.lms.userservice.response.dto.BranchMinResponseDto;
import com.lms.userservice.response.dto.BranchPlanResponseDto;
import com.lms.userservice.response.dto.BranchResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.CitiesSchoolCountDetailsResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SchoolBranchDetailsResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;
import com.lms.userservice.response.dto.SchoolBranchesResponseDto;
import com.lms.userservice.response.dto.SchoolDetailsResponseDto;
import com.lms.userservice.response.dto.SchoolMinResponseDto;
import com.lms.userservice.response.dto.SchoolResponseDto;
import com.lms.userservice.response.dto.SchoolsBranchsMinResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;
import com.lms.userservice.service.SchoolService;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * The {@code SchoolServiceImpl} implements {@code SchoolService}
 * 
 * <AUTHOR>
 * <AUTHOR> C Achari
 * @since 1.0.0 MongoDb to PostgreSQL
 */
@Slf4j
@Service("schoolService")
public class SchoolServiceImpl implements SchoolService {

	@Autowired
	private JwtUtil jwtUtil;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private ModelMapper modelMapper;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchMapper branchMapper;

	@Autowired
	private SchoolMapper schoolMapper;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private TeacherRepository teacherRepo;

	@Autowired
	private StudentsRepository studentRepository;

	@Autowired
	ContentFeignClient contentFeignClient;

	@Autowired
	private BranchPlanMappingsRepository branchPlanMappingsRepo;

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private UsersInstitutionMappingRepository usersInstitutionMappingRepo;

	@Autowired
	private UsersRoleMappingRepository usersRoleMappingRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	/**
	 * Create a School with or without a Branch by checking the Branch existence
	 * 
	 * @param schoolRequest The School Request DTO
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public SchoolResponseDto createSchool(SchoolRequestDto schoolRequest) {

		log.info("School Registration started");
		if (schoolRepository.existsByNameAndDeleted(schoolRequest.getName(), false))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("school.already.exist", null) + " by this name");

		if (schoolRepository.existsByCodeAndDeleted(schoolRequest.getCode(), false))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("school.already.exist", null) + " by this code");

		if (schoolRepository.existsByPocEmailAndDeleted(schoolRequest.getPocEmail(), false))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("school.already.exist", null) + " by this email");

//		if ((!schoolRequest.isHasBranch()) && (StringUtils.isEmpty(schoolRequest.getLocality())
//				|| schoolRequest.getPlanIds().isEmpty()))
//			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("school.bad.request", null));

		if ((!schoolRequest.isHasBranch()) && (StringUtils.isEmpty(schoolRequest.getLocality())))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("school.bad.request", null));

		if (!schoolRequest.isHasBranch()) {
			schoolRequest.getPlanIds().forEach(plan -> {
				if (branchRepository.findCountOfBranchByPlanId(plan.getPlanId()) > 1)
					throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("branch.plan.already.exist", null));
			});
		}

		if (!StringUtils.isBlank(schoolRequest.getLmsEnv())) {
			if (!Enums.getIfPresent(LMSEnvironment.class, schoolRequest.getLmsEnv()).isPresent())
				throw new USException(ErrorCodes.BAD_REQUEST,
						Translator.toLocale("specify.application.environment", null));
		}

		try {
			SchoolResponseDto schoolResponse = new SchoolResponseDto();
			String currentUser = jwtUtil.currentLoginUser();
			log.info("Mapping School details...");
			Schools schools = modelMapper.map(schoolRequest, Schools.class);
			schools.setCreatedBy(currentUser);
			schools = schoolRepository.save(schools);

			if (!StringUtils.isEmpty(schools.getId()) && !schools.isHasBranch()) {
				for (BranchPlansRequestDto plan : schoolRequest.getPlanIds())
					schools.setPlanId(plan.getPlanId());
				updateExistingOrCreateNewBranchIfNoSchoolBranch(schools, schoolRequest.getPlanIds());
			}

			if (!StringUtils.isEmpty(schools.getId())) {
				log.info("School: " + schoolRequest.getName() + " created successfully!");
				schoolResponse = new SchoolResponseDto(schoolRepository.findSchoolsById(schools.getId()));

				LMSResponse<CitiesResponseDto> cityMasterResponse = mastersFeignClient.getCityById(schools.getCityId());
				if (!ObjectUtils.isEmpty(cityMasterResponse))
					schoolResponse.setCity(cityMasterResponse.getData().getCity());

				// modelMapper.map(schools, SchoolResponseDto.class);
				// setting the boards from branches
				List<BoardsResponseDto> branches = getAllBoardFromBranchBySchoolId(schools.getId());
				schoolResponse.setBoardList(branches);
			}

			if (!StringUtils.isBlank(schoolRequest.getLmsEnv()) && schoolResponse != null) {
				log.info("Sending mail to registered email");
				generateEmailBodyAndSendMail(schoolResponse.getId(), "CREATED", schoolRequest.getLmsEnv());
			}

			log.info("School registration completed");
			return schoolResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("school.create.failed", null));
		}
	}

	/**
	 * Get School Details By ID
	 * 
	 * @param id The School ID
	 * @return School details by ID
	 */
	@Override
	@SuppressWarnings("all")
	public SchoolResponseDto getSchoolById(String id) {
		log.info("Get School details by Id started...");
		if (StringUtils.isEmpty(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("school.id.not.found", null));

		if (!schoolRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		try {
			SchoolResponseDto response = new SchoolResponseDto(schoolRepository.findSchoolsById(id));
			LMSResponse<CitiesResponseDto> citiesLMSResponse = mastersFeignClient.getCityById(response.getCityId());
			CitiesResponseDto cities = !ObjectUtils.isEmpty(citiesLMSResponse) ? citiesLMSResponse.getData() : null;
			response.setCity(cities != null ? cities.getCity() : null);

			response = modelMapper.map(response, SchoolResponseDto.class);

			// setting the boards from branches
			List<BoardsResponseDto> branches = getAllBoardFromBranchBySchoolId(id);
			response.setBoardList(branches);
			log.info("School details fetched successfully");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("school.get.by.id.failed", null));
		}
	}

	/**
	 * <li>Get All Schools by Pagination</li>
	 * 
	 * <li>board is non-mandatory since app version 1.3.1</li>
	 * 
	 * @return Schools list by Pagination
	 */
	@Override
	public PaginatedResponse<SchoolResponseDto> getPageSchools(int pageNumber, int pageSize, String sortBy,
			boolean order, String search, String schoolId, List<String> boardId, Boolean active) {

		log.info("Get all school details started...");
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			log.info("Get All Schools in progress..." + new Date());
			String sortField = FieldMappers.schoolApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			List<SchoolResponseDto> schoolResponseList = new ArrayList<>();
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(order ? Direction.ASC : Direction.DESC, sortField));

			Page<SchoolsProjection> pagedSchools = !CollectionUtils.isEmpty(boardId)
					? schoolRepository.withBoardFindAllSchoolsByPagination(schoolId, boardId, searchFormat, pageable,
							active)
					: schoolRepository.findAllSchoolsByPagination(schoolId, searchFormat, pageable, active);
			log.info("pagedSchools..."  +  pagedSchools.getContent().size());

			// setting the total_elements/pages and response JsonProperties
			if (!pagedSchools.getContent().isEmpty()) {

				totalElements = pagedSchools.getTotalElements();
				totalPages = pagedSchools.getTotalPages();

//				List<String> listCityIds = pagedSchools.getContent().stream().map(SchoolsProjection::getCityId)
//						.distinct().collect(Collectors.toList());
//				LMSResponse<List<CitiesResponseDto>> cityMasterResponse = !CollectionUtils.isEmpty(listCityIds)
//						? mastersFeignClient.getAllCitiesById(listCityIds)
//						: null;
//				List<CitiesResponseDto> cities = !ObjectUtils.isEmpty(cityMasterResponse) ? cityMasterResponse.getData()
//						: null;

				// response JsonProperties
				pagedSchools.getContent().forEach(projection -> {
					SchoolResponseDto response = new SchoolResponseDto(projection);
//					if (!CollectionUtils.isEmpty(cities)) { // setting the cities from fiegn response
//						Optional<CitiesResponseDto> city = cities.stream()
//								.filter(c -> c.getId().equals(response.getCityId())).findAny();
//						if (city.isPresent())
//							response.setCity(city.get().getCity());
//					}

					// finding the assigned board from branches and setting to the response with
					// fiegn call
//					List<String> listBoardIds =  new ArrayList<>();//  branchRepository.findAllBoardFromBranchBySchooldId(projection.getId());
//					listBoardIds.add("2c9180868e53665d018e5ab3268e0002");

					List<BoardsResponseDto> boards = new ArrayList<>();

					BoardsResponseDto board = new BoardsResponseDto();
					board.setId("2c9180868e53665d018e5ab3268e0002");
					board.setBoard("CBSE");
					boards.add(board);
					response.setBoardList(boards);
					//Rajesh temp -- avoid feign calls

//					if (!CollectionUtils.isEmpty(listBoardIds)) {
//						List<BoardsResponseDto> filteredBoards = new ArrayList<>();
//
//						if (!CollectionUtils.isEmpty(boardId)) {
//							for (String id : boardId) {
//								if (listBoardIds.contains(id)) {
//									LMSResponse<BoardsResponseDto> boardMasterResponse = mastersFeignClient
//											.getBoardsById(id);
//									if (boardMasterResponse != null && boardMasterResponse.getData() != null) {
//										BoardsResponseDto board = boardMasterResponse.getData();
//										filteredBoards.add(board);
//									}
//								}
//							}
//						} else {
//							LMSResponse<List<BoardsResponseDto>> boardMasterResponse = mastersFeignClient
//									.getAllBoardById(listBoardIds);
//							List<BoardsResponseDto> boards = !ObjectUtils.isEmpty(boardMasterResponse)
//									? boardMasterResponse.getData()
//									: null;
//							filteredBoards.addAll(boards);
//						}
//						response.setBoardList(!filteredBoards.isEmpty() ? filteredBoards : null);
//					}

					schoolResponseList.add(response);
				});
			}
			log.info("School Pagination list fetched successfully");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1),
					schoolResponseList.size(), schoolResponseList);
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("school.get.all.failed", null));
		}
	}

	/**
	 * Update School
	 * 
	 * @param id            The School ID
	 * @param schoolRequest The School Request DTO
	 * @return
	 */
	@Override
	public SchoolResponseDto updateSchool(String id, SchoolRequestDto schoolRequest) {

		log.info("Updation of School started...");
		if (StringUtils.isEmpty(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("school.id.not.found", null));

		if (!schoolRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if ((!schoolRequest.isHasBranch())
				&& (StringUtils.isEmpty(schoolRequest.getLocality()) || schoolRequest.getPlanIds().isEmpty()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("school.bad.request", null));

		if (!schoolRequest.isHasBranch()) {
			schoolRequest.getPlanIds().forEach(plan -> {
				if (branchRepository.findCountOfBranchByPlanId(plan.getPlanId()) > 1)
					throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("branch.plan.already.exist", null));
			});
		}

		if (schoolRepository.existsByNameAndDeletedAndIdNot(schoolRequest.getName(), false, id))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("school.already.exist", null) + " by this name");

		if (schoolRepository.existsByCodeAndDeletedAndIdNot(schoolRequest.getCode(), false, id))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("school.already.exist", null) + " by this code");

		if (schoolRepository.existsByPocEmailAndDeletedAndIdNot(schoolRequest.getPocEmail(), false, id))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("school.already.exist", null) + " by this email");

		if (!StringUtils.isBlank(schoolRequest.getLmsEnv())) {
			if (!Enums.getIfPresent(LMSEnvironment.class, schoolRequest.getLmsEnv()).isPresent())
				throw new USException(ErrorCodes.BAD_REQUEST,
						Translator.toLocale("specify.application.environment", null));
		}

		try {
			String currentUser = jwtUtil.currentLoginUser();
			Long currentTime = new Date().getTime();

			SchoolResponseDto schoolResponse = null;
			Schools schools = schoolRepository.getById(id);
			modelMapper.map(schoolRequest, schools);
			schools.setModifiedAt(currentTime);
			schools.setLastModifiedBy(currentUser);
			schools = schoolRepository.save(schools);
			schoolResponse = schoolMapper.mapSchoolModelToResponseDto(schools);
			if (!StringUtils.isEmpty(schools.getId())) {
				schoolResponse = new SchoolResponseDto(schoolRepository.findSchoolsById(schools.getId()));
				LMSResponse<CitiesResponseDto> cityMasterResponse = mastersFeignClient.getCityById(schools.getCityId());
				CitiesResponseDto cities = !ObjectUtils.isEmpty(cityMasterResponse) ? cityMasterResponse.getData()
						: null;
				schoolResponse.setCity(!StringUtils.isEmpty(cities.getCity()) ? cities.getCity() : null);
			}
			if (!StringUtils.isEmpty(schools.getId()) && !schools.isHasBranch()) {
				for (BranchPlansRequestDto plan : schoolRequest.getPlanIds())
					schools.setPlanId(plan.getPlanId());
				updateExistingOrCreateNewBranchIfNoSchoolBranch(schools, schoolRequest.getPlanIds());
			}
			// setting the boards from branches
			List<BoardsResponseDto> branches = getAllBoardFromBranchBySchoolId(schools.getId());
			schoolResponse.setBoardList(branches);

			if (!StringUtils.isBlank(schoolRequest.getLmsEnv())) {
				log.info("Sending mail to registered email, after update");
				generateEmailBodyAndSendMail(schoolResponse.getId(), "UPDATED", schoolRequest.getLmsEnv());
			}

			log.info("School details updated successfully!");
			return schoolResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("school.update.failed", null));
		}
	}

	/**
	 * Mark School as Deleted
	 * 
	 * @param id The School ID
	 * @return boolean
	 */
	@Override
	public Boolean deleteSchoolById(String id) {
		if (!schoolRepository.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			Long currentTime = new Date().getTime();

			Schools schools = fetchSchoolById(id);
			schools.setModifiedAt(currentTime);
			schools.setActive(false);
			schools.setDeleted(true);
			schools.setLastModifiedBy(currentUser);
			schools = schoolRepository.save(schools);
			return schools.isDeleted();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("school.delete.failed", null));
		}
	}

	/**
	 * Fetch School by ID
	 * 
	 * @param id The School ID
	 * @return School Details by School ID
	 * @throws Exception
	 */
	public Schools fetchSchoolById(String id) {
		return schoolRepository.findById(id)
				.orElseThrow(() -> new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null)));
	}

	@Override
	public UsersCountResponseDto getAllCounts(String schoolId, List<String> boardIds, boolean administrator,
			String userName) {
		try {
			if (administrator && StringUtils.isBlank(userName))
				throw new USException(ErrorCodes.BAD_REQUEST,
						Translator.toLocale("school.count.username.mandatory", null));

			TeacherCountProjection teacherProjection = null;
			UsersCountResponseDto response = null;
			long schoolCount = 0;
			long studentCount = 0;
			long branchCount = 0;

			if (administrator) {
				log.info("Count for School-Admin or Management");
				teacherProjection = !CollectionUtils.isEmpty(boardIds)
						? teacherRepo.findTeacherCountsWithBoardForAdministrator(schoolId, boardIds, userName)
						: teacherRepo.findTeacherCountsForAdministrator(schoolId, userName);
				response = modelMapper.map(teacherProjection, UsersCountResponseDto.class);

				schoolCount = !CollectionUtils.isEmpty(boardIds)
						? schoolRepository.findSchoolCountWithBoardForAdministrator(schoolId, boardIds, userName)
						: schoolRepository.findSchoolCountForAdministrator(schoolId, userName);

				branchCount = !CollectionUtils.isEmpty(boardIds)
						? branchRepository.findBranchCountWithBoardForAdministrator(schoolId, boardIds, userName)
						: branchRepository.findBranchCountForAdministrator(schoolId, userName);

				studentCount = !CollectionUtils.isEmpty(boardIds)
						? studentRepository.findStudentCountWithBoardForAdministrator(schoolId, boardIds, userName)
						: studentRepository.findStudentCountForAdministrator(schoolId, userName);

			} else {
				log.info("Count for Super-Admin");

				teacherProjection = !CollectionUtils.isEmpty(boardIds)
						? teacherRepo.getTeacherCountsWithBoard(schoolId, boardIds)
						: teacherRepo.getTeacherCounts(schoolId);
				response = modelMapper.map(teacherProjection, UsersCountResponseDto.class);

				schoolCount = !CollectionUtils.isEmpty(boardIds)
						? schoolRepository.getSchoolCountWithBoard(schoolId, boardIds)
						: schoolRepository.getSchoolCount(schoolId);

				studentCount = !CollectionUtils.isEmpty(boardIds)
						? studentRepository.getStudentCountWithBoard(schoolId, boardIds)
						: studentRepository.getStudentCount(schoolId);

				branchCount = !CollectionUtils.isEmpty(boardIds)
						? branchRepository.getBranchCountWithBoard(schoolId, boardIds)
						: branchRepository.getBranchCount(schoolId);
			}
			response.setSchoolCount(schoolCount);
			response.setStudentCount(studentCount);
			response.setBranchCount(branchCount);
			return response;
		} catch (USException e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("counts.failed", null));
		}
	}

	@Override
	public List<SchoolMinResponseDto> getAllSchools(String userId, String search) {
		try {
			List<SchoolMinResponseDto> responseList = new ArrayList<>();
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;
			List<SchoolsProjection> projectionList = !StringUtils.isEmpty(userId)
					? schoolRepository.getSchoolsForSchoolAdmin(userId, searchFormat)
					: schoolRepository.getSchools(searchFormat);
			projectionList.forEach(projection -> responseList
					.add(new SchoolMinResponseDto(projection.getId(), projection.getName(), projection.getActive())));
			return responseList;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("school.get.all.failed", null));
		}

	}

	@Override
	public List<SchoolBranchesResponseDto> getAllSchoolsWithBranches(String search) {

		try {
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			List<SchoolBranchesProjection> projectionList = schoolRepository.getSchooWithlBranches(searchFormat);

			Map<String, SchoolBranchesResponseDto> projectionMap = projectionList.stream()
					.collect(Collectors.toMap(SchoolBranchesProjection::getSchoolId,
							projection -> schoolMapper.mapSchoolBranchesProjectionToDto(projection), (s1, s2) -> s1));

			projectionList.forEach(projection -> {
				SchoolBranchesResponseDto responseDto = projectionMap.get(projection.getSchoolId());
				BranchesMinDataResponseDto branchesResponse = new BranchesMinDataResponseDto();
				branchesResponse.setBranchId(projection.getBranchId());
				branchesResponse.setBranch(projection.getBranchName());
				branchesResponse.setActive(projection.getBranchActive());
				if (responseDto.getBranches().isEmpty()) {
					responseDto.setBranches(new ArrayList<>(Arrays.asList(branchesResponse)));
				} else {
					responseDto.getBranches().add(branchesResponse);
				}

			});

			return new ArrayList<>(projectionMap.values());
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("school.get.all.failed", null));
		}
	}

	/**
	 * Update existing School with no Branch/create new Branch if School has Branch
	 * 
	 * @param schools The Schools Entity Object
	 * @return Branch Response DTO
	 */
	public BranchResponseDto updateExistingOrCreateNewBranchIfNoSchoolBranch(Schools schools,
			List<BranchPlansRequestDto> plans) {
		try {
			boolean branchExist = false;
			String currentUser = jwtUtil.currentLoginUser();
			Long currentTime = new Date().getTime();

			Branches branches = null;
			if (!schools.isHasBranch()) {
				String schoolId = schools.getId();
				branchExist = branchRepository.existsBySchoolsIdAndDeleted(schoolId, false);
				log.info("branch exist: " + branchExist);
				if (branchExist) {
					log.info("inside Branch exist true");
					branches = branchRepository.findBySchoolsAndDeleted(schools, false);
					branches = branchMapper.mapFromSchoolsToBranches(schools, branches);
					branches.setModifiedAt(currentTime);
					branches.setLastModifiedBy(currentUser);
					branches = branchRepository.save(branches);
					if (!StringUtils.isEmpty(branches.getId())) {
						log.info("Branch has been updated");
					}
				} else {
					log.info("Mapping School DTO to Branches...");
					branches = new Branches();
					branches = branchMapper.mapFromSchoolsToBranches(schools, branches);
					branches.setSchools(schools);
					branches.setCreatedBy(currentUser);
					branches = branchRepository.save(branches);
					if (!StringUtils.isEmpty(branches.getId())) {
						log.info("Branch is created");
					}
				}
			}
			setBranchPlansFromDto(plans, branches);
			return branchMapper.mapBranchModelToResponseDto(branches);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("branch.update.failed", null));
		}
	}

	/**
	 * Set Branch Plans
	 *
	 * @param plans    The branch plans request DTO list
	 * @param branches The Branches entity
	 * @return
	 */
	private List<BranchPlanResponseDto> setBranchPlansFromDto(List<BranchPlansRequestDto> plans, Branches branches) {

		List<BranchPlanResponseDto> response = new ArrayList<>();
		if (!branchPlanMappingsRepo.findByBranches(branches).isEmpty())
			branchPlanMappingsRepo.deleteByBranches(branches);

		plans.forEach(plan -> {
			BranchPlanMappings mapping = modelMapper.map(plan, BranchPlanMappings.class);
			mapping.setBranches(branches);
			mapping = branchPlanMappingsRepo.save(mapping);
			response.add(modelMapper.map(mapping, BranchPlanResponseDto.class));
		});
		return response;
	}

	@Override
	public Boolean updateActiveField(String id, boolean active) {
		if (!schoolRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			Schools school = schoolRepository.getById(id);
			if (!active) {
				// Check if any child branches are active
				long activeBranchCount = branchRepository.getBranchCount(id);
				if (activeBranchCount > 0)
					throw new USException(ErrorCodes.INVALID_OPERATION,
							Translator.toLocale("school.deactivate.has.active.branches", null));
			}
			school.setActive(active);
			school.setModifiedAt(currentTime);
			school.setLastModifiedBy(jwtUtil.currentLoginUser());
			school = schoolRepository.save(school);
			return school.isActive() == active;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("update.active.failed", null));
		}
	}

	/**
	 * Return the mapping tables related to the school, if the school's column
	 * has_branch==false return the default message. Or check the count, if the
	 * count==0 return the default message or show the school has mapping as in a
	 * message format.
	 * 
	 * currently mapping entities :: branch, grade_section_mapping, student,
	 * teacher, token, users_institution_mapping
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType) {
		if (!schoolRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		try {
			boolean isExits = false;
			String message = "There is no mapping related to this school currently.";
			Schools schools = schoolRepository.getById(id);
			if (!schools.isHasBranch()) {
				return message;
			} else {
				List<Long> mappingCounts = schoolRepository.findAllMappingCountOfSchool(id);
				if (!mappingCounts.isEmpty()) {
					isExits = mappingCounts.stream().anyMatch(count -> count > 0);
					if (isExits) {
						if (OperationType.DELETE == OperationType.valueOf(operationType))
							message = "Some data are connected with this school. Deleting may cause some loss of data. "
									+ "Do you still want to delete the School, " + schools.getName();
						else
							message = "Some data are connected with this school. "
									+ "Changing the active status may cause some constrain violation issues. "
									+ "Do you still want to change the active status of the School, "
									+ schools.getName();
					} else
						return message;
				}
			}
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	/**
	 * Confirm School and Branch Mapped data by Board Id before delete
	 * 
	 * @param boardId
	 * @return
	 */
	@Override
	public Boolean getCountOfSchoolAndBranchByBoardId(String boardId) {
		try {
			Boolean response = false;
			List<Long> countList = schoolRepository.findCountOfSchoolAndBranchByBoardId(boardId);
			response = countList.stream().anyMatch(boardCount -> boardCount > 0);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("school.branch.mapped.with.board.failed", null));
		}
	}

	/**
	 * Confirm School and Branch data by city Id before delete
	 * 
	 * @param cityId
	 * @return
	 */
	@Override
	public Boolean getCountOfSchoolAndBranchByCityId(String cityId) {
		try {
			Boolean response = false;
			List<Long> countList = schoolRepository.findCountOfSchoolAndBranchByCityId(cityId);
			response = countList.stream().anyMatch(cityCount -> cityCount > 0);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("school.branch.mapped.with.city.failed", null));
		}
	}

	/**
	 * Get the last modification done on the table schools return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return schoolRepository.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	/**
	 * Listing all the board from the related branches of a school
	 * 
	 * @param schoolId
	 * @return
	 */
	private List<BoardsResponseDto> getAllBoardFromBranchBySchoolId(String schoolId) {
		try {
			List<BoardsResponseDto> branches = new ArrayList<>();
			List<String> branchBoardList = branchRepository.findAllBoardFromBranchBySchooldId(schoolId);
			if (!CollectionUtils.isEmpty(branchBoardList)) {
				log.info("School has branch and displaying it to the schools");
				branches = mastersFeignClient.getAllBoardById(branchBoardList).getData();
			}
			return branches;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("school.branch.board.not.found", null));
		}
	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto confirmationApiForSchool(String id, String operationType) {
		if (!schoolRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			boolean isExits = false;
			String message = Translator.toLocale("confirmation.api.default.message", null);
			List<Long> usages = schoolRepository.findUsages(id);
			usages.addAll(contentFeignClient.schoolIds(id).getData());
			if (!usages.isEmpty()) {
				isExits = usages.stream().anyMatch(count -> count > 0);
				if (isExits)
					message = Translator.toLocale("confirmation.api.permission.denied", null);
				else {
					message = (OperationType.DELETE == OperationType.valueOf(operationType))
							? Translator.toLocale("confirmation.api.delete", null)
							: Translator.toLocale("confirmation.api.toggle.active", null);
				}
			}
			return new ConfirmationApiResponseDto(isExits, message);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("confirmation.api.failed", null));
		}
	}

	/**
	 * This feign call for content service to get schoolName and branchName
	 * 
	 * @param schoolId
	 * @param branchId
	 * @return
	 */
	@Override
	public SchoolBranchResponseDto getSchoolAndBranchById(String schoolId, String branchId) {
		try {
			SchoolBranchResponseDto response = new SchoolBranchResponseDto();
			if (branchId != null && !branchId.isEmpty()) {
				String school = schoolRepository.findSchoolNameById(schoolId);
				if (school != null) {
					response.setSchoolId(schoolId);
					response.setSchool(school);
				}
			}
			if (branchId != null && !branchId.isEmpty()) {
				String branch = branchRepository.findBranchNameById(branchId);
				if (branch != null) {
					response.setBranchId(branchId);
					response.setBranch(branch);
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("school.branch.get.by.id.failed", null));
		}
	}

	/**
	 * Feign call to master-service for Dash board, get schools by city
	 *
	 */
	@Override
	public List<CitiesSchoolCountDetailsResponseDto> getCitiesSchoolDetails(List<String> cityIds) {
		try {
			List<CitiesSchoolCountDetailsResponseDto> response = new ArrayList<>();
			// master feign call for city name
			LMSResponse<List<CitiesResponseDto>> masterLmsResponse = cityIds != null
					? mastersFeignClient.getAllCitiesById(cityIds)
					: null;
			List<CitiesResponseDto> cities = masterLmsResponse != null ? masterLmsResponse.getData() : null;

			if (!CollectionUtils.isEmpty(cityIds)) {
				for (String cityId : cityIds) {
					List<BranchesProjection> projection = schoolRepository.getSchoolDetails(cityId);
					if (!CollectionUtils.isEmpty(projection)) {

						List<SchoolDetailsResponseDto> schoolResponse = projection.stream()
								.map(SchoolDetailsResponseDto::new).collect(Collectors.toList());
						if (!CollectionUtils.isEmpty(schoolResponse)) {
							if (!CollectionUtils.isEmpty(cities)) {
								schoolResponse.forEach(item -> {
									CitiesResponseDto city = cities.stream()
											.filter(cDto -> cDto.getId().equals(item.getCityId())).findFirst()
											.orElse(null);
									item.setLocality(
											city != null && !StringUtils.isBlank(city.getCity()) ? city.getCity()
													: null);
								});
							}
							response.add(new CitiesSchoolCountDetailsResponseDto(cityId, schoolResponse.size(),
									schoolResponse));
						}
					}
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("school.details.failed", null));
		}
	}

	/**
	 * This API is used for management/super_admin/school_admin dash_board for
	 * getting school's branches details based on user name
	 * 
	 * @return
	 *
	 */
	@Override
	public PaginatedResponse<SchoolBranchDetailsResponseDto> getAllSchoolDetailsWithUserName(int pageNumber,
			int pageSize, String sortBy, boolean order, String search, String schoolId, Boolean active) {
		try {

			log.info("Get All Schools in progress...");

			Long totalElements = 0L;
			int totalPages = 0;
			String currentUser = jwtUtil.currentLoginUser();
			Users users = usersRepository.findByUserName(currentUser);

			List<SchoolBranchDetailsResponseDto> response = new ArrayList<>();

			String sortField = FieldMappers.schoolApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(order ? Direction.ASC : Direction.DESC, sortField));
			Page<SchoolsProjection> projection = null;

			// find out the current user has any entry in user-institution-mapping
			List<SchoolsBranchsMinResponseDto> uimResponse = null;
			if (usersInstitutionMappingRepo.checkingUserIdExists(users.getId())) {
				uimResponse = usersInstitutionMappingRepo.findSchoolBranchsForAdmin(users.getId());
			} else {
				if (!users.isAdminUser())
					throw new USException(ErrorCodes.UNAUTHORIZED,
							Translator.toLocale("api.is.accessible.for.admin.user", null));
			}

			// School listing started by pagination.
			if (!CollectionUtils.isEmpty(uimResponse)) {
				// finding details for school-admin or management.
				List<String> schoolIds = uimResponse.stream().map(SchoolsBranchsMinResponseDto::getSchoolId).distinct()
						.collect(Collectors.toList());
				projection = schoolRepository.findSchoolsForAdministration(schoolIds, searchFormat, active, pageable);
			} else
				// finding details for Super-Admin
				projection = schoolRepository.findSchoolsForAdminUsers(schoolId, searchFormat, active, pageable);

			// traversing through the projection
			if (projection != null && !CollectionUtils.isEmpty(projection.getContent())) {
				totalElements = projection.getTotalElements();
				totalPages = projection.getTotalPages();
				List<SchoolBranchDetailsResponseDto> schoolOverview = projection.getContent().stream()
						.map(SchoolBranchDetailsResponseDto::new).collect(Collectors.toList());

				// find out the branches and setting the details
				List<String> branchIds = null;
				if (!CollectionUtils.isEmpty(uimResponse))
					branchIds = uimResponse.stream().map(SchoolsBranchsMinResponseDto::getBranchId).distinct()
							.collect(Collectors.toList());
				else {
					List<String> schoolIds = projection.getContent().stream().map(SchoolsProjection::getId).distinct()
							.collect(Collectors.toList());
					branchIds = branchRepository.findAllIdsBySchoolIds(schoolIds);
				}

				List<BranchesProjection> branchProjections = !CollectionUtils.isEmpty(branchIds)
						? branchRepository.findAllForSchoolOverview(branchIds)
						: null;

				// traverse through the branch to set the branch details.
				List<BranchMinResponseDto> branches = null;
				if (!CollectionUtils.isEmpty(branchProjections)) {
					branches = branchProjections.stream().map(BranchMinResponseDto::new).collect(Collectors.toList());

					List<String> boardIds = branchProjections.stream()
							.filter(item -> !StringUtils.isBlank(item.getBoardId())).map(BranchesProjection::getBoardId)
							.distinct().collect(Collectors.toList());
					LMSResponse<List<BoardsResponseDto>> boardLMSResponse = !CollectionUtils.isEmpty(boardIds)
							? mastersFeignClient.getAllBoardById(boardIds)
							: null;
					List<BoardsResponseDto> boards = boardLMSResponse != null
							&& !CollectionUtils.isEmpty(boardLMSResponse.getData()) ? boardLMSResponse.getData() : null;

					List<String> cityIds = branchProjections.stream()
							.filter(item -> !StringUtils.isBlank(item.getCityId())).map(BranchesProjection::getCityId)
							.distinct().collect(Collectors.toList());
					LMSResponse<List<CitiesResponseDto>> citiLMSResponse = !CollectionUtils.isEmpty(cityIds)
							? mastersFeignClient.getAllCitiesById(cityIds)
							: null;
					List<CitiesResponseDto> cities = citiLMSResponse != null
							&& !CollectionUtils.isEmpty(citiLMSResponse.getData()) ? citiLMSResponse.getData() : null;

					List<String> planIds = branchProjections.stream()
							.filter(item -> !StringUtils.isBlank(item.getPlanId())).map(BranchesProjection::getPlanId)
							.distinct().collect(Collectors.toList());
					LMSResponse<List<PlansResponseDto>> planLMSResponse = !CollectionUtils.isEmpty(planIds)
							? mastersFeignClient.getAllPlansByIds(planIds)
							: null;
					List<PlansResponseDto> plans = planLMSResponse != null
							&& !CollectionUtils.isEmpty(planLMSResponse.getData()) ? planLMSResponse.getData() : null;

					if (!CollectionUtils.isEmpty(branches)) {
						branches.forEach(item -> {
							// setting board name
							if (!CollectionUtils.isEmpty(boards)) {
								BoardsResponseDto board = boards.stream()
										.filter(bDto -> bDto.getId().equals(item.getBoardId())).findFirst()
										.orElse(null);
								item.setBoard(board != null && !StringUtils.isBlank(board.getBoard()) ? board.getBoard()
										: null);
							}

							// setting city name
							if (!CollectionUtils.isEmpty(cities)) {
								CitiesResponseDto city = cities.stream()
										.filter(cDto -> cDto.getId().equals(item.getCityId())).findFirst().orElse(null);
								item.setCityName(
										city != null && !StringUtils.isBlank(city.getCity()) ? city.getCity() : null);
							}

							// setting plan name
							if (!CollectionUtils.isEmpty(plans)) {
								PlansResponseDto plan = plans.stream()
										.filter(pDto -> pDto.getId().equals(item.getPlanId())).findFirst().orElse(null);
								item.setPlan(
										plan != null && !StringUtils.isBlank(plan.getPlan()) ? plan.getPlan() : null);
								item.setGradeCount(
										plan != null && plan.getGradeCount() != null ? plan.getGradeCount() : 0);
							}
						});
					}
				}

				// Setting the branch and branch count.
				for (SchoolBranchDetailsResponseDto schoolItem : schoolOverview) {
					if (!CollectionUtils.isEmpty(branches)) {
						List<BranchMinResponseDto> innerResopnse = branches.stream()
								.filter(bItem -> bItem.getSchoolId().equals(schoolItem.getSchoolId())).distinct()
								.collect(Collectors.toList());
						schoolItem.setBranches(innerResopnse);
						schoolItem.setBranchCount(!CollectionUtils.isEmpty(innerResopnse) ? innerResopnse.size() : 0);
					} else
						schoolItem.setBranchCount(0);
				}

				// sorting based on the branch count in descending order
				response = schoolOverview.stream()
						.sorted(Comparator.comparing(SchoolBranchDetailsResponseDto::getBranchCount).reversed())
						.collect(Collectors.toList());

			}
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("school.branch.overview.failed", null));
		}
	}

	/**
	 * By plan find the school/branches to content-service.
	 * 
	 * @param planIds
	 * @return
	 */
	@Override
	public List<SchoolBranchResponseDto> schoolAndBranchForAssignAssessment(List<String> planIds) {
		try {
			return branchRepository.findForAssignAssessments(planIds);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("institutions.for.assign.assessments.failed", null));
		}
	}

	/**
	 * Get the school/branch name for the assessment papers(if the assessment paper
	 * has assigned to any school/branch). Fiegn to content-service
	 * 
	 * @param schoolIds
	 * @param branchIds
	 * @return
	 */
	@Override
	public List<SchoolBranchResponseDto> assignedInstitutesDetails(List<String> schoolIds, List<String> branchIds) {
		try {
			return branchRepository.findInstituteDetailsForAssessmentPaper(schoolIds, branchIds);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("institutions.for.assign.assessments.failed", null));
		}
	}

	/**
	 * Send the mail to after create or update the school details
	 * 
	 * @param id
	 * @param operationType
	 * @param lmsEnv
	 */
	public void generateEmailBodyAndSendMail(String id, String operationType, String lmsEnv) {
		try {
			SchoolsProjection projection = schoolRepository.findDetailsForSendMail(id);
			if (projection != null) {
				LMSResponse<String> configByKey = mastersFeignClient.getValueByKey("FE_BASE_URL_" + lmsEnv);
				String baseUrl = configByKey != null && !StringUtils.isBlank(configByKey.getData())
						? configByKey.getData()
						: null;
				SchoolNotificationResponseDto schoolNotification = new SchoolNotificationResponseDto(projection);
				schoolNotification.setTypeOfOperation(operationType);
				schoolNotification.setBaseFEUrl(baseUrl);

				// get role and name of created/modified person
				Users user = usersRepository.findByUserName(schoolNotification.getCreateOrModifiedBy());
				if (user != null) {
					List<String> roles = usersRoleMappingRepository.findAllRolesByUserId(user.getId());
					List<RolesFeignDto> currentUserRoles = mastersFeignClient.getRolesByIds(roles).getData();
					schoolNotification.setRoleNameOfAdmin(currentUserRoles.get(0).getRole());
					schoolNotification.setAdminName(user.getFirstName() + " " + user.getLastName());
				}
				notificationFeignClient.afterSchoolCreatedOrEdited(schoolNotification);
			}
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INVALID_OPERATION,
					Translator.toLocale("email.send.for.school.faild", null));
		}
	}
}