package com.lms.userservice.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.userservice.assignedTeacher.ChapterTrackingWithQuizResponseDto;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.lms.userservice.assignedTeacher.AssignedTeacherResponseDto;
import com.lms.userservice.assignedTeacher.ChapterTrackingResponseDto;
import com.lms.userservice.assignedTeacher.PaginatedResponse;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Teachers;
import com.lms.userservice.enums.AcademicStaffProfile;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.StudentEncourageMessages;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.content.ContentFeignClient;
import com.lms.userservice.feign.content.PrincipalSubjectWisePerformanceResponseDto;
import com.lms.userservice.feign.content.QuizAttemptAverageResponseDto;
import com.lms.userservice.feign.content.QuizChapterTotalMarksResponseDto;
import com.lms.userservice.feign.content.QuizTotalMarkMinResponseDto;
import com.lms.userservice.feign.content.QuizTotalMarksMinResponseDto;
import com.lms.userservice.feign.content.ReleasedUnitPracticeQuizResponseDto;
import com.lms.userservice.feign.content.TeacherFormativeAssessmentsResponseDto;
import com.lms.userservice.feign.content.TeacherReportQuizResponseDto;
import com.lms.userservice.feign.master.AcademicYearResponseDto;
import com.lms.userservice.feign.master.BoardsResponseDto;
import com.lms.userservice.feign.master.ChapterFeignResponseDto;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.feign.master.SubjectsResponseDto;
import com.lms.userservice.feign.student.QuizReleaseObtainedMarksResponseDto;
import com.lms.userservice.feign.student.ScoreRangeResponseDto;
import com.lms.userservice.feign.student.SectionObtainedMarksResponseDto;
import com.lms.userservice.feign.student.StudentFeignClient;
import com.lms.userservice.feign.student.StudentLevelMinResponseDto;
import com.lms.userservice.feign.student.TeacherReportStudentQuizResponseDto;
import com.lms.userservice.feign.student.UnitPracticeQuizPerformanceMinResponseDto;
import com.lms.userservice.feign.student.UnitQuizPerformanceMinResponseDto;
import com.lms.userservice.feign.teacher.ChapterEndedResponseDto;
import com.lms.userservice.feign.teacher.TeacherFeignClient;
import com.lms.userservice.model.ChapterQuizModel;
import com.lms.userservice.model.GradeWiseCover;
import com.lms.userservice.model.InstituteQuizzesModel;
import com.lms.userservice.model.InstitutionList;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.StudentsWithQuizzes;
import com.lms.userservice.model.SubjectWisePrincipalCoordinator;
import com.lms.userservice.model.SubjectsSubtopicUnderGradeModel;
import com.lms.userservice.model.TeacherGlobalAvgGradeWise;
import com.lms.userservice.projection.DashboardProjection;
import com.lms.userservice.projection.PrincipalCountProjection;
import com.lms.userservice.projection.TeacherAssignProjection;
import com.lms.userservice.repository.AssignTeacherRepository;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.GradeSectionMappingRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.response.dto.AllTypeUserMinResponseDto;
import com.lms.userservice.response.dto.AttemRateAndPercentageResponseDto;
import com.lms.userservice.response.dto.AverageResponseDto;
import com.lms.userservice.response.dto.BlueprintLevelResponse;
import com.lms.userservice.response.dto.BranchSchoolNamesResponseDto;
import com.lms.userservice.response.dto.ChapterEndedBySectionResponseDto;
import com.lms.userservice.response.dto.ChapterScoreResponseDto;
import com.lms.userservice.response.dto.ChaptersVSQuizzesRelasesBySectionResponseDto;
import com.lms.userservice.response.dto.ChaptersVSQuizzesRelasesResponseDto;
import com.lms.userservice.response.dto.DashboardResponseDto;
import com.lms.userservice.response.dto.DashboardUnitQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.EmberStudentsResponseDto;
import com.lms.userservice.response.dto.GradeAccessInfoStudentsDetailsResponseDto;
import com.lms.userservice.response.dto.GradeAccessStudentsDetailsResponseDto;
import com.lms.userservice.response.dto.GradeSectionSubjectsResponseDto;
import com.lms.userservice.response.dto.GradeSubjectScoreResponseDto;
import com.lms.userservice.response.dto.GradeWisePerformanceResponseDto;
import com.lms.userservice.response.dto.GradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.InstitutionStudentCountResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.PlansResponseDto;
import com.lms.userservice.response.dto.PrincipalGradeDetailedPerformanceResponseDto;
import com.lms.userservice.response.dto.PrincipalGradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.QuestionWisePerformanceResponseDto;
import com.lms.userservice.response.dto.QuizAttemptUQPQResponseDto;
import com.lms.userservice.response.dto.SectionAccessStudentsDetailsResponseDto;
import com.lms.userservice.response.dto.SectionSubjectScoreResponseDto;
import com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto;
import com.lms.userservice.response.dto.StudentLevelResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.SubTopicBulkInnerResponseDto;
import com.lms.userservice.response.dto.SubTopicWisePerformanceResponseDto;
import com.lms.userservice.response.dto.SubTopicsMinResponseDto;
import com.lms.userservice.response.dto.SubjectScoreResponseDto;
import com.lms.userservice.response.dto.SubjectWiseGradePerformanceResponseDto;
import com.lms.userservice.response.dto.SubjectWisePerformanceResponseDto;
import com.lms.userservice.response.dto.SubjectsMinResponseDto;
import com.lms.userservice.response.dto.SubtopicScoreResponseDto;
import com.lms.userservice.response.dto.SyllabusGradeAccessResponseDto;
import com.lms.userservice.response.dto.SyllabusSectionAccessResponseDto;
import com.lms.userservice.response.dto.TeacherFormativeAssessmentResponseDto;
import com.lms.userservice.response.dto.TeacherGradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.TeacherReportQuizOverviewCardResponseDto;
import com.lms.userservice.response.dto.TotalVsComplatedResponseDto;
import com.lms.userservice.service.DashboardService;
import com.lms.userservice.service.StudentsService;
import com.lms.userservice.service.TeacherService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.DateUtilities;
import com.lms.userservice.util.JwtUtil;
import com.lms.userservice.util.MathUtilitiess;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Service
@Slf4j
public class DashboardServiceImpl implements DashboardService {

	@Autowired
	private TeacherRepository teacherRepo;

	@Autowired
	private SchoolRepository schoolRepo;

	@Autowired
	private BranchRepository branchRepo;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private AssignTeacherRepository assignTeacherRepo;

	@Autowired
	private StudentsRepository studentsRepo;

	@Autowired
	private TeacherFeignClient teacherFeignClient;

	@Autowired
	private ContentFeignClient contentFeignClient;

	@Autowired
	private GradeSectionMappingRepository gradeSectionMappingRepository;

	@Autowired
	private StudentFeignClient studentFeignClient;

	@Autowired
	private JwtUtil jwtUtil;
	
	@Autowired
	private BranchPlanMappingsRepository branchPlanMappingsRepo;
	
	@Autowired
	private ContentFeignClient contentFeign;
	
	@Autowired
	private StudentsService studentsService;
	
	@Autowired
	private TeacherService teacherService;

	/**
	 * This Api is used for counting student, section, grade, teacher, coordinator,
	 * subject and subtopic.
	 */
	@Override
	public DashboardResponseDto countToDashboard(String teacherId, String schoolId, String branchId, String gradeId,
			String subjectId, String subtopicId) {
		log.info("Dashboard count Details fetch started...");
		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		try {
			DashboardResponseDto response = null;
			Teachers teachers = teacherRepo.getById(teacherId);
			if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
				// Counting section and student for teacher dashboard.
				List<String> sectionList = teacherRepo.findSectionIdListByFilters(teacherId, schoolId, branchId,
						gradeId, subjectId, subtopicId);
				long studentCount = 0;
				long sectionCount = 0;
				if (!CollectionUtils.isEmpty(sectionList) && sectionList.get(0) != null) {
					studentCount = teacherRepo.getStudentCountWithSectionList(schoolId, branchId, gradeId, sectionList);
					sectionCount = Long.valueOf(sectionList.size());

				} else {
					studentCount = teacherRepo.getStudentCount(schoolId, branchId, gradeId, subjectId, subtopicId);
					// response.setStudentCount(studentCount);
					// return response;
				}

				response = new DashboardResponseDto(sectionCount, studentCount);

			} else if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR) {
				// Counting section and student for coordinator and list the subject/subtopic.
				List<String> sectionList = assignTeacherRepo.getSectionBySubjectAndGrade(schoolId, branchId, gradeId,
						subjectId, subtopicId);
				long studentCount = 0;
				long sectionCount = 0;
				if (!CollectionUtils.isEmpty(sectionList)) {
					studentCount = studentsRepo.countOfStudentbasedOnGradeAndSections(schoolId, branchId, gradeId,
							sectionList);
					sectionCount = Long.valueOf(sectionList.size());
				} else {
					studentCount = studentsRepo.countOfStudentbasedOnGrade(schoolId, branchId, gradeId);
					// response.setStudentCount(studentCount);
					// return response;
				}

				response = new DashboardResponseDto(sectionCount, studentCount);

				List<String> subjectIdList = assignTeacherRepo.getAllSubjectIdsByFilters(teacherId, gradeId, null);
				if (!CollectionUtils.isEmpty(subjectIdList)) {
					LMSResponse<List<SubjectsMinResponseDto>> mSubjectResponse = mastersFeignClient
							.getAllSubjectsByIds(subjectIdList);
					if (mSubjectResponse != null) {
						response.setAssignedSubjects(mSubjectResponse.getData());
					}
				}
				return response;
			} else {
				// Counting grade, student, teacher and coordinator for principal dashboard.
				DashboardProjection projection = teacherRepo.getGradeStudentTeacherCoordinatorCount(teacherId, schoolId,
						branchId);
				response = new DashboardResponseDto(projection);
				return response;
			}
			log.info("Dashboard count Details fetch successfully...");
			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("fetch.teacher.grade.section.student.count.failed", null));
		}
	}

	/**
	 * This Api is used for coordinator counting chapters and quizzes, subject .
	 */
	@Override
	public List<TotalVsComplatedResponseDto> countForChapterQuizzes(String teacherId, String boardId, String schoolId,
			String branchId, String gradeId, String subjectId, String subTopicId) {
		log.info("Dashboard Coordinator count Details fetch started...");
		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		try {
			List<TotalVsComplatedResponseDto> response = new ArrayList<>();
			Teachers teachers = teacherRepo.getById(teacherId);

			if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				TotalVsComplatedResponseDto dashboardResponseDto = null;
				TotalVsComplatedResponseDto dashboardResponse = null;

				LMSResponse<Integer> totalChapterCount = mastersFeignClient.chapterCountForCoordinator(boardId, gradeId,
						subjectId, subTopicId);
				if (!ObjectUtils.isEmpty(totalChapterCount) && totalChapterCount != null) {
					dashboardResponseDto = new TotalVsComplatedResponseDto();
					dashboardResponseDto.setMainHeading("Chapters vs Completed");
					dashboardResponseDto.setSubHeadingOne("Total Chapters");
					dashboardResponseDto.setTotalCount(totalChapterCount.getData());
				}
				LMSResponse<Integer> endedChapterCount = teacherFeignClient.endedChapterCount(boardId, schoolId,
						branchId, gradeId, subjectId, subTopicId);
				if (!ObjectUtils.isEmpty(endedChapterCount)) {
					dashboardResponseDto.setSubHeadingTwo("Completed Chapters");
					dashboardResponseDto.setCompletedCount(endedChapterCount.getData());
					response.add(dashboardResponseDto);
				}

				LMSResponse<Integer> totalQuizzes = contentFeignClient.totalQuizCount(boardId, gradeId, subjectId,
						subTopicId);
				if (!ObjectUtils.isEmpty(totalQuizzes) && totalQuizzes != null) {
					dashboardResponse = new TotalVsComplatedResponseDto();
					dashboardResponse.setMainHeading("Quizzes vs Released");
					dashboardResponse.setSubHeadingOne("Total Number Quizzes");
					dashboardResponse.setTotalCount(totalQuizzes.getData());
				}
				LMSResponse<Integer> releasedQuizCount = contentFeignClient.releasedQuizCount(boardId, schoolId,
						branchId, gradeId, subjectId, subTopicId);
				if (!ObjectUtils.isEmpty(releasedQuizCount) && releasedQuizCount != null) {
					dashboardResponse.setSubHeadingTwo("Total No.of Releases");
					dashboardResponse.setCompletedCount(releasedQuizCount.getData());
					response.add(dashboardResponse);
				}
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("fetch.teacher.chapter.quizzes.count.failed", null));
		}
	}

	@Override
	public List<SyllabusGradeAccessResponseDto> percentageForSyllabus(String boardId, String schoolId, String branchId,
			String gradeId, String subjectId, String subTopicId) {
		log.info("Dashboard Principal count Details fetch started...");
		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		if (!gradeSectionMappingRepository.existsByGradeIdAndDeleted(gradeId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("grade.id.not.found", null));
		try {
			List<SyllabusGradeAccessResponseDto> response = new ArrayList<>();
			List<String> sectionIds = gradeSectionMappingRepository.findSectionByIds(schoolId, branchId, gradeId);
			LMSResponse<List<SectionsResponseDto>> plMasterSectionResponse = !CollectionUtils.isEmpty(sectionIds)
					? mastersFeignClient.getAllSectionsByIds(sectionIds)
					: null;
			List<SectionsResponseDto> plSectionIdsList = plMasterSectionResponse != null
					? plMasterSectionResponse.getData()
					: null;

			LMSResponse<GradesResponseDto> masterGradeResponse = mastersFeignClient.getGradesById(gradeId);
			GradesResponseDto plGradeIdList = masterGradeResponse != null ? masterGradeResponse.getData() : null;
			LMSResponse<Integer> totalMasterChapterCount = mastersFeignClient.chapterCountForPrincipal(boardId, gradeId,
					subjectId, subTopicId);
			Integer totalChapterCount = totalMasterChapterCount != null ? totalMasterChapterCount.getData() : null;

			if (!ObjectUtils.isEmpty(plGradeIdList) && plGradeIdList != null) {
				SyllabusGradeAccessResponseDto gradeAccess = new SyllabusGradeAccessResponseDto(plGradeIdList.getId(),
						plGradeIdList.getGrade());

				if (!CollectionUtils.isEmpty(plSectionIdsList)) {
					LMSResponse<Long> completedSectionChapterCount = teacherFeignClient
							.endedChapterCountSectionForPrincipal(boardId, schoolId, branchId, gradeId, sectionIds,
									subjectId, subTopicId);
					Long endedChapterCount = completedSectionChapterCount != null
							? completedSectionChapterCount.getData()
							: null;

					int totalPercentage = MathUtilitiess.calcPercentage(totalChapterCount);
					int completedPercentage = MathUtilitiess.calculatePercentage(endedChapterCount, totalChapterCount);

					List<SyllabusSectionAccessResponseDto> sectionAccess = new ArrayList<>();
					for (String sectionId : sectionIds) {
						Optional<SectionsResponseDto> commonSectionList = plSectionIdsList.stream()
								.filter(item -> item.getId().equals(sectionId)).findAny();
						if (commonSectionList.isPresent()) {
							sectionAccess.add(new SyllabusSectionAccessResponseDto(commonSectionList.get().getId(),
									commonSectionList.get().getSection(), totalPercentage, completedPercentage));
						}
					}
					gradeAccess.setSections(sectionAccess);
				} else {
					LMSResponse<Long> completedGradeChapterCount = teacherFeignClient
							.endedChapterCountForPrincipal(boardId, schoolId, branchId, gradeId, subjectId, subTopicId);
					Long completedChapterCount = completedGradeChapterCount != null
							? completedGradeChapterCount.getData()
							: null;

					int totalPercentage = MathUtilitiess.calcPercentage(totalChapterCount);
					int completedPercentage = MathUtilitiess.calculatePercentage(completedChapterCount,
							totalChapterCount);
					gradeAccess.setTotalPercentage(totalPercentage);
					gradeAccess.setCompletedPercentage(completedPercentage);
				}
				response.add(gradeAccess);
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("fetch.teacher.chapter.percentage.failed", null));
		}
	}

	/**
	 * Dashboard Student level for teacher and coordinator.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	@Override
	public List<StudentLevelResponseDto> getScoreRangeAndPercentageOfStudents(String teacherId, String boardId,
			String schoolId, String branchId, String gradeId, String subjectId, String subTopicId,
			String academicYearId) {
		log.info("Dashboard student level Details fetch started...");
		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		if (!gradeSectionMappingRepository.existsByGradeIdAndDeleted(gradeId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("grade.id.not.found", null));

		Teachers teachers = teacherRepo.getById(teacherId);
		if (AcademicStaffProfile.PRINCIPAL == teachers.getAcademicStaffProfile())
			throw new USException(ErrorCodes.BAD_REQUEST,
					Translator.toLocale("teacher.principal.type.not.allowed", null));

		try {
			List<StudentLevelResponseDto> response = new ArrayList<>();
			List<String> sectionList = null;
			if (AcademicStaffProfile.TEACHER == teachers.getAcademicStaffProfile()) {
				sectionList = teacherRepo.findSectionIdListByFilters(teacherId, schoolId, branchId, gradeId, subjectId,
						subTopicId);
			}
			if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				sectionList = assignTeacherRepo.getSectionBySubjectAndGrade(schoolId, branchId, gradeId, subjectId,
						subTopicId);
			}

			List<String> assignedStudents = assignTeacherRepo.getAssignedStudentsCountByFilters(schoolId, branchId,
					subjectId, gradeId, sectionList, subTopicId);

			LMSResponse<List<StudentLevelMinResponseDto>> studentFeign = studentFeignClient
					.getStudentsAttendedQuizAndObtainedMarks(schoolId, branchId, gradeId, subjectId, sectionList,
							subTopicId, academicYearId);
			List<StudentLevelMinResponseDto> studentDto = studentFeign != null ? studentFeign.getData() : null;

			LMSResponse<List<ScoreRangeResponseDto>> scoreRangeDto = studentFeignClient.getAllScoreRanges();
			List<ScoreRangeResponseDto> scoreRanges = scoreRangeDto != null ? scoreRangeDto.getData() : null;

			LMSResponse<List<QuizTotalMarksMinResponseDto>> contentFeign = contentFeignClient.getAllUnitQuizzes(boardId,
					gradeId, subjectId, subTopicId, academicYearId);
			List<QuizTotalMarksMinResponseDto> quizDto = contentFeign != null ? contentFeign.getData() : null;

			List<StudentLevelMinResponseDto> filteredStudentDto = studentDto != null ? studentDto.stream()
					.filter(student -> assignedStudents.contains(student.getStudentId())).collect(Collectors.toList())
					: new ArrayList<>();

			Map<String, Integer> cumulativeObtainedMarks = filteredStudentDto.stream()
					.filter(student -> quizDto.stream().anyMatch(quiz -> quiz.getQuizId().equals(student.getQuizId())))
					.collect(Collectors.toMap(StudentLevelMinResponseDto::getStudentId,
							StudentLevelMinResponseDto::getTotalObtainedMark, Integer::sum));

			Map<String, Integer> cumulativeTotalMarks = filteredStudentDto.stream()
					.collect(Collectors.toMap(StudentLevelMinResponseDto::getStudentId, student -> {
						Optional<QuizTotalMarksMinResponseDto> matchingQuiz = quizDto.stream()
								.filter(quiz -> quiz.getQuizId().equals(student.getQuizId())).findFirst();
						return matchingQuiz.map(QuizTotalMarksMinResponseDto::getTotalMarks).orElse(0);
					}, Integer::sum));

			// Calculate count and percentage for each score range
			Map<String, Integer> scoreRangeCounts = new HashMap<>();
			for (ScoreRangeResponseDto scoreRange : scoreRanges) {
				scoreRangeCounts.put(scoreRange.getName(), 0);
			}

			assignedStudents.forEach(studentId -> {
				int obtainedMarks = cumulativeObtainedMarks.getOrDefault(studentId, 0);
				int totalMarks = cumulativeTotalMarks.getOrDefault(studentId, 0);
				int percentage = MathUtilitiess.calculatePercentage(obtainedMarks, totalMarks);

				scoreRanges.stream().filter(scoreRange -> percentage >= scoreRange.getStartRange()
						&& percentage <= scoreRange.getEndRange()).findFirst().ifPresent(scoreRange -> {
							int count = scoreRangeCounts.get(scoreRange.getName());
							scoreRangeCounts.put(scoreRange.getName(), count + 1);
						});
			});

			// Calculate total count of students
			int totalStudents = cumulativeObtainedMarks.size();

			// Calculate percentage for each score range
			List<StudentLevelResponseDto> scoreRangeResponse = new ArrayList<>();
			for (ScoreRangeResponseDto scoreRange : scoreRanges) {
				String rangeName = scoreRange.getName();
				int count = scoreRangeCounts.get(rangeName);
				int percentage = MathUtilitiess.calculatePercentage(count, totalStudents);

				scoreRangeResponse
						.add(new StudentLevelResponseDto(rangeName, scoreRange.getRangeName(), percentage, count));
			}
			response.addAll(scoreRangeResponse);

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.student.level.failed", null));
		}
	}

	/**
	 * Find the count of total chapters, teaching completed chapter and
	 * released-quiz. Use this API mainly for the Dash-board.
	 * 
	 * @param teacherId
	 * @param boardId
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @param academicYearId
	 * @return
	 */
	@Override
	public ChaptersVSQuizzesRelasesResponseDto countForTotalChaptersReleasedQuizzes(String teacherId, String boardId,
			String schoolId, String branchId, String gradeId, String subjectId, String subTopicId,
			String academicYearId) {
		log.info("Dashboard Teacher count Details fetch started...");

		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		try {

			ChaptersVSQuizzesRelasesResponseDto response = null;

			// From master get the grade details
			LMSResponse<GradesResponseDto> gradeResponse = mastersFeignClient.getGradesById(gradeId);
			if (gradeResponse != null && gradeResponse.getData() != null)
				response = new ChaptersVSQuizzesRelasesResponseDto(gradeResponse.getData().getId(),
						gradeResponse.getData().getGrade());

			Teachers teachers = teacherRepo.getById(teacherId);
			List<String> sectionList = new ArrayList<>();
			List<SectionsResponseDto> sectionMasters = new ArrayList<>();

			// find the section if the grade-section mapping exists.
			boolean isSectionExists = gradeSectionMappingRepository.existsBySectionByFilter(schoolId, branchId,
					gradeId);
			if (isSectionExists) {
				sectionList = (teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER)
						? assignTeacherRepo.findSectionIdsForTeacher(teacherId, schoolId, branchId, gradeId, subjectId,
								subTopicId)
						: gradeSectionMappingRepository.findAllSectionByGradeIdBranchAndSchool(gradeId, branchId,
								schoolId);

				// Get the name from master-service
				LMSResponse<List<SectionsResponseDto>> sectionMasterResponse = mastersFeignClient
						.getAllSectionsByIds(sectionList);
				if (sectionMasterResponse != null && !CollectionUtils.isEmpty(sectionMasterResponse.getData()))
					sectionMasters = sectionMasterResponse.getData();
			}

			// feign call for total chapter count
			LMSResponse<Long> chapterCount = mastersFeignClient.getchapterCountForTeacher(boardId, gradeId, subjectId,
					subTopicId);
			Long totalChapter = chapterCount != null ? chapterCount.getData() : null;

			// feign call for end chapter count
			LMSResponse<ChapterEndedResponseDto> teacherResponse = teacherFeignClient.getEndedChpterCountForTeacher(
					boardId, schoolId, branchId, gradeId, subjectId, subTopicId, sectionList, academicYearId);
			ChapterEndedResponseDto endedChapterCount = teacherResponse != null && teacherResponse.getData() != null
					? teacherResponse.getData()
					: null;

			if (endedChapterCount != null) {
				log.info("Bind the end chapter count and released quiz count");
				if (!CollectionUtils.isEmpty(endedChapterCount.getSections())) {
					// setting the section data.
					List<ChaptersVSQuizzesRelasesBySectionResponseDto> sections = new ArrayList<>();
					for (ChapterEndedBySectionResponseDto sectionChapterDto : endedChapterCount.getSections()) {
						Optional<SectionsResponseDto> sectionDto = sectionMasters.stream()
								.filter(item -> item.getId().equals(sectionChapterDto.getSectionId())).findFirst();
						if (sectionDto.isPresent()) {
							ChaptersVSQuizzesRelasesBySectionResponseDto sectionResponse = new ChaptersVSQuizzesRelasesBySectionResponseDto(
									sectionDto.get().getId(), sectionDto.get().getSection(), totalChapter,
									sectionChapterDto.getChapterCount(), sectionChapterDto.getQuizReleaseCount());
							sections.add(sectionResponse);
						}
					}
					response.setSections(sections);
				} else {
					// Setting the grade if there is no grades
					response.setTotalChapters(totalChapter);
					response.setCompletedChapters(endedChapterCount.getChapterCount());
					response.setReleasedQuizzes(endedChapterCount.getQuizReleaseCount());
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.chapter.released.quizzes.count.failed", null));
		}
	}

	/**
	 * This api is used for teacher dashboard unit quiz performance.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param boardId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @param chapterId
	 * @return
	 */
	@Override
	public DashboardUnitQuizPerformanceResponseDto getUnitQuizPerformance(String teacherId, String schoolId,
			String branchId, String boardId, String gradeId, String subjectId, String subTopicId, String chapterId) {

		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		try {

			DashboardUnitQuizPerformanceResponseDto response = new DashboardUnitQuizPerformanceResponseDto();
			List<String> sectionIdList = assignTeacherRepo.getSectionBySubjectAndGrade(teacherId, schoolId, branchId,
					gradeId, subjectId, subTopicId);

			List<String> sectionList = sectionIdList != null ? sectionIdList : null;

			// Feign call for content service
			LMSResponse<QuizTotalMarkMinResponseDto> contentFeign = contentFeignClient.getUnitQuizTotalMark(boardId,
					gradeId, subjectId, subTopicId, chapterId);
			QuizTotalMarkMinResponseDto quizResponseDto = contentFeign != null ? contentFeign.getData() : null;

			// Feign call for student service
			LMSResponse<UnitQuizPerformanceMinResponseDto> studentFeign = studentFeignClient
					.getUnitQuizTotalObtainedMarks(schoolId, branchId, gradeId, subjectId, subTopicId,
							quizResponseDto.getQuizId(), sectionList);
			UnitQuizPerformanceMinResponseDto unitQuizResponseDto = studentFeign != null ? studentFeign.getData()
					: null;

			// Feign call for master service
			LMSResponse<ChapterFeignResponseDto> chapterLMSResponse = mastersFeignClient
					.getChapterByIdForFeignCall(chapterId);
			ChapterFeignResponseDto chapter = chapterLMSResponse != null ? chapterLMSResponse.getData() : null;

			LMSResponse<GradesResponseDto> gradeLMSResponse = mastersFeignClient.getGradesById(gradeId);
			GradesResponseDto grades = gradeLMSResponse != null ? gradeLMSResponse.getData() : null;

			LMSResponse<SubjectsResponseDto> subjectLMSResponse = mastersFeignClient.getSubjectsById(subjectId);
			SubjectsResponseDto subject = subjectLMSResponse != null ? subjectLMSResponse.getData() : null;

			LMSResponse<SubTopicsMinResponseDto> subtopicLMSResponse = null;
			if (!StringUtils.isBlank(subTopicId))
				subtopicLMSResponse = mastersFeignClient.getBySubTopicId(subTopicId);

			if (subtopicLMSResponse != null && subtopicLMSResponse.getData() != null)
				response.setSubtopic(subtopicLMSResponse.getData().getSubTopic());

			Map<String, Long> studentNumberBySection = new HashMap<>();
			long totalStudentsInGrade = 0;

			if (!CollectionUtils.isEmpty(sectionList)) {
				for (String section : sectionList) {
					long totalStudentsInSection = studentsRepo.countOfStudentbasedOnGradeAndSection(schoolId, branchId,
							gradeId, section);
					studentNumberBySection.put(section, totalStudentsInSection);
				}

				List<AverageResponseDto> sectionResponse = new ArrayList<>();
				for (SectionObtainedMarksResponseDto section : unitQuizResponseDto
						.getSectionObtainedMarksResponseDto()) {

					LMSResponse<SectionsResponseDto> sectionLMSResponse = mastersFeignClient
							.getSectionsById(section.getSectionId());
					SectionsResponseDto sections = sectionLMSResponse != null ? sectionLMSResponse.getData() : null;
					int percentage = MathUtilitiess.calculatePercentage(section.getTotalObtainedMarks(),
							quizResponseDto.getTotalMarks());
					long attendeeAverage = MathUtilitiess.calculatePercentage(
							studentNumberBySection.get(section.getSectionId()), section.getStudentCount());

					AverageResponseDto avgerageResponse = new AverageResponseDto(section.getSectionId(),
							sections.getSection(), percentage, attendeeAverage);
					sectionResponse.add(avgerageResponse);
				}
				response.setAverageResponseDto(sectionResponse);
			} else {
				totalStudentsInGrade = studentsRepo.countOfStudentbasedOnGrade(schoolId, branchId, gradeId);

				int percentage = MathUtilitiess.calculatePercentage(unitQuizResponseDto.getTotalObtainedMarks(),
						quizResponseDto.getTotalMarks());
				response.setGradeAverage(percentage);

				int attendeeAverage = MathUtilitiess.calculatePercentage(totalStudentsInGrade,
						unitQuizResponseDto.getStudentCount());
				response.setAttendeesAvg(attendeeAverage);
			}

			response.setChapterId(chapterId);
			response.setChapterName(chapter.getChapter());
			response.setQuizTypeId(quizResponseDto.getQuizTypeId());
			response.setQuizType(quizResponseDto.getQuizType());
			response.setGradeId(gradeId);
			response.setGrade(grades.getGrade());
			response.setSubjectId(subjectId);
			response.setSubject(subject.getSubject());
			response.setSubtopicId(subTopicId);

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.unit.quiz.performance.failed", null));
		}
	}

	/**
	 * This api used for Teacher Dashboard Formative Assessment.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param boardId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	@Override
	public List<TeacherFormativeAssessmentResponseDto> getTeacherFormativeAssessment(String teacherId, String boardId,
			String schoolId, String branchId, String gradeId, String subjectId, String subTopicId,
			String academicYearId) {

		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.id.not.found", null));

		if (!branchRepo.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.id.not.found", null));

		try {
			List<String> sectionList = teacherRepo.findSectionIdListByFilters(teacherId, schoolId, branchId, gradeId,
					subjectId, subTopicId);

			LMSResponse<List<String>> teacherFeign = teacherFeignClient.endedChapterList(boardId, schoolId, branchId,
					gradeId, subjectId, subTopicId, sectionList, academicYearId);
			List<String> chapterList = teacherFeign != null ? teacherFeign.getData() : null;

			if (CollectionUtils.isEmpty(chapterList))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("chapter.not.ended.by.teacher", null));

			LMSResponse<List<TeacherFormativeAssessmentsResponseDto>> contentFeign = contentFeignClient
					.getTeacherFormativeAssessment(boardId, gradeId, subjectId, subTopicId, chapterList);
			List<TeacherFormativeAssessmentsResponseDto> contentResponse = contentFeign != null ? contentFeign.getData()
					: null;

			List<TeacherFormativeAssessmentResponseDto> response = null;
			if (contentResponse != null) {
				response = contentResponse.stream()
						.map(assessment -> new TeacherFormativeAssessmentResponseDto(assessment.getQuizTypeId(),
								assessment.getQuizType(), assessment.getQuizId(), assessment.getName()))
						.collect(Collectors.toList());
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.formative.assessment.failed", null));
		}
	}

	/**
	 * Fetching the grade-section based students-count from assign-teacher and
	 * grade-section-mapping tables * assignedGrades or not assigned for principal
	 * and coordinator, getting grades and students details. This API is used for
	 * Teacher grade wise quiz performance.
	 * 
	 * @return
	 */
	@Override
	public GradeAccessInfoStudentsDetailsResponseDto getGradeAndStudentDetails(String teacherId, String gradeId) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			if (!teacherRepo.checkingTeacherExits(currentUser))
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.exist", null));

			Teachers teachers = (teacherId != null) ? teacherRepo.getById(teacherId)
					: teacherRepo.findByUserNameIgnoreCase(currentUser);

			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();
			String boardId = branchRepo.getBoardWithBranchAndSchool(branchId, schoolId);

			// master feign call for getting academic yearId
			LMSResponse<String> masterAcademicLmsResponse = mastersFeignClient.getLatestAcademicYearId();
			String academicYearId = masterAcademicLmsResponse != null ? masterAcademicLmsResponse.getData() : null;

			GradeAccessInfoStudentsDetailsResponseDto response = new GradeAccessInfoStudentsDetailsResponseDto();
			List<GradeAccessStudentsDetailsResponseDto> accessibleGradeList = new ArrayList<>();
			List<String> gradeIdForQuery = null;
			// Getting grades data information
			List<String> activeGradeIds = gradeSectionMappingRepository.findAllGradesSections(boardId, schoolId,
					branchId, gradeId, academicYearId);

			if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				// calling feign client from master-service and getting list of grade Ids
				LMSResponse<List<String>> mGradeResponse = mastersFeignClient
						.getGradeListByCoordinatorId(teachers.getCoordinatorTypeId());
				if (mGradeResponse != null && !CollectionUtils.isEmpty(mGradeResponse.getData())) {
					gradeIdForQuery = mGradeResponse.getData().stream().filter(activeGradeIds::contains)
							.collect(Collectors.toList());
				}
			} else
				gradeIdForQuery = activeGradeIds;

			// finding the grade-section combo
			List<TeacherAssignProjection> gradeSectionMapping = gradeSectionMappingRepository
					.findAllActiveGradesSections(boardId, schoolId, branchId, gradeId, academicYearId);

			List<String> mappingSectionIds = gradeSectionMapping.stream()
					.filter(item -> !StringUtils.isEmpty(item.getSectionId()))
					.map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.toList());

			// master feign call for principal and coordinator
			LMSResponse<List<GradesResponseDto>> plMasterGradeResponse = !CollectionUtils.isEmpty(gradeIdForQuery)
					? mastersFeignClient.getAllGradesByIds(gradeIdForQuery)
					: null;
			List<GradesResponseDto> plGradeDtoList = plMasterGradeResponse != null ? plMasterGradeResponse.getData()
					: null;

			LMSResponse<List<SectionsResponseDto>> plMasterSectionResponse = !CollectionUtils.isEmpty(mappingSectionIds)
					? mastersFeignClient.getAllSectionsByIds(mappingSectionIds)
					: null;
			List<SectionsResponseDto> plSectionDtoList = plMasterSectionResponse != null
					? plMasterSectionResponse.getData()
					: null;

			PrincipalCountProjection projection = teacherRepo.getAcademicStaffCount(schoolId, branchId, boardId);

			// counting academic staff based on loged in
			if (projection != null && AcademicStaffProfile.PRINCIPAL == teachers.getAcademicStaffProfile()) {
				response.setTotalTeachers(projection.getTeacherCount());
				response.setTotalCoordinators(projection.getCoordinatorCount());
			} else if (projection != null && AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				response.setTotalTeachers(projection.getTeacherCount());
			}

			// principal related listing the grades, if the section not exist student-count
			// will find for grade
			if (!CollectionUtils.isEmpty(gradeIdForQuery)) {
				Integer studentsCount = 0;
				long sectionsCount = 0L;
				if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()
						|| AcademicStaffProfile.PRINCIPAL == teachers.getAcademicStaffProfile()) {
					// GradeAccessInfoStudentsDetailsResponseDto responseDto = new
					// GradeAccessInfoStudentsDetailsResponseDto();
					for (String plGradeId : gradeIdForQuery) {
						Optional<GradesResponseDto> commonGradeDto = plGradeDtoList.stream()
								.filter(item -> item.getId().equals(plGradeId)).findFirst();

						if (commonGradeDto.isPresent()) {
							GradeAccessStudentsDetailsResponseDto gradeAccessDto = new GradeAccessStudentsDetailsResponseDto(
									commonGradeDto.get().getId(), commonGradeDto.get().getGrade());

							studentsCount += studentsRepo.findStudentsCountByGradesWithSection(schoolId, branchId,
									plGradeId, null);

							// particular grade
							Integer totalStudentsUnderGrade = studentsRepo
									.findStudentsCountByGradesWithSection(schoolId, branchId, plGradeId, null);
							List<String> extractedSectionList = new ArrayList<>();
							if (gradeSectionMappingRepository.existsBySectionByFilter(schoolId, branchId, plGradeId)) {
								List<SectionAccessStudentsDetailsResponseDto> accessSectionList = new ArrayList<>();
								extractedSectionList = gradeSectionMapping.stream()
										.filter(item -> item.getGradeId().equals(plGradeId))
										.map(TeacherAssignProjection::getSectionId).distinct()
										.collect(Collectors.toList());

								sectionsCount = extractedSectionList.stream().count();
								for (String plSectionId : extractedSectionList) {
									Optional<SectionsResponseDto> commonSectionDto = plSectionDtoList.stream()
											.filter(item -> item.getId().equals(plSectionId)).findFirst();

									if (commonSectionDto.isPresent()) {
										long pCountStudent = studentsRepo.findCountByPrincipalGradeSections(schoolId,
												branchId, plGradeId, plSectionId);
										accessSectionList.add(new SectionAccessStudentsDetailsResponseDto(
												commonSectionDto.get().getId(), commonSectionDto.get().getSection(),
												pCountStudent));
									}
								}
								gradeAccessDto.setSectionDetails(accessSectionList);
								gradeAccessDto.setTotalSections(sectionsCount);
								gradeAccessDto.setTotalStudentsUnderGrade(totalStudentsUnderGrade);

							} else {
								long totalStudentsBasedOnGrade = studentsRepo.findCountByPrincipalGradeSections(
										schoolId, branchId, commonGradeDto.get().getId(), null);
								gradeAccessDto.setTotalStudentsUnderGrade(totalStudentsBasedOnGrade);
							}
							accessibleGradeList.add(gradeAccessDto);
						}
					}
					response.setTotalGradesPL(gradeIdForQuery.size());
					response.setTotalStudentsPL(studentsCount);
					response.setGradeDetails(accessibleGradeList);
				}
			}
			// finding the assigned grades finding the student under the grade OR
			// section
			if (assignTeacherRepo.existsByTeacherIdAndDeleted(teachers.getId(), false)
					&& AcademicStaffProfile.TEACHER == teachers.getAcademicStaffProfile()) {
				List<TeacherAssignProjection> teacherAccessItems = assignTeacherRepo
						.findAllAssignedGradesByTeacher(teachers.getId());

				Integer studentsCoun = 0;
				long sectionsCoun = 0L;
				if (!CollectionUtils.isEmpty(teacherAccessItems)) {
					List<String> gradeIds = teacherAccessItems.stream().map(TeacherAssignProjection::getGradeId)
							.distinct().collect(Collectors.toList());
					Map<String, List<String>> gradeSectionMap = new HashMap<>();

					for (TeacherAssignProjection project : teacherAccessItems) {
						List<String> sectionValues = new ArrayList<>();
						if (!gradeSectionMap.containsKey(project.getGradeId())) {
							sectionValues.add(project.getSectionId());
							gradeSectionMap.put(project.getGradeId(), sectionValues);
						} else {
							gradeSectionMap.get(project.getGradeId()).add(project.getSectionId());
						}
					}

					if (!CollectionUtils.isEmpty(gradeSectionMap)) {
						// List<GradeAccessStudentsDetailsResponseDto> accessGradeList = new
						// ArrayList<>();
						for (Map.Entry<String, List<String>> gsMap : gradeSectionMap.entrySet()) {
							Optional<GradesResponseDto> commonGradeDto = plGradeDtoList.stream()
									.filter(item -> item.getId().equals(gsMap.getKey())).findFirst();

							sectionsCoun = gsMap.getValue().stream().count();
							if (commonGradeDto.isPresent()) {
								GradeAccessStudentsDetailsResponseDto gradeAccess = new GradeAccessStudentsDetailsResponseDto(
										commonGradeDto.get().getId(), commonGradeDto.get().getGrade());

								if (!CollectionUtils.isEmpty(gsMap.getValue())) {
									List<SectionAccessStudentsDetailsResponseDto> accessSectionList = new ArrayList<>();
									studentsCoun += studentsRepo.getStudentsCountByGradeWithSectionList(schoolId,
											branchId, gsMap.getKey(), gsMap.getValue());

									// particular grade
									Integer totalStudentsUnderGrade = studentsRepo
											.getStudentsCountByGradeWithSectionList(schoolId, branchId, gsMap.getKey(),
													gsMap.getValue());
									for (String sectionId : gsMap.getValue()) {
										Optional<SectionsResponseDto> commonSectionDto = plSectionDtoList.stream()
												.filter(item -> item.getId().equals(sectionId)).findFirst();

										if (commonSectionDto.isPresent()) {
											long countStudent = studentsRepo.findCountByPrincipalGradeSections(schoolId,
													branchId, gsMap.getKey(), sectionId);
											accessSectionList.add(new SectionAccessStudentsDetailsResponseDto(
													commonSectionDto.get().getId(), commonSectionDto.get().getSection(),
													countStudent));
										}
									}
									gradeAccess.setSectionDetails(accessSectionList);
									gradeAccess.setTotalSections(sectionsCoun);
									gradeAccess.setTotalStudentsUnderGrade(totalStudentsUnderGrade);
								} else {
									Integer totalStudentsBasedOnGrade = studentsRepo
											.findStudentsCountByGradesWithSection(schoolId, branchId, gsMap.getKey(),
													null);
									gradeAccess.setTotalStudentsUnderGrade(totalStudentsBasedOnGrade);
								}
								accessibleGradeList.add(gradeAccess);
							}
						}
						response.setTotalGradesTR(gradeIds.size());
						response.setTotalStudentsTR(studentsCoun);
						response.setGradeDetails(accessibleGradeList);
					}
				}
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.principal.grades.students.counted.failed", null));
		}
	}

	/**
	 * This API is used for the Teacher Dashboard Grade-wise Quiz Performance.
	 * 
	 * @param teacherId
	 * @param gradeId
	 * @return
	 */
	@Override
	public TeacherGradeWiseQuizPerformanceResponseDto getTeacherGradeWiseQuizPerformance(String teacherId,
			String gradeId) {
		try {
			Teachers teacher = !StringUtils.isEmpty(teacherId) ? teacherRepo.getById(teacherId)
					: teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());

			if (!teacherRepo.existsByIdAndDeleted(teacher.getId(), false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));
			if (AcademicStaffProfile.TEACHER != teacher.getAcademicStaffProfile())
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));

			String schoolId = teacher.getSchools().getId();
			String school = teacher.getSchools().getName();
			String branchId = teacher.getBranches().getId();
			String branch = teacher.getBranches().getName();
			String boardId = teacher.getBranches().getBoardId();
			TeacherGradeWiseQuizPerformanceResponseDto response = null;

			// Master feign call for board, academicYear and grade.
			LMSResponse<BoardsResponseDto> boardLMSResponse = mastersFeignClient.getBoardsById(boardId);
			BoardsResponseDto boardResponse = boardLMSResponse != null ? boardLMSResponse.getData() : null;

			LMSResponse<AcademicYearResponseDto> academicLMSResponse = mastersFeignClient.getLatestAcademicYear();
			AcademicYearResponseDto academic = academicLMSResponse != null ? academicLMSResponse.getData() : null;

			// To find out the assigned grade, section, subject and subtopic.
			List<GradeSectionSubjectsResponseDto> gradeSectionSubjects = teacherRepo
					.getGradeSectionSubjectsDetails(teacher.getId(), schoolId, branchId, gradeId, null);

			List<String> gardeIds = gradeSectionSubjects.stream().map(GradeSectionSubjectsResponseDto::getGradeId)
					.distinct().collect(Collectors.toList());

			List<String> sectionList = gradeSectionSubjects.stream().map(GradeSectionSubjectsResponseDto::getSectionId)
					.distinct().collect(Collectors.toList());

			LMSResponse<List<SectionsResponseDto>> sectionLMSResponse = mastersFeignClient
					.getAllSectionsByIds(sectionList);
			List<SectionsResponseDto> sectionDto = sectionLMSResponse != null ? sectionLMSResponse.getData() : null;

			if (!CollectionUtils.isEmpty(gardeIds)) {
				LMSResponse<List<GradesResponseDto>> gradeLMSResponse = mastersFeignClient.getAllGradesByIds(gardeIds);
				List<GradesResponseDto> gradesResponse = gradeLMSResponse != null ? gradeLMSResponse.getData() : null;

				List<GradeWisePerformanceResponseDto> gradeResponseList = new ArrayList<>();
				for (String currentGradeId : gardeIds) {
					String grade = gradesResponse.stream().filter(item -> item.getId().equals(currentGradeId))
							.map(GradesResponseDto::getGrade).findFirst().orElse(null);

					int gradeStudentCount = (int) studentsRepo
							.getStudentIds(boardId, schoolId, branchId, currentGradeId, null).stream().count();

					List<String> sectionIds = gradeSectionSubjects.stream()
							.filter(item -> item.getGradeId().equals(currentGradeId))
							.map(GradeSectionSubjectsResponseDto::getSectionId).distinct().collect(Collectors.toList());

					// section wise performance
					if (!CollectionUtils.isEmpty(sectionIds)) {
						List<SectionSubjectScoreResponseDto> sectionResponseList = new ArrayList<>();
						for (String sectionId : sectionIds) {
							String section = sectionDto.stream().filter(item -> item.getId().equals(sectionId))
									.map(SectionsResponseDto::getSection).findFirst().orElse(null);

							int sectionStudentCount = (int) studentsRepo
									.getStudentIds(boardId, schoolId, branchId, currentGradeId, sectionId).stream()
									.count();

							List<GradeSectionSubjectsResponseDto> filteredSections = gradeSectionSubjects.stream()
									.filter(sectionSubjects -> sectionId.equals(sectionSubjects.getSectionId()))
									.collect(Collectors.toList());

							List<String> subjectIds = filteredSections.stream()
									.map(GradeSectionSubjectsResponseDto::getSubjectId).distinct()
									.collect(Collectors.toList());

							List<SubjectScoreResponseDto> subjectResponseList = getSubjectScoreResposne(subjectIds,
									filteredSections, boardId, schoolId, branchId, currentGradeId, sectionId,
									academic.getId(), sectionStudentCount);

							SectionSubjectScoreResponseDto sectionResponse = new SectionSubjectScoreResponseDto(
									sectionId, section, subjectResponseList);
							sectionResponseList.add(sectionResponse);
						}
						GradeWisePerformanceResponseDto gradeResponse = new GradeWisePerformanceResponseDto(
								currentGradeId, grade, sectionResponseList);
						gradeResponseList.add(gradeResponse);

					} else {
						// grade wise performance
						List<String> subjectIds = gradeSectionSubjects.stream()
								.filter(item -> item.getGradeId().equals(currentGradeId))
								.map(GradeSectionSubjectsResponseDto::getSubjectId).distinct()
								.collect(Collectors.toList());

						List<SubjectScoreResponseDto> subjectResponseList = getSubjectScoreResposne(subjectIds,
								gradeSectionSubjects, boardId, schoolId, branchId, currentGradeId, null,
								academic.getId(), gradeStudentCount);
						GradeWisePerformanceResponseDto gradeResponse = new GradeWisePerformanceResponseDto(
								currentGradeId, grade, null, subjectResponseList);
						gradeResponseList.add(gradeResponse);
					}
				}
				response = new TeacherGradeWiseQuizPerformanceResponseDto(boardId, boardResponse.getBoard(), schoolId,
						school, branchId, branch, gradeResponseList);
			}

			// Global average, set into the response. Setting the global average
			List<TeacherGlobalAvgGradeWise> globalAvergaeList = forTeachersGradeWiseGlobalAverage(teacher,
					academic.getId(), gradeId);
			if (response != null && !CollectionUtils.isEmpty(response.getGrades())
					&& !CollectionUtils.isEmpty(globalAvergaeList)) {
				for (GradeWisePerformanceResponseDto grade : response.getGrades()) {
					List<SectionSubjectScoreResponseDto> gSections = grade.getSections();
					List<SubjectScoreResponseDto> gSubjects = grade.getSubjects();

					// check grade-with section has data
					if (!CollectionUtils.isEmpty(gSections)) {
						for (SectionSubjectScoreResponseDto gSection : gSections) {
							// check in side the section subjects exists
							if (!CollectionUtils.isEmpty(gSection.getSubjects())) {
								// traverse through the subject, find it has sub-topic list
								for (SubjectScoreResponseDto gSecSubject : gSection.getSubjects()) {
									if (!CollectionUtils.isEmpty(gSecSubject.getSubtopics())) {
										for (SubtopicScoreResponseDto gSecSubTopic : gSecSubject.getSubtopics()) {
											TeacherGlobalAvgGradeWise gSecSubTopicAvg = globalAvergaeList.stream()
													.filter(itemThree -> itemThree.getGradeId()
															.equals(grade.getGradeId())
															&& itemThree.getSubjectId()
																	.equals(gSecSubject.getSubjectId())
															&& itemThree.getSubTopicId()
																	.equals(gSecSubTopic.getSubtopicId()))
													.findAny().orElse(null);
											if (gSecSubTopicAvg != null) {
												gSecSubTopic.setGlobalQuizAverageScorePercentage(
														gSecSubTopicAvg.getGlobalAvg());
												gSecSubTopic.setUnitGlobalQuizAttemptRate(
														gSecSubTopicAvg.getUqGlobalAvgAttempt());
												gSecSubTopic.setPracticeGlobalQuizAttemptRate(
														gSecSubTopicAvg.getPqGlobalAvgAttempt());
											}
										}
									} else {
										// not sub-topic only subject
										TeacherGlobalAvgGradeWise gSubjectAvg = globalAvergaeList.stream()
												.filter(itemThree -> itemThree.getGradeId().equals(grade.getGradeId())
														&& itemThree.getSubjectId().equals(gSecSubject.getSubjectId()))
												.findAny().orElse(null);
										if (gSubjectAvg != null) {
											BigDecimal defaultGlobalAvg = BigDecimal.ZERO;
											gSecSubject.setGlobalQuizAverageScorePercentage(
													MathUtilitiess.bigDecimalConvertion(gSubjectAvg.getGlobalAvg() != null ? gSubjectAvg.getGlobalAvg() : defaultGlobalAvg));
											gSecSubject.setUnitGlobalQuizAttemptRate(MathUtilitiess
													.bigDecimalConvertion(gSubjectAvg.getUqGlobalAvgAttempt() != null ? gSubjectAvg.getUqGlobalAvgAttempt() : defaultGlobalAvg));
											gSecSubject.setPracticeGlobalQuizAttemptRate(MathUtilitiess
													.bigDecimalConvertion(gSubjectAvg.getPqGlobalAvgAttempt() != null ? gSubjectAvg.getPqGlobalAvgAttempt() : defaultGlobalAvg));
										}
									}
								}
							}
						}
					}

					// Grade without section
					if (!CollectionUtils.isEmpty(gSubjects)) {
						// traverse through the subject, find it has sub-topic list
						for (SubjectScoreResponseDto gSecSubject : gSubjects) {
							if (!CollectionUtils.isEmpty(gSecSubject.getSubtopics())) {
								for (SubtopicScoreResponseDto gSecSubTopic : gSecSubject.getSubtopics()) {
									TeacherGlobalAvgGradeWise gSecSubTopicAvg = globalAvergaeList.stream()
											.filter(itemThree -> itemThree.getGradeId().equals(grade.getGradeId())
													&& itemThree.getSubjectId().equals(gSecSubject.getSubjectId())
													&& itemThree.getSubTopicId().equals(gSecSubTopic.getSubtopicId()))
											.findAny().orElse(null);
									if (gSecSubTopicAvg != null) {
										gSecSubTopic
												.setGlobalQuizAverageScorePercentage(gSecSubTopicAvg.getGlobalAvg());
										gSecSubTopic
												.setUnitGlobalQuizAttemptRate(gSecSubTopicAvg.getUqGlobalAvgAttempt());
										gSecSubTopic.setPracticeGlobalQuizAttemptRate(
												gSecSubTopicAvg.getPqGlobalAvgAttempt());
									}
								}
							} else {
								// not sub-topic only subject
								TeacherGlobalAvgGradeWise gSecSubjectAvg = globalAvergaeList.stream()
										.filter(itemThree -> itemThree.getGradeId().equals(grade.getGradeId())
												&& itemThree.getSubjectId().equals(gSecSubject.getSubjectId()))
										.findAny().orElse(null);
								if (gSecSubjectAvg != null) {
									BigDecimal defaultGlobalAvg = BigDecimal.ZERO;
									gSecSubject.setGlobalQuizAverageScorePercentage(
											MathUtilitiess.bigDecimalConvertion(gSecSubjectAvg.getGlobalAvg() != null ? gSecSubjectAvg.getGlobalAvg() : defaultGlobalAvg));
									gSecSubject.setUnitGlobalQuizAttemptRate(MathUtilitiess
											.bigDecimalConvertion(gSecSubjectAvg.getUqGlobalAvgAttempt() != null ? gSecSubjectAvg.getUqGlobalAvgAttempt() : defaultGlobalAvg));
									gSecSubject.setPracticeGlobalQuizAttemptRate(MathUtilitiess
											.bigDecimalConvertion(gSecSubjectAvg.getPqGlobalAvgAttempt() != null ? gSecSubjectAvg.getPqGlobalAvgAttempt() : defaultGlobalAvg));
								}
							}
						}
					}
				}
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.grade.wise.quiz.performance.failed", null));
		}
	}

	/**
	 * Quiz attempt rate both UQ and PQ
	 * 
	 * @param subjectIds
	 * @param filteredSections
	 * @param boardId
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @param academicYearId
	 * @param sectionStudentCount
	 * @return
	 */
	private List<SubjectScoreResponseDto> getSubjectScoreResposne(List<String> subjectIds,
			List<GradeSectionSubjectsResponseDto> filteredSections, String boardId, String schoolId, String branchId,
			String gradeId, String sectionId, String academicYearId, int sectionStudentCount) {
		try {
			LMSResponse<List<SubjectsMinResponseDto>> subjectLMSResponse = mastersFeignClient
					.getAllSubjectsByIds(subjectIds);
			List<SubjectsMinResponseDto> subjectsResponse = subjectLMSResponse != null ? subjectLMSResponse.getData()
					: null;
			SubjectScoreResponseDto subjectResponse = null;
			List<SubjectScoreResponseDto> subjectResponseList = new ArrayList<>();
			for (String subjectId : subjectIds) {
				String subject = subjectsResponse.stream().filter(item -> item.getSubjectId().equals(subjectId))
						.map(SubjectsMinResponseDto::getSubject).findFirst().orElse(null);
				
				
				//Math subject subtopic fetch changes
				List<String> subtopicIds = new ArrayList<>();
				if (subject != null && subject.equalsIgnoreCase("Mathematics")) {
					// check if subject and subtopics mapping exist
					LMSResponse<SubjectsResponseDto> subjectSubTopic = mastersFeignClient.getSubjectsById(subjectId);

					if (subjectSubTopic != null) {
						List<SubTopicBulkInnerResponseDto> subtopics = subjectSubTopic.getData().getSubTopics();
						subtopicIds = subtopics != null
								? subtopics.stream().filter(SubTopicBulkInnerResponseDto::isActive)
										.map(SubTopicBulkInnerResponseDto::getId).collect(Collectors.toList())
								: new ArrayList<>();
					}
				} else {
					List<GradeSectionSubjectsResponseDto> filteredSubjects = filteredSections.stream()
							.filter(subjectsSubtopic -> subjectsSubtopic.getSubjectId().equals(subjectId)).distinct()
							.collect(Collectors.toList());
					subtopicIds = filteredSubjects.stream().map(GradeSectionSubjectsResponseDto::getSubTopicId)
							.distinct().collect(Collectors.toList());
				}
				

				/* List<GradeSectionSubjectsResponseDto> filteredSubjects = filteredSections.stream()
						.filter(subjectsSubtopic -> subjectsSubtopic.getSubjectId().equals(subjectId)).distinct()
						.collect(Collectors.toList());

				List<String> subtopicIds = filteredSubjects.stream().map(GradeSectionSubjectsResponseDto::getSubTopicId)
						.distinct().collect(Collectors.toList()); */

				QuizAttemptAverageResponseDto quizSubjectAttemptResponse = null;
				List<SubtopicScoreResponseDto> subtopicListResponse = new ArrayList<>();
				// subTopic wise performance
				if (subtopicIds != null && !subtopicIds.isEmpty() && subtopicIds.stream().anyMatch(Objects::nonNull)) {
					LMSResponse<List<SubTopicsMinResponseDto>> subtopicLMSResponse = mastersFeignClient
							.getAllSubTopicsByIds(subtopicIds);
					List<SubTopicsMinResponseDto> subtopicsResponse = subtopicLMSResponse != null
							? subtopicLMSResponse.getData()
							: null;

					QuizAttemptAverageResponseDto quizAttemptResponse = null;
					for (String subtopicId : subtopicIds) {
						String subtopic = subtopicsResponse.stream().filter(item -> item.getId().equals(subtopicId))
								.map(SubTopicsMinResponseDto::getSubTopic).findFirst().orElse(null);

						LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(boardId,
								schoolId, branchId, gradeId, sectionId, subjectId, subtopicId, academicYearId);
						List<String> teacherResponse = teacherLMSResponse != null ? teacherLMSResponse.getData() : null;

						if (!CollectionUtils.isEmpty(teacherResponse)) {
							LMSResponse<List<QuizChapterTotalMarksResponseDto>> contentLMSResponse = contentFeignClient
									.getQuizReleaseTotalMarksCompletedChapter(boardId, schoolId, branchId, gradeId,
											sectionId, subjectId, subtopicId, academicYearId, teacherResponse);
							List<QuizChapterTotalMarksResponseDto> quizResponse = contentLMSResponse != null
									? contentLMSResponse.getData()
									: null;

							String unitAttemptMessage = null;
							String practceAttemptMessage = null;
							String averageMessage = null;
							if (!CollectionUtils.isEmpty(quizResponse)) {
								List<QuizReleaseObtainedMarksResponseDto> studentResponse = getStudentResponse(
										quizResponse, boardId, schoolId, branchId, gradeId, sectionId, subjectId,
										academicYearId);

								if (studentResponse != null && !studentResponse.isEmpty()) {
									quizAttemptResponse = getQuizAttemptAndAverage(quizResponse, studentResponse,
											sectionStudentCount);

									unitAttemptMessage = messageBasedOnQuizAttempt(
											quizAttemptResponse.getUnitQuizAttemptRate());
									practceAttemptMessage = quizAttemptResponse.getPracticeQuizAttemptRate()
											.compareTo(BigDecimal.ZERO) > 0
													? messageBasedOnQuizAttempt(
															quizAttemptResponse.getPracticeQuizAttemptRate())
													: null;
									averageMessage = messageBasedOnQuizAverage(
											quizAttemptResponse.getQuizAverageScorePercentage());

								}

								subtopicListResponse.add(new SubtopicScoreResponseDto(subtopicId, subtopic,
										quizAttemptResponse != null ? quizAttemptResponse.getUnitQuizAttemptRate()
												: BigDecimal.ZERO,
										null, unitAttemptMessage,
										quizAttemptResponse != null ? quizAttemptResponse.getPracticeQuizAttemptRate()
												: BigDecimal.ZERO,
										null, practceAttemptMessage,
										quizAttemptResponse != null
												? quizAttemptResponse.getQuizAverageScorePercentage()
												: null,
										null, averageMessage));
								subjectResponse = new SubjectScoreResponseDto(subjectId, subject, subtopicListResponse);
								
								//Subject skilledSubject set		
								boolean skillSubject = subjectsResponse.stream().filter(item -> item.getSubjectId().equals(subjectId))
										.map(SubjectsMinResponseDto::isSkilledSubject).findFirst().orElse(null);
								if(skillSubject) {
									subjectResponse.setSkilledSubject(skillSubject);								
								}
								subjectResponseList.add(subjectResponse);
							}

						}
					}

				} else {
					// subject wise without subTopic performance
					LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(boardId,
							schoolId, branchId, gradeId, sectionId, subjectId, null, academicYearId);
					List<String> teacherResponse = teacherLMSResponse != null ? teacherLMSResponse.getData() : null;

					if (!CollectionUtils.isEmpty(teacherResponse)) {
						LMSResponse<List<QuizChapterTotalMarksResponseDto>> contentLMSResponse = contentFeignClient
								.getQuizReleaseTotalMarksCompletedChapter(boardId, schoolId, branchId, gradeId,
										sectionId, subjectId, null, academicYearId, teacherResponse);
						List<QuizChapterTotalMarksResponseDto> quizResponse = contentLMSResponse != null
								? contentLMSResponse.getData()
								: null;

						String unitAttemptMessage = null;
						String practceAttemptMessage = null;
						String averageMessage = null;
						if (!CollectionUtils.isEmpty(quizResponse)) {
							List<QuizReleaseObtainedMarksResponseDto> studentResponse = getStudentResponse(quizResponse,
									boardId, schoolId, branchId, gradeId, sectionId, subjectId, academicYearId);

							if (studentResponse != null && !studentResponse.isEmpty()) {
								quizSubjectAttemptResponse = getQuizAttemptAndAverage(quizResponse, studentResponse,
										sectionStudentCount);

								unitAttemptMessage = messageBasedOnQuizAttempt(
										quizSubjectAttemptResponse.getUnitQuizAttemptRate());
								practceAttemptMessage = quizSubjectAttemptResponse.getPracticeQuizAttemptRate()
										.compareTo(BigDecimal.ZERO) > 0
												? messageBasedOnQuizAttempt(
														quizSubjectAttemptResponse.getPracticeQuizAttemptRate())
												: null;
								averageMessage = messageBasedOnQuizAverage(
										quizSubjectAttemptResponse.getQuizAverageScorePercentage());
							}

							subjectResponse = new SubjectScoreResponseDto(subjectId, subject,
									quizSubjectAttemptResponse != null ? MathUtilitiess.bigDecimalConvertion(
											quizSubjectAttemptResponse.getUnitQuizAttemptRate()) : 0L,
									null, unitAttemptMessage,
									quizSubjectAttemptResponse != null ? MathUtilitiess.bigDecimalConvertion(
											quizSubjectAttemptResponse.getPracticeQuizAttemptRate()) : 0L,
									null, practceAttemptMessage,
									quizSubjectAttemptResponse != null ? MathUtilitiess.bigDecimalConvertion(
											quizSubjectAttemptResponse.getQuizAverageScorePercentage()) : 0L,
									null, averageMessage);
							
							//Subject skilledSubject set		
							boolean skillSubject = subjectsResponse.stream().filter(item -> item.getSubjectId().equals(subjectId))
									.map(SubjectsMinResponseDto::isSkilledSubject).findFirst().orElse(null);
							if(skillSubject) {
								subjectResponse.setSkilledSubject(skillSubject);								
							}
							subjectResponseList.add(subjectResponse);
						}

					}
				}
			}
			return subjectResponseList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.subject.quiz.performance.failed", null));
		}
	}

	private List<QuizReleaseObtainedMarksResponseDto> getStudentResponse(
			List<QuizChapterTotalMarksResponseDto> quizResponse, String boardId, String schoolId, String branchId,
			String gradeId, String sectionId, String subjectId, String academicYearId) {
		try {
			List<String> chapterIds = quizResponse != null
					? quizResponse.stream().filter(item -> item.getQuizType().equals("Unit Quiz"))
							.map(QuizChapterTotalMarksResponseDto::getChapterId).distinct().collect(Collectors.toList())
					: Collections.emptyList();

			List<QuizReleaseObtainedMarksResponseDto> response = new ArrayList<>();
			chapterIds.forEach(chapterId -> {
				List<String> unitQuizIdList = quizResponse.stream()
						.filter(item -> "Unit Quiz".equals(item.getQuizType()) && chapterId.equals(item.getChapterId()))
						.map(QuizChapterTotalMarksResponseDto::getQuizId).distinct().collect(Collectors.toList());

				LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> unitLMSResponse = studentFeignClient
						.getUnitQuizzesAndTotalMarks(boardId, schoolId, branchId, gradeId, sectionId, subjectId,
								academicYearId, unitQuizIdList);

				List<QuizReleaseObtainedMarksResponseDto> unitResponse = unitLMSResponse != null
						? unitLMSResponse.getData()
						: null;

				if (unitResponse != null)
					response.addAll(unitResponse);

			});

			List<String> practiceQuizIdList = quizResponse != null
					? quizResponse.stream().filter(item -> item.getQuizType().equals("Practice Quiz"))
							.map(QuizChapterTotalMarksResponseDto::getQuizId).distinct().collect(Collectors.toList())
					: Collections.emptyList();

			if (!CollectionUtils.isEmpty(practiceQuizIdList)) {
				LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> studentPracticeLMSResponse = studentFeignClient
						.getQuizzezAndTotalMarks(boardId, schoolId, branchId, gradeId, sectionId, subjectId,
								academicYearId, practiceQuizIdList);

				List<QuizReleaseObtainedMarksResponseDto> studentPracticeResponse = studentPracticeLMSResponse != null
						? studentPracticeLMSResponse.getData()
						: null;

				if (studentPracticeResponse != null)
					response.addAll(studentPracticeResponse);

			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.subject.quiz.performance.failed", null));
		}
	}

	private QuizAttemptAverageResponseDto getQuizAttemptAndAverage(List<QuizChapterTotalMarksResponseDto> quizResponse,
			List<QuizReleaseObtainedMarksResponseDto> studentResponse, int sectionStudentCount) {
		try {
			double totalUnitQuizObtainedMarks = 0.0;
			int unitQuizCount = 0;
			int practiceQuizCount = 0;
			int unitStudentcount = 0;
			int practiceStudentcount = 0;
			double unitStudentAttemptedCount = 0;
			double totalPracticeStudentCount = 0;
			int unitQuizTotalMark = 0;
			double classAverage = 0;
			double classTotalAverage = 0;
			List<String> attendedQuizIds = studentResponse.stream().map(QuizReleaseObtainedMarksResponseDto::getQuizId)
					.distinct().collect(Collectors.toList());

			for (String quizId : attendedQuizIds) {
				String quizType = quizResponse.stream().filter(item -> item.getQuizId().equals(quizId))
						.map(QuizChapterTotalMarksResponseDto::getQuizType).findFirst().orElse(null);
				if ("Unit Quiz".equalsIgnoreCase(quizType)) {

					List<QuizReleaseObtainedMarksResponseDto> studentQuiz = studentResponse.stream()
							.filter(student -> student.getQuizId().equals(quizId)).collect(Collectors.toList());

					if (!CollectionUtils.isEmpty(studentQuiz)) {
						unitStudentcount = studentQuiz.stream().filter(item -> item.getQuizId().equals(quizId))
								.map(QuizReleaseObtainedMarksResponseDto::getStudentId).distinct()
								.collect(Collectors.toList()).size();

						unitStudentAttemptedCount += unitStudentcount > 0 && sectionStudentCount > 0
								? ((double) unitStudentcount / sectionStudentCount) * 100
								: 0;

						totalUnitQuizObtainedMarks = studentQuiz.stream()
								.mapToInt(QuizReleaseObtainedMarksResponseDto::getTotalObtainedMark).sum();
						unitQuizTotalMark = quizResponse.stream().filter(item -> item.getQuizId().equals(quizId))
								.map(QuizChapterTotalMarksResponseDto::getTotalMarks).findFirst().orElse(0);
						double unitQuizTotalMarks = (double) unitQuizTotalMark * unitStudentcount;
						unitQuizCount++;
						classAverage = totalUnitQuizObtainedMarks > 0 && unitQuizTotalMarks > 0
								? (totalUnitQuizObtainedMarks / unitQuizTotalMarks) * 100
								: 0;
						classTotalAverage += classAverage;
					}
				} else {
					List<QuizReleaseObtainedMarksResponseDto> studentQuiz = studentResponse.stream()
							.filter(student -> student.getQuizId().equals(quizId)).collect(Collectors.toList());

					if (!CollectionUtils.isEmpty(studentQuiz)) {
						practiceStudentcount = (int) studentQuiz.stream()
								.map(QuizReleaseObtainedMarksResponseDto::getStudentId).distinct().count();
						totalPracticeStudentCount += practiceStudentcount > 0 && sectionStudentCount > 0
								? ((double) practiceStudentcount / sectionStudentCount) * 100
								: 0;
						practiceQuizCount++;
					}
				}
			}

			double quizAttemptPercentage = (unitStudentAttemptedCount > 0 && unitQuizCount > 0)
					? unitStudentAttemptedCount / unitQuizCount
					: 0;

			double practiceAttemptPercentage = (totalPracticeStudentCount > 0 && practiceQuizCount > 0)
					? totalPracticeStudentCount / practiceQuizCount
					: 0;

			double unitQuizAverage = classTotalAverage / unitQuizCount;

			BigDecimal unitQuizAttemptPercentage = quizAttemptPercentage > 0
					? MathUtilitiess.roundUpHalfUsingBigDecimal(quizAttemptPercentage, 2)
					: BigDecimal.ZERO;
			BigDecimal unitPracticeAttemptPercentage = practiceAttemptPercentage > 0
					? MathUtilitiess.roundUpHalfUsingBigDecimal(practiceAttemptPercentage, 2)
					: BigDecimal.ZERO;
			BigDecimal quizAveragePercentage = unitQuizAverage > 0
					? MathUtilitiess.roundUpHalfUsingBigDecimal(unitQuizAverage, 2)
					: BigDecimal.ZERO;

			return new QuizAttemptAverageResponseDto(unitQuizAttemptPercentage, unitPracticeAttemptPercentage,
					quizAveragePercentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.quiz.attempt.average.failed", null));
		}
	}

	private String messageBasedOnQuizAverage(Long quizAverage) {
		try {
			String message = null;
			if (quizAverage >= 50 && quizAverage <= 100)
				message = StudentEncourageMessages.SCORE_SECOND_LEVEL.getMessage();
			else
				message = StudentEncourageMessages.SCORE_FIRST_LEVEL.getMessage();
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("kudos.message.config.failed", null));
		}
	}

	private String messageBasedOnQuizAttempt(double quizAttempt) {
		try {
			String message = null;
			if (quizAttempt >= 60 && quizAttempt <= 100)
				message = StudentEncourageMessages.QUIZ_ATTEMPT_HIGHER.getMessage();
			else
				message = StudentEncourageMessages.QUIZ_ATTEMPT_LOWER.getMessage();
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("kudos.message.config.failed", null));
		}
	}

	private String messageBasedOnQuizAverage(BigDecimal quizAverage) {
		try {
			String message = null;
			if (quizAverage.intValue() >= 50 && quizAverage.intValue() <= 100)
				message = StudentEncourageMessages.SCORE_SECOND_LEVEL.getMessage();
			else
				message = StudentEncourageMessages.SCORE_FIRST_LEVEL.getMessage();
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("kudos.message.config.failed", null));
		}
	}

	private String messageBasedOnQuizAttempt(BigDecimal quizAttempt) {
		try {
			String message = null;
			if (quizAttempt.intValue() >= 60 && quizAttempt.intValue() <= 100)
				message = StudentEncourageMessages.QUIZ_ATTEMPT_HIGHER.getMessage();
			else
				message = StudentEncourageMessages.QUIZ_ATTEMPT_LOWER.getMessage();
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("kudos.message.config.failed", null));
		}
	}

	/**
	 * This api is used for Teacher dashboard chapter-wise quiz performance.
	 * 
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	@Override
	public GradeSubjectScoreResponseDto getChapterWiseQuizPerformance(String teacherId, String gradeId,
			String sectionId, String subjectId, String subTopicId) {
		try {
			Teachers teacher = (teacherId != null) ? teacherRepo.getById(teacherId)
					: teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());
			if (AcademicStaffProfile.TEACHER != teacher.getAcademicStaffProfile())
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));

			String schoolId = teacher.getSchools().getId();
			String school = teacher.getSchools().getName();
			String branchId = teacher.getBranches().getId();
			String branch = teacher.getBranches().getName();
			String boardId = teacher.getBranches().getBoardId();
			GradeSubjectScoreResponseDto response = null;

			// Master feign call for board, academicYear and grade.
			LMSResponse<BoardsResponseDto> boardLMSResponse = mastersFeignClient.getBoardsById(boardId);
			BoardsResponseDto boardResponse = boardLMSResponse != null ? boardLMSResponse.getData() : null;

			LMSResponse<AcademicYearResponseDto> academicLMSResponse = mastersFeignClient.getLatestAcademicYear();
			AcademicYearResponseDto academicResponse = academicLMSResponse != null ? academicLMSResponse.getData()
					: null;

			LMSResponse<GradesResponseDto> garedLMSResponse = mastersFeignClient.getGradesById(gradeId);
			GradesResponseDto gradeResponse = garedLMSResponse != null ? garedLMSResponse.getData() : null;

			LMSResponse<SubjectsResponseDto> subjectLMSResponse = mastersFeignClient.getSubjectsById(subjectId);
			SubjectsResponseDto subjectResponse = subjectLMSResponse != null ? subjectLMSResponse.getData() : null;

			LMSResponse<SubTopicsMinResponseDto> subtopicLMSResponse = null;
			if (!StringUtils.isEmpty(subTopicId))
				subtopicLMSResponse = mastersFeignClient.getBySubTopicId(subTopicId);
			SubTopicsMinResponseDto subTopics = subtopicLMSResponse != null ? subtopicLMSResponse.getData() : null;

			LMSResponse<SectionsResponseDto> sectionLMSResponse = null;
			if (!StringUtils.isEmpty(sectionId))
				sectionLMSResponse = mastersFeignClient.getSectionsById(sectionId);
			SectionsResponseDto sections = sectionLMSResponse != null ? sectionLMSResponse.getData() : null;

			LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(boardId, schoolId,
					branchId, gradeId, sectionId, subjectId, subTopicId, academicResponse.getId());
			List<String> teacherResponse = Optional.ofNullable(teacherLMSResponse).map(LMSResponse::getData)
					.orElse(null);

			if (!CollectionUtils.isEmpty(teacherResponse)) {
				LMSResponse<List<QuizChapterTotalMarksResponseDto>> contentLMSResponse = contentFeignClient
						.getQuizzesTotalMarksCompletedChapter(boardId, schoolId, branchId, gradeId, sectionId,
								subjectId, subTopicId, academicResponse.getId(), teacherResponse, "Unit Quiz");
				List<QuizChapterTotalMarksResponseDto> quizResponse = Optional.ofNullable(contentLMSResponse)
						.map(LMSResponse::getData).orElse(Collections.emptyList());

				if (!CollectionUtils.isEmpty(quizResponse)) {
					List<String> chapterList = quizResponse != null
							? quizResponse.stream().map(QuizChapterTotalMarksResponseDto::getChapterId).distinct()
									.collect(Collectors.toList())
							: Collections.emptyList();

					List<ChapterScoreResponseDto> chapters = new ArrayList<>();
					if (!CollectionUtils.isEmpty(chapterList)) {
						LMSResponse<List<ChapterFeignResponseDto>> chapterLMSRespnse = mastersFeignClient
								.getAllChaptersByIdForFeign(chapterList);
						List<ChapterFeignResponseDto> chaptersRespnseList = chapterLMSRespnse != null
								? chapterLMSRespnse.getData()
								: null;

						chapters = chapterList.stream().map(chapterId -> {
							List<QuizChapterTotalMarksResponseDto> chapterResponse = quizResponse.stream()
									.filter(item -> item.getChapterId().equals(chapterId)).distinct()
									.collect(Collectors.toList());

							String chapter = chaptersRespnseList.stream().filter(item -> item.getId().equals(chapterId))
									.map(ChapterFeignResponseDto::getChapter).findFirst().orElse(null);

							List<String> quizIds = chapterResponse.stream()
									.map(QuizChapterTotalMarksResponseDto::getQuizId).distinct()
									.collect(Collectors.toList());

							LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> unitLMSResponse = studentFeignClient
									.getUnitQuizzesAndTotalMarks(boardId, schoolId, branchId, gradeId, sectionId,
											subjectId, academicResponse.getId(), quizIds);
							List<QuizReleaseObtainedMarksResponseDto> unitResponse = unitLMSResponse != null
									? unitLMSResponse.getData()
									: null;

							if (!CollectionUtils.isEmpty(unitResponse)) {
								Integer obtainedMarks = unitResponse.stream()
										.mapToInt(QuizReleaseObtainedMarksResponseDto::getTotalObtainedMark).sum();

								String quizId = unitResponse.stream()
										.map(QuizReleaseObtainedMarksResponseDto::getQuizId).findFirst().orElse(null);

								Integer totalmarks = chapterResponse.stream()
										.filter(item -> item.getQuizId().equals(quizId))
										.mapToInt(QuizChapterTotalMarksResponseDto::getTotalMarks).sum();
								Integer totalMarksAllQuizzes = totalmarks > 0 && !unitResponse.isEmpty()
										? totalmarks * unitResponse.size()
										: 0;

								double quizAverage = obtainedMarks > 0 && totalMarksAllQuizzes > 0
										? ((double) obtainedMarks / totalMarksAllQuizzes) * 100
										: 0;

								Long quizAveragePercentage = quizAverage > 0 ? Math.round(quizAverage) : 0L;

								String message = messageBasedOnQuizAverage(quizAveragePercentage);
								List<EmberStudentsResponseDto> emberStudentList = getEmberStudentDetails(unitResponse,
										chapterResponse, quizId);

								Integer totalStudentCount = (int) unitResponse.stream()
										.map(QuizReleaseObtainedMarksResponseDto::getStudentId).count();
								Integer emberStudent = emberStudentList.size();
								double studentPercentage = emberStudent > 0 && totalStudentCount > 0
										? ((double) emberStudent / totalStudentCount) * 100
										: 0;
								Long studentPercentageInEmber = studentPercentage > 0 ? Math.round(studentPercentage)
										: 0L;

								return new ChapterScoreResponseDto(chapterId, chapter, quizAveragePercentage, null,
										message, emberStudentList, studentPercentageInEmber,null,null,null,null,null,null,null,null);
							}
							return null;
						}).filter(Objects::nonNull).collect(Collectors.toList());

						response = new GradeSubjectScoreResponseDto(boardId, boardResponse.getBoard(), schoolId, school,
								branchId, branch, gradeId, gradeResponse.getGrade(), sectionId,
								sections != null ? sections.getSection() : null, subjectId,
								subjectResponse.getSubject(), subTopicId,
								subTopics != null ? subTopics.getSubTopic() : null, chapters,null);
					}
				} else
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("subject.quiz.not.released", null));
			}

			// Setting the global average of the UQ for each chapter
			if (response != null && !CollectionUtils.isEmpty(response.getChapters())) {
				List<ChapterQuizModel> globalAverageList = forTeachersChapterWise(teacher, gradeId, subjectId,
						subTopicId, academicResponse.getId());
				if (!CollectionUtils.isEmpty(globalAverageList)) {
					for (ChapterScoreResponseDto chapter : response.getChapters()) {
						BigDecimal globalAverage = globalAverageList.stream()
								.filter(item -> item.getChapterId().equals(chapter.getChapterId()))
								.map(item -> Optional.ofNullable(item.getGlobalAvg()).orElse(BigDecimal.ZERO))
								.findFirst().orElse(BigDecimal.ZERO);
						chapter.setGlobalQuizAverage(MathUtilitiess.bigDecimalConvertion(globalAverage));
					}
				}
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.chapter.wise.quiz.performance.failed", null));
		}
	}

	private List<EmberStudentsResponseDto> getEmberStudentDetails(
			List<QuizReleaseObtainedMarksResponseDto> unitResponse,
			List<QuizChapterTotalMarksResponseDto> chapterResponse, String quizId) {
		try {
			List<EmberStudentsResponseDto> emberStudentsList = new ArrayList<>();
			Map<String, Integer> studentPercentageMap = new HashMap<>();

			Integer totalMarks = chapterResponse.stream().filter(item -> item.getQuizId().equals(quizId))
					.map(QuizChapterTotalMarksResponseDto::getTotalMarks).findFirst().orElse(0);

			// Use Streams to filter matching students and calculate percentages
			unitResponse.stream().forEach(student -> {
				String studentId = student.getStudentId();
				Integer obtainedMarks = student.getTotalObtainedMark();

				// Calculate the percentage and store in the map
				int percentage = (obtainedMarks != 0 && totalMarks != 0)
						? MathUtilitiess.calculatePercentage(obtainedMarks, totalMarks)
						: 0;
				studentPercentageMap.put(studentId, percentage);
			});

			LMSResponse<ScoreRangeResponseDto> scoreRangeLMSResponse = studentFeignClient.getEmberScoreRange();
			ScoreRangeResponseDto scoreRangeResponse = scoreRangeLMSResponse != null ? scoreRangeLMSResponse.getData()
					: null;

			Map<String, Integer> filteredStudentPercentageMap = studentPercentageMap.entrySet().stream()
					.filter(entry -> entry.getValue() >= scoreRangeResponse.getStartRange()
							&& entry.getValue() <= scoreRangeResponse.getEndRange())
					.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

			List<String> filteredStudentIds = filteredStudentPercentageMap.keySet().stream()
					.collect(Collectors.toList());
			List<NameCommonResponseDto> studentDetails = studentsRepo.getStudentsByStudentIds(filteredStudentIds);

			// Iterate over filteredStudentIds and studentDetails to combine data
			emberStudentsList = filteredStudentIds.stream().map(studentId -> {
				NameCommonResponseDto studentDetail = studentDetails.stream()
						.filter(detail -> studentId.equals(detail.getId())).findFirst().orElse(null);

				if (studentDetail != null) {
					String studentName = studentDetail.getFirstName() + " " + studentDetail.getLastName();
					Integer percentage = filteredStudentPercentageMap.get(studentId);
					return new EmberStudentsResponseDto(studentId, studentName, percentage);
				}

				return null;
			}).filter(Objects::nonNull).sorted(Comparator.comparingInt(EmberStudentsResponseDto::getEmberPercentage))
					.collect(Collectors.toList());
			return emberStudentsList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.ember.student.fetch.failed", null));
		}
	}

	/**
	 * Fetching the grade-section based students Average score vs Global Score from
	 * grade-section-mapping tables * assignedGrades or not assigned for principal
	 * and coordinator dash-board.
	 * 
	 * @return
	 */
	@Override
	public PrincipalGradeWiseQuizPerformanceResponseDto getGradeWiseQuizPerformanceForPrincipal(String teacherId,
			String gradeId) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			if (!teacherRepo.checkingTeacherExits(currentUser))
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.exist", null));

			Teachers teachers = (teacherId != null) ? teacherRepo.getById(teacherId)
					: teacherRepo.findByUserNameIgnoreCase(currentUser);
			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();

			PrincipalGradeWiseQuizPerformanceResponseDto response = new PrincipalGradeWiseQuizPerformanceResponseDto();
			List<GradeWiseQuizPerformanceResponseDto> accessbleGradeList = new ArrayList<>();
			String boardId = teachers.getBranches().getBoardId();
			// branchRepo.getBoardWithBranchAndSchool(branchId, schoolId);

			String planId = branchRepo.getByPlanId(boardId, branchId, schoolId);
			// master feign call for getting academic yearId
			LMSResponse<String> masterAcademicLmsResponse = mastersFeignClient.getLatestAcademicYearId();
			String academicYearId = masterAcademicLmsResponse != null ? masterAcademicLmsResponse.getData() : null;

			List<String> gradeIdForQuery = null;
			// Getting grades data information
			List<String> activeGradeIds = gradeSectionMappingRepository.findAllGradesSections(boardId, schoolId,
					branchId, gradeId, academicYearId);

			if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				// calling feign client from master-service and getting list of grade Ids
				LMSResponse<List<String>> mGradeResponse = mastersFeignClient
						.getGradeListByCoordinatorId(teachers.getCoordinatorTypeId());
				if (mGradeResponse != null && !CollectionUtils.isEmpty(mGradeResponse.getData())) {
					gradeIdForQuery = mGradeResponse.getData().stream().filter(activeGradeIds::contains)
							.collect(Collectors.toList());
				}
			} else
				gradeIdForQuery = activeGradeIds;

			// finding the grade-section combo
			List<TeacherAssignProjection> gradeSectionMapping = gradeSectionMappingRepository
					.findAllActiveGradesSections(boardId, schoolId, branchId, gradeId, academicYearId);

			List<String> mappingSectionIds = gradeSectionMapping.stream()
					.filter(item -> !StringUtils.isEmpty(item.getSectionId()))
					.map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.toList());

			// master feign call for principal and coordinator
			LMSResponse<List<GradesResponseDto>> plMasterGradeResponse = !CollectionUtils.isEmpty(gradeIdForQuery)
					? mastersFeignClient.getAllGradesByIds(gradeIdForQuery)
					: null;
			List<GradesResponseDto> plGradeDtoList = plMasterGradeResponse != null ? plMasterGradeResponse.getData()
					: null;

			LMSResponse<List<SectionsResponseDto>> plMasterSectionResponse = !CollectionUtils.isEmpty(mappingSectionIds)
					? mastersFeignClient.getAllSectionsByIds(mappingSectionIds)
					: null;
			List<SectionsResponseDto> plSectionDtoList = plMasterSectionResponse != null
					? plMasterSectionResponse.getData()
					: null;

			LMSResponse<BoardsResponseDto> plMasterBoardResponse = mastersFeignClient.findBoardsById(boardId);
			BoardsResponseDto plBoardDto = plMasterBoardResponse != null ? plMasterBoardResponse.getData() : null;

			BranchSchoolNamesResponseDto branchSchool = branchRepo.getBranchAndSchoolName(boardId, branchId, schoolId);

			if (branchSchool != null) {
				response = new PrincipalGradeWiseQuizPerformanceResponseDto(plBoardDto.getId(), plBoardDto.getBoard(),
						schoolId, branchSchool.getSchoolName(), branchId, branchSchool.getBranchName());
			}

			// principal related listing the grades, if the section not exist student-count
			// will find for grade
			if (!CollectionUtils.isEmpty(gradeIdForQuery)) {
				for (TeacherAssignProjection gradeSection : gradeSectionMapping) {
					Optional<GradesResponseDto> commonGrade = plGradeDtoList.stream()
							.filter(item -> item.getId().equals(gradeSection.getGradeId())).findFirst();

					if (commonGrade.isPresent()) {
						String grade = gradeSection.getGradeId();

						if (!StringUtils.isEmpty(planId)) {
							// master feign call for subjects and subTopics
							LMSResponse<List<SubjectsMinResponseDto>> planLmsResponse = AcademicStaffProfile.COORDINATOR == teachers
									.getAcademicStaffProfile()
											? mastersFeignClient.getAllSubjectsByPlanIdGradeIdOrCoordinatorId(planId,
													teachers.getCoordinatorTypeId(), grade)
											: mastersFeignClient.getAllSubjectsByPlanIdGradeIdOrCoordinatorId(planId,
													null, grade);
							List<SubjectsMinResponseDto> planResponse = planLmsResponse != null
									? planLmsResponse.getData()
									: null;

							if (!CollectionUtils.isEmpty(planResponse)) {
								// section wise listing
								if (gradeSectionMappingRepository.existsBySectionByFilter(schoolId, branchId,
										gradeSection.getGradeId())) {
									Optional<SectionsResponseDto> commonSectionDto = plSectionDtoList.stream()
											.filter(item -> item.getId().equals(gradeSection.getSectionId()))
											.findFirst();

									if (commonSectionDto.isPresent()) {
										List<SubjectWiseGradePerformanceResponseDto> gradeWiseSubjectsResponse = getSubjectsWithGradeWisePerformance(
												boardId, schoolId, branchId, grade, commonSectionDto.get().getId(),
												academicYearId, planResponse);

										double unitQuizAvgScore = 0;
										double unitQuizAvgAttemptRate = 0;
										double practiceQuizAvgAttemptRate = 0;
										double subjectCount = 0;
										if (!CollectionUtils.isEmpty(gradeWiseSubjectsResponse)) {
											for (SubjectWiseGradePerformanceResponseDto gradeSubject : gradeWiseSubjectsResponse) {
												if (gradeSubject != null
														&& gradeSubject.getAvgAttemptPercentageUQ() > 0) {
													unitQuizAvgScore += gradeSubject.getAvgScorePercentageUQ();
													unitQuizAvgAttemptRate += gradeSubject.getAvgAttemptPercentageUQ();
													practiceQuizAvgAttemptRate += gradeSubject
															.getAvgAttemptPercentagePQ();
													subjectCount++;
												}
											}
										}

										double totalUnitScore = unitQuizAvgScore != 0 && subjectCount > 0
												? unitQuizAvgScore / subjectCount
												: 0;

										Long unitScore = unitQuizAvgAttemptRate > 0 ? Math.round(totalUnitScore) : 0;

										Long unitAttempt = unitQuizAvgAttemptRate > 0
												? Math.round(unitQuizAvgAttemptRate / subjectCount)
												: 0L;

										Long practiceAttempt = practiceQuizAvgAttemptRate > 0
												? Math.round(practiceQuizAvgAttemptRate / subjectCount)
												: 0L;

										String unitQuizAttemptsMsg = unitAttempt != null && unitAttempt.intValue() > 0
												? messagesBasedOnQuizAttempts(unitAttempt)
												: null;
										String practiceQuizAttemptsMsg = practiceAttempt != null
												&& practiceAttempt.intValue() > 0
														? messagesBasedOnQuizAttempts(practiceAttempt)
														: null;

										accessbleGradeList.add(new GradeWiseQuizPerformanceResponseDto(
												commonGrade.get().getId(), commonGrade.get().getGrade(),
												commonSectionDto.get().getId(), commonSectionDto.get().getSection(),
												unitScore, null, unitAttempt, null, practiceAttempt, null,
												unitQuizAttemptsMsg, practiceQuizAttemptsMsg));
									}
								} else {
									// grade wise average score
									GradeWiseQuizPerformanceResponseDto responseDto = new GradeWiseQuizPerformanceResponseDto(
											commonGrade.get().getId(), commonGrade.get().getGrade());

									List<SubjectWiseGradePerformanceResponseDto> gradeWiseSubjectsResponse = getSubjectsWithGradeWisePerformance(
											boardId, schoolId, branchId, grade, null, academicYearId, planResponse);

									double unitQuizAvgScore = 0;
									double unitQuizAvgAttemptRate = 0;
									double practiceQuizAvgAttemptRate = 0;
									double subjectCount = 0;
									if (!CollectionUtils.isEmpty(gradeWiseSubjectsResponse)) {
										for (SubjectWiseGradePerformanceResponseDto gradeSubject : gradeWiseSubjectsResponse) {
											if (gradeSubject != null && gradeSubject.getAvgAttemptPercentageUQ() > 0) {
												unitQuizAvgScore += gradeSubject.getAvgScorePercentageUQ();
												unitQuizAvgAttemptRate += gradeSubject.getAvgAttemptPercentageUQ();
												practiceQuizAvgAttemptRate += gradeSubject.getAvgAttemptPercentagePQ();
												subjectCount++;
											}
										}
									}

									double totalUnitScore = unitQuizAvgScore != 0 && subjectCount > 0
											? unitQuizAvgScore / subjectCount
											: 0;
									Long unitScore = unitQuizAvgAttemptRate > 0 ? Math.round(totalUnitScore) : 0L;

									Long unitAttempt = unitQuizAvgAttemptRate > 0
											? Math.round(unitQuizAvgAttemptRate / subjectCount)
											: 0L;

									Long practiceAttempt = practiceQuizAvgAttemptRate > 0
											? Math.round(practiceQuizAvgAttemptRate / subjectCount)
											: 0L;

									String unitQuizAttemptsMsg = practiceAttempt != null && unitAttempt.intValue() > 0
											? messagesBasedOnQuizAttempts(unitAttempt)
											: null;
									String practiceQuizAttemptsMsg = practiceAttempt != null
											&& practiceAttempt.intValue() > 0
													? messagesBasedOnQuizAttempts(practiceAttempt)
													: null;

									responseDto.setUnitAttemptRateMessage(unitQuizAttemptsMsg);
									responseDto.setAvgAttemptPercentageUQ(unitAttempt);
									responseDto.setAvgScorePercentageUQ(unitScore);
									responseDto.setPracticeAttemptRateMessage(practiceQuizAttemptsMsg);
									responseDto.setAvgAttemptPercentagePQ(practiceAttempt);
									accessbleGradeList.add(responseDto);
								}
							}
						}
					}
				}
				response.setGradeDetails(accessbleGradeList);
			}
			// Setting the global values
			List<GradeWiseCover> globalValues = globalScoreAttempRateGradeWise(teachers, academicYearId,
					gradeIdForQuery);
			if (!CollectionUtils.isEmpty(globalValues) && !CollectionUtils.isEmpty(response.getGradeDetails())) {
				for (GradeWiseQuizPerformanceResponseDto innerResponse : response.getGradeDetails()) {
					GradeWiseCover gradeCover = globalValues.stream()
							.filter(item -> item.getGradeId().equals(innerResponse.getGradeId())).findAny()
							.orElse(null);
					log.debug("gradeCover.getUqGlobalScoreAvg()" + gradeCover);
					if (gradeCover != null) {
						innerResponse.setGlobalAvgScorePercentageUQ(gradeCover.getUqGlobalScoreAvg() != null ?
								MathUtilitiess.bigDecimalConvertion(gradeCover.getUqGlobalScoreAvg()): 0);
						innerResponse.setGlobalAvgAttemptPercentageUQ(gradeCover.getUqGlobalAttemptRate() !=null ?
								MathUtilitiess.bigDecimalConvertion(gradeCover.getUqGlobalAttemptRate()):0);
						innerResponse.setGlobalAvgAttemptPercentagePQ(gradeCover.getPqGlobalAttemptRate() != null ?
								MathUtilitiess.bigDecimalConvertion(gradeCover.getPqGlobalAttemptRate()):0);
					}
				}
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.principal.grade.wise.performance.failed", null));
		}
	}

	/**
	 * Calculate the score average of Unit Quiz and attempt rate of Unit
	 * quiz/Practice Quiz.
	 * 
	 * @return
	 */
	private AttemRateAndPercentageResponseDto quizAttempRateAndPercentage(String boardId, String schoolId,
			String branchId, String gradeId, String sectionId, List<String> unitReleasedIds, String academicYearId,
			List<ReleasedUnitPracticeQuizResponseDto> contentAvg, long totalStudentCount, String subjectId) {
		try {
			LMSResponse<List<UnitPracticeQuizPerformanceMinResponseDto>> studentUQLmsAvg = studentFeignClient
					.getUnitPracticeQuizTotalObtainedMarks(boardId, schoolId, branchId, gradeId, unitReleasedIds,
							sectionId, academicYearId, subjectId);
			List<UnitPracticeQuizPerformanceMinResponseDto> studentUQAvg = studentUQLmsAvg != null
					? studentUQLmsAvg.getData()
					: null;

			double unitQuizAvgAttempts = 0;
			double unitQuizAvgScore = 0;
			int count = 0;
			if (!CollectionUtils.isEmpty(studentUQAvg)) {
				for (UnitPracticeQuizPerformanceMinResponseDto stuQuiz : studentUQAvg) {
					if (stuQuiz != null) {
						if (stuQuiz.getStudentCount() > 0) {
							count++;
						}
						unitQuizAvgAttempts += MathUtilitiess.calculatePercentage(stuQuiz.getStudentCount(),
								totalStudentCount);

						if (contentAvg != null) {
							long unitTotalMarks = contentAvg.stream()
									.filter(item -> item.getQuizId().equals(stuQuiz.getQuizId()))
									.map(ReleasedUnitPracticeQuizResponseDto::getTotalMarks).findFirst().orElse(0);

							long total = unitTotalMarks * stuQuiz.getStudentCount();

							unitQuizAvgScore += MathUtilitiess.calculatePercentage(stuQuiz.getTotalObtainedMark(),
									total);
						}
					}
				}
			}
			double unitQuizAvgAttempt = unitQuizAvgAttempts > 0 && count > 0 ? unitQuizAvgAttempts / count : 0;
//			BigDecimal unitAttempt = unitQuizAvgAttempt > 0
//					? MathUtilitiess.roundUpHalfUsingBigDecimal(unitQuizAvgAttempt, 2)
//					: null;

			double unitQuizScore = unitQuizAvgScore > 0 && count > 0 ? unitQuizAvgScore / count : 0;
//			BigDecimal unitScore = unitQuizAvgAttempts > 0 ? MathUtilitiess.roundUpHalfUsingBigDecimal(unitQuizScore, 2)
//					: null;

			return new AttemRateAndPercentageResponseDto(unitQuizAvgAttempt, unitQuizScore);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculate.grade.wise.principal.coordinator.failed", null));
		}
	}

	/**
	 * Calculate the Global score average of Unit Quiz and attempt rate of Unit
	 * quiz/Practice Quiz. For a set of grade from current logic academic
	 * staff(Principal or Coordinator). Grade-Wise feature from Dashboard
	 * 
	 * @param teachers
	 * @param academicYearId
	 * @param gradeIds
	 * @return
	 */
	private List<GradeWiseCover> globalScoreAttempRateGradeWise(Teachers teachers, String academicYearId,
			List<String> gradeIds) {
		try {
			String boardId = teachers.getBranches().getBoardId();
			// String coordinatorId = teachers.getCoordinatorTypeId();
			String planId = branchRepo.getByPlanId(boardId, teachers.getBranches().getId(),
					teachers.getSchools().getId());
//			// master-service: calling for get grades and similar plans
//			LMSResponse<GradesAndPlans> masterResponse = mastersFeignClient.getSimilarPlansGradeWise(planId,
//					coordinatorId);
//			List<String> gradeIds = masterResponse != null && masterResponse.getData() != null
//					? masterResponse.getData().getGradeIds()
//					: null;

			List<GradeWiseCover> request = new ArrayList<>();
			if (!CollectionUtils.isEmpty(gradeIds)) {
				for (String gradeId : gradeIds) {
					List<InstitutionList> institutes = schoolRepo.findAllInstitutionbyGradeAndBoardAndNotBranch(gradeId,
							boardId);
					request.add(new GradeWiseCover(gradeId, boardId, academicYearId, institutes));

					// find the subjects(sub-topics) under the given grade and plan.(Master-Service
					// feign)
					LMSResponse<List<SubjectsSubtopicUnderGradeModel>> subjectSubTopicResponse = mastersFeignClient
							.getTheSubjectsAndSimilarPlans(Arrays.asList(gradeId), planId);
					List<SubjectsSubtopicUnderGradeModel> subjectsSubTopics = subjectSubTopicResponse != null
							&& !CollectionUtils.isEmpty(subjectSubTopicResponse.getData())
									? subjectSubTopicResponse.getData()
									: null;
					if (!CollectionUtils.isEmpty(subjectsSubTopics)) {
						if (!CollectionUtils.isEmpty(request)) {
							for (SubjectsSubtopicUnderGradeModel subjectSubTopic : subjectsSubTopics) {
								request.forEach(item -> {
									if (item.getGradeId().equals(gradeId)) {
										item.setSubjectAndSubTopic(subjectSubTopic.getSubjectAndSubTopic());
									}
								});
							}
						}
					}
				}
			}

			/*
			 * Request to content-service, it'll produce the released UQ & PQ. then request
			 * will send to student-service from content-service itself. Student-service
			 * will produce the global calculations.
			 */
			LMSResponse<List<GradeWiseCover>> contentServiceResponse = contentFeignClient
					.gradeWiseUqPqReleases(request);
			return contentServiceResponse != null && !CollectionUtils.isEmpty(contentServiceResponse.getData())
					? contentServiceResponse.getData()
					: null;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculate.grade.wise.principal.coordinator.failed", null));
		}
	}

	/**
	 * Fetching the grade-section based students Average score from assignedGrade or
	 * not assigned for principal and coordinator dash-board.
	 * 
	 * @return
	 */
	private List<SubjectWiseGradePerformanceResponseDto> getSubjectsWithGradeWisePerformance(String boardId,
			String schoolId, String branchId, String gradeId, String sectionId, String academicYearId,
			List<SubjectsMinResponseDto> planResponse) {
		try {
			Integer totalStudentCount = studentsRepo.findStudentCountForPrincipal(boardId, schoolId, branchId, gradeId,
					sectionId);

			List<SubjectWiseGradePerformanceResponseDto> response = new ArrayList<>();

			if (!CollectionUtils.isEmpty(planResponse)) {
				// subject wise
				planResponse.forEach(subject -> {
//					SubjectWiseGradePerformanceResponseDto subjectDto = new SubjectWiseGradePerformanceResponseDto(
//							subject.getSubjectId(), subject.getSubject());

					if (!CollectionUtils.isEmpty(subject.getSubTopics())) {
						// subTopic wise quiz performance
						subject.getSubTopics().forEach(subTopic -> {
							// average score
							LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(
									boardId, schoolId, branchId, gradeId, sectionId, subject.getSubjectId(),
									subTopic.getId(), academicYearId);
							List<String> chapterList = teacherLMSResponse != null ? teacherLMSResponse.getData() : null;

							if (chapterList != null && !CollectionUtils.isEmpty(chapterList)) {
								LMSResponse<List<PrincipalSubjectWisePerformanceResponseDto>> contentLmsResponse = contentFeignClient
										.getSubjectWiseUnitPracticeQuizzes(boardId, schoolId, branchId, gradeId,
												sectionId, subject.getSubjectId(), subTopic.getId(), academicYearId,
												chapterList);
								List<PrincipalSubjectWisePerformanceResponseDto> contentResponse = contentLmsResponse != null
										? contentLmsResponse.getData()
										: null;

								if (!CollectionUtils.isEmpty(contentResponse)) {
									// listing chapterIds
									List<String> unitChapters = contentResponse.stream()
											.filter(quiz -> quiz.getQuizType().equals("Unit Quiz"))
											.map(PrincipalSubjectWisePerformanceResponseDto::getChapterId).distinct()
											.collect(Collectors.toList());

									List<String> practiceQuizList = contentResponse.stream()
											.filter(item -> "Practice Quiz".equals(item.getQuizType()))
											.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId).distinct()
											.collect(Collectors.toList());

									double unitQuizAvgAttempts = 0;
									double unitQuizAvgScore = 0;
									Integer unitQuizCount = 0;
									if (!CollectionUtils.isEmpty(unitChapters)) {
										for (String chapterId : unitChapters) {
											List<String> unitQuizList = contentResponse.stream()
													.filter(item -> "Unit Quiz".equals(item.getQuizType())
															&& chapterId.equals(item.getChapterId()))
													.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId)
													.distinct().collect(Collectors.toList());

											LMSResponse<UnitPracticeQuizPerformanceMinResponseDto> unitLMSResponse = unitQuizList != null
													? studentFeignClient.getUnitQuizPerformance(boardId, schoolId,
															branchId, gradeId, unitQuizList, subject.getSubjectId(),
															sectionId, academicYearId, subTopic.getId())
													: null;

											UnitPracticeQuizPerformanceMinResponseDto studentUnitResponse = unitLMSResponse != null
													? unitLMSResponse.getData()
													: null;

											if (studentUnitResponse != null
													&& !ObjectUtils.isEmpty(studentUnitResponse)) {
												Integer unitTotalMarks = contentResponse.stream()
														.filter(item -> item.getQuizId()
																.equals(studentUnitResponse.getQuizId()))
														.map(PrincipalSubjectWisePerformanceResponseDto::getTotalMarks)
														.findFirst().orElse(0);

												long totalMarks = unitTotalMarks
														* studentUnitResponse.getStudentCount();

												unitQuizAvgAttempts += MathUtilitiess.calculatePercentage(
														studentUnitResponse.getStudentCount(), totalStudentCount);

												unitQuizAvgScore += MathUtilitiess.calculatePercentage(
														studentUnitResponse.getTotalObtainedMark(), totalMarks);
												unitQuizCount++;
											}
										}
									}

									AttemRateAndPercentageResponseDto practiceQuizAttempts = practiceQuizList != null
											? quizAttempRateAndPercentage(boardId, schoolId, branchId, gradeId,
													sectionId, practiceQuizList, academicYearId, null,
													totalStudentCount, subject.getSubjectId())
											: null;

									double unitAvgScore = unitQuizAvgScore != 0 && unitQuizCount > 0
											? unitQuizAvgScore / unitQuizCount
											: 0;

									double unitAttemptRate = unitQuizAvgAttempts > 0
											? unitQuizAvgAttempts / unitQuizCount
											: 0;

									double pqAttemptRate = practiceQuizAttempts != null
											? practiceQuizAttempts.getAttemptPercentage()
											: 0;
									response.add(new SubjectWiseGradePerformanceResponseDto(subject.getSubjectId(),
											subject.getSubject(), subTopic.getId(), subTopic.getSubTopic(),
											unitAvgScore, 0, unitAttemptRate, 0, pqAttemptRate, 0));
								}
							}
						});
					} else {
						// subject wise quiz performance

						LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(boardId,
								schoolId, branchId, gradeId, sectionId, subject.getSubjectId(), null, academicYearId);
						List<String> chapterList = teacherLMSResponse != null ? teacherLMSResponse.getData() : null;

						if (chapterList != null && !CollectionUtils.isEmpty(chapterList)) {
							SubjectWiseGradePerformanceResponseDto subjectDto = new SubjectWiseGradePerformanceResponseDto(
									subject.getSubjectId(), subject.getSubject());

							LMSResponse<List<PrincipalSubjectWisePerformanceResponseDto>> contentLmsResponse = contentFeignClient
									.getSubjectWiseUnitPracticeQuizzes(boardId, schoolId, branchId, gradeId, sectionId,
											subject.getSubjectId(), null, academicYearId, chapterList);
							List<PrincipalSubjectWisePerformanceResponseDto> contentResponse = contentLmsResponse != null
									? contentLmsResponse.getData()
									: null;

							if (!CollectionUtils.isEmpty(contentResponse)) {
								// listing chapterIds
								List<String> unitChapters = contentResponse.stream()
										.filter(quiz -> quiz.getQuizType().equals("Unit Quiz"))
										.map(PrincipalSubjectWisePerformanceResponseDto::getChapterId).distinct()
										.collect(Collectors.toList());

								List<String> practiceQuizList = contentResponse.stream()
										.filter(item -> "Practice Quiz".equals(item.getQuizType()))
										.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId).distinct()
										.collect(Collectors.toList());

								double unitQuizAvgAttempts = 0;
								double unitQuizAvgScore = 0;
								Integer unitQuizCount = 0;
								if (!CollectionUtils.isEmpty(unitChapters)) {
									for (String chapterId : unitChapters) {
										List<String> unitQuizList = contentResponse.stream()
												.filter(item -> "Unit Quiz".equals(item.getQuizType())
														&& chapterId.equals(item.getChapterId()))
												.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId).distinct()
												.collect(Collectors.toList());

										LMSResponse<UnitPracticeQuizPerformanceMinResponseDto> unitLMSResponse = unitQuizList != null
												? studentFeignClient.getUnitQuizPerformance(boardId, schoolId, branchId,
														gradeId, unitQuizList, subject.getSubjectId(), sectionId,
														academicYearId, null)
												: null;

										UnitPracticeQuizPerformanceMinResponseDto studentUnitResponse = unitLMSResponse != null
												? unitLMSResponse.getData()
												: null;

										if (studentUnitResponse != null && !ObjectUtils.isEmpty(studentUnitResponse)) {
											Integer unitTotalMarks = contentResponse.stream()
													.filter(item -> item.getQuizId()
															.equals(studentUnitResponse.getQuizId()))
													.map(PrincipalSubjectWisePerformanceResponseDto::getTotalMarks)
													.findFirst().orElse(0);

											long totalMarks = unitTotalMarks * studentUnitResponse.getStudentCount();

											unitQuizAvgAttempts += MathUtilitiess.calculatePercentage(
													studentUnitResponse.getStudentCount(), totalStudentCount);

											unitQuizAvgScore += MathUtilitiess.calculatePercentage(
													studentUnitResponse.getTotalObtainedMark(), totalMarks);
											unitQuizCount++;
										}
									}
								}
								AttemRateAndPercentageResponseDto practiceQuizAttempts = practiceQuizList != null
										? quizAttempRateAndPercentage(boardId, schoolId, branchId, gradeId, sectionId,
												practiceQuizList, academicYearId, null, totalStudentCount,
												subject.getSubjectId())
										: null;

								double unitAvgScore = unitQuizAvgScore != 0 && unitQuizCount > 0
										? unitQuizAvgScore / unitQuizCount
										: 0;

								double unitAttemptRate = unitQuizAvgAttempts > 0 ? unitQuizAvgAttempts / unitQuizCount
										: 0;

								double pqAttemptRate = practiceQuizAttempts != null
										? practiceQuizAttempts.getAttemptPercentage()
										: 0;
								subjectDto.setAvgAttemptPercentagePQ(pqAttemptRate);
								subjectDto.setAvgAttemptPercentageUQ(unitAttemptRate);
								subjectDto.setAvgScorePercentageUQ(unitAvgScore);
								response.add(subjectDto);
							}
						}
					}
				});
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculate.grade.wise.principal.coordinator.failed", null));
		}
	}

	/**
	 * Fetching the grade-section based students Average score vs Global Score from
	 * assignedGrade or not assigned for principal and coordinator dash-board.
	 * 
	 * @return
	 */
	@Override
	public PrincipalGradeDetailedPerformanceResponseDto getGradeDetailedPerformanceForPrincipal(String teacherId,
			String gradeId, String sectionId) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			if (!teacherRepo.checkingTeacherExits(currentUser))
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.exist", null));

			Teachers teachers = (teacherId != null) ? teacherRepo.getById(teacherId)
					: teacherRepo.findByUserNameIgnoreCase(currentUser);
			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();

			PrincipalGradeDetailedPerformanceResponseDto response = null;
			List<SubjectWisePerformanceResponseDto> accessbleSubjectList = new ArrayList<>();
			String boardId = teachers.getBranches().getBoardId();
			// branchRepo.getBoardWithBranchAndSchool(branchId, schoolId);
			String planId = branchRepo.getByPlanId(boardId, branchId, schoolId);

			// master feign call for getting academic yearId
			LMSResponse<String> masterAcademicLmsResponse = mastersFeignClient.getLatestAcademicYearId();
			String academicYearId = masterAcademicLmsResponse != null ? masterAcademicLmsResponse.getData() : null;

			// master feign call for grade name
			LMSResponse<GradesResponseDto> gradeLmsResponse = mastersFeignClient.getGradesById(gradeId);
			GradesResponseDto gradeResponse = gradeLmsResponse != null ? gradeLmsResponse.getData() : null;

			response = new PrincipalGradeDetailedPerformanceResponseDto(gradeResponse.getId(),
					gradeResponse.getGrade());
			// master feign call for section name
			LMSResponse<SectionsResponseDto> sectionLmsResponse = sectionId != null
					? mastersFeignClient.getSectionsById(sectionId)
					: null;
			SectionsResponseDto sectionResponse = sectionLmsResponse != null ? sectionLmsResponse.getData() : null;

			if (sectionResponse != null) {
				response.setSectionId(sectionResponse.getId());
				response.setSection(sectionResponse.getSection());
			}

			Integer totalStudentCount = studentsRepo.findStudentCountForPrincipal(boardId, schoolId, branchId,
					response.getGradeId(), response.getSectionId());

			if (!StringUtils.isEmpty(planId)) {
				// master feign call for subjects and subTopics
				LMSResponse<List<SubjectsMinResponseDto>> planLmsResponse = AcademicStaffProfile.COORDINATOR == teachers
						.getAcademicStaffProfile()
								? mastersFeignClient.getAllSubjectsByPlanIdGradeIdOrCoordinatorId(planId,
										teachers.getCoordinatorTypeId(), gradeId)
								: mastersFeignClient.getAllSubjectsByPlanIdGradeIdOrCoordinatorId(planId, null,
										gradeId);
				List<SubjectsMinResponseDto> planResponse = planLmsResponse != null ? planLmsResponse.getData() : null;
				if (!CollectionUtils.isEmpty(planResponse)) {
					// subject wise
					planResponse.forEach(subject -> {
						SubjectWisePerformanceResponseDto subjectDto = new SubjectWisePerformanceResponseDto(
								subject.getSubjectId(), subject.getSubject());

						if (!CollectionUtils.isEmpty(subject.getSubTopics())) {
							List<SubTopicWisePerformanceResponseDto> accessbleSubTopicList = new ArrayList<>();
							// subTopic wise quiz performance
							subject.getSubTopics().forEach(subTopic -> {
								// average score
								SubTopicWisePerformanceResponseDto responseDto = new SubTopicWisePerformanceResponseDto(
										subTopic.getId(), subTopic.getSubTopic());

								LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(
										boardId, schoolId, branchId, gradeId, sectionId, subject.getSubjectId(),
										subTopic.getId(), academicYearId);
								List<String> chapterList = teacherLMSResponse != null ? teacherLMSResponse.getData()
										: null;

								if (chapterList != null && !CollectionUtils.isEmpty(chapterList)) {
									LMSResponse<List<PrincipalSubjectWisePerformanceResponseDto>> contentLmsResponse = contentFeignClient
											.getSubjectWiseUnitPracticeQuizzes(boardId, schoolId, branchId, gradeId,
													sectionId, subject.getSubjectId(), subTopic.getId(), academicYearId,
													chapterList);
									List<PrincipalSubjectWisePerformanceResponseDto> contentResponse = contentLmsResponse != null
											? contentLmsResponse.getData()
											: null;

									if (!CollectionUtils.isEmpty(contentResponse)) {
										// listing chapterIds
										List<String> unitChapters = contentResponse.stream()
												.filter(quiz -> quiz.getQuizType().equals("Unit Quiz"))
												.map(PrincipalSubjectWisePerformanceResponseDto::getChapterId)
												.distinct().collect(Collectors.toList());

										List<String> practiceQuizList = contentResponse.stream()
												.filter(item -> "Practice Quiz".equals(item.getQuizType()))
												.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId).distinct()
												.collect(Collectors.toList());

//										long practiceTotalMarks = contentResponse.stream()
//												.filter(mark -> mark.getQuizType().equals("Practice Quiz"))
//												.mapToInt(PrincipalSubjectWisePerformanceResponseDto::getTotalMarks)
//												.sum();

										double unitQuizAvgAttempts = 0;
										double unitQuizAvgScore = 0;
										Integer unitQuizCount = 0;
										if (!CollectionUtils.isEmpty(unitChapters)) {
											for (String chapterId : unitChapters) {
												List<String> unitQuizList = contentResponse.stream()
														.filter(item -> "Unit Quiz".equals(item.getQuizType())
																&& chapterId.equals(item.getChapterId()))
														.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId)
														.distinct().collect(Collectors.toList());

												LMSResponse<UnitPracticeQuizPerformanceMinResponseDto> unitLMSResponse = unitQuizList != null
														? studentFeignClient.getUnitQuizPerformance(boardId, schoolId,
																branchId, gradeId, unitQuizList, subject.getSubjectId(),
																sectionId, academicYearId, subTopic.getId())
														: null;

												UnitPracticeQuizPerformanceMinResponseDto studentUnitResponse = unitLMSResponse != null
														? unitLMSResponse.getData()
														: null;

												if (studentUnitResponse != null
														&& !ObjectUtils.isEmpty(studentUnitResponse)) {
													Integer unitTotalMarks = contentResponse.stream()
															.filter(item -> item.getQuizId()
																	.equals(studentUnitResponse.getQuizId()))
															.map(PrincipalSubjectWisePerformanceResponseDto::getTotalMarks)
															.findFirst().orElse(0);

													long totalMarks = unitTotalMarks
															* studentUnitResponse.getStudentCount();

													unitQuizAvgAttempts += MathUtilitiess.calculatePercentage(
															studentUnitResponse.getStudentCount(), totalStudentCount);

													unitQuizAvgScore += MathUtilitiess.calculatePercentage(
															studentUnitResponse.getTotalObtainedMark(), totalMarks);
													unitQuizCount++;
												}
											}
										}

										AttemRateAndPercentageResponseDto practiceQuizAttempts = practiceQuizList != null
												? quizAttempRateAndPercentage(boardId, schoolId, branchId, gradeId,
														sectionId, practiceQuizList, academicYearId, null,
														totalStudentCount, subject.getSubjectId())
												: null;

//										QuizAttemptUQPQResponseDto attemptAvg = quizAttemptAndAverage(contentResponse,
//												quizObtained, totalStudentCount);

										String unitQuizAttemptsMsg = unitQuizAvgAttempts > 0
												? messageBasedOnQuizAttempt(unitQuizAvgAttempts / unitQuizCount)
												: null;
										String practiceQuizAttemptsMsg = practiceQuizAttempts != null
												&& practiceQuizAttempts.getAttemptPercentage() > 0
														? messageBasedOnQuizAttempt(
																practiceQuizAttempts.getAttemptPercentage())
														: null;

										double percentage = unitQuizAvgScore != 0 && unitQuizCount > 0
												? unitQuizAvgScore / unitQuizCount
												: 0;

										double totalUnitAttemptRate = unitQuizAvgAttempts > 0
												? unitQuizAvgAttempts / unitQuizCount
												: 0;

										Long unitScore = unitQuizAvgAttempts > 0 ? Math.round(percentage) : 0L;

										Long unitAttempt = unitQuizAvgAttempts > 0 ? Math.round(totalUnitAttemptRate)
												: 0L;

										Long practiceAttempt = practiceQuizAttempts != null
												&& practiceQuizAttempts.getAttemptPercentage() > 0
														? Math.round(practiceQuizAttempts.getAttemptPercentage())
														: 0L;

										responseDto.setAvgScorePercentageUQ(unitScore);
										responseDto.setAvgAttemptPercentageUQ(unitAttempt);
										responseDto.setAvgAttemptPercentagePQ(practiceAttempt);
										responseDto.setUnitAttemptRateMessage(unitQuizAttemptsMsg);
										responseDto.setPracticeAttemptRateMessage(practiceQuizAttemptsMsg);
									}
								}
								accessbleSubTopicList.add(responseDto);
							});
							subjectDto.setSubTopicDetails(accessbleSubTopicList);
						} else {
							// subject wise quiz performance
							LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(
									boardId, schoolId, branchId, gradeId, sectionId, subject.getSubjectId(), null,
									academicYearId);
							List<String> chapterList = teacherLMSResponse != null ? teacherLMSResponse.getData() : null;

							if (chapterList != null && !CollectionUtils.isEmpty(chapterList)) {
								LMSResponse<List<PrincipalSubjectWisePerformanceResponseDto>> contentLmsResponse = contentFeignClient
										.getSubjectWiseUnitPracticeQuizzes(boardId, schoolId, branchId, gradeId,
												sectionId, subject.getSubjectId(), null, academicYearId, chapterList);
								List<PrincipalSubjectWisePerformanceResponseDto> contentResponse = contentLmsResponse != null
										? contentLmsResponse.getData()
										: null;

								// List<QuizReleaseObtainedMarksResponseDto> quizObtained = new ArrayList<>();
								if (!CollectionUtils.isEmpty(contentResponse)) {
									// listing chapterIds
									List<String> unitChapters = contentResponse.stream()
											.filter(quiz -> quiz.getQuizType().equals("Unit Quiz"))
											.map(PrincipalSubjectWisePerformanceResponseDto::getChapterId).distinct()
											.collect(Collectors.toList());

									List<String> practiceQuizList = contentResponse.stream()
											.filter(item -> "Practice Quiz".equals(item.getQuizType()))
											.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId).distinct()
											.collect(Collectors.toList());

//									long practiceTotalMarks = contentResponse.stream()
//											.filter(mark -> mark.getQuizType().equals("Practice Quiz"))
//											.mapToInt(PrincipalSubjectWisePerformanceResponseDto::getTotalMarks).sum();

									double unitQuizAvgAttempts = 0;
									double unitQuizAvgScore = 0;
									Integer unitQuizCount = 0;
									if (!CollectionUtils.isEmpty(unitChapters)) {
										for (String chapterId : unitChapters) {
											List<String> unitQuizList = contentResponse.stream()
													.filter(item -> "Unit Quiz".equals(item.getQuizType())
															&& chapterId.equals(item.getChapterId()))
													.map(PrincipalSubjectWisePerformanceResponseDto::getQuizId)
													.distinct().collect(Collectors.toList());

											LMSResponse<UnitPracticeQuizPerformanceMinResponseDto> unitLMSResponse = unitQuizList != null
													? studentFeignClient.getUnitQuizPerformance(boardId, schoolId,
															branchId, gradeId, unitQuizList, subject.getSubjectId(),
															sectionId, academicYearId, null)
													: null;

											UnitPracticeQuizPerformanceMinResponseDto studentUnitResponse = unitLMSResponse != null
													? unitLMSResponse.getData()
													: null;

											if (studentUnitResponse != null
													&& !ObjectUtils.isEmpty(studentUnitResponse)) {
												Integer unitTotalMarks = contentResponse.stream()
														.filter(item -> item.getQuizId()
																.equals(studentUnitResponse.getQuizId()))
														.map(PrincipalSubjectWisePerformanceResponseDto::getTotalMarks)
														.findFirst().orElse(0);

												long totalMarks = unitTotalMarks
														* studentUnitResponse.getStudentCount();

												unitQuizAvgAttempts += MathUtilitiess.calculatePercentage(
														studentUnitResponse.getStudentCount(), totalStudentCount);

												unitQuizAvgScore += MathUtilitiess.calculatePercentage(
														studentUnitResponse.getTotalObtainedMark(), totalMarks);
												unitQuizCount++;
											}
										}
									}
									AttemRateAndPercentageResponseDto practiceQuizAttempts = practiceQuizList != null
											? quizAttempRateAndPercentage(boardId, schoolId, branchId, gradeId,
													sectionId, practiceQuizList, academicYearId, null,
													totalStudentCount, subject.getSubjectId())
											: null;

//									QuizAttemptUQPQResponseDto attemptAvg = quizAttemptAndAverage(contentResponse,
//											quizObtained, totalStudentCount);

									String unitQuizAttemptsMsg = unitQuizAvgAttempts > 0
											? messageBasedOnQuizAttempt(unitQuizAvgAttempts / unitQuizCount)
											: null;
									String practiceQuizAttemptsMsg = practiceQuizAttempts != null
											&& practiceQuizAttempts.getAttemptPercentage() > 0
													? messageBasedOnQuizAttempt(
															practiceQuizAttempts.getAttemptPercentage())
													: null;

									Long practiceAttempt = practiceQuizAttempts != null
											&& practiceQuizAttempts.getAttemptPercentage() > 0
													? Math.round(practiceQuizAttempts.getAttemptPercentage())
													: 0L;

									subjectDto.setAvgAttemptPercentagePQ(practiceAttempt);
									subjectDto.setAvgAttemptPercentageUQ(
											unitQuizAvgAttempts > 0 ? Math.round(unitQuizAvgAttempts / unitQuizCount)
													: 0L);

									double percentage = unitQuizAvgScore != 0 && unitQuizCount > 0
											? unitQuizAvgScore / unitQuizCount
											: 0;
									subjectDto.setAvgScorePercentageUQ(
											unitQuizAvgAttempts > 0 ? Math.round(percentage) : 0L);
									subjectDto.setUnitAttemptRateMessage(unitQuizAttemptsMsg);
									subjectDto.setPracticeAttemptRateMessage(practiceQuizAttemptsMsg);
								}
							}
						}
						accessbleSubjectList.add(subjectDto);
					});
					response.setSubjectDetails(accessbleSubjectList);
				}
			}

			// setting the global average calculation
			List<SubjectWisePrincipalCoordinator> globalCalculations = pricipalCoordinatorSubjectWiseGlobal(teachers,
					gradeId, planId, academicYearId);
			if (!CollectionUtils.isEmpty(globalCalculations)
					&& !CollectionUtils.isEmpty(response.getSubjectDetails())) {
				for (SubjectWisePerformanceResponseDto subjectWise : response.getSubjectDetails()) {
					if (!CollectionUtils.isEmpty(subjectWise.getSubTopicDetails())) {
						for (SubTopicWisePerformanceResponseDto subTopicWise : subjectWise.getSubTopicDetails()) {
							SubjectWisePrincipalCoordinator subTopicGlobalCalci = globalCalculations.stream()
									.filter(item -> item.getGradeId().equals(gradeId)
											&& item.getSubjectId().equals(subjectWise.getSubjectId())
											&& !StringUtils.isBlank(item.getSubTopicId())
											&& item.getSubTopicId().equals(subTopicWise.getSubTopicId()))
									.findAny().orElse(null);
							if (subTopicGlobalCalci != null) {
								subTopicWise.setGlobalAvgScorePercentageUQ(
										MathUtilitiess.bigDecimalConvertion(subTopicGlobalCalci.getUqCumulative()!= null ? subTopicGlobalCalci.getUqCumulative(): null));
								subTopicWise.setGlobalAvgAttemptPercentageUQ(
										MathUtilitiess.bigDecimalConvertion(subTopicGlobalCalci.getUqAttempRate()));
										if(subTopicGlobalCalci.getPqAttempRate() != null){
											subTopicWise.setGlobalAvgAttemptPercentagePQ(
												MathUtilitiess.bigDecimalConvertion(subTopicGlobalCalci.getPqAttempRate()));
										}
								
							}
						}
					} else {
						// only for subject w/o sub-topic
						SubjectWisePrincipalCoordinator subjectGlobalCalci = globalCalculations.stream()
								.filter(item -> item.getGradeId().equals(gradeId)
										&& item.getSubjectId().equals(subjectWise.getSubjectId())
										&& StringUtils.isBlank(item.getSubTopicId()))
								.findAny().orElse(null);
						if (subjectGlobalCalci != null) {
							subjectWise.setGlobalAvgScorePercentageUQ(
									MathUtilitiess.bigDecimalConvertion(subjectGlobalCalci.getUqCumulative()));
							subjectWise.setGlobalAvgAttemptPercentageUQ(
									MathUtilitiess.bigDecimalConvertion(subjectGlobalCalci.getUqAttempRate()));
							subjectWise.setGlobalAvgAttemptPercentagePQ(
									MathUtilitiess.bigDecimalConvertion(subjectGlobalCalci.getPqAttempRate()));
						}
					}
				}
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.principal.grade.detailed.performance.failed", null));
		}
	}

	private QuizAttemptUQPQResponseDto quizAttemptAndAverage(
			List<PrincipalSubjectWisePerformanceResponseDto> contentResponse,
			List<QuizReleaseObtainedMarksResponseDto> quizObtained, Integer totalStudentCount) {
		try {
			Integer totalUnitQuizObtainedMarks = 0;
			Integer unitQuizTotalMarks = 0;
			int unitQuizCount = 0;
			int practiceQuizCount = 0;
			int unitStudentcount = 0;
			int averageUnitQuizAttempt = 0;
			int practiceStudentcount = 0;
			int averagePracticeQuizAttempt = 0;
			int totalUnitStudentCount = 0;
			int totalPracticeStudentCount = 0;

			for (PrincipalSubjectWisePerformanceResponseDto quiz : contentResponse) {
				if ("Unit Quiz".equalsIgnoreCase(quiz.getQuizType())) {

					List<QuizReleaseObtainedMarksResponseDto> studentQuiz = quizObtained.stream()
							.filter(student -> student.getQuizId().equals(quiz.getQuizId()))
							.collect(Collectors.toList());

					if (!CollectionUtils.isEmpty(studentQuiz)) {
						unitStudentcount = studentQuiz.stream()
								.filter(item -> item.getQuizId().equals(quiz.getQuizId()))
								.map(QuizReleaseObtainedMarksResponseDto::getStudentId).distinct()
								.collect(Collectors.toList()).size();

						totalUnitStudentCount += unitStudentcount;
						totalUnitQuizObtainedMarks += studentQuiz.stream()
								.mapToInt(QuizReleaseObtainedMarksResponseDto::getTotalObtainedMark).sum();
						unitQuizTotalMarks += quiz.getTotalMarks();
						unitQuizCount++;
					}
				} else {

					List<QuizReleaseObtainedMarksResponseDto> studentQuiz = quizObtained.stream()
							.filter(student -> student.getQuizId().equals(quiz.getQuizId()))
							.collect(Collectors.toList());

					if (!CollectionUtils.isEmpty(studentQuiz)) {
						practiceStudentcount = studentQuiz.stream()
								.filter(item -> item.getQuizId().equals(quiz.getQuizId()))
								.map(QuizReleaseObtainedMarksResponseDto::getStudentId).distinct()
								.collect(Collectors.toList()).size();
						totalPracticeStudentCount += practiceStudentcount;
						practiceQuizCount++;
					}
				}
			}

			averageUnitQuizAttempt = totalUnitStudentCount != 0 ? totalUnitStudentCount / unitQuizCount : 0;
			Integer unitQuizAttemptPercentage = MathUtilitiess.calculatePercentage(averageUnitQuizAttempt,
					totalStudentCount);

			averagePracticeQuizAttempt = totalPracticeStudentCount != 0 ? totalPracticeStudentCount / practiceQuizCount
					: 0;
			int unitPracticeAttemptPercentage = MathUtilitiess.calculatePercentage(averagePracticeQuizAttempt,
					totalStudentCount);

			Integer quizAveragePercentage = MathUtilitiess.calculatePercentage(totalUnitQuizObtainedMarks,
					unitQuizTotalMarks);

			return new QuizAttemptUQPQResponseDto(unitQuizAttemptPercentage, unitPracticeAttemptPercentage,
					quizAveragePercentage);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.principal.grade.detailed.performance.failed", null));
		}
	}

	private String messagesBasedOnQuizAttempts(Long quizAttempt) {
		try {
			String message = null;
			if (quizAttempt.intValue() >= 60 && quizAttempt.intValue() <= 100)
				message = StudentEncourageMessages.QUIZ_ATTEMPT_HIGHER.getMessage();
			else
				message = StudentEncourageMessages.QUIZ_ATTEMPT_LOWER.getMessage();
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("kudos.message.config.failed", null));
		}
	}

	/**
	 * To find the global score average, global attempt rate of Unit/Practice
	 * Quizzes for each subject for principal and coordinator.
	 * 
	 * @param teachers
	 * @param gradeId
	 * @param planId
	 * @param academicYearId
	 * @return
	 */
	private List<SubjectWisePrincipalCoordinator> pricipalCoordinatorSubjectWiseGlobal(Teachers teachers,
			String gradeId, String planId, String academicYearId) {
		try {
			List<SubjectWisePrincipalCoordinator> response = new ArrayList<>();
			String boardId = teachers.getBranches().getBoardId();

			// find the matching institution by board, grade and branch is not equal to the
			// login staff's
			List<InstitutionStudentCountResponseDto> institutionAndTotalStudents = schoolRepo
					.findInstitutionWithFilters(gradeId, boardId);

			// find the subjects(sub-topics) under the given grade and plan.(Master-Service
			// feign)
			LMSResponse<List<SubjectsSubtopicUnderGradeModel>> subjectSubTopicResponse = mastersFeignClient
					.getTheSubjectsAndSimilarPlans(Arrays.asList(gradeId), planId);
			List<SubjectsSubtopicUnderGradeModel> subjectsSubTopics = subjectSubTopicResponse != null
					&& !CollectionUtils.isEmpty(subjectSubTopicResponse.getData()) ? subjectSubTopicResponse.getData()
							: null;

			if (!CollectionUtils.isEmpty(institutionAndTotalStudents)) {
				// go to the content-service by grade, school, branch, academic year find the UQ
				// & PQ for each subject(sub-topics)
				if (!CollectionUtils.isEmpty(subjectsSubTopics)) {
					for (SubjectsSubtopicUnderGradeModel subjectSubTopic : subjectsSubTopics) {
						institutionAndTotalStudents.forEach(item -> {
							if (item.getTotalStudents() > 0) {
								item.setGradeId(gradeId);
								item.setBoardId(boardId);
								item.setAcademicYearId(academicYearId);
								item.setSubjectAndSubTopic(subjectSubTopic.getSubjectAndSubTopic());
							}
						});
					}
				}

				/*
				 * A feign call chain is starting user-service via content-service to
				 * student-service. Content-service will produces released quizzes and
				 * student-service will calculate the score and attempt rate.
				 */
				LMSResponse<List<SubjectWisePrincipalCoordinator>> contentService = contentFeignClient
						.getTheGlobalScoreAverageAndAttempRate(institutionAndTotalStudents);
				response = contentService != null && !CollectionUtils.isEmpty(contentService.getData())
						? contentService.getData()
						: null;
			} else
				log.info("No similar schoool/branch found, end of the global calculation");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculate.subject.wise.principal.coordinator.failed", null));
		}
	}

	/**
	 * Global average in Teacher Dashboard, mainly based on the Unit Quizzes only.
	 * Return global average, UQ/PQ global attempt rate
	 * 
	 * @param teachers
	 * @param academicYearId
	 */
	private List<TeacherGlobalAvgGradeWise> forTeachersGradeWiseGlobalAverage(Teachers teachers, String academicYearId,
			String gradeId) {
		try {
			List<TeacherGlobalAvgGradeWise> gradeWiseResponse = null;

			String boardId = teachers.getBranches().getBoardId();
			// find the assigned subjects, subtopic and grade for teacher
			List<TeacherAssignProjection> assignedPremisses = assignTeacherRepo.findAllAccessOfTeacher(teachers.getId(),
					gradeId);
			HashMap<String, List<String>> gradeSubjectMap = new HashMap<>();
			if (!CollectionUtils.isEmpty(assignedPremisses)) {
				for (TeacherAssignProjection assigned : assignedPremisses) {
					if (!gradeSubjectMap.containsKey(assigned.getGradeId())) {
						// If grade not exist add new one with List<Subjects>
						List<String> subjectIds = new ArrayList<>();
						subjectIds.add(assigned.getSubjectId());
						gradeSubjectMap.put(assigned.getGradeId(), subjectIds);
					} else // already exist update with subjectId
						gradeSubjectMap.get(assigned.getGradeId()).add(assigned.getSubjectId());
				}
			}

			// find similar institutes by board, grade and not equal to branch
			List<String> gradeIds = assignedPremisses.stream().map(TeacherAssignProjection::getGradeId).distinct()
					.collect(Collectors.toList());

			List<StudentAssignedMinDetailResponseDto> studentsDetails = studentsRepo
					.findAllStudentsByTheFilters(boardId, gradeIds);

			// if Students exist find the quizzes released for each grade for the subjects,
			// sub_topic(optional) by academicYearId
			List<InstituteQuizzesModel> contentResponse = null;
			List<InstituteQuizzesModel> pqContentResponse = null;
			if (!CollectionUtils.isEmpty(studentsDetails)) {
				List<String> subjectIds = assignedPremisses.stream().map(TeacherAssignProjection::getSubjectId)
						.distinct().collect(Collectors.toList());

				// find the released unit quizzes from content-service
//				List<InstituteQuizzesModel> contentRequest = studentsDetails.stream()
//						.map(stdItem -> new InstituteQuizzesModel(stdItem.getBoardId(), stdItem.getSchoolId(),
//								stdItem.getBranchId(), stdItem.getGradeId(), stdItem.getSectionId(), null, null,
//								"Unit Quiz", null))
//						.distinct().collect(Collectors.toList());
//				if (!CollectionUtils.isEmpty(contentRequest)) {					
//					Set<String> uniqueSubjectIds = new HashSet<>();
//					for (InstituteQuizzesModel requestModel : contentRequest) {
//						
//						String subjectId=null;
//						// Loop through assignedPremisses to find unique subjectIds
//						 for (TeacherAssignProjection assigned : assignedPremisses) {
//							 if (assigned.getGradeId().equals(requestModel.getGradeId())) {
//								 String potentialSubjectId = assigned.getSubjectId();
//								 if (!uniqueSubjectIds.contains(potentialSubjectId)) {
//						                // This is a unique subjectId, set it in the requestModel
//						                subjectId = potentialSubjectId;
//						                uniqueSubjectIds.add(subjectId); // Add to the set to avoid duplicates
//						                break; // Exit the loop once a unique subjectId is found
//						            }								
//							 }							 							 
//						 }			
//						requestModel.setSubjectId(subjectId);
//						Set<String> uniqueSubtopicIds = new HashSet<>();
////						String subTopicId = !StringUtils.isBlank(subjectId) ? assignedPremisses.stream()
////								.filter(item -> item.getGradeId().equals(requestModel.getGradeId())
////										&& item.getSubjectId().equals(subjectId))
////								.findAny().orElse(null).getSubTopicId() : null;
//						String subtopicId = null;
//						 for (TeacherAssignProjection assigned : assignedPremisses) {
//								if (assigned.getGradeId().equals(requestModel.getGradeId())
//										&& assigned.getSubjectId().equals(requestModel.getSubjectId())) {
//									 String potentialSubtopicId = assigned.getSubTopicId();
//								 if (!uniqueSubtopicIds.contains(potentialSubtopicId)) {
//						                // This is a unique subjectId, set it in the requestModel
//						                subtopicId = potentialSubtopicId;
//						                uniqueSubtopicIds.add(subtopicId); // Add to the set to avoid duplicates
//						                break; // Exit the loop once a unique subjectId is found
//						            }								
//							 }							 							 
//						 }
//											
//						requestModel.setSubTopicId(subtopicId);
//						requestModel.setAcademicYearId(academicYearId);
//
//					}
				List<InstituteQuizzesModel> contentRequest = new ArrayList<>();
				List<InstituteQuizzesModel> pqContentRequest = new ArrayList<>();
				for (String subjectId : subjectIds) {
					List<String> subtopicIds = assignedPremisses.stream()
							.filter(item -> item.getSubjectId().equals(subjectId))
							.map(TeacherAssignProjection::getSubTopicId).filter(Objects::nonNull).distinct()
							.collect(Collectors.toList());

					Iterator<String> subtopicIterator = subtopicIds.iterator();

					for (StudentAssignedMinDetailResponseDto stdItem : studentsDetails) {
						InstituteQuizzesModel requestModel = new InstituteQuizzesModel(stdItem.getBoardId(),
								stdItem.getSchoolId(), stdItem.getBranchId(), stdItem.getGradeId(),
								stdItem.getSectionId(), null, null, "Unit Quiz", null);
						requestModel.setSubjectId(subjectId);

						InstituteQuizzesModel requestPqModel = new InstituteQuizzesModel(stdItem.getBoardId(),
								stdItem.getSchoolId(), stdItem.getBranchId(), stdItem.getGradeId(),
								stdItem.getSectionId(), null, null, "Practice Quiz", null);
						requestModel.setSubjectId(subjectId);
						requestPqModel.setSubjectId(subjectId);

						// Find subtopic for the subject (if available)
						if (subtopicIterator.hasNext()) {
							String subtopicId = subtopicIterator.next();
							requestModel.setSubTopicId(subtopicId);
							requestPqModel.setSubTopicId(subtopicId);
						} else {
							// Reset the iterator if it's empty and start from the first subtopic
							subtopicIterator = subtopicIds.iterator();
							if (subtopicIterator.hasNext()) {
								String subtopicId = subtopicIterator.next();
								requestModel.setSubTopicId(subtopicId);
								requestPqModel.setSubTopicId(subtopicId);
							}
						}

						requestModel.setAcademicYearId(academicYearId);
						requestPqModel.setAcademicYearId(academicYearId);
						contentRequest.add(requestModel);
						pqContentRequest.add(requestPqModel);
					}

				}

				if (!CollectionUtils.isEmpty(contentRequest)) {
					// request body is set for the content service to get the Released Unit Quizzes
					LMSResponse<List<InstituteQuizzesModel>> contentResponseBody = contentFeignClient
							.forSelectedInstitutes(contentRequest);
					contentResponse = contentResponseBody != null
							&& !CollectionUtils.isEmpty(contentResponseBody.getData()) ? contentResponseBody.getData()
									: null;
				}
				// ============= Request for Practice Quiz started =============//
//				List<InstituteQuizzesModel> pqContentRequest = studentsDetails.stream()
//						.map(stdItem -> new InstituteQuizzesModel(stdItem.getBoardId(), stdItem.getSchoolId(),
//								stdItem.getBranchId(), stdItem.getGradeId(), stdItem.getSectionId(), null, null,
//								"Practice Quiz", null))
//						.distinct().collect(Collectors.toList());
//
//				if (!CollectionUtils.isEmpty(pqContentRequest)) {
//					// set the subject and subtopic for find practice quiz
//					for (InstituteQuizzesModel requestModel : pqContentRequest) {
//						String pqSubjectId = assignedPremisses.stream()
//								.filter(item -> item.getGradeId().equals(requestModel.getGradeId())).findAny()
//								.orElse(null).getSubjectId();
//						requestModel.setSubjectId(pqSubjectId);
//
//						String pqSubTopicId = !StringUtils.isBlank(pqSubjectId) ? assignedPremisses.stream()
//								.filter(item -> item.getGradeId().equals(requestModel.getGradeId())
//										&& item.getSubjectId().equals(pqSubjectId))
//								.findAny().orElse(null).getSubTopicId() : null;
//						requestModel.setSubTopicId(pqSubTopicId);
//						requestModel.setAcademicYearId(academicYearId);
//					}

				// request body is set for the content service to get the Released Practice
				// Quizzes
				if (!CollectionUtils.isEmpty(pqContentRequest)) {
					LMSResponse<List<InstituteQuizzesModel>> pqContentResponseBody = contentFeignClient
							.forSelectedInstitutes(pqContentRequest);
					pqContentResponse = pqContentResponseBody != null
							&& !CollectionUtils.isEmpty(pqContentResponseBody.getData())
									? pqContentResponseBody.getData()
									: null;
				}
				// ==== End of retrieving the PRactice Quizzes =====//
			}

			// Go to student-service, based on student id find out the obtained mark of UQ
			List<StudentsWithQuizzes> obtainedMarkList = null;
			if (!CollectionUtils.isEmpty(contentResponse) && !CollectionUtils.isEmpty(studentsDetails)) {
				List<StudentsWithQuizzes> requestToStudentService = new ArrayList<>();

				for (InstituteQuizzesModel institute : contentResponse) {
					List<StudentsWithQuizzes> studentsForInstitute = studentsDetails.stream()
							.filter(item -> institute.getGradeId().equals(item.getGradeId())
									&& !StringUtils.isBlank(institute.getSubjectId()))
							.map(mapItem -> new StudentsWithQuizzes(mapItem.getId(), institute.getQuizId(),
									institute.getTotalMark(), 0, academicYearId))
							.distinct().collect(Collectors.toList());
					requestToStudentService.addAll(studentsForInstitute);
				}

				// to find the unit quizzes attempted by students
				if (!CollectionUtils.isEmpty(requestToStudentService)) {
					LMSResponse<List<StudentsWithQuizzes>> studentResponse = studentFeignClient
							.obtainedMarkOfSelectedStudents(requestToStudentService);
					obtainedMarkList = studentResponse != null && !CollectionUtils.isEmpty(studentResponse.getData())
							? studentResponse.getData()
							: null;
				}
			}

			// == Student-Service to find the Practice quiz attempt started ==//
			List<StudentsWithQuizzes> pqObtainedMarkList = null;
			if (!CollectionUtils.isEmpty(pqContentResponse) && !CollectionUtils.isEmpty(studentsDetails)) {
				List<StudentsWithQuizzes> pqRequestToStudentService = new ArrayList<>();
				for (InstituteQuizzesModel institute : pqContentResponse) {
					List<StudentsWithQuizzes> pqRequestToStudent = studentsDetails.stream()
							.filter(item -> institute.getGradeId().equals(item.getGradeId())
									&& !StringUtils.isBlank(institute.getSubjectId()))
							.map(mapItem -> new StudentsWithQuizzes(mapItem.getId(), institute.getQuizId(),
									institute.getTotalMark(), 0, academicYearId))
							.distinct().collect(Collectors.toList());
					pqRequestToStudentService.addAll(pqRequestToStudent);
				}

				// to find the Practice quizzes attempted by students
				if (!CollectionUtils.isEmpty(pqRequestToStudentService)) {
					LMSResponse<List<StudentsWithQuizzes>> studentResponse = studentFeignClient
							.obtainedMarkOfSelectedStudents(pqRequestToStudentService);
					pqObtainedMarkList = studentResponse != null && !CollectionUtils.isEmpty(studentResponse.getData())
							? studentResponse.getData()
							: null;
				}
			}
			// ======== Student-Service to find the Practice quiz attempt end ==========//

			// Calculating the global avg & UQ attempt rate is finding out.
			// contentResponse : has grade, subject/subtopic & quizId (not studentId)
			// studentsDetails : no subject details(has student Id & grade)
			// obtainedMarkList : has quizId, studentId
			gradeWiseResponse = !CollectionUtils.isEmpty(contentResponse)
					? contentResponse.stream().map(item -> new TeacherGlobalAvgGradeWise(item.getGradeId(),
							item.getSubjectId(), item.getSubTopicId())).distinct().collect(Collectors.toList())
					: null;

			if (!CollectionUtils.isEmpty(gradeWiseResponse)
					&& (!CollectionUtils.isEmpty(obtainedMarkList) || !CollectionUtils.isEmpty(pqObtainedMarkList))) {
				for (TeacherGlobalAvgGradeWise response : gradeWiseResponse) {

					// == Global Practice Quiz release & attempt rate started//
					if (!CollectionUtils.isEmpty(pqContentResponse)) {
						// Released PQ:
						List<String> pqGloballyReleased = !StringUtils.isBlank(response.getSubTopicId())
								? pqContentResponse.stream()
										.filter(pq -> pq.getGradeId().equals(response.getGradeId())
												&& pq.getSubjectId().equals(response.getSubjectId())
												&& !StringUtils.isBlank(pq.getSubTopicId())
												&& pq.getSubTopicId().equals(response.getSubTopicId()))
										.map(InstituteQuizzesModel::getQuizId).distinct().collect(Collectors.toList())
								: pqContentResponse.stream()
										.filter(pq -> pq.getGradeId().equals(response.getGradeId())
												&& pq.getSubjectId().equals(response.getSubjectId()))
										.map(InstituteQuizzesModel::getQuizId).distinct().collect(Collectors.toList());
						// Attempted PQ:
						List<String> pqGlobalAttemps = !CollectionUtils.isEmpty(pqGloballyReleased)
								&& !CollectionUtils.isEmpty(pqObtainedMarkList)
										? pqObtainedMarkList.stream()
												.filter(obtain -> pqGloballyReleased.contains(obtain.getQuizId()))
												.map(StudentsWithQuizzes::getQuizId).distinct().collect(
														Collectors.toList())
										: null;

						if (!CollectionUtils.isEmpty(pqGloballyReleased) && !CollectionUtils.isEmpty(pqGlobalAttemps)) {
							List<StudentsWithQuizzes> pqObtainedMarkLists = pqObtainedMarkList.stream()
									.filter(item -> pqGlobalAttemps.contains(item.getQuizId()))
									.collect(Collectors.toList());

							double percentage = ((double) pqObtainedMarkLists.size()
									/ (studentsDetails.size() * pqGloballyReleased.size())) * 100;

//							double percentage = ((double) pqGlobalAttemps.size() / pqGloballyReleased.size()) * 100;
							BigDecimal pqGlobalAvgAttempt = percentage > 0
									? MathUtilitiess.roundUpHalfUsingBigDecimal(percentage, 2)
									: BigDecimal.ZERO;
							response.setPqGlobalAvgAttempt(pqGlobalAvgAttempt);
						} else if (!CollectionUtils.isEmpty(pqGloballyReleased))
							response.setPqGlobalAvgAttempt(BigDecimal.ZERO);
					}
					// == Global Practice Quiz release & attempt rate ended==//

					// == Globally released UQ started ==== //
					List<String> uqGloballyReleased = !StringUtils.isBlank(response.getSubTopicId())
							? contentResponse.stream()
									.filter(uq -> uq.getGradeId().equals(response.getGradeId())
											&& uq.getSubjectId().equals(response.getSubjectId())
											&& !StringUtils.isBlank(uq.getSubTopicId())
											&& uq.getSubTopicId().equals(response.getSubTopicId()))
									.map(InstituteQuizzesModel::getQuizId).distinct().collect(Collectors.toList())
							: contentResponse.stream()
									.filter(uq -> uq.getGradeId().equals(response.getGradeId())
											&& uq.getSubjectId().equals(response.getSubjectId()))
									.map(InstituteQuizzesModel::getQuizId).distinct().collect(Collectors.toList());

					// ==== count of UQ attempt globally started ===//
					List<String> uqGlobalAttemps = !CollectionUtils.isEmpty(uqGloballyReleased) ? obtainedMarkList
							.stream().filter(obtain -> uqGloballyReleased.contains(obtain.getQuizId()))
							.map(StudentsWithQuizzes::getQuizId).distinct().collect(Collectors.toList()) : null;

					// Calculate the global attempt Rate of subject/subtopic
					if (!CollectionUtils.isEmpty(uqGloballyReleased) && !CollectionUtils.isEmpty(uqGlobalAttemps)) {
						List<StudentsWithQuizzes> obtainedMarkLists = obtainedMarkList.stream()
								.filter(item -> uqGlobalAttemps.contains(item.getQuizId()))
								.collect(Collectors.toList());

						double percentage = ((double) obtainedMarkLists.size()
								/ (studentsDetails.size() * uqGloballyReleased.size())) * 100;

//						double percentage = ((double) uqGlobalAttemps.size() / uqGloballyReleased.size()) * 100;
						BigDecimal uqGlobalAvgAttempt = percentage > 0
								? MathUtilitiess.roundUpHalfUsingBigDecimal(percentage, 2)
								: null;
						response.setUqGlobalAvgAttempt(uqGlobalAvgAttempt);
					} else if (!CollectionUtils.isEmpty(uqGloballyReleased))
						response.setUqGlobalAvgAttempt(BigDecimal.ZERO);

					// ==== end global attempt rate of UQ =====//

					// listing the students under the grade

//					List<String> obtainedMarkStudentIds = obtainedMarkList.stream()
//							.map(StudentsWithQuizzes::getStudentId).collect(Collectors.toList());

					List<String> quizIds = contentResponse.stream()
							.filter(item -> Objects.equals(item.getSubjectId(), response.getSubjectId())
									&& Objects.equals(item.getSubTopicId(), response.getSubTopicId()))
							.map(InstituteQuizzesModel::getQuizId).distinct().collect(Collectors.toList());

//					List<String> studentIds = studentsDetails.stream()
//							.filter(stdDto -> stdDto.getGradeId().equals(response.getGradeId()))
//							.filter(stdDto -> obtainedMarkStudentIds.contains(stdDto.getId()))						
//							.map(StudentAssignedMinDetailResponseDto::getId).distinct().collect(Collectors.toList());
//					int numberOfStudents = studentIds.size();

					// find the mark of each student

//					// find the global average of that grade
//					double average = sum > 0 ? sum / numberOfStudents : 0;
//					BigDecimal globalAverage = average > 0 ? MathUtilitiess.roundUpHalfUsingBigDecimal(average, 2)
//							: null;
//					response.setGlobalAvg(globalAverage);

					double totalSum = 0;
					int quizCount = 0;
					for (String quizId : quizIds) {

						List<String> studentIds = obtainedMarkList.stream()
								.filter(item -> quizId.equals(item.getQuizId())).map(StudentsWithQuizzes::getStudentId)
								.filter(studentId -> studentsDetails.stream()
										.anyMatch(stdDto -> stdDto.getId().equals(studentId)
												&& stdDto.getGradeId().equals(response.getGradeId())))
								.collect(Collectors.toList());

						int numberOfStudents = studentIds.size();
						double sum = 0;
						if (!CollectionUtils.isEmpty(studentIds)) {
							for (String student : studentIds) {
								long totalObtained = obtainedMarkList.stream()
										.filter(item -> item.getStudentId().equals(student))
										.filter(item -> quizId.equals(item.getQuizId()))
										.mapToLong(StudentsWithQuizzes::getTotalObtainedMark).sum();

								long totalQuizMark = obtainedMarkList.stream()
										.filter(item -> item.getStudentId().equals(student))
										.filter(item -> quizId.equals(item.getQuizId()))
										.mapToLong(StudentsWithQuizzes::getTotalMark).sum();

								double eachStdPercentage = totalObtained > 0 && totalQuizMark > 0
										? ((double) totalObtained / totalQuizMark) * 100
										: 0;
								sum += eachStdPercentage;
							}
							totalSum += numberOfStudents > 0 ? sum / numberOfStudents : 0;
							quizCount++;
						}
					}

					double average = totalSum > 0 ? totalSum / quizCount : 0;
					BigDecimal globalAverage = average > 0 ? MathUtilitiess.roundUpHalfUsingBigDecimal(average, 2)
							: BigDecimal.ZERO;
					response.setGlobalAvg(globalAverage);
				}
			}
			return !CollectionUtils.isEmpty(gradeWiseResponse) ? gradeWiseResponse : null;
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculation.grade.wise.failes", null));
		}
	}

	/**
	 * Based on selected grade, subject or subtopic find the chapters and calculate
	 * the global-average. Here Unit Quiz is considering.
	 * 
	 * @param teachers
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @param academicYearId
	 * @return
	 */
	private List<ChapterQuizModel> forTeachersChapterWise(Teachers teachers, String gradeId, String subjectId,
			String subTopicId, String academicYearId) {
		try {
			List<ChapterQuizModel> chapterDetails = new ArrayList<>();

			String boardId = teachers.getBranches().getBoardId();
			HashMap<String, List<String>> gradeSubjectMap = new HashMap<>();
			gradeSubjectMap.put(gradeId, Arrays.asList(subjectId));

			// based on the plans find the students and their institute
			// Previously we are not considering current branch while calculating global
			// calculation.
//			List<StudentAssignedMinDetailResponseDto> studentsDetails = studentsRepo
//					.findAllStudentsByTheFilters(boardId, Arrays.asList(gradeId), teachers.getBranches().getId());

			// Now we are considering current branch also while calculating global
			// calculation.
			List<StudentAssignedMinDetailResponseDto> studentsDetails = studentsRepo
					.findAllStudentsByTheFilters(boardId, Arrays.asList(gradeId));

			// find ending chapter of each schools
			List<InstituteQuizzesModel> quizChapterDetails = null;
			if (!CollectionUtils.isEmpty(studentsDetails)) {
				// find the released unit quizzes from content-service
				List<InstituteQuizzesModel> teacherRequest = studentsDetails.stream()
						.map(stdItem -> new InstituteQuizzesModel(boardId, stdItem.getSchoolId(), stdItem.getBranchId(),
								gradeId, null, subjectId, subTopicId, "Unit Quiz", academicYearId))
						.distinct().collect(Collectors.toList());

				if (!CollectionUtils.isEmpty(teacherRequest)) {
					// request body is set for the content service to get the Released Unit Quizzes
					LMSResponse<List<InstituteQuizzesModel>> teacherResponse = teacherFeignClient
							.getTheTeachingCompletedChapter(teacherRequest);
					quizChapterDetails = teacherResponse != null && !CollectionUtils.isEmpty(teacherResponse.getData())
							? teacherResponse.getData()
							: null;
				}
			}

			// Go to student-service, based on student id find out the obtained mark of UQ
			List<StudentsWithQuizzes> obtainedMarkList = null;
			if (!CollectionUtils.isEmpty(quizChapterDetails) && !CollectionUtils.isEmpty(studentsDetails)) {
				List<StudentsWithQuizzes> requestToStudentService = new ArrayList<>();

				for (InstituteQuizzesModel institute : quizChapterDetails) {
					List<StudentsWithQuizzes> requestToStudent = studentsDetails.stream()
							.filter(item -> institute.getGradeId().equals(item.getGradeId())
									&& !StringUtils.isBlank(institute.getSubjectId()))
							.map(mapItem -> new StudentsWithQuizzes(mapItem.getId(), institute.getQuizId(),
									institute.getTotalMark(), 0, academicYearId))
							.distinct().collect(Collectors.toList());
					requestToStudentService.addAll(requestToStudent);
				}

				// to find the unit quizzes attempted by students
				if (!CollectionUtils.isEmpty(requestToStudentService)) {
					LMSResponse<List<StudentsWithQuizzes>> studentResponse = studentFeignClient
							.obtainedMarkOfSelectedStudents(requestToStudentService);
					obtainedMarkList = studentResponse != null && !CollectionUtils.isEmpty(studentResponse.getData())
							? studentResponse.getData()
							: null;
				}
			}

			// contain chapter and list of Quizzes
			if (!CollectionUtils.isEmpty(quizChapterDetails) && !CollectionUtils.isEmpty(obtainedMarkList)) {
				List<String> chapters = quizChapterDetails.stream().map(InstituteQuizzesModel::getChapterId).distinct()
						.collect(Collectors.toList());

				for (String chapterId : chapters) {
					List<String> quizIds = quizChapterDetails.stream()
							.filter(item -> item.getChapterId().equals(chapterId)).map(InstituteQuizzesModel::getQuizId)
							.distinct().collect(Collectors.toList());

					List<StudentsWithQuizzes> studentsQuiz = obtainedMarkList.stream()
							.filter(item -> quizIds.contains(item.getQuizId())).distinct().collect(Collectors.toList());

					// find the global average
					double netQuizScore = 0;
					for (String quizId : quizIds) {
						double eachQuizAllStudentsMark = !CollectionUtils.isEmpty(studentsQuiz)
								? studentsQuiz.stream().filter(item -> item.getQuizId().equals(quizId))
										.mapToDouble(StudentsWithQuizzes::getTotalObtainedMark).sum()
								: 0;

						double totalMarksEachQuiz = !CollectionUtils.isEmpty(studentsQuiz)
								? studentsQuiz.stream().filter(item -> item.getQuizId().equals(quizId))
										.mapToDouble(StudentsWithQuizzes::getTotalMark).sum()
								: 0;
						netQuizScore += eachQuizAllStudentsMark > 0 && totalMarksEachQuiz > 0
								? (eachQuizAllStudentsMark / totalMarksEachQuiz) * 100
								: 0;
					}
					double netQuizAverage = netQuizScore > 0 ? netQuizScore / quizIds.size() : 0;
					BigDecimal globalAverage = netQuizAverage > 0
							? MathUtilitiess.roundUpHalfUsingBigDecimal(netQuizAverage, 2)
							: null;
					chapterDetails.add(new ChapterQuizModel(chapterId, globalAverage));
				}
			}
			return !CollectionUtils.isEmpty(chapterDetails) ? chapterDetails : null;
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculation.chapter.wise.failes", null));
		}
	}

	@Override
	public long getGlobalStudentCount(String boardId, String gradeId) {
		try {
			return studentsRepo.globalStudentsCount(boardId, gradeId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("global.calculation.chapter.wise.failes", null));
		}
	}

	@Override
	public GradeSubjectScoreResponseDto getChapterWiseQuizPerformanceDashboard(String teacherId, String gradeId,
			String subjectId) {
		try {
			
			Long currentTime = new Date().getTime();
			log.info("getChapterWiseQuizPerformanceDashboard start old: {}", currentTime);
			
			Teachers teacher = (teacherId != null) ? teacherRepo.getById(teacherId)
					: teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());
			// if (AcademicStaffProfile.TEACHER != teacher.getAcademicStaffProfile())
			// throw new USException(ErrorCodes.UNAUTHORIZED,
			// Translator.toLocale("teacher.not.valid", null));
			log.debug(teacher + "teacher");
			if (teacher == null) {
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));
			}

			String schoolId = teacher.getSchools().getId();
			String school = teacher.getSchools().getName();
			String branchId = teacher.getBranches().getId();
			String branch = teacher.getBranches().getName();
			String boardId = teacher.getBranches().getBoardId();
			GradeSubjectScoreResponseDto response = null;

			log.debug(schoolId + " " + branchId + " " + " " + boardId + " " + " " + " " + " " + " ");

			// Master feign call for board, academicYear and grade.
			LMSResponse<BoardsResponseDto> boardLMSResponse = mastersFeignClient.getBoardsById(boardId);
			BoardsResponseDto boardResponse = boardLMSResponse != null ? boardLMSResponse.getData() : null;
			log.debug(boardResponse + "boardResponse");
			LMSResponse<AcademicYearResponseDto> academicLMSResponse = mastersFeignClient.getLatestAcademicYear();
			AcademicYearResponseDto academicResponse = academicLMSResponse != null ? academicLMSResponse.getData()
					: null;
			log.debug(academicResponse + "academicResponse");
			LMSResponse<GradesResponseDto> garedLMSResponse = mastersFeignClient.getGradesById(gradeId);
			GradesResponseDto gradeResponse = garedLMSResponse != null ? garedLMSResponse.getData() : null;
			log.debug(gradeResponse + "gradeResponse");
			LMSResponse<SubjectsResponseDto> subjectLMSResponse = mastersFeignClient.getSubjectsById(subjectId);
			SubjectsResponseDto subjectResponse = subjectLMSResponse != null ? subjectLMSResponse.getData() : null;
			log.debug(subjectId + "subjectId");

			log.debug(subjectResponse + "subjectResponse");

			List<String> assignedSections;
			if (teacherId == null) {
				log.debug("======inside if=======" + gradeId, branchId, schoolId);
				assignedSections = gradeSectionMappingRepository.findSections(gradeId, branchId, schoolId);
			} else {
				assignedSections = assignTeacherRepo.getAllSectionsByGradeAndTeacherId(teacherId, gradeId);
			}
			
			LMSResponse<String> masterAcademicLmsResponse = mastersFeignClient.getLatestAcademicYearId();
			String academicYearId = masterAcademicLmsResponse != null ? masterAcademicLmsResponse.getData() : null;

			
			LMSResponse<PaginatedResponse<ChapterTrackingResponseDto>> responseDetails = null;
			List<ChapterScoreResponseDto> filteredChapters = new ArrayList<>();
			for (String sectionId : assignedSections) {
				responseDetails = teacherFeignClient.getChapterTracking(0, 1000, true, boardId, schoolId, branchId,
						teacher.getId(), gradeId, subjectId, null, sectionId,academicYearId, null);

				PaginatedResponse<ChapterTrackingResponseDto> trackingResponse = Optional.ofNullable(responseDetails)
						.map(LMSResponse::getData).orElseGet(() -> {
							PaginatedResponse<ChapterTrackingResponseDto> emptyResponse = new PaginatedResponse<>();
							emptyResponse.setData(Collections.emptyList());
							return emptyResponse;
						});
				LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(boardId,
						schoolId, branchId, gradeId, null, subjectId, null, academicResponse.getId());
				List<String> teacherResponse = Optional.ofNullable(teacherLMSResponse).map(LMSResponse::getData)
						.orElse(null);

				log.debug(teacherResponse + "teacherResponse");
				List<QuizChapterTotalMarksResponseDto> quizResponse = new ArrayList<>();
				if (!CollectionUtils.isEmpty(teacherResponse)) {
					LMSResponse<List<QuizChapterTotalMarksResponseDto>> contentLMSResponse = contentFeignClient
							.getQuizzesTotalMarksCompletedChapter(boardId, schoolId, branchId, gradeId, null, subjectId,
									null, academicResponse.getId(), teacherResponse, "Unit Quiz");
					quizResponse = Optional.ofNullable(contentLMSResponse).map(LMSResponse::getData)
							.orElse(Collections.emptyList());
					log.debug(quizResponse + "quizResponse");
				}
				// Extract the list of ChapterTrackingResponseDto items
				List<ChapterTrackingResponseDto> chapterTrackingList = trackingResponse.getData();

				for (ChapterTrackingResponseDto chapterTrackingResponseDto : chapterTrackingList) {

					ChapterScoreResponseDto chapterTrackingWithQuizResponseDto = new ChapterScoreResponseDto();
					// BeanUtils.copyProperties(chapterTrackingWithQuizResponseDto,
					// chapterTrackingResponseDto);
					chapterTrackingWithQuizResponseDto
							.setTeacherName(teacher.getFirstName() + " " + teacher.getLastName());
					chapterTrackingWithQuizResponseDto.setChapterId(chapterTrackingResponseDto.getChapterId());
					chapterTrackingWithQuizResponseDto.setChapter(chapterTrackingResponseDto.getChapter());
					chapterTrackingWithQuizResponseDto.setTeacherId(teacher.getId());
					chapterTrackingWithQuizResponseDto.setSectionId(chapterTrackingResponseDto.getSectionId());
					chapterTrackingWithQuizResponseDto.setSection(chapterTrackingResponseDto.getSection());
					chapterTrackingWithQuizResponseDto
							.setStartDate(CommonUtilities.formatLocalDate(chapterTrackingResponseDto.getStartDate()));
					chapterTrackingWithQuizResponseDto
							.setEndDate(CommonUtilities.formatLocalDate(chapterTrackingResponseDto.getEndDate()));
					;

					if (!CollectionUtils.isEmpty(quizResponse)) {
						List<QuizChapterTotalMarksResponseDto> chapterResponse = quizResponse.stream()
								.filter(item -> item.getChapterId().equals(chapterTrackingResponseDto.getChapterId()))
								.distinct().collect(Collectors.toList());

						List<String> quizIds = chapterResponse.stream().map(QuizChapterTotalMarksResponseDto::getQuizId)
								.distinct().collect(Collectors.toList());

						LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> unitLMSResponse = studentFeignClient
								.getUnitQuizzesAndTotalMarks(boardId, schoolId, branchId, gradeId, null, subjectId,
										academicResponse.getId(), quizIds);
						List<QuizReleaseObtainedMarksResponseDto> unitResponse = unitLMSResponse != null
								? unitLMSResponse.getData()
								: null;

						try {
							LMSResponse<QuestionWisePerformanceResponseDto> avgDetails = teacherFeignClient
									.getQustionWiseQuizPerformance(teacher.getId(), schoolId, branchId, boardId,
											gradeId, chapterTrackingResponseDto.getSectionId(), subjectId, null,
											chapterTrackingResponseDto.getChapterId(),
											academicYearId);
							QuestionWisePerformanceResponseDto classQuizScore = Optional.ofNullable(avgDetails)
									.map(LMSResponse::getData).orElse(null);

							if (classQuizScore != null) {
								Long classAverage = Optional.ofNullable(classQuizScore)
										.map(QuestionWisePerformanceResponseDto::getClassAverage)
										.map(avg -> avg != null ? avg : 0L).orElse(0L);
								chapterTrackingWithQuizResponseDto.setQuizAverage(classAverage);
								String message = messageBasedOnQuizAverage(classAverage);
								chapterTrackingWithQuizResponseDto.setAverageMessage(message);

							}

							LMSResponse<TeacherReportQuizOverviewCardResponseDto> quizData = teacherFeignClient
									.getTeacherReportCardQuizOverview(teacher.getId(), schoolId, branchId, boardId,
											gradeId, chapterTrackingResponseDto.getSectionId(), subjectId, null,
											chapterTrackingResponseDto.getChapterId(),
											academicYearId);

							TeacherReportQuizOverviewCardResponseDto teacherReportQuizOverviewCardResponseDto = Optional
									.ofNullable(quizData).map(LMSResponse::getData).orElse(null);

							if (teacherReportQuizOverviewCardResponseDto != null) {
								chapterTrackingWithQuizResponseDto.setAttendance(Optional
										.ofNullable(teacherReportQuizOverviewCardResponseDto.getQuizAttendance())
										.orElse(0L));

								String releaseDate = releaseDateFormatChange(teacherReportQuizOverviewCardResponseDto);
								chapterTrackingWithQuizResponseDto.setQuizRelease(releaseDate);

							}

							if (!CollectionUtils.isEmpty(unitResponse)) {

								String quizId = unitResponse.stream()
										.map(QuizReleaseObtainedMarksResponseDto::getQuizId).findFirst().orElse(null);

								List<EmberStudentsResponseDto> emberStudentList = getEmberStudentDetails(unitResponse,
										chapterResponse, quizId);

								Integer totalStudentCount = (int) unitResponse.stream()
										.map(QuizReleaseObtainedMarksResponseDto::getStudentId).count();
								Integer emberStudent = emberStudentList.size();
								double studentPercentage = emberStudent > 0 && totalStudentCount > 0
										? ((double) emberStudent / totalStudentCount) * 100
										: 0;
								Long studentPercentageInEmber = studentPercentage > 0 ? Math.round(studentPercentage)
										: 0L;
								chapterTrackingWithQuizResponseDto
										.setStudentPercentageInEmber(studentPercentageInEmber);
								chapterTrackingWithQuizResponseDto.setEmberStudents(emberStudentList);
							}

						} catch (Exception e) {
							e.printStackTrace();
						}
					}
					filteredChapters.add(chapterTrackingWithQuizResponseDto);
				}
			}
			response = new GradeSubjectScoreResponseDto(boardId, boardResponse.getBoard(), schoolId, school, branchId,
					branch, gradeId, gradeResponse.getGrade(), null, null, subjectId, subjectResponse.getSubject(),
					null, null, filteredChapters, subjectResponse.getSubTopics());
			// Setting the global average of the UQ for each chapter
			if (response != null && !CollectionUtils.isEmpty(response.getChapters())) {
				List<ChapterQuizModel> globalAverageList = forTeachersChapterWise(teacher, gradeId, subjectId, null,
						academicResponse.getId());
				if (!CollectionUtils.isEmpty(globalAverageList)) {
					for (ChapterScoreResponseDto chapter : response.getChapters()) {
						BigDecimal globalAverage = globalAverageList.stream()
								.filter(item -> item.getChapterId().equals(chapter.getChapterId()))
								.map(item -> Optional.ofNullable(item.getGlobalAvg()).orElse(BigDecimal.ZERO))
								.findFirst().orElse(BigDecimal.ZERO);
						chapter.setGlobalQuizAverage(MathUtilitiess.bigDecimalConvertion(globalAverage));
					}
				}
			}
			Long endTime = new Date().getTime();
			log.info("getChapterWiseQuizPerformanceDashboard end old: {}", endTime);
			
			// Optionally log the duration
	        Long duration = endTime - currentTime;
	        log.info("getChapterWiseQuizPerformanceDashboard duration old: {} ms",duration);
			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.chapter.wise.quiz.performance.failed", null));
		}
	}

	@Override
	public List<BlueprintLevelResponse> getBlueprintLevelDetailsBybranchId(String branchId) {
		try {
			if (!branchRepo.existsById(branchId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

			String planId = branchPlanMappingsRepo.findPlanIdByBranchId(branchId);
			LMSResponse<PlansResponseDto> responseDetails = mastersFeignClient.getPlanDetails(planId);
			PlansResponseDto response = Optional.ofNullable(responseDetails).map(LMSResponse::getData)
					.orElseThrow(() -> new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
							Translator.toLocale("fetch.blueprint.level.details.failed", null)));

			return response.getBlueprintLevels().stream().map(blueprintLevelResponseDto -> {
				BlueprintLevelResponse blueprintLevelResponse = new BlueprintLevelResponse();
				blueprintLevelResponse.setId(blueprintLevelResponseDto.getId());
				blueprintLevelResponse.setLevelNumber(blueprintLevelResponseDto.getLevelNumber());
				blueprintLevelResponse.setLevelName(blueprintLevelResponseDto.getLevelName());
				blueprintLevelResponse.setDescription(blueprintLevelResponseDto.getDescription());
				blueprintLevelResponse.setActive(blueprintLevelResponseDto.isActive());
				return blueprintLevelResponse;
			}).collect(Collectors.toList());
		} catch (USException ts) {
			log.error(ExceptionUtils.getStackTrace(ts));
			throw ts;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.blueprint.level.details.failed", null));
		}
	}

	@Override
	public List<ChapterTrackingWithQuizResponseDto> getChapterWiseQuizPerformanceDashboardAdmin(String boardId, String schoolId, String branchId, String gradeId,
																								String subjectId) {
		
		Long currentTime = new Date().getTime();
		log.info("getChapterWiseQuizPerformanceDashboardAdmin start old: {}", currentTime);
		
		List<ChapterTrackingWithQuizResponseDto> response = new ArrayList<>();
		try {
			List<AllTypeUserMinResponseDto> teacherListRsponse = null;
			List<Teachers> teacherList = new ArrayList<>();
			List<String> gradeIds = new ArrayList<>();
			Teachers teacher = teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());
			log.debug(teacher + "teacher");
			if (teacher == null) {
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
				teacherList.add(teacher);
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.PRINCIPAL) {
				teacherListRsponse = teacherRepo.findStaffsMinDetailsForPrincipal(teacher.getSchools().getId(),
						teacher.getBranches().getId(), gradeId);
				List<String> teacherIdList = teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
						.collect(Collectors.toList());
				teacherList.addAll(teacherRepo.findAllById(teacherIdList));
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR) {
				LMSResponse<List<String>> coordinatorGrades = mastersFeignClient
						.getGradeListByCoordinatorId(teacher.getCoordinatorTypeId());
				gradeIds = coordinatorGrades != null && !CollectionUtils.isEmpty(coordinatorGrades.getData())
						? coordinatorGrades.getData()
						: new ArrayList<>();
				String selectedGrade = !StringUtils.isBlank(gradeId)
						&& gradeIds.stream().anyMatch(item -> item.contains(gradeId)) ? gradeId : null;
				teacherListRsponse = !StringUtils.isBlank(selectedGrade)
						? teacherRepo.findStaffsMinDetailsForPrincipal(teacher.getSchools().getId(),
								teacher.getBranches().getId(), gradeId)
						: teacherRepo.findStaffsMinDetailsWithGradeIds(teacher.getSchools().getId(),
								teacher.getBranches().getId(), gradeIds);
				List<String> teacherIdList = teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
						.collect(Collectors.toList());
				teacherList.addAll(teacherRepo.findAllById(teacherIdList));
			}
			if(teacherList != null) {
				LMSResponse<String> masterAcademicLmsResponse = mastersFeignClient.getLatestAcademicYearId();
				String academicYearId = masterAcademicLmsResponse != null ? masterAcademicLmsResponse.getData() : null;

				for(Teachers teacherDetails : teacherList) {
					LMSResponse<PaginatedResponse<ChapterTrackingResponseDto>> responseDetails = teacherFeignClient.getChapterTracking(
						    0, 1000, true, boardId, schoolId, branchId, teacherDetails.getId(), gradeId,
						    subjectId, null, null, academicYearId, null
						);

					/*if(responseDetails == null){
						List<ChapterTrackingWithQuizResponseDto> emptyResponse = new ArrayList<>();

						return emptyResponse;
					}else{

					}*/
						
						PaginatedResponse<ChapterTrackingResponseDto> trackingResponse = Optional.ofNullable(responseDetails)
						    .map(LMSResponse::getData)
						    .orElseGet(() -> {
						        PaginatedResponse<ChapterTrackingResponseDto> emptyResponse = new PaginatedResponse<>();
						        emptyResponse.setData(Collections.emptyList());
						        return emptyResponse;
						    });

						// Extract the list of ChapterTrackingResponseDto items
						List<ChapterTrackingResponseDto> chapterTrackingList = trackingResponse.getData();

						for (ChapterTrackingResponseDto chapterTrackingResponseDto :chapterTrackingList){

							ChapterTrackingWithQuizResponseDto chapterTrackingWithQuizResponseDto = new ChapterTrackingWithQuizResponseDto();
							BeanUtils.copyProperties(chapterTrackingWithQuizResponseDto, chapterTrackingResponseDto);
							chapterTrackingWithQuizResponseDto.setTeacherName(teacherDetails.getFirstName() + " " +teacherDetails.getLastName());

						try {
							LMSResponse<QuestionWisePerformanceResponseDto> avgDetails = teacherFeignClient
									.getQustionWiseQuizPerformance(teacherDetails.getId(), schoolId, branchId,
											boardId, gradeId, chapterTrackingResponseDto.getSectionId(),
											subjectId, null, chapterTrackingResponseDto.getChapterId(),
											academicYearId);
							QuestionWisePerformanceResponseDto classQuizScore = Optional.ofNullable(avgDetails)
									.map(LMSResponse::getData).orElse(null);
							
							if(classQuizScore != null) {								
								Long classAverage = Optional.ofNullable(classQuizScore)
			                            .map(QuestionWisePerformanceResponseDto::getClassAverage)
			                            .map(avg -> avg != null ? avg : 0L)
			                            .orElse(0L);
								chapterTrackingWithQuizResponseDto.setAvgQuizScore(String.valueOf(classAverage));								
							}							
							
							LMSResponse<TeacherReportQuizOverviewCardResponseDto> quizData = teacherFeignClient
									.getTeacherReportCardQuizOverview(teacherDetails.getId(), schoolId, branchId,
											boardId, gradeId, chapterTrackingResponseDto.getSectionId(), subjectId,
											null, chapterTrackingResponseDto.getChapterId(),
											academicYearId);

							TeacherReportQuizOverviewCardResponseDto teacherReportQuizOverviewCardResponseDto = Optional.ofNullable(quizData)
									.map(LMSResponse::getData).orElse(null);

							
							if(teacherReportQuizOverviewCardResponseDto != null) {
								    chapterTrackingWithQuizResponseDto.setAttendance(
								    	    Optional.ofNullable(teacherReportQuizOverviewCardResponseDto.getQuizAttendance()).orElse(0L));

								    String releaseDate = releaseDateFormatChange(teacherReportQuizOverviewCardResponseDto);								    
								    chapterTrackingWithQuizResponseDto.setReleaseDate(releaseDate);							    			
								
							}
							
						}catch (Exception e){
							e.printStackTrace();
						}
							response.add(chapterTrackingWithQuizResponseDto);
						}

				}
			}			
		} catch (Exception e) {
			e.printStackTrace();
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.chapter.wise.quiz.performance.failed", null));
		}
		Long endTime = new Date().getTime();
		log.info("getChapterWiseQuizPerformanceDashboardAdmin end old: {}", endTime);
		
		// Optionally log the duration
        Long duration = endTime - currentTime;
        log.info("getChapterWiseQuizPerformanceDashboardAdmin duration old: {} ms",duration);        
		return response;
	}

	private String releaseDateFormatChange(
			TeacherReportQuizOverviewCardResponseDto teacherReportQuizOverviewCardResponseDto) {
		String releaseDate = Optional.ofNullable(teacherReportQuizOverviewCardResponseDto.getLatestReleaseDate()).orElse(null);

		if (releaseDate != null) {
		    try {								           
		        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
		        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");							            
		        
		        LocalDate date = LocalDate.parse(releaseDate, inputFormatter);							            
		        releaseDate = date.format(outputFormatter);
		    } catch (Exception e) {								            
		        e.printStackTrace();
		        releaseDate = null; 
		    }
		}
		return releaseDate;
	}
	
	
	@Override
	public List<GradeSubjectScoreResponseDto> getChapterWiseQuizPerformance(String gradeId,
			String sectionId, String subjectId, String subTopicId) {
		List<GradeSubjectScoreResponseDto> responseList = new ArrayList<>();
		try {
			List<AllTypeUserMinResponseDto> allTypeUserMinResponseDtoList = teacherService
					.getAllTeachersForPrincipalOrCoodinator(gradeId);

			if (allTypeUserMinResponseDtoList != null) {
				for (AllTypeUserMinResponseDto allTypeUserMinResponseDto : allTypeUserMinResponseDtoList) {
					String teacherId = allTypeUserMinResponseDto.getId();
					log.info("teacherId:" + teacherId);

					Teachers teacher = teacherRepo.getById(teacherId);
//					if (AcademicStaffProfile.TEACHER != teacher.getAcademicStaffProfile())
//						throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));

					String schoolId = teacher.getSchools().getId();
					String school = teacher.getSchools().getName();
					String branchId = teacher.getBranches().getId();
					String branch = teacher.getBranches().getName();
					String boardId = teacher.getBranches().getBoardId();
					GradeSubjectScoreResponseDto response = null;

					// Master feign call for board, academicYear and grade.
					LMSResponse<BoardsResponseDto> boardLMSResponse = mastersFeignClient.getBoardsById(boardId);
					BoardsResponseDto boardResponse = boardLMSResponse != null ? boardLMSResponse.getData() : null;

					LMSResponse<AcademicYearResponseDto> academicLMSResponse = mastersFeignClient
							.getLatestAcademicYear();
					AcademicYearResponseDto academicResponse = academicLMSResponse != null
							? academicLMSResponse.getData()
							: null;

					LMSResponse<GradesResponseDto> garedLMSResponse = mastersFeignClient.getGradesById(gradeId);
					GradesResponseDto gradeResponse = garedLMSResponse != null ? garedLMSResponse.getData() : null;

					LMSResponse<SubjectsResponseDto> subjectLMSResponse = mastersFeignClient.getSubjectsById(subjectId);
					SubjectsResponseDto subjectResponse = subjectLMSResponse != null ? subjectLMSResponse.getData()
							: null;

					LMSResponse<SubTopicsMinResponseDto> subtopicLMSResponse = null;
					if (!StringUtils.isEmpty(subTopicId))
						subtopicLMSResponse = mastersFeignClient.getBySubTopicId(subTopicId);
					SubTopicsMinResponseDto subTopics = subtopicLMSResponse != null ? subtopicLMSResponse.getData()
							: null;

					LMSResponse<SectionsResponseDto> sectionLMSResponse = null;
					if (!StringUtils.isEmpty(sectionId))
						sectionLMSResponse = mastersFeignClient.getSectionsById(sectionId);
					SectionsResponseDto sections = sectionLMSResponse != null ? sectionLMSResponse.getData() : null;

					LMSResponse<List<String>> teacherLMSResponse = teacherFeignClient.completedChapterLists(boardId,
							schoolId, branchId, gradeId, sectionId, subjectId, subTopicId, academicResponse.getId());
					List<String> teacherResponse = Optional.ofNullable(teacherLMSResponse).map(LMSResponse::getData)
							.orElse(null);

					if (!CollectionUtils.isEmpty(teacherResponse)) {
						LMSResponse<List<QuizChapterTotalMarksResponseDto>> contentLMSResponse = contentFeignClient
								.getQuizzesTotalMarksCompletedChapter(boardId, schoolId, branchId, gradeId, sectionId,
										subjectId, subTopicId, academicResponse.getId(), teacherResponse, "Unit Quiz");
						List<QuizChapterTotalMarksResponseDto> quizResponse = Optional.ofNullable(contentLMSResponse)
								.map(LMSResponse::getData).orElse(Collections.emptyList());

						if (!CollectionUtils.isEmpty(quizResponse)) {
							List<String> chapterList = quizResponse != null
									? quizResponse.stream().map(QuizChapterTotalMarksResponseDto::getChapterId)
											.distinct().collect(Collectors.toList())
									: Collections.emptyList();

							List<ChapterScoreResponseDto> chapters = new ArrayList<>();
							if (!CollectionUtils.isEmpty(chapterList)) {
								LMSResponse<List<ChapterFeignResponseDto>> chapterLMSRespnse = mastersFeignClient
										.getAllChaptersByIdForFeign(chapterList);
								List<ChapterFeignResponseDto> chaptersRespnseList = chapterLMSRespnse != null
										? chapterLMSRespnse.getData()
										: null;

								chapters = chapterList.stream().map(chapterId -> {
									List<QuizChapterTotalMarksResponseDto> chapterResponse = quizResponse.stream()
											.filter(item -> item.getChapterId().equals(chapterId)).distinct()
											.collect(Collectors.toList());

									String chapter = chaptersRespnseList.stream()
											.filter(item -> item.getId().equals(chapterId))
											.map(ChapterFeignResponseDto::getChapter).findFirst().orElse(null);

									List<String> quizIds = chapterResponse.stream()
											.map(QuizChapterTotalMarksResponseDto::getQuizId).distinct()
											.collect(Collectors.toList());

									LMSResponse<List<QuizReleaseObtainedMarksResponseDto>> unitLMSResponse = studentFeignClient
											.getUnitQuizzesAndTotalMarks(boardId, schoolId, branchId, gradeId,
													sectionId, subjectId, academicResponse.getId(), quizIds);
									List<QuizReleaseObtainedMarksResponseDto> unitResponse = unitLMSResponse != null
											? unitLMSResponse.getData()
											: null;

									if (!CollectionUtils.isEmpty(unitResponse)) {
										Integer obtainedMarks = unitResponse.stream()
												.mapToInt(QuizReleaseObtainedMarksResponseDto::getTotalObtainedMark)
												.sum();

										String quizId = unitResponse.stream()
												.map(QuizReleaseObtainedMarksResponseDto::getQuizId).findFirst()
												.orElse(null);

										Integer totalmarks = chapterResponse.stream()
												.filter(item -> item.getQuizId().equals(quizId))
												.mapToInt(QuizChapterTotalMarksResponseDto::getTotalMarks).sum();
										Integer totalMarksAllQuizzes = totalmarks > 0 && !unitResponse.isEmpty()
												? totalmarks * unitResponse.size()
												: 0;

										double quizAverage = obtainedMarks > 0 && totalMarksAllQuizzes > 0
												? ((double) obtainedMarks / totalMarksAllQuizzes) * 100
												: 0;

										Long quizAveragePercentage = quizAverage > 0 ? Math.round(quizAverage) : 0L;

										String message = messageBasedOnQuizAverage(quizAveragePercentage);
										List<EmberStudentsResponseDto> emberStudentList = getEmberStudentDetails(
												unitResponse, chapterResponse, quizId);

										Integer totalStudentCount = (int) unitResponse.stream()
												.map(QuizReleaseObtainedMarksResponseDto::getStudentId).count();
										Integer emberStudent = emberStudentList.size();
										double studentPercentage = emberStudent > 0 && totalStudentCount > 0
												? ((double) emberStudent / totalStudentCount) * 100
												: 0;
										Long studentPercentageInEmber = studentPercentage > 0
												? Math.round(studentPercentage)
												: 0L;

										return new ChapterScoreResponseDto(chapterId, chapter, quizAveragePercentage,
												null, message, emberStudentList, studentPercentageInEmber, null, null,
												null, null, null, null, null, null);
									}
									return null;
								}).filter(Objects::nonNull).collect(Collectors.toList());

								response = new GradeSubjectScoreResponseDto(boardId, boardResponse.getBoard(), schoolId,
										school, branchId, branch, gradeId, gradeResponse.getGrade(), sectionId,
										sections != null ? sections.getSection() : null, subjectId,
										subjectResponse.getSubject(), subTopicId,
										subTopics != null ? subTopics.getSubTopic() : null, chapters, null);
							}
						} else
							throw new USException(ErrorCodes.NOT_FOUND,
									Translator.toLocale("subject.quiz.not.released", null));
					}

					// Setting the global average of the UQ for each chapter
					if (response != null && !CollectionUtils.isEmpty(response.getChapters())) {
						List<ChapterQuizModel> globalAverageList = forTeachersChapterWise(teacher, gradeId, subjectId,
								subTopicId, academicResponse.getId());
						if (!CollectionUtils.isEmpty(globalAverageList)) {
							for (ChapterScoreResponseDto chapter : response.getChapters()) {
								BigDecimal globalAverage = globalAverageList.stream()
										.filter(item -> item.getChapterId().equals(chapter.getChapterId()))
										.map(item -> Optional.ofNullable(item.getGlobalAvg()).orElse(BigDecimal.ZERO))
										.findFirst().orElse(BigDecimal.ZERO);
								chapter.setGlobalQuizAverage(MathUtilitiess.bigDecimalConvertion(globalAverage));
							}
						}
					}

					responseList.add(response);

				}
			}
			return responseList;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.chapter.wise.quiz.performance.failed", null));
		}
	}

	
	@Override
	public List<TeacherGradeWiseQuizPerformanceResponseDto> getGradeWiseQuizPerformance(String gradeId, String subjectId)
		 {
		List<TeacherGradeWiseQuizPerformanceResponseDto> responseList = new ArrayList<>();
		try {
			String username = jwtUtil.currentLoginUser();
			Teachers teachers = teacherRepo.findByUserNameIgnoreCase(username);
			List<AllTypeUserMinResponseDto> allTypeUserMinResponseDtoList=new ArrayList<>();
			if (teachers != null && teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
				AllTypeUserMinResponseDto teacher=new AllTypeUserMinResponseDto();
				teacher.setId(teachers.getId());
				teacher.setName(teachers.getUserName());
				allTypeUserMinResponseDtoList.add(teacher);
			}else {
				allTypeUserMinResponseDtoList= teacherService
						.getAllTeachersForPrincipalOrCoodinator(gradeId);
			}
			
			if (allTypeUserMinResponseDtoList != null) {
				for (AllTypeUserMinResponseDto allTypeUserMinResponseDto : allTypeUserMinResponseDtoList) {
					String teacherId = allTypeUserMinResponseDto.getId();
					log.info("teacherId:" + teacherId);

					Teachers teacher = !StringUtils.isEmpty(teacherId) ? teacherRepo.getById(teacherId)
							: teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());

//					if (!teacherRepo.existsByIdAndDeleted(teacher.getId(), false))
//						throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));
//					if (AcademicStaffProfile.TEACHER != teacher.getAcademicStaffProfile())
//						throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));

					String schoolId = teacher.getSchools().getId();
					String school = teacher.getSchools().getName();
					String branchId = teacher.getBranches().getId();
					String branch = teacher.getBranches().getName();
					String boardId = teacher.getBranches().getBoardId();
					TeacherGradeWiseQuizPerformanceResponseDto response = null;

					// Master feign call for board, academicYear and grade.
					LMSResponse<BoardsResponseDto> boardLMSResponse = mastersFeignClient.getBoardsById(boardId);
					BoardsResponseDto boardResponse = boardLMSResponse != null ? boardLMSResponse.getData() : null;

					LMSResponse<AcademicYearResponseDto> academicLMSResponse = mastersFeignClient
							.getLatestAcademicYear();
					AcademicYearResponseDto academic = academicLMSResponse != null ? academicLMSResponse.getData()
							: null;

					// To find out the assigned grade, section, subject and subtopic.
					List<GradeSectionSubjectsResponseDto> gradeSectionSubjectsAll = teacherRepo
							.getGradeSectionSubjectsDetails(teacher.getId(), schoolId, branchId, gradeId, null);	
					

			        // Filter the list by subjects to User given subject.
			        List<GradeSectionSubjectsResponseDto> gradeSectionSubjects = gradeSectionSubjectsAll.stream()
			                .filter(subject -> subjectId.equals(subject.getSubjectId()))
			                .collect(Collectors.toList());        
			        

					List<String> gardeIds = gradeSectionSubjects.stream()
							.map(GradeSectionSubjectsResponseDto::getGradeId).distinct().collect(Collectors.toList());

					List<String> sectionList = gradeSectionSubjects.stream()
							.map(GradeSectionSubjectsResponseDto::getSectionId).distinct().collect(Collectors.toList());

					LMSResponse<List<SectionsResponseDto>> sectionLMSResponse = mastersFeignClient
							.getAllSectionsByIds(sectionList);
					List<SectionsResponseDto> sectionDto = sectionLMSResponse != null ? sectionLMSResponse.getData()
							: null;

					if (!CollectionUtils.isEmpty(gardeIds)) {
						LMSResponse<List<GradesResponseDto>> gradeLMSResponse = mastersFeignClient
								.getAllGradesByIds(gardeIds);
						List<GradesResponseDto> gradesResponse = gradeLMSResponse != null ? gradeLMSResponse.getData()
								: null;

						List<GradeWisePerformanceResponseDto> gradeResponseList = new ArrayList<>();
						for (String currentGradeId : gardeIds) {
							String grade = gradesResponse.stream().filter(item -> item.getId().equals(currentGradeId))
									.map(GradesResponseDto::getGrade).findFirst().orElse(null);

							int gradeStudentCount = (int) studentsRepo
									.getStudentIds(boardId, schoolId, branchId, currentGradeId, null).stream().count();

							List<String> sectionIds = gradeSectionSubjects.stream()
									.filter(item -> item.getGradeId().equals(currentGradeId))
									.map(GradeSectionSubjectsResponseDto::getSectionId).distinct()
									.collect(Collectors.toList());

							// section wise performance
							if (!CollectionUtils.isEmpty(sectionIds)) {
								List<SectionSubjectScoreResponseDto> sectionResponseList = new ArrayList<>();
								for (String sectionId : sectionIds) {
									String section = sectionDto.stream().filter(item -> item.getId().equals(sectionId))
											.map(SectionsResponseDto::getSection).findFirst().orElse(null);

									int sectionStudentCount = (int) studentsRepo
											.getStudentIds(boardId, schoolId, branchId, currentGradeId, sectionId)
											.stream().count();

									List<GradeSectionSubjectsResponseDto> filteredSections = gradeSectionSubjects
											.stream()
											.filter(sectionSubjects -> sectionId.equals(sectionSubjects.getSectionId()))
											.collect(Collectors.toList());

									List<String> subjectIds = filteredSections.stream()
											.map(GradeSectionSubjectsResponseDto::getSubjectId).distinct()
											.collect(Collectors.toList());

									List<SubjectScoreResponseDto> subjectResponseList = getSubjectScoreResposne(
											subjectIds, filteredSections, boardId, schoolId, branchId, currentGradeId,
											sectionId, academic.getId(), sectionStudentCount);

									SectionSubjectScoreResponseDto sectionResponse = new SectionSubjectScoreResponseDto(
											sectionId, section, subjectResponseList);
									sectionResponseList.add(sectionResponse);
								}
								GradeWisePerformanceResponseDto gradeResponse = new GradeWisePerformanceResponseDto(
										currentGradeId, grade, sectionResponseList);
								gradeResponseList.add(gradeResponse);

							} else {
								// grade wise performance
								List<String> subjectIds = gradeSectionSubjects.stream()
										.filter(item -> item.getGradeId().equals(currentGradeId))
										.map(GradeSectionSubjectsResponseDto::getSubjectId).distinct()
										.collect(Collectors.toList());

								List<SubjectScoreResponseDto> subjectResponseList = getSubjectScoreResposne(subjectIds,
										gradeSectionSubjects, boardId, schoolId, branchId, currentGradeId, null,
										academic.getId(), gradeStudentCount);
								GradeWisePerformanceResponseDto gradeResponse = new GradeWisePerformanceResponseDto(
										currentGradeId, grade, null, subjectResponseList);
								gradeResponseList.add(gradeResponse);
							}
						}
						response = new TeacherGradeWiseQuizPerformanceResponseDto(boardId, boardResponse.getBoard(),
								schoolId, school, branchId, branch, gradeResponseList);
					}

					// Global average, set into the response. Setting the global average
					List<TeacherGlobalAvgGradeWise> globalAvergaeList = forTeachersGradeWiseGlobalAverage(teacher,
							academic.getId(), gradeId);
					if (response != null && !CollectionUtils.isEmpty(response.getGrades())
							&& !CollectionUtils.isEmpty(globalAvergaeList)) {
						for (GradeWisePerformanceResponseDto grade : response.getGrades()) {
							List<SectionSubjectScoreResponseDto> gSections = grade.getSections();
							List<SubjectScoreResponseDto> gSubjects = grade.getSubjects();

							// check grade-with section has data
							if (!CollectionUtils.isEmpty(gSections)) {
								for (SectionSubjectScoreResponseDto gSection : gSections) {
									// check in side the section subjects exists
									if (!CollectionUtils.isEmpty(gSection.getSubjects())) {
										// traverse through the subject, find it has sub-topic list
										for (SubjectScoreResponseDto gSecSubject : gSection.getSubjects()) {
											if (!CollectionUtils.isEmpty(gSecSubject.getSubtopics())) {
												for (SubtopicScoreResponseDto gSecSubTopic : gSecSubject
														.getSubtopics()) {
													TeacherGlobalAvgGradeWise gSecSubTopicAvg = globalAvergaeList
															.stream()
															.filter(itemThree -> itemThree.getGradeId()
																	.equals(grade.getGradeId())
																	&& itemThree.getSubjectId()
																			.equals(gSecSubject.getSubjectId())
																	&& itemThree.getSubTopicId()
																			.equals(gSecSubTopic.getSubtopicId()))
															.findAny().orElse(null);
													if (gSecSubTopicAvg != null) {
														gSecSubTopic.setGlobalQuizAverageScorePercentage(
																gSecSubTopicAvg.getGlobalAvg());
														gSecSubTopic.setUnitGlobalQuizAttemptRate(
																gSecSubTopicAvg.getUqGlobalAvgAttempt());
														gSecSubTopic.setPracticeGlobalQuizAttemptRate(
																gSecSubTopicAvg.getPqGlobalAvgAttempt());
													}
												}
											} else {
												// not sub-topic only subject
												TeacherGlobalAvgGradeWise gSubjectAvg = globalAvergaeList.stream()
														.filter(itemThree -> itemThree.getGradeId()
																.equals(grade.getGradeId())
																&& itemThree.getSubjectId()
																		.equals(gSecSubject.getSubjectId()))
														.findAny().orElse(null);
												if (gSubjectAvg != null) {
													BigDecimal defaultGlobalAvg = BigDecimal.ZERO;
													gSecSubject.setGlobalQuizAverageScorePercentage(MathUtilitiess
															.bigDecimalConvertion(gSubjectAvg.getGlobalAvg() != null
																	? gSubjectAvg.getGlobalAvg()
																	: defaultGlobalAvg));
													gSecSubject.setUnitGlobalQuizAttemptRate(
															MathUtilitiess.bigDecimalConvertion(
																	gSubjectAvg.getUqGlobalAvgAttempt() != null
																			? gSubjectAvg.getUqGlobalAvgAttempt()
																			: defaultGlobalAvg));
													gSecSubject.setPracticeGlobalQuizAttemptRate(
															MathUtilitiess.bigDecimalConvertion(
																	gSubjectAvg.getPqGlobalAvgAttempt() != null
																			? gSubjectAvg.getPqGlobalAvgAttempt()
																			: defaultGlobalAvg));
												}
											}
										}
									}
								}
							}

							// Grade without section
							if (!CollectionUtils.isEmpty(gSubjects)) {
								// traverse through the subject, find it has sub-topic list
								for (SubjectScoreResponseDto gSecSubject : gSubjects) {
									if (!CollectionUtils.isEmpty(gSecSubject.getSubtopics())) {
										for (SubtopicScoreResponseDto gSecSubTopic : gSecSubject.getSubtopics()) {
											TeacherGlobalAvgGradeWise gSecSubTopicAvg = globalAvergaeList.stream()
													.filter(itemThree -> itemThree.getGradeId()
															.equals(grade.getGradeId())
															&& itemThree.getSubjectId()
																	.equals(gSecSubject.getSubjectId())
															&& itemThree.getSubTopicId()
																	.equals(gSecSubTopic.getSubtopicId()))
													.findAny().orElse(null);
											if (gSecSubTopicAvg != null) {
												gSecSubTopic.setGlobalQuizAverageScorePercentage(
														gSecSubTopicAvg.getGlobalAvg());
												gSecSubTopic.setUnitGlobalQuizAttemptRate(
														gSecSubTopicAvg.getUqGlobalAvgAttempt());
												gSecSubTopic.setPracticeGlobalQuizAttemptRate(
														gSecSubTopicAvg.getPqGlobalAvgAttempt());
											}
										}
									} else {
										// not sub-topic only subject
										TeacherGlobalAvgGradeWise gSecSubjectAvg = globalAvergaeList.stream()
												.filter(itemThree -> itemThree.getGradeId().equals(grade.getGradeId())
														&& itemThree.getSubjectId().equals(gSecSubject.getSubjectId()))
												.findAny().orElse(null);
										if (gSecSubjectAvg != null) {
											BigDecimal defaultGlobalAvg = BigDecimal.ZERO;
											gSecSubject.setGlobalQuizAverageScorePercentage(MathUtilitiess
													.bigDecimalConvertion(gSecSubjectAvg.getGlobalAvg() != null
															? gSecSubjectAvg.getGlobalAvg()
															: defaultGlobalAvg));
											gSecSubject.setUnitGlobalQuizAttemptRate(MathUtilitiess
													.bigDecimalConvertion(gSecSubjectAvg.getUqGlobalAvgAttempt() != null
															? gSecSubjectAvg.getUqGlobalAvgAttempt()
															: defaultGlobalAvg));
											gSecSubject.setPracticeGlobalQuizAttemptRate(MathUtilitiess
													.bigDecimalConvertion(gSecSubjectAvg.getPqGlobalAvgAttempt() != null
															? gSecSubjectAvg.getPqGlobalAvgAttempt()
															: defaultGlobalAvg));
										}
									}
								}
							}
						}
					}
					if(response != null) {
						responseList.add(response);
					}
				}
			}
			return responseList;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.grade.wise.quiz.performance.failed", null));
		}
	}

}
