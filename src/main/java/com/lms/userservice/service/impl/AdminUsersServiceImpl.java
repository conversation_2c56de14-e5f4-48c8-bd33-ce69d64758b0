package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.CommunicationAction;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.mapper.UsersMapper;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.projection.UsersProjection;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.request.dto.AdminUsersRequestDto;
import com.lms.userservice.request.dto.ToggleActiveStatusRequestDto;
import com.lms.userservice.request.dto.UserRolesRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SmsGatewayRequestDto;
import com.lms.userservice.response.dto.UserRolesResponseDto;
import com.lms.userservice.service.AdminUsersService;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * Implemented following operation.
 * <p>
 * Admin-user create, update, get by Id, get by pagination, toggle active
 * status, delete and update password
 * <p>
 * Pending : Share the details
 * 
 * <AUTHOR> C Achari
 *
 */
@Slf4j
@Service("adminUsersService")
public class AdminUsersServiceImpl implements AdminUsersService {

	@Autowired
	private JwtUtil jwtUtil;

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private UserService userService;

	@Autowired
	private UsersMapper usersMapper;

	@Autowired
	private MastersFeignClient masterFeignClient;

	@Autowired
	private UsersRoleMappingRepository usersRoleMappingRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	@Override
	@Transactional
	public AdminUsersResponseDto createAdminUserAndSendEmail(AdminUsersRequestDto request) {
		if (request.getId() != null)
			throw new USException(ErrorCodes.METHOD_NOT_SUPPORTED,
					Translator.toLocale("update.not.allowed.in.create", null));

		if (StringUtils.isBlank(request.getLmsEnv())
				|| !Enums.getIfPresent(LMSEnvironment.class, request.getLmsEnv()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("specify.application.environment", null));

		if (!checkRoles(request.getUserRoles()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("only.admin.users", null));

		try {
			log.info("Admin user start to create...");
			String currentUser = jwtUtil.currentLoginUser();
			AdminUsersResponseDto response = null;

			log.info("Creating the username");
			String username = userService.generateUserName(request.getFirstName(), request.getLastName());
			String password = CommonUtilities.generatePassayPassword();
			log.info("encryprting the password: " + password);
			String encryptedPass = CommonUtilities.passwordEncryptor(password);

			Users users = usersMapper.fromAdminUserRequestToEntity(request);
			users.setUserName(username);
			users.setPassword(encryptedPass);
			users.setAdminUser(true);
			users.setCreatedBy(currentUser);
			users = usersRepository.save(users);
			if (!StringUtils.isBlank(users.getId())) {
				log.info("Admin user created, mapping to the role is intiated.");
				response = usersMapper.fromEntityToAdminUsersResponse(users);
				response.setPassword(password);
				List<UserRolesResponseDto> userRoleMapping = userService.addRoles(users, request.getUserRoles());
				if (!CollectionUtils.isEmpty(userRoleMapping)) {
					log.info("Roles mapped with users, getting the role data from master-service");
					List<String> roleList = userRoleMapping.stream().map(UserRolesResponseDto::getRoleId)
							.collect(Collectors.toList());
					List<RolesFeignDto> roles = masterFeignClient.getRolesByIds(roleList).getData();
					response.setRoles(!CollectionUtils.isEmpty(roles) ? roles : null);
					response.setLmsEnv(request.getLmsEnv());
					response.setRoleName(roles.get(0).getRole());
					response.setUserId(users.getId());
					response.setTypeOfEmailSend("CREATE");
				}
				log.info("Admin create process completed.");
				// sending the sms.
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(users.getFirstName(), username,
						users.getPhoneNumber(), users.getEmail(), CommunicationAction.USER_CREATION.getCode());
        log.info(smsGatewayRequestDto.toString());
				// LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				// if (notificationLms != null && notificationLms.getData())
				// 	log.info("Admin user created. SMS send");
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.create.failed", null));
		}
	}

	@Override
	public AdminUsersResponseDto updateAdminUser(AdminUsersRequestDto request) {
		if (StringUtils.isBlank(request.getId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("admin.user.id.madatory", null));

		if (!usersRepository.existsById(request.getId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		if (!checkRoles(request.getUserRoles()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("only.admin.users", null));

		try {
			log.info("Admin user start to update...");
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			AdminUsersResponseDto response = null;

			Users users = usersRepository.getById(request.getId());
			users.setFirstName(request.getFirstName());
			users.setLastName(request.getLastName());
			users.setEmail(request.getEmail());
			users.setPhoneNumber(request.getPhoneNumber());
			users.setModifiedAt(currentTime);
			users.setLastModifiedBy(currentUser);

			users = usersRepository.save(users);
			if (!StringUtils.isBlank(users.getId())) {
				log.info("Admin user updated, mapping to the role is intiated.");
				response = usersMapper.fromEntityToAdminUsersResponse(users);
				List<UserRolesResponseDto> userRoleMapping = userService.addRoles(users, request.getUserRoles());
				if (!CollectionUtils.isEmpty(userRoleMapping)) {
					log.info("Roles mapped with users, getting the role data from master-service");
					List<String> roleList = userRoleMapping.stream().map(UserRolesResponseDto::getRoleId)
							.collect(Collectors.toList());
					List<RolesFeignDto> roles = masterFeignClient.getRolesByIds(roleList).getData();
					response.setRoles(!CollectionUtils.isEmpty(roles) ? roles : null);
				}
				log.info("Admin user update process completed.");

				// find the created person name and role name
				Users updateUser = usersRepository.findByUserName(currentUser);
				if (updateUser != null) {
					List<String> createdUserRoles = usersRoleMappingRepository.findAllRolesByUserId(updateUser.getId());
					List<RolesFeignDto> currentUserRoles = masterFeignClient.getRolesByIds(createdUserRoles).getData();
					response.setRoleNameOfAdmin(currentUserRoles.get(0).getRole());
					response.setAdminName(updateUser.getFirstName() + " " + updateUser.getLastName());
				}
				response.setLmsEnv(request.getLmsEnv());
				response.setUserId(users.getId());
				response.setTypeOfEmailSend("UPDATE");
				// sending the sms.
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(users.getFirstName(),
						users.getUserName(), users.getPhoneNumber(), users.getEmail(),
						CommunicationAction.UPDATE_PROFILE.getCode());
				// LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				// if (notificationLms != null && notificationLms.getData())
				// 	log.info("Admin user details update. SMS send");
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.update.failed", null));
		}
	}

	@Override
	public AdminUsersResponseDto getAdminUserById(String id) {
		if (StringUtils.isBlank(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("admin.user.id.madatory", null));

		if (!usersRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			log.info("Admin user start to update...");
			AdminUsersResponseDto response = usersRepository.findAdminUsersById(id);
			List<String> roleIds = usersRoleMappingRepository.findAllRolesByUserId(id);
			if (!CollectionUtils.isEmpty(roleIds)) {
				log.info("Finding the roles and setting to the response.");
				List<RolesFeignDto> roles = masterFeignClient.getRolesByIds(roleIds).getData();
				response.setRoles(!CollectionUtils.isEmpty(roles) ? roles : null);
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.by.id.failed", null));
		}
	}

	/**
	 * @see <a href=
	 *      "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/1e2b14a7-1d99-4606-9f48-fb04d9bb7e3c/">Add
	 *      Admin User</a>
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortOrder
	 * @param sortBy
	 * @param search
	 * @param roleId
	 * @return
	 */
	@Override
	public PaginatedResponse<AdminUsersResponseDto> getAllAdminUsersByPagniation(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String search, String roleId, Boolean active) {
		log.info("User filtering and searching started");
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			List<AdminUsersResponseDto> response = new ArrayList<>();
			String sortByField = FieldMappers.usersApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortByField));

			log.info("Admin-user pagination in progress...");
			Page<UsersProjection> projectionList = usersRepository.findAllUserBySearchAndFilter(true, searchFormat,
					roleId, pageable, active);
			if (!CollectionUtils.isEmpty(projectionList.getContent())) {
				log.info("Admin-user data setting into the response list.");
				totalElements = projectionList.getTotalElements();
				totalPages = projectionList.getTotalPages();
				projectionList.getContent().forEach(user -> {
					AdminUsersResponseDto responseDto = new AdminUsersResponseDto(user);
					if (usersRoleMappingRepository.existsByUsersId(responseDto.getId())) {
						List<String> roleIds = usersRoleMappingRepository.findAllRolesByUserId(responseDto.getId());
						List<RolesFeignDto> rolesDto = masterFeignClient.getRolesByIds(roleIds).getData();
						responseDto.setRoles(rolesDto);
					}
					response.add(responseDto);
				});
				log.info(!CollectionUtils.isEmpty(response) ? "Admin-users listed." : "Fialed to fetch admin-users.");
			}
			log.info("User filtering and searching completed");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.list.failed", null));
		}
	}

	@Override
	public Boolean toggleActiveStatus(ToggleActiveStatusRequestDto request) {
		if (!usersRepository.existsByIdAndDeleted(request.getId(), false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			log.info("Toggling the active status...");
			usersRepository.setActiveStatus(request.getId(), request.isActive(), currentTime, currentUser);
			boolean toggled = usersRepository.findActiveStatus(request.getId());
			String currentState = request.isActive() ? "Active state." : "De-active dtate.";
			log.info(toggled == request.isActive() ? "User set into " + currentState
					: "failed to set into " + currentState);
			return toggled == request.isActive();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("update.active.failed", null));
		}
	}

	@Override
	public Boolean deleteAdminUser(String id) {
		if (!usersRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			log.info("Deleteting the user in progress...");

			Users users = usersRepository.getById(id);
			String email = users.getEmail() + "-" + currentTime;
			String mobile = users.getPhoneNumber() + "-" + currentTime;

			int deleted = usersRepository.setDeleteStatus(id, currentTime, currentUser, email, mobile);
			log.info(deleted > 0 ? "User's status set into deleted" : "failed to set status into deleted");
			return deleted > 0;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.delete.failed", null));
		}
	}

	private boolean checkRoles(List<UserRolesRequestDto> userRoles) {
		try {
			boolean response = true;
			if (!CollectionUtils.isEmpty(userRoles)) {
				log.info("Extracting only roles and making a feign call to master-service");
				List<String> rolesList = userRoles.stream().map(UserRolesRequestDto::getRoleId)
						.collect(Collectors.toList());
				List<RolesFeignDto> roles = masterFeignClient.getRolesByIds(rolesList).getData();
				for (RolesFeignDto dto : roles) {
					String lowerRole = dto.getRole().toLowerCase();
					if (lowerRole.equals("teacher") || lowerRole.equals("student")) {
						response = false;
						break;
					}
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("faild.to.check.role", null));
		}
	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto checkTheMappingForConcept(String id, String operationType) {
		if (!usersRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			boolean isExits = false;
			String message = Translator.toLocale("confirmation.api.default.message", null);
			List<Long> mappingCounts = usersRepository.findUsages(id);
			if (!mappingCounts.isEmpty()) {
				isExits = mappingCounts.stream().anyMatch(count -> count > 0);
				if (isExits)
					message = Translator.toLocale("confirmation.api.permission.denied", null);
				else {
					message = (OperationType.DELETE == OperationType.valueOf(operationType))
							? Translator.toLocale("confirmation.api.delete", null)
							: Translator.toLocale("confirmation.api.toggle.active", null);
				}
			}
			return new ConfirmationApiResponseDto(isExits, message);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("confirmation.api.failed", null));
		}
	}

	@Override
	@Transactional
	public Boolean toggleActiveStatusForMaster(String id, boolean active) {
		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			log.info("Toggling the active status...");
			usersRoleMappingRepository.setActiveStatus(id, active, currentTime, currentUser);
			boolean toggled = usersRoleMappingRepository.findActiveStatus(id);
			String currentState = active ? "Active state." : "De-active state.";
			log.info(toggled == active ? "User set into " + currentState : "failed to set into " + currentState);
			return toggled == active;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("update.active.failed", null));
		}
	}
}
