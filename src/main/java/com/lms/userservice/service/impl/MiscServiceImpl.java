package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Teachers;
import com.lms.userservice.enums.SchoolMenus;
import com.lms.userservice.enums.AcademicStaffProfile;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.Gender;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.repository.AssignTeacherRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.GradeSectionMappingRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.request.dto.QuizReleaseCheckingRequestDto;
import com.lms.userservice.response.dto.EnumsResponseDto;
import com.lms.userservice.response.dto.SchoolBranchTeacherNameResponseDto;
import com.lms.userservice.service.MiscService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("miscService")
public class MiscServiceImpl implements MiscService {

	@Autowired
	private GradeSectionMappingRepository gradeSectionMappingRepository;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private TeacherRepository teacherRepository;

	@Autowired
	private AssignTeacherRepository assignTeacherRepository;

	/**
	 * List the Coordinator type from the enumeration
	 * 
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public List<EnumsResponseDto> listOfCoordinatorType() {
		try {
			List<EnumsResponseDto> response = new ArrayList<>();
			log.info("Converting AcademicStaffProfile into list");
			List<AcademicStaffProfile> eumList = new ArrayList<>(EnumSet.allOf(AcademicStaffProfile.class));
			eumList.forEach(item -> response.add(new EnumsResponseDto(item.getCode(), item.getName())));
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("enum.listing.failed", null));
		}
	}

	/**
	 * List the Gender from the enumeration
	 * 
	 * @return
	 */
	@Override
	public List<EnumsResponseDto> listOfGender() {
		try {
			List<EnumsResponseDto> response = new ArrayList<>();
			log.info("Converting Gender into list");
			List<Gender> eumList = new ArrayList<>(EnumSet.allOf(Gender.class));
			eumList.forEach(item -> response.add(new EnumsResponseDto(item.getCode(), item.getName())));
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("enum.listing.failed", null));
		}
	}

	/**
	 * List the Operation Type from the enumeration, use if while do the
	 * confirmation API
	 * 
	 * @return
	 */
	@Override
	public List<EnumsResponseDto> listOfOperationType() {
		try {
			List<EnumsResponseDto> response = new ArrayList<>();
			log.info("Converting OperationType into list");
			List<OperationType> eumList = new ArrayList<>(EnumSet.allOf(OperationType.class));
			eumList.forEach(item -> response.add(new EnumsResponseDto(item.getCode(), item.getName())));
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("enum.listing.failed", null));
		}
	}

	/**
	 * @return the list of LMSEnvironment
	 */
	@Override
	public List<EnumsResponseDto> listOfApplicationEnvironment() {
		try {
			List<EnumsResponseDto> response = new ArrayList<>();
			log.info("Converting LMSEnvironment into list");
			List<LMSEnvironment> eumList = new ArrayList<>(EnumSet.allOf(LMSEnvironment.class));
			eumList.forEach(item -> response.add(new EnumsResponseDto(item.getCode(), item.getName())));
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("enum.listing.failed", null));
		}
	}

	/**
	 * Find the GradeSectionMapping of AcademicYear. This API will be act as a feign
	 * call.
	 * 
	 * @param AcademicYearId
	 * @return
	 */
	@Override
	public boolean checkTheGradeSectionMappingForAcademicYearId(String academicYearId) {
		try {
			if (StringUtils.isEmpty(academicYearId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("board.not.found", null));

			boolean isExists = false;
			List<Long> academicYearIdList = gradeSectionMappingRepository
					.findTheGradeSectionMappingAcademicYearId(academicYearId);
			if (!CollectionUtils.isEmpty(academicYearIdList)) {
				isExists = academicYearIdList.stream().anyMatch(item -> item > 0);
				log.info(Translator.toLocale("mapping.found", null));
			}
			return isExists;
		} catch (USException cse) {
			log.error(ExceptionUtils.getStackTrace(cse));
			throw new USException(cse.getErrorCode(), cse.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("mapping.not.found", null));
		}
	}

	/**
	 * Feign call from the teacher-service. Checking the relationships and
	 * teacher-access before trigger the teach-status.
	 *
	 * @param schoolId
	 * @param branchId
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	@Override
	public boolean fiegnCallFromTeacherServiceForTeachStatus(String schoolId, String branchId, String teacherId,
			String gradeId, String sectionId, String subjectId, String subTopicId) {
		try {

			boolean isCorrect = true;

			if (!schoolRepository.existsByIdAndDeletedAndActive(schoolId, false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

			if (!branchRepository.existsByIdAndDeletedAndActive(branchId, false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

			if (!branchRepository.existsByIdAndSchoolsIdAndDeletedAndActive(branchId, schoolId, false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.school.relation", null));

			if (!teacherRepository.existsByIdAndDeletedAndActive(teacherId, false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.found", null));

			if (!teacherRepository.existsByIdAndBranchesIdAndSchoolsIdAndDeletedAndActive(teacherId, branchId, schoolId,
					false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.belong", null));

			if (!assignTeacherRepository.checkingTeacherAccessGiven(teacherId, gradeId, sectionId, subjectId,
					subTopicId, false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.access.checking", null));

			return isCorrect;
		} catch (USException cse) {
			log.error(ExceptionUtils.getStackTrace(cse));
			throw new USException(cse.getErrorCode(), cse.getMessage());
		}
	}

	/**
	 * Get name to teacher-service for teach-status.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param startedById
	 * @param endedById
	 * @return
	 */
	@Override
	public SchoolBranchTeacherNameResponseDto getSchoolBranchAndTeachersName(String schoolId, String branchId,
			String startedById, String endedById) {
		try {
			Schools schools = schoolRepository.getById(schoolId);
			Branches branches = branchRepository.getById(branchId);
			Teachers started = teacherRepository.getById(startedById);
			Teachers ended = !StringUtils.isEmpty(endedById) ? teacherRepository.getById(endedById) : null;

			String startedBy = started.getFirstName() + " " + started.getLastName();
			String endedBy = ended != null ? ended.getFirstName() + " " + ended.getLastName() : null;

			return new SchoolBranchTeacherNameResponseDto(schools.getName(), branches.getName(), startedBy, endedBy);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("names.extract.failed", null));
		}
	}

	/**
	 * Menu names for Academic staffs or students
	 * 
	 * @return
	 */
	@Override
	public List<EnumsResponseDto> listOfSchoolMenus() {
		try {
			List<EnumsResponseDto> response = new ArrayList<>();
			log.info("Converting SchoolMenus into list");
			List<SchoolMenus> eumList = new ArrayList<>(EnumSet.allOf(SchoolMenus.class));
			eumList.forEach(item -> response.add(new EnumsResponseDto(item.getCode(), item.getName())));
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("enum.listing.failed", null));
		}
	}

	/**
	 * Feign call for content-service. Before quiz release or re-release checking
	 * the teacher has access to give school, branch, grade, section, subject and
	 * sub-topics. Section is exist then checking will do with section or with
	 * grade.
	 * 
	 * @param request
	 */
	@Override
	public boolean checkingAccessProvideBeforeQuizRelease(QuizReleaseCheckingRequestDto request) {
		try {
			if (!schoolRepository.existsByIdAndDeletedAndActive(request.getSchoolId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

			if (!branchRepository.existsByIdAndDeletedAndActive(request.getBranchId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

			if (!branchRepository.existsByIdAndSchoolsIdAndDeletedAndActive(request.getBranchId(),
					request.getSchoolId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.school.relation", null));

			if (!teacherRepository.existsByIdAndDeletedAndActive(request.getTeacherId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.found", null));

			if (!teacherRepository.existsByIdAndBranchesIdAndSchoolsIdAndDeletedAndActive(request.getTeacherId(),
					request.getBranchId(), request.getSchoolId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.belong", null));

			// if section exit traversing through it and checking access given, or else
			// checking will do with grade
			if (!CollectionUtils.isEmpty(request.getSections())) {
				request.getSections().forEach(section -> {
					if (!assignTeacherRepository.checkingTeacherAccessGiven(request.getTeacherId(),
							request.getGradeId(), section, request.getSubjectId(), request.getSubTopicId(), false,
							true))
						throw new USException(ErrorCodes.NOT_FOUND,
								Translator.toLocale("teacher.access.checking", null));
				});
			} else {
				if (!assignTeacherRepository.checkingTeacherAccessGiven(request.getTeacherId(), request.getGradeId(),
						null, request.getSubjectId(), request.getSubTopicId(), false, true))
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.access.checking", null));
			}
			return true;
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		}
	}
}
