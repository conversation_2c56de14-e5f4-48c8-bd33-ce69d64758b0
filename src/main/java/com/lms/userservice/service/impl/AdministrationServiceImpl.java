package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.modelmapper.PropertyMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Administration;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.AdministrationType;
import com.lms.userservice.enums.CommunicationAction;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.Gender;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.projection.AdministrationProjection;
import com.lms.userservice.repository.AdministrationRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.UsersInstitutionMappingRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.request.dto.AdministrationRequestDto;
import com.lms.userservice.request.dto.SchoolsBranchsRequestDto;
import com.lms.userservice.request.dto.UserRolesRequestDto;
import com.lms.userservice.response.dto.AdministrationResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.SchoolsBranchsResponseDto;
import com.lms.userservice.response.dto.SmsGatewayRequestDto;
import com.lms.userservice.service.AdministrationService;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * The AdministrationServiceImpl class<br>
 * 
 * The {@code AdministrationServiceImpl} implements
 * {@code AdministrationService} <br>
 * 
 * {@code @Service} is a stereotypical annotation used for Service Layer <br>
 * 
 * {@code @Slf4j} is a Logger annotation obtained from Lombok dependency for
 * logging the requests and responses
 */
@Slf4j
@Service("AdministrationService")
public class AdministrationServiceImpl implements AdministrationService {

	@Autowired
	private JwtUtil jwtUtil;

	private Long currentTime = new Date().getTime();

	private ModelMapper modelMapper;

	@Autowired
	private AdministrationRepository adminRepository;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private UserService userService;

	@Autowired
	private UsersInstitutionMappingRepository uiMappingRepository;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private UsersRoleMappingRepository usersRoleMappingRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	@Autowired
	public AdministrationServiceImpl(ModelMapper modelMapper) {
		this.modelMapper = modelMapper;
		this.modelMapper.addMappings(skipAdminRequestFieldsMap);
		this.modelMapper.addMappings(skipAdminUserFieldsMap);
	}

	PropertyMap<AdministrationRequestDto, Administration> skipAdminRequestFieldsMap = new PropertyMap<AdministrationRequestDto, Administration>() {
		protected void configure() {
			skip(destination.getId());
		}
	};
	PropertyMap<Administration, Users> skipAdminUserFieldsMap = new PropertyMap<Administration, Users>() {
		protected void configure() {
			skip(destination.getId());
		}
	};

	/**
	 * Create either School_Admin or Management persona
	 * 
	 * School admin can only handle 1 School and N number of Branches of the school.
	 * Management can hadle N number of schools and N number of branches under the
	 * schools.
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public AdministrationResponseDto createAdministrationAndSendEmail(AdministrationRequestDto request) {
		log.info("Administration registration started...");
		if (!Enums.getIfPresent(LMSEnvironment.class, request.getLmsEnv()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("specify.application.environment", null));

		if (!checkTheSchoolMapping(request.getInstitutions()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.branch.not.found", null));

		try {
			String currentUser = jwtUtil.currentLoginUser();
			AdministrationResponseDto response = new AdministrationResponseDto();
			log.info("Mapping Administration...");

			Administration administration = modelMapper.map(request, Administration.class);
			String userName = userService.generateUserName(request.getFirstName(), request.getLastName());
			administration.setGender(Gender.valueOf(request.getGender()));
			administration.setAdministrationType(AdministrationType.valueOf(request.getRole()));
			administration.setUserName(userName);
			administration.setCreatedBy(currentUser);
			administration = adminRepository.save(administration);
			if (!StringUtils.isEmpty(administration.getId())) {
				String password = CommonUtilities.generatePassayPassword();
				Users users = modelMapper.map(administration, Users.class);
				users.setPassword(CommonUtilities.passwordEncryptor(password));
				users.setPhoneNumber(administration.getMobile());
				users.setCreatedBy(currentUser);
				users = usersRepository.save(users);
				if (!StringUtils.isEmpty(users.getId())) {
					log.info("user role and institution mapping");

					String roleName = CommonUtilities.convertToLowerOrUpper(request.getRole(), false);
					String roleId = mastersFeignClient.getOnlyIdByRole(roleName).getData();

					List<UserRolesRequestDto> usersRole = Arrays.asList(new UserRolesRequestDto(roleId, true));
					userService.addRoles(users, usersRole);

					log.info("User, school and branch mapping");
					List<SchoolsBranchsResponseDto> institutions = userService.addInstitution(users,
							request.getInstitutions());
					response = new AdministrationResponseDto(
							adminRepository.findAdministrationByIdOrUsername(administration.getId()));
					response.setPassword(password);
					response.setInstitutions(institutions);
					response.setRoleName(roleName);
					response.setTypeOfEmailSend("CREATE");
				}
				// sending the sms.
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(administration.getFirstName(),
						userName, administration.getMobile(), administration.getEmail(),
						CommunicationAction.USER_CREATION.getCode());
				LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				if (notificationLms != null && notificationLms.getData())
					log.info("Administration created. SMS send");
			}
			log.info("Administration registered successfully");
			response.setLmsEnv(request.getLmsEnv());
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.create.failed", null));
		}
	}

	/**
	 * To check the school is exist, given branch is mapped and exist in the system.
	 * 
	 * @param request
	 * @return
	 */
	private boolean checkTheSchoolMapping(List<SchoolsBranchsRequestDto> request) {
		try {
			boolean isExist = true;
			for (SchoolsBranchsRequestDto dto : request) {
				if (schoolRepository.existsById(dto.getSchoolId())) {
					if (!branchRepository.existsBySchoolsIdAndDeletedAndIdIn(dto.getSchoolId(), false,
							Arrays.asList(dto.getBranchesId()))) {
						isExist = false;
					}
				} else {
					isExist = false;
				}
			}
			return isExist;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.create.failed", null));
		}
	}

	/**
	 * Paginated response for both management and admin
	 * 
	 * search by: firstName, lastName, email and mobile filter by: schoolId,
	 * branchId and roleId
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortOrder
	 * @param sortBy
	 * @param schoolId
	 * @param branchId
	 * @param roleId
	 * @param search
	 * @return
	 */
	@Override
	public PaginatedResponse<AdministrationResponseDto> getAdministrationByPagniation(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String schoolId, String branchId, String roleId, String search,
			String adminType, Boolean active) {
		log.info("Administration pagination started...");

		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			String sortByField = FieldMappers.administrationApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;
			List<AdministrationResponseDto> adminList = new ArrayList<>();
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortByField));

			Page<AdministrationProjection> projectionList = adminRepository.findAllAdministrations(schoolId, branchId,
					roleId, searchFormat, adminType, active, pageable);

			if (!CollectionUtils.isEmpty(projectionList.getContent())) {
				totalElements = projectionList.getTotalElements();
				totalPages = projectionList.getTotalPages();
				projectionList.getContent().forEach(projection -> {
					List<SchoolsBranchsResponseDto> schoolsBranches = generateSchoolBranchesList(
							projection.getUserId());
					adminList.add(new AdministrationResponseDto(projection, schoolsBranches));
				});
			}
			log.info("Administration pagination completed.");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), adminList.size(),
					adminList);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.get.all.failed", null));
		}
	}

	/**
	 * Generate associated school and branch for school-admin or managementID.
	 * 
	 * @param userId
	 * @return
	 */
	private List<SchoolsBranchsResponseDto> generateSchoolBranchesList(String userId) {
		try {
			List<SchoolsBranchsResponseDto> response = new ArrayList<>();
			List<String> schoolsIds = this.uiMappingRepository.findAllSchoolIdByUserId(userId);
			if (!schoolsIds.isEmpty()) {
				schoolsIds.forEach(school -> {
					Schools schools = schoolRepository.getById(school);
					List<String> branchIds = this.uiMappingRepository.findAllBranchIdByUserIdAndSchoolId(userId,
							school);
					List<BranchesMinDataResponseDto> branchMinList = new ArrayList<>();
					branchIds.forEach(branch -> {
						Branches branches = branchRepository.getById(branch);
						branchMinList.add(new BranchesMinDataResponseDto(branches.getId(), branches.getName(),
								branches.isActive()));
					});
					response.add(new SchoolsBranchsResponseDto(schools.getId(), schools.getCode(), schools.getName(),
							branchMinList));
				});
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.get.all.failed", null));
		}
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public AdministrationResponseDto getAdministrationById(String id) {
		log.info("Administration Details fetch started...");
		if (adminRepository.exitAdministrationByIdOrUsername(id, false) <= 0)
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("admin.not.found", null));

		try {
			AdministrationProjection projection = adminRepository.findAdministrationByIdOrUsername(id);
			List<SchoolsBranchsResponseDto> schoolsBranches = generateSchoolBranchesList(projection.getUserId());
			log.info("Administration Details fetch completed...");
			return new AdministrationResponseDto(projection, schoolsBranches);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("admin.get.by.id.failed", null));
		}
	}

	/**
	 * @param id
	 * @param request
	 * @return
	 */
	@Override
	public AdministrationResponseDto updateAdministrationById(String id, AdministrationRequestDto request) {
		log.info("Administration updation started...");
		if (!adminRepository.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("admin.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			log.info("Updating the admin");
			AdministrationResponseDto response = null;
			Administration administration = adminRepository.getById(id);
			modelMapper.map(request, administration);
			administration.setModifiedAt(currentTime);
			administration.setLastModifiedBy(currentUser);
			administration = adminRepository.save(administration);

			log.info("Updating the user");

			String roleName = CommonUtilities.convertToLowerOrUpper(request.getRole(), false);
			String roleId = mastersFeignClient.getOnlyIdByRole(roleName).getData();

			Users users = usersRepository.findByUserName(administration.getUserName());
			modelMapper.map(administration, users);
			users.setModifiedAt(currentTime);
			users.setLastModifiedBy(currentUser);
			users = usersRepository.save(users);

			log.info("Updating the user-role mapping");
			List<UserRolesRequestDto> usersRole = Arrays.asList(new UserRolesRequestDto(roleId, true));
			userService.addRoles(users, usersRole);

			log.info("Updating the user-institution mapping");
			List<SchoolsBranchsResponseDto> institutions = userService.addInstitution(users, request.getInstitutions());

			response = new AdministrationResponseDto(
					adminRepository.findAdministrationByIdOrUsername(administration.getId()));
			response.setInstitutions(institutions);
			log.info("Update of admin completed.");

			if (!StringUtils.isEmpty(administration.getId())) {
				// sending the sms.
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(administration.getFirstName(),
						administration.getUserName(), administration.getMobile(), administration.getEmail(),
						CommunicationAction.UPDATE_PROFILE.getCode());
				LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				if (notificationLms != null && notificationLms.getData())
					log.info("Administration updated. SMS send");
			}
			// find the current user's role_name, first_name & last_name
			Users updateUser = usersRepository.findByUserName(currentUser);
			if (updateUser != null) {
				List<String> createdUserRoles = usersRoleMappingRepository.findAllRolesByUserId(updateUser.getId());
				List<RolesFeignDto> currentUserRoles = mastersFeignClient.getRolesByIds(createdUserRoles).getData();
				response.setRoleNameOfAdmin(currentUserRoles.get(0).getRole());
				response.setAdminName(updateUser.getFirstName() + " " + updateUser.getLastName());
			}
			response.setLmsEnv(request.getLmsEnv());
			response.setTypeOfEmailSend("UPDATE");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.update.failed", null));
		}
	}

	/**
	 * Mark Administration as Deleted
	 * 
	 * @param id The Administration ID String
	 * @return boolean
	 */
	@Override
	@Modifying
	@Transactional
	public Boolean deleteAdministration(String id) {
		if (!adminRepository.existsById(id)) {
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			boolean isDeleted = false;
			Administration administration = adminRepository.getById(id);
			int deleteStatus = adminRepository.setDeleteStatus(id, currentTime, currentUser);
			if (deleteStatus > 0) {
				log.info("Delete the user");
				Users users = usersRepository.findByUserName(administration.getUserName());
				String email = users.getEmail() + "-" + currentTime;
				String mobile = users.getPhoneNumber() + "-" + currentTime;

				int deleted = usersRepository.setDeleteStatus(users.getId(), currentTime, currentUser, email, mobile);
				log.info(deleted > 0 ? "User's status set into deleted" : "failed to set status into deleted");

				log.info("Delete the user-role mapping");
				Long userRole = userService.deleteUsersRoleMapping(users.getId());

				log.info("Delete the user-institution mapping");
				Long userInst = userService.deleteUsersInstitutionMapping(users.getId());

				if (deleteStatus > 0 && deleted > 0 && userRole > 0 && userInst > 0) {
					log.info("All administration relations are removed.");
					isDeleted = true;
				}
			}
			return isDeleted;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("admin.delete.failed", null));
		}
	}

	@Override
	public Boolean updateActiveField(String id, boolean active) {
		if (!adminRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("admin.not.found", null));

		try {
			String currentUser = jwtUtil.currentLoginUser();
			Administration admin = adminRepository.getById(id);
			admin.setActive(active);
			admin.setLastModifiedBy(jwtUtil.currentLoginUser());
			admin.setModifiedAt(currentTime);
			admin = adminRepository.save(admin);

			log.info("Toggle the Administration from users table");
			Users users = usersRepository.findByUserName(admin.getUserName());

			int toggled = usersRepository.setActiveStatus(users.getId(), active, currentTime, currentUser);
			log.info(toggled > 0 ? "User's active-status toggled" : "failed to toggled the active status.");

			return (admin.isActive() == active) && (toggled > 0);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.failed", null));
		}
	}

	/**
	 * Get the last modification done on the table administration return format
	 * example will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	@Override
	public String getLastModifiedAt(String adminType) {
		if (!Enums.getIfPresent(AdministrationType.class, adminType).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("admin.type.allowed.value", null));
		try {
			return adminRepository.findLastModifiedAt(adminType);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	@Override
	public List<NameCommonResponseDto> getAllAdministrationDetailsByRoleMapping(String roleId) {
		try {
			return adminRepository.getAllAdministrationDetailsByRoleId(roleId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id            : it has to be id from table users
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto confirmationAPI(String id, String operationType) {
		if (!usersRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			boolean isExits = false;
			String message = Translator.toLocale("confirmation.api.default.message", null);
			List<Long> mappingCounts = usersRepository.findAllMappingCountOfUser(id);
			if (!mappingCounts.isEmpty()) {
				isExits = mappingCounts.stream().anyMatch(count -> count > 0);
				if (isExits)
					message = Translator.toLocale("confirmation.api.permission.denied", null);
				else {
					message = (OperationType.DELETE == OperationType.valueOf(operationType))
							? Translator.toLocale("confirmation.api.delete", null)
							: Translator.toLocale("confirmation.api.toggle.active", null);
				}
			}
			return new ConfirmationApiResponseDto(isExits, message);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("confirmation.api.failed", null));
		}
	}
}
