package com.lms.userservice.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.modelmapper.PropertyMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.AssignTeacher;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Teachers;
import com.lms.userservice.entity.Users;
import com.lms.userservice.entity.UsersRoleMapping;
import com.lms.userservice.enums.AcademicStaffProfile;
import com.lms.userservice.enums.CommunicationAction;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.FileExtensions;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.enums.Gender;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.enums.SchoolMenus;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.content.ContentFeignClient;
import com.lms.userservice.feign.master.CoordinatorTypeResponseDto;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.GradesSectionFeignResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.feign.master.SubjectsResponseDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.mapper.TeacherMapper;
import com.lms.userservice.model.GradesSubjectModel;
import com.lms.userservice.model.ImportedFiles;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.model.SubjectAndSubTopicIds;
import com.lms.userservice.model.TeachersFile;
import com.lms.userservice.model.UserMinDetails;
import com.lms.userservice.projection.StudentsProjection;
import com.lms.userservice.projection.TeacherAssignProjection;
import com.lms.userservice.projection.TeachersProjection;
import com.lms.userservice.repository.AssignTeacherRepository;
import com.lms.userservice.repository.BranchCommunicationRepository;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.GradeSectionMappingRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.repository.TokensRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.TeacherAssignRequest;
import com.lms.userservice.request.dto.TeacherRequestDto;
import com.lms.userservice.request.dto.TeachersSelfRegRequestDto;
import com.lms.userservice.request.dto.UserRolesRequestDto;
import com.lms.userservice.response.dto.AcademicStaffResponseDto;
import com.lms.userservice.response.dto.AllTypeUserMinResponseDto;
import com.lms.userservice.response.dto.BulkUploadResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.GradeAccessInfoResponseDto;
import com.lms.userservice.response.dto.GradeAccessResponseDto;
import com.lms.userservice.response.dto.GradeSectionResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.SectionAccessResponseDto;
import com.lms.userservice.response.dto.SmsGatewayRequestDto;
import com.lms.userservice.response.dto.StudentFormativeMinResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentNameDetails;
import com.lms.userservice.response.dto.SubTopicBulkInnerResponseDto;
import com.lms.userservice.response.dto.SubTopicsMinResponseDto;
import com.lms.userservice.response.dto.SubjectAccessResponseDto;
import com.lms.userservice.response.dto.SubjectsMinResponseDto;
import com.lms.userservice.response.dto.SubtopicAccessResponseDto;
import com.lms.userservice.response.dto.TeacherAccessResponseDto;
import com.lms.userservice.response.dto.TeacherAssignResponse;
import com.lms.userservice.response.dto.TeacherAssignmentResponse;
import com.lms.userservice.response.dto.TeacherDetailsForFeignResponseDto;
import com.lms.userservice.response.dto.TeacherEditAccessResponseDto;
import com.lms.userservice.response.dto.TeacherNameResponse;
import com.lms.userservice.response.dto.TeacherResponseDto;
import com.lms.userservice.response.dto.TeacherSubjectMappingFeignResponse;
import com.lms.userservice.response.dto.TokensResponseDto;
import com.lms.userservice.response.dto.UsersResponseDto;
import com.lms.userservice.service.GradeSectionMappingService;
import com.lms.userservice.service.TeacherService;
import com.lms.userservice.service.TokensService;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.Constants;
import com.lms.userservice.util.DateUtilities;
import com.lms.userservice.util.EncryptionAndDecryption;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.FileOperations;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TeacherServiceImpl implements TeacherService {

	@Autowired
	private JwtUtil jwtUtil;

	@Autowired
	private TeacherRepository teacherRepo;

	private ModelMapper modelMapper;

	@Autowired
	private UsersRepository userRepository;

	@Autowired
	private TokensService tokensService;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private TeacherMapper teacherMapper;

	@Autowired
	private UserService userService;

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private TokensRepository tokensRepository;

	@Autowired
	private GradeSectionMappingService gradeSectionMappingService;

	@Autowired
	private AssignTeacherRepository assignTeacherRepo;

	@Autowired
	private ContentFeignClient contentFeign;

	@Autowired
	private BranchPlanMappingsRepository branchPlanRepo;

	@Autowired
	private StudentsRepository studentsRepository;

	@Autowired
	private GradeSectionMappingRepository gradeSectionMappingRepository;

	@Autowired
	private UsersRoleMappingRepository usersRoleMappingRepository;

	@Autowired
	private BranchCommunicationRepository branchCommunicationRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	@Autowired
	public TeacherServiceImpl(ModelMapper modelMapper) {
		this.modelMapper = modelMapper;
		this.modelMapper.addMappings(skipTeacherRequestFieldsMap);
	}

	PropertyMap<TeacherRequestDto, Teachers> skipTeacherRequestFieldsMap = new PropertyMap<TeacherRequestDto, Teachers>() {
		protected void configure() {
			skip(destination.getId());
		}
	};

	/**
	 * Create Teacher
	 * 
	 * @param request The Teacher Request DTO class Object
	 * @return
	 */
	@Override
	@Transactional
	public TeacherResponseDto addTeachers(TeacherRequestDto request) {
		if (!Enums.getIfPresent(LMSEnvironment.class, request.getLmsEnv()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("specify.application.environment", null));

		if (!Enums.getIfPresent(AcademicStaffProfile.class, request.getAcademicStaffProfile()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("invalid.data", null));

		if (!checkTheProfileAndCoordinator(request.getAcademicStaffProfile(), request.getCoordinatorTypeId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.check.profile", null));

		boolean checkCoordinatorTypeExist = mastersFeignClient
				.feignCheckAllCoordinatorTypeByIds(Arrays.asList(request.getCoordinatorTypeId())).getData();
		if ((AcademicStaffProfile.COORDINATOR == AcademicStaffProfile.valueOf(request.getAcademicStaffProfile()))
				&& !checkCoordinatorTypeExist)
			throw new USException(ErrorCodes.NOT_FOUND,
					Translator.toLocale("teacher.coordinator.type.not.exist", null));

		if (teacherRepo.existsByEmailAndDeleted(request.getEmail(), false)
				|| teacherRepo.existsByMobileAndDeleted(request.getMobile(), false))
			throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("academic.staff.already.exist", null));

		try {
			log.info("All checking completed, Teacher registration started...");
			String roleName = CommonUtilities.convertToLowerOrUpper(request.getAcademicStaffProfile(), false);
			String roleId = mastersFeignClient.getOnlyIdByRole(roleName).getData();
			RolesFeignDto roleDto = mastersFeignClient.getRolesById(roleId).getData();
			TeacherResponseDto teacherResponse = new TeacherResponseDto();
			String currentUser = jwtUtil.currentLoginUser();

//			Schools school = schoolRepository.getById(request.getSchool());
			Branches branches = branchRepository.findById(request.getBranch()).get();

			log.info("generating the user name");
			String userName = generateUserName(request.getFirstName(), request.getLastName(), branches.getBranchCode());

			log.info("Mapping Teachers...");
			Teachers teacher = teacherMapper.toEntityFromRequest(request);
			teacher.setRoleId(roleId);
			teacher.setUserName(userName);
			teacher.setCreatedBy(currentUser);
			teacher = teacherRepo.save(teacher);

			if (!StringUtils.isBlank(teacher.getId())) {
				teacherResponse = teacherMapper.mapTeacherEntityToResponseDto(teacher);
				teacherResponse.setRoleId(roleId);
				teacherResponse.setRole(roleDto.getRole());
				teacherResponse.setRoleName(request.getAcademicStaffProfile());
				teacherResponse.setTypeOfEmailSend("CREATE");
				if (!StringUtils.isBlank(request.getCoordinatorTypeId())) {
					CoordinatorTypeResponseDto coordinatorDto = mastersFeignClient
							.feignCoordinatorTypeById(request.getCoordinatorTypeId()).getData();
					teacherResponse.setCoordinatorType(coordinatorDto.getCoordinatorType());
				}
				if (!StringUtils.isBlank(teacher.getId())) {
					log.info("Mapping teachers to user table...");
					Users users = modelMapper.map(teacher, Users.class);
					users.setPhoneNumber(teacher.getMobile());
					users.setCreatedBy(currentUser);
					String password = CommonUtilities.generatePassayPassword();
					users.setPassword(CommonUtilities.passwordEncryptor(password));
					users = userRepository.save(users);

					if (!StringUtils.isBlank(users.getId())) {
						teacherResponse.setUserId(users.getId());
						teacherResponse.setUserName(users.getUserName());
						teacherResponse.setPassword(password);
						log.info("Adding the user role...");
						List<UserRolesRequestDto> userRoles = new ArrayList<>();
						userRoles.add(new UserRolesRequestDto(roleId, true));
						userService.addRoles(users, userRoles);
					}
				}
				/**
				 * sending the sms. check whether the branch is allowed to send the SMS.
				 * Temporally commenting the condition as there are no UI.
				 */
//					if (branchCommunicationRepository.checkSmsAllowed(teacher.getBranches().getId(),
//							CommunicationAction.USER_CREATION.getCode())) {
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(teacher.getFirstName(), userName,
						teacher.getMobile(), teacher.getEmail(), CommunicationAction.USER_CREATION.getCode());
				LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				if (notificationLms != null && notificationLms.getData())
					log.info("SMS send to teacher.");
//					}
			}
			log.info("Teachers registered successfully");
			teacherResponse.setLmsEnv(request.getLmsEnv());
			return teacherResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("teacher.create.failed", null));
		}
	}

	/**
	 * Update Teacher
	 * 
	 * @param teacherId         The Teacher ID String
	 * @param TeacherRequestDto The Teacher Request DTO Object
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public TeacherResponseDto updateTeacher(String teacherId, TeacherRequestDto request) {
		if (!Enums.getIfPresent(AcademicStaffProfile.class, request.getAcademicStaffProfile()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("invalid.data", null));

		if (!checkTheProfileAndCoordinator(request.getAcademicStaffProfile(), request.getCoordinatorTypeId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.check.profile", null));

		if (StringUtils.isBlank(teacherId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.id.not.found", null));

		if (!teacherRepo.existsById(teacherId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.found", null));

		boolean checkCoordinatorTypeExist = mastersFeignClient
				.feignCheckAllCoordinatorTypeByIds(Arrays.asList(request.getCoordinatorTypeId())).getData();
		if ((AcademicStaffProfile.COORDINATOR == AcademicStaffProfile.valueOf(request.getAcademicStaffProfile()))
				&& !checkCoordinatorTypeExist)
			throw new USException(ErrorCodes.NOT_FOUND,
					Translator.toLocale("teacher.coordinator.type.not.exist", null));

		if (teacherRepo.existsByEmailAndDeletedAndIdNot(request.getEmail(), false, teacherId)
				|| teacherRepo.existsByMobileAndDeletedAndIdNot(request.getMobile(), false, teacherId))
			throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("academic.staff.already.exist", null));

		try {
			log.info("All checking completed, Teacher Updation started...");
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			String roleName = CommonUtilities.convertToLowerOrUpper(request.getAcademicStaffProfile(), false);
			String roleId = mastersFeignClient.getOnlyIdByRole(roleName).getData();
			RolesFeignDto rolesDto = mastersFeignClient.getRolesById(roleId).getData();
			log.info("Updating Teachers...");
			Teachers existingTeacher = teacherRepo.getById(teacherId);
			Teachers teacher = teacherMapper.forUpdateToEntityFromRequest(request, existingTeacher.getId());
			teacher.setRoleId(roleId);
			teacher.setUserName(existingTeacher.getUserName());
			teacher.setModifiedAt(currentTime);
			teacher.setLastModifiedBy(currentUser);
			teacher = teacherRepo.save(teacher);
			TeacherResponseDto teacherResponse = null;
			if (!StringUtils.isBlank(teacher.getId())) {
				teacherResponse = teacherMapper.mapTeacherEntityToResponseDto(teacher);
				if (!StringUtils.isBlank(request.getCoordinatorTypeId())) {
					CoordinatorTypeResponseDto coordinatorDto = mastersFeignClient
							.feignCoordinatorTypeById(request.getCoordinatorTypeId()).getData();
					teacherResponse.setCoordinatorType(coordinatorDto.getCoordinatorType());
				}
				UsersResponseDto userResponse = userRepository.findUsersByUserName(teacherResponse.getUserName());
				if (!StringUtils.isBlank(userResponse.getId())) {
					teacherResponse.setUserId(userResponse.getId());
					teacherResponse.setRole(rolesDto.getRole());
				}

				// find the created person name and role name
				Users updateUser = usersRepository.findByUserName(currentUser);
				if (updateUser != null) {
					List<String> createdUserRoles = usersRoleMappingRepository.findAllRolesByUserId(updateUser.getId());
					List<RolesFeignDto> currentUserRoles = mastersFeignClient.getRolesByIds(createdUserRoles).getData();
					teacherResponse.setRoleNameOfAdmin(currentUserRoles.get(0).getRole());
					teacherResponse.setAdminName(updateUser.getFirstName() + " " + updateUser.getLastName());
				}
				teacherResponse.setLmsEnv(request.getLmsEnv());
				teacherResponse.setTypeOfEmailSend("UPDATE");

				// update in users repository
				if (!StringUtils.isEmpty(teacher.getId())) {

					if (!usersRepository.existsByUserName(teacher.getUserName())) {
						throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
					}
					Users users = userRepository.findByUserName(teacher.getUserName());
					users.setEmail(request.getEmail());
					users.setPhoneNumber(request.getMobile());
					users.setModifiedAt(currentTime);
					users.setLastModifiedBy(currentUser);
					userRepository.save(users);
					log.info("Teacher details updated successfully in user table!");
					// Role Mapping Update@pankaj 2024-11-26
					if (!StringUtils.isEmpty(teacherResponse.getUserId())) {
						UsersRoleMapping existingRoleMapping = usersRoleMappingRepository.findByUserId(teacherResponse.getUserId());
			
						if (existingRoleMapping != null) {
							// Update the existing record
							existingRoleMapping.setRoleId(roleId);
							existingRoleMapping.setModifiedAt(currentTime);
							existingRoleMapping.setLastModifiedBy(currentUser);
							usersRoleMappingRepository.save(existingRoleMapping);
							log.info("Updated role mapping for user ID: {} with new role ID: {}", teacherResponse.getUserId(), roleId);
						} else {
							log.warn("No existing role mapping found for user ID: {}", teacherResponse.getUserId());
							throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.role.mapping.not.found", null));
						}
					}
				}
				/**
				 * sending the sms. check whether the branch is allowed to send the SMS.
				 * Temporally commenting the condition as there are no UI.
				 */
//				if (branchCommunicationRepository.checkSmsAllowed(teacher.getBranches().getId(),
//						CommunicationAction.UPDATE_PROFILE.getCode())) {
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(teacher.getFirstName(),
						teacher.getUserName(), teacher.getMobile(), teacher.getEmail(),
						CommunicationAction.UPDATE_PROFILE.getCode());
				LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				if (notificationLms != null && notificationLms.getData())
					log.info("Update info send to teacher.");
//				}
			}
			log.info("Teacher updated successfully");
			return teacherResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("teacher.update.failed", null));
		}
	}

	/**
	 * Fill the coordinatorId if the AcademicStaffProfile is COORDINATOR
	 * 
	 * @param academicStaffProfile
	 * @param coordinatorId
	 * @return
	 */
	private boolean checkTheProfileAndCoordinator(String academicStaffProfile, String coordinatorId) {
		try {
			boolean check = false;
			if (AcademicStaffProfile.COORDINATOR.toString().equals(academicStaffProfile)) {
				if (!StringUtils.isBlank(coordinatorId))
					check = true;
			} else {
				if (coordinatorId == null)
					check = true;
				else
					check = false;
			}
			return check;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.profile.set.failed", null));
		}
	}

	/**
	 * Get Teacher By ID
	 * 
	 * @param teacherId
	 * @return Teacher of particular Teacher ID
	 */
	@Override
	public TeacherResponseDto getTeacherById(String teacherId) {
		log.info("Teacher Details fetch started...");
		if (StringUtils.isBlank(teacherId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.id.not.found", null));

		if (!teacherRepo.existsById(teacherId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.found", null));

		try {
			log.info("Teacher details in progress...");

			Teachers teacher = teacherRepo.getById(teacherId);

			String roleName = CommonUtilities.convertToLowerOrUpper(teacher.getAcademicStaffProfile().getCode(), false);
			String roleId = mastersFeignClient.getOnlyIdByRole(roleName).getData();
			RolesFeignDto rolesDto = mastersFeignClient.getRolesById(roleId).getData();

			TeacherResponseDto teacherGetByIdResponse = new TeacherResponseDto(teacherRepo.getTeachersById(teacherId));
			if (!StringUtils.isBlank(teacher.getCoordinatorTypeId())) {
				CoordinatorTypeResponseDto coordinatorDto = mastersFeignClient
						.feignCoordinatorTypeById(teacher.getCoordinatorTypeId()).getData();
				teacherGetByIdResponse.setCoordinatorType(coordinatorDto.getCoordinatorType());
			}
			UsersResponseDto userResponse = userRepository.findUsersByUserName(teacherGetByIdResponse.getUserName());
			teacherGetByIdResponse.setUserId(userResponse.getId());
			teacherGetByIdResponse.setRole(rolesDto.getRole());
			log.info("Teacher Details fetch complete");
			return teacherGetByIdResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("id.error", null));
		}
	}

	/**
	 * Get Teachers by Pagination
	 * 
	 * @param pageable The pageable
	 * @return Teachers using Pagination
	 */
	@Override
	@SuppressWarnings("all")
	public PaginatedResponse<AcademicStaffResponseDto> getTeachersByPage(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String search, String schoolId, String branchId, String profile,
			Boolean active) {
		log.info("Teacher pagination fetch started...");
		try {
			log.info("Teacher pagination in progress...");
			long totalElements = 0L;
			int totalPages = 0;
			String searchFormat = (!StringUtils.isBlank(search)) ? search.toLowerCase() : null;
			String sortKey = FieldMappers.teacherApiFieldMapper(sortBy);

			List<AcademicStaffResponseDto> response = new ArrayList<>();
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortKey));
			Page<TeachersProjection> paginations = teacherRepo.getPageResponseByFilters(searchFormat, schoolId,
					branchId, profile, active, pageable);

			if (!CollectionUtils.isEmpty(paginations.getContent())) {
				totalElements = paginations.getTotalElements();
				totalPages = paginations.getTotalPages();

				Set<String> coordinatorTypeSet = paginations.getContent().stream()
						.filter(item -> !StringUtils.isBlank(item.getCoordinatorTypeId()))
						.map(TeachersProjection::getCoordinatorTypeId).collect(Collectors.toSet());

				List<String> coordinatorTypeList = (coordinatorTypeSet != null && !coordinatorTypeSet.isEmpty())
						? coordinatorTypeSet.stream().collect(Collectors.toList())
						: null;
				List<CoordinatorTypeResponseDto> coordinatorTypeData = (coordinatorTypeList != null
						&& !coordinatorTypeList.isEmpty())
								? mastersFeignClient.feignAllCoordinatorTypeByIds(coordinatorTypeList).getData()
								: null;

				paginations.getContent().forEach(page -> {
					AcademicStaffResponseDto responseDto = new AcademicStaffResponseDto(page);

					String roleName = CommonUtilities
							.convertToLowerOrUpper(responseDto.getAcademicStaffProfile().getCode(), false);
					String roleId = mastersFeignClient.getOnlyIdByRole(roleName).getData();
					RolesFeignDto roleDto = mastersFeignClient.getRolesById(roleId).getData();

					if (coordinatorTypeData != null && !coordinatorTypeData.isEmpty()) {
						Optional<CoordinatorTypeResponseDto> coordinatorTypeDto = coordinatorTypeData.stream()
								.filter(ct -> ct.getId().equals(responseDto.getCoordinatorTypeId())).findAny();
						if (coordinatorTypeDto.isPresent())
							responseDto.setCoordinatorType(coordinatorTypeDto.get().getCoordinatorType());
					}

					if (!StringUtils.isBlank(responseDto.getRoleId()))
						responseDto.setRole(roleDto.getRole());

					response.add(responseDto);
				});
			}
			log.info("Teacher pagination fetch complete");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.get.list.failed", null));
		}
	}

	/**
	 * Reading and saving the Teachers from a .csv file AcademicStaffProfile is
	 * setting as TEACHER now, need to change after the clarification from client.
	 * 
	 * @param filePath  the file path
	 * @param extension the file extension
	 * @param fileTypes the file type
	 */
	@Override
	@SuppressWarnings("all")
	@Transactional
	public BulkUploadResponseDto readAndSaveTeachersFromFile(String filePath, FileExtensions extension,
			FileTypes fileTypes) {
		log.info("Teachers data reading started..");
		try {
			List<TeacherResponseDto> successData = new ArrayList<>();
			List<TeachersFile> failedData = new ArrayList<>();

			String roleId = mastersFeignClient.getOnlyIdByRole("teacher").getData();
			RolesFeignDto rolesDto = mastersFeignClient.getRolesById(roleId).getData();

			log.info("Reading the data from file");
			ImportedFiles importedFiles = FileOperations.readFromFile(filePath, fileTypes, extension);
			int originalSize = importedFiles != null && !CollectionUtils.isEmpty(importedFiles.getTeachersFile())
					? importedFiles.getTeachersFile().size()
					: 0;

			if (!CollectionUtils.isEmpty(importedFiles.getTeachersFile())) {

				/**
				 * Validating the data and keeping in the failed response. Only accepting the
				 * correct data.
				 */
				List<TeachersFile> validationFailed = validationForCSV(importedFiles.getTeachersFile());
				List<TeachersFile> processingDate = !CollectionUtils.isEmpty(validationFailed)
						? processingDate = importedFiles.getTeachersFile().stream()
								.filter(item -> !validationFailed.contains(item)).distinct()
								.collect(Collectors.toList())
						: importedFiles.getTeachersFile();

				if (!CollectionUtils.isEmpty(validationFailed))// keeping the failed data.
					failedData = validationFailed;

				log.info("Reading Teachers file...");
				for (TeachersFile records : processingDate) {
					StringBuilder remarks = new StringBuilder();

					if (!records.isNull()) {
						boolean isExistByEmail = teacherRepo.existsByEmailAndDeleted(records.getEmail(), false);
						boolean isExistByMob = teacherRepo.existsByMobileAndDeleted(records.getMobile(), false);

						// Academic staff shouldn't be allowed to created with same email/mob.
						if (!isExistByEmail && !isExistByMob) {
							Schools schools = schoolRepository.getByCode(records.getSchoolCode());
							Branches branches = branchRepository.getByNameAndSchoolsAndDeleted(records.getBranchName(),
									schools, false);

							log.info("Generating the user_name");
							String userName = generateUserName(records.getFirstName(), records.getLastName(),
									branches.getBranchCode());

							Teachers teachers = modelMapper.map(records, Teachers.class);
							teachers.setDob(!StringUtils.isBlank(records.getDob())
									? DateUtilities.convertStringToLocalDate(records.getDob())
									: null);
							teachers.setJoinDate(!StringUtils.isBlank(records.getJoinDate())
									? DateUtilities.convertStringToLocalDate(records.getJoinDate())
									: null);
							teachers.setSchools(schools);
							teachers.setBranches(branches);
							teachers.setUserName(userName);
							teachers.setAcademicStaffProfile(AcademicStaffProfile.TEACHER);
							String upperGender = records.getGender().toUpperCase();
							teachers.setGender(Gender.valueOf(upperGender));

							if (schools != null && branches != null) // if school, branch exist save
								teachers = teacherRepo.save(teachers);
							else {
								// if school/branch empty catch the user details with reason
								String schoolMessage = schools == null ? "Unable to find the school." : null;
								String branchMessage = branches == null ? "Unable to find the branch." : null;
								if (!StringUtils.isBlank(schoolMessage))
									remarks.append(remarks != null && remarks.length() > 0 ? schoolMessage
											: ", " + schoolMessage);
								if (!StringUtils.isBlank(branchMessage))
									remarks.append(remarks != null && remarks.length() > 0 ? branchMessage
											: ", " + branchMessage);
								records.setRemarks(remarks);
								records.setFailed(true);
								failedData.add(records);
							}

							TeacherResponseDto teacherResponse = null;
							if (!StringUtils.isBlank(teachers.getId())) {
								teacherResponse = teacherMapper.mapTeacherEntityToResponseDto(teachers);

								teacherResponse.setRoleId(roleId);
								teacherResponse.setRoleName("Teacher");
								teacherResponse.setTypeOfEmailSend("CREATE");
								if (!StringUtils.isBlank(teacherResponse.getCoordinatorTypeId())) {
									CoordinatorTypeResponseDto coordinatorDto = mastersFeignClient
											.feignCoordinatorTypeById(teacherResponse.getCoordinatorTypeId()).getData();
									teacherResponse.setCoordinatorType(coordinatorDto.getCoordinatorType());
								}
								log.info("Academic staff added to the system, mapping to Users in-progress");
								Users user = modelMapper.map(teachers, Users.class);
								user.setPhoneNumber(teachers.getMobile());
								user.setUserName(teachers.getUserName());
								String password = CommonUtilities.generatePassayPassword();
								user.setPassword(CommonUtilities.passwordEncryptor(password));
								user = userRepository.save(user);
								if (!StringUtils.isBlank(user.getId())) {
									log.info("Academic staffs mapped with user, user-role mapping in-progress");
									List<UserRolesRequestDto> userRoles = new ArrayList<>();
									userRoles.add(new UserRolesRequestDto(roleId, true));
									teacherResponse.setRole(rolesDto.getRole());
									userService.addRoles(user, userRoles);
								}

//								TeacherResponseDto response = new TeacherResponseDto();
//								BeanUtils.copyProperties(response, teachers);
								teacherResponse.setPassword(password);

								teacherResponse.setTypeOfEmailSend("CREATE");
								teacherResponse.setLmsEnv("DEV");
								sendEmail(teacherResponse);
								log.info("Academic staff imported to the system.");
								successData.add(teacherResponse);
							}
						} else {
							// if along with mail/mobile already exist catch it in failed response.
							String emailMessage = isExistByEmail
									? Translator.toLocale("teacher.already.exist", null) + " with the email."
									: null;
							String mobileMessage = isExistByMob
									? Translator.toLocale("teacher.already.exist", null) + " with the mobile."
									: null;

							if (!StringUtils.isBlank(emailMessage))
								remarks.append(
										remarks != null && remarks.length() > 0 ? emailMessage : ", " + emailMessage);

							if (!StringUtils.isBlank(mobileMessage))
								remarks.append(
										remarks != null && remarks.length() > 0 ? mobileMessage : ", " + mobileMessage);

							records.setRemarks(remarks);
							records.setFailed(true);
							failedData.add(records);
						}
					} else {
						// if any field got null capture those record in failed list
						String mandatory = "All the column in the CSV files are mandatory please fill it.";
						remarks.append(remarks != null && remarks.length() > 0 ? mandatory : ", " + mandatory);
						records.setRemarks(remarks);
						records.setFailed(true);
						failedData.add(records);
					}
				}
			} else
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("read.csv.file.failed", null));

			int successSize = !CollectionUtils.isEmpty(successData) ? successData.size() : 0;
			int failedSize = !CollectionUtils.isEmpty(failedData) ? failedData.size() : 0;
			String message = null;
			if (successSize > 0 && failedSize > 0)
				message = "Out of " + originalSize + " " + successSize + " data have been saved, " + failedSize
						+ " failed to save.";
			else if (successSize > 0 && failedSize == 0)
				message = "Out of " + originalSize + ", all were successfully saved.";
			else
				message = "Out of " + originalSize + ", all failed to save.";

			return new BulkUploadResponseDto(successData, failedData, message);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("teacher.create.failed", null));
		}
	}

	private void sendEmail(TeacherResponseDto dto) {
		try {

			CreateUserEmailRequestDto requestDto = new CreateUserEmailRequestDto();

			BeanUtils.copyProperties(requestDto, dto);

			String userName = requestDto.getUserName();
			Users users = userRepository.findByUserName(userName);
			requestDto.setToEmail(users.getEmail());
			String userId = EncryptionAndDecryption.encrypt(requestDto.getUserId());
			String resetPasswordUrl = mastersFeignClient.getResetPasswordWithBaseUrl(requestDto.getLmsEnv()).getData();
			requestDto.setForgotPasswordEmailLink(resetPasswordUrl + "?userId=" + userId);
			requestDto.setBaseFEUrl(resetPasswordUrl.substring(0, resetPasswordUrl.indexOf("#")));
			log.info("Sending email started...");

			try {
				if (requestDto.getToEmail() != null && !requestDto.getToEmail().isBlank()
						&& !requestDto.getToEmail().isEmpty()) {
					if (requestDto.getTypeOfEmailSend().equals("CREATE"))
						notificationFeignClient.userCreated(requestDto);
					else
						notificationFeignClient.editUser(requestDto);
				}
			} catch (Exception e) {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("user.create.email.failed", null));
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.info("Unable send Email: " + e.getMessage());
		}
	}

	/**
	 * Checking the validation of CSV files, if any record not pass the condition
	 * return it.
	 * 
	 * @param request
	 * @return
	 */
	private List<TeachersFile> validationForCSV(List<TeachersFile> request) {
		try {
			List<TeachersFile> failedRecords = new ArrayList<>();
			for (TeachersFile record : request) {
				StringBuilder remarks = new StringBuilder();

//				if (StringUtils.isBlank(record.getFirstName())) {
//					String firstNameMandatory = "First name is mandatory.";
//					remarks.append(remarks!=null ? firstNameMandatory : ", " + firstNameMandatory);
//				} else {
//					boolean firstNameValid = CommonUtilities.validationByRegex(Constants.INDIAN_NAME_IN_ENG,
//							record.getFirstName());
//					if (!firstNameValid) {
//						String firstNameValidation = "Name format is not proper, please check the first name given.";
//						remarks.append(remarks!=null ? firstNameValidation : ", " + firstNameValidation);
//					}
//				}

//				if (StringUtils.isBlank(record.getLastName())) {
//					String lastNameMandatory = "Last name is mandatory.";
//					remarks.append(remarks!=null ? lastNameMandatory : ", " + lastNameMandatory);
//				} else {
//					boolean lastNameValid = CommonUtilities.validationByRegex(Constants.INDIAN_NAME_IN_ENG,
//							record.getLastName());
//					if (!lastNameValid) {
//						String lastNameValidation = "Name format is not proper, please check the last name given.";
//						remarks.append(remarks.isEmpty() ? lastNameValidation : ", " + lastNameValidation);
//					}
//				}

				if (StringUtils.isBlank(record.getEmail())) {
					String emailMandatory = "Email is mandatory.";
					remarks.append(remarks != null && remarks.length() > 0 ? emailMandatory : ", " + emailMandatory);
				} else {
					boolean emailValid = CommonUtilities.validationByRegex(Constants.EMAIL_REGEX, record.getEmail());
					if (!emailValid) {
						String emailValidation = "Please check the given email.";
						remarks.append(
								remarks != null && remarks.length() > 0 ? emailValidation : ", " + emailValidation);
					}
				}

				if (StringUtils.isBlank(record.getMobile())) {
					String mobileMandatory = "Mobile number is mandatory.";
					remarks.append(remarks != null && remarks.length() > 0 ? mobileMandatory : ", " + mobileMandatory);
				} else {
					boolean mobileValid = CommonUtilities.validationByRegex(Constants.PHONE_REGEX, record.getMobile());
					if (!mobileValid) {
						String mobileValidation = "Please provide the Indian mobile number.";
						remarks.append(
								remarks != null && remarks.length() > 0 ? mobileValidation : ", " + mobileValidation);
					}
				}

//				if (StringUtils.isBlank(record.getGender())) {
//					String genderMandatory = "Gender is mandatory.";
//					remarks.append(remarks!=null ? genderMandatory : ", " + genderMandatory);
//				} else {
//					boolean genderValid = Enums.getIfPresent(Gender.class, record.getGender()).isPresent();
//					if (!genderValid) {
//						String genderValidation = "Please provide the gender in capital letters.";
//						remarks.append(remarks!=null ? genderValidation : ", " + genderValidation);
//					}
//				}

				// Validation stoped_05_04_2024
//				if (StringUtils.isBlank(record.getDob())) {
//					String dobMandatory = "Date of birth is mandatory.";
//					remarks.append(remarks != null && remarks.length() > 0 ? dobMandatory : ", " + dobMandatory);
//				} else {
//					boolean dobValid = CommonUtilities.validationByRegex(Constants.DD_MM_YYYY, record.getDob());
//					if (!dobValid) {
//						String dobValidation = "Please provide the date of birth format in dd-MM-yyyy.";
//						remarks.append(remarks != null && remarks.length() > 0 ? dobValidation : ", " + dobValidation);
//					}
//
//					boolean isExpiredDate = DateUtilities.checkIfTheDateHasPassed(record.getDob());
//					if (!isExpiredDate) {
//						String expiredDate = "Sorry the given date of birth is not an expired date.";
//						remarks.append(remarks != null && remarks.length() > 0 ? expiredDate : ", " + expiredDate);
//					}
//				}
//
//				if (StringUtils.isBlank(record.getJoinDate())) {
//					String joinMandatory = "Join date is mandatory.";
//					remarks.append(remarks != null && remarks.length() > 0 ? joinMandatory : ", " + joinMandatory);
//				} else {
//					boolean joinValid = CommonUtilities.validationByRegex(Constants.DD_MM_YYYY, record.getJoinDate());
//					if (!joinValid) {
//						String joinValidation = "Please provide the join date format in dd-MM-yyyy.";
//						remarks.append(
//								remarks != null && remarks.length() > 0 ? joinValidation : ", " + joinValidation);
//					}
//				}

				if (StringUtils.isBlank(record.getSchoolCode())) {
					String schoolMandatory = "School code is mandatory and make sure existing code is providing.";
					remarks.append(remarks != null && remarks.length() > 0 ? schoolMandatory : ", " + schoolMandatory);
				}

				if (StringUtils.isBlank(record.getBranchName())) {
					String branchMandatory = "Branch name is mandatory and branch-school realtion should have to exists.";
					remarks.append(remarks != null && remarks.length() > 0 ? branchMandatory : ", " + branchMandatory);
				}

				// setting the failed records
				if (remarks != null && remarks.length() > 0) {
					record.setRemarks(remarks);
					record.setFailed(true);
					failedRecords.add(record);
				}
			}
			return failedRecords;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("csv.validation.failed", null));
		}
	}

	/**
	 * Teacher self registration using token
	 * 
	 * @param request The Teacher request DTO
	 * @return
	 */
	@Override
	@Transactional
	public TeacherResponseDto teacherSelfRegistration(TeachersSelfRegRequestDto request) {
		if (!Enums.getIfPresent(AcademicStaffProfile.class, request.getAcademicStaffProfile()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("invalid.data", null));

		if (!checkTheProfileAndCoordinator(request.getAcademicStaffProfile(), request.getCoordinatorTypeId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.check.profile", null));

		if (!tokensRepository.existsByToken(request.getToken()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.token.not.exist", null));

		try {
			log.info("Teacher self registration started...");
			TeacherResponseDto selfRegistrationResponse = new TeacherResponseDto();
			String roleId = mastersFeignClient.getOnlyIdByRole("teacher").getData();
			RolesFeignDto rolesDto = mastersFeignClient.getRolesById(roleId).getData();
			Schools school = schoolRepository.getById(request.getSchool());
			Branches branch = branchRepository.getById(request.getBranch());
			String userName = generateUserName(request.getFirstName(), request.getLastName(), branch.getBranchCode());

			Teachers teachers = teacherMapper.selfRegistrationToEntityFromRequest(request);
			teachers.setUserName(userName);
			teachers.setSchools(school);
			teachers.setBranches(branch);
			teachers.setCoordinatorTypeId(request.getCoordinatorTypeId());
			teachers = teacherRepo.save(teachers);

			if (!StringUtils.isBlank(teachers.getId())) {
				selfRegistrationResponse = teacherMapper.mapTeacherEntityToResponseDto(teachers);
				String teacherId = teachers.getId();
				if (!StringUtils.isBlank(request.getCoordinatorTypeId())) {
					CoordinatorTypeResponseDto coordinatorDto = mastersFeignClient
							.feignCoordinatorTypeById(request.getCoordinatorTypeId()).getData();
					selfRegistrationResponse.setCoordinatorType(coordinatorDto.getCoordinatorType());
				}
				log.info("Academic staff added to the system, mapping to Users in-progress");
				Users user = modelMapper.map(teachers, Users.class);
				String password = CommonUtilities.generatePassayPassword();
				user.setPassword(CommonUtilities.passwordEncryptor(password));
				user.setPhoneNumber(teachers.getMobile());
				user = userRepository.save(user);

				if (!StringUtils.isBlank(user.getId())) {
					selfRegistrationResponse.setUserId(user.getId());
					selfRegistrationResponse.setUserName(user.getUserName());
					log.info("Mapping of the Academic staff is completed!");
					List<UserRolesRequestDto> userRoles = new ArrayList<>();
					userRoles.add(new UserRolesRequestDto(roleId, true));
					selfRegistrationResponse.setRole(rolesDto.getRole());
					userService.addRoles(user, userRoles);
				}

				if (!StringUtils.isEmpty(request.getToken())) {
					TokensResponseDto tokensResponseDto = tokensService
							.updateTokenDuringRegistration(request.getToken(), user.getId());
					if (!tokensResponseDto.getTokenDetails().isEmpty()) {
						boolean isExist = tokensResponseDto.getTokenDetails().stream()
								.anyMatch(token -> token.getUserId().contains(teacherId));
						if (isExist)
							log.info("User mapped with token successfully");
						else
							log.info("failed to map the User with token");
					}
				}
			}
			return selfRegistrationResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.self.registration.failed", null));
		}
	}

	/**
	 * Mark Teacher as Deleted
	 * 
	 * @param teacherId
	 * @return boolean
	 */
	@Override
	public Boolean removeTeacher(String teacherId) {
		if (!teacherRepo.existsById(teacherId))
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("id.invalid", null));

		try {
			Long currentTime = new Date().getTime();
			boolean response = false;
			String currentUser = jwtUtil.currentLoginUser();
			Teachers teachers = teacherRepo.getById(teacherId);
			int deleteStatus = teacherRepo.setDeleteStatus(teacherId, currentTime, currentUser);
			if (deleteStatus > 0) {
				log.info("Teacher marked as deleted");

				log.info("Delete the teacher from users table");
				Users users = usersRepository.findByUserName(teachers.getUserName());
				String email = users.getEmail() + "-" + currentTime;
				String mobile = users.getPhoneNumber() + "-" + currentTime;

				int deleted = usersRepository.setDeleteStatus(users.getId(), currentTime, currentUser, email, mobile);
				log.info(deleted > 0 ? "User's status set into deleted" : "failed to set status into deleted");

				log.info("Delete the user-role mapping");
				Long userRole = userService.deleteUsersRoleMapping(users.getId());

				if (deleteStatus > 0 && deleted > 0 && userRole > 0) {
					log.info("All relationship with teacher removed.");
					response = true;
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("teacher.delete.failed", null));
		}
	}

	/**
	 * Method to generate user name
	 * 
	 * @param firstName  The user first name
	 * @param lastName   The user last name
	 * @param branchCode The school code
	 * @return dot separated combination of first name, last name and School code
	 */
	private String generateUserName(String firstName, String lastName, String branchCode) {
		try {
			String[] fname = firstName.split(" ");
			String firstNames = fname[0];
			System.out.println(firstNames);
			String lowerCaseFirstName = firstNames.toLowerCase();
			System.out.println("Lowercase first name: " + lowerCaseFirstName);
			String[] lname = lastName.split(" ");
			String lastNames = lname[0];
			System.out.println(lastNames);
			String lowerCaseLastName = lastNames.toLowerCase();
			System.out.println("Lowercase last name: " + lowerCaseLastName);
			String userName = lowerCaseFirstName + "." + lowerCaseLastName + "." + branchCode.toLowerCase();
			Long userCount = userRepository.countByStudentUsername(userName);

			if (userCount != null && userCount > 0)
				userName = lowerCaseFirstName + "." + lowerCaseLastName + "." + branchCode.toLowerCase() + "."
						+ Long.toString(userCount);

			return userName;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.USER_ID_GENERATION_FAILED);
		}
	}

	@Override
	public Boolean updateActiveField(String id, boolean active) {
		if (!teacherRepo.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("teacher.id.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			Teachers teacher = teacherRepo.getById(id);
			teacher.setActive(active);
			teacher.setModifiedAt(currentTime);
			teacher.setLastModifiedBy(currentUser);
			teacher = teacherRepo.save(teacher);

			log.info("Toggle the teacher from users table");
			Users users = usersRepository.findByUserName(teacher.getUserName());

			int toggled = usersRepository.setActiveStatus(users.getId(), active, currentTime, currentUser);
			log.info(toggled > 0 ? "User's active-status toggled" : "failed to toggled the active status.");

			return (teacher.isActive() == active) && (toggled > 0);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.failed", null));
		}
	}

	/**
	 * Find the academic_staff and then find the user class by the user_name
	 * 
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType) {
		if (!teacherRepo.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		try {
			boolean isExits = false;
			String message = "There is no mapping related to this staff currently.";
			Teachers teachers = teacherRepo.getById(id);
			Users users = userRepository.findByUserName(teachers.getUserName());
			List<Long> mappingCounts = userRepository.findAllMappingCountOfUser(users.getId());
			if (!mappingCounts.isEmpty()) {
				isExits = mappingCounts.stream().anyMatch(count -> count > 0);
				if (isExits) {
					if (OperationType.DELETE == OperationType.valueOf(operationType))
						message = "Some data are connected with this staff. Deleting may cause some loss of data. "
								+ "Do you still want to delete " + teachers.getFirstName() + " "
								+ teachers.getLastName();
					else
						message = "Some data are connected with this staff. "
								+ "Changing the active status may cause some constrain violation issues. "
								+ "Do you still want to change the active status of " + teachers.getFirstName() + " "
								+ teachers.getLastName();
				} else
					return message;
			}
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	/**
	 * Get the last modification done on the table teachers return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return teacherRepo.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	@Override
	public List<NameCommonResponseDto> getAllTeacherMappingsForCoordinatorType(String coordinatorTypeId) {
		try {
			return teacherRepo.getAllTeacherMappingsByCoordinatorType(coordinatorTypeId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.coordinator.type.mapping.fetch.failed", null));
		}
	}

	@Override
	public List<NameCommonResponseDto> getAllTeacherMappingsForRole(String roleId) {
		try {
			return teacherRepo.getAllTeacherMappingsByRole(roleId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	@Override
	public List<TeacherAssignResponse> assignSubjectToTeacher(String teacherId, TeacherAssignRequest request) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("teacher.id.not.found", null));

			/**
			 * Removing the already existing exceptional handling by the requirement of
			 * Azvasa on 07-Dec-2023
			 * 
			 * checkingDuplicateTeacherAccess(teacherId, request);
			 */

			Teachers teacher = teacherRepo.getById(teacherId);
			String schoolId = teacher.getBranches().getSchools().getId();
			String branchId = teacher.getBranches().getId();

			// check if sections and grades mapping exist
			List<SectionsResponseDto> sectionData = gradeSectionMappingService
					.getAllSectionByGradesSchoolAndBranch(null, request.getGradeId(), branchId, schoolId);

			List<String> sectionIds = sectionData.stream().map(SectionsResponseDto::getId).collect(Collectors.toList());

			if (sectionIds != null && !CollectionUtils.isEmpty(sectionIds)) {
				if (request.getSectionIds().stream().anyMatch(section -> !sectionIds.contains(section)))
					throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
							Translator.toLocale("gs.mapping.corrupted.data", null));
			}

			// check if subject and subtopics mapping exist
			LMSResponse<SubjectsResponseDto> subjectLMSResponse = mastersFeignClient
					.getSubjectsById(request.getSubjectId());
			List<String> subTopicsResponse = new ArrayList<>();

			if (subjectLMSResponse != null) {
				List<SubTopicBulkInnerResponseDto> subtopics = subjectLMSResponse.getData().getSubTopics();
				subTopicsResponse = subtopics != null
						? subtopics.stream().filter(SubTopicBulkInnerResponseDto::isActive)
								.map(SubTopicBulkInnerResponseDto::getId).collect(Collectors.toList())
						: new ArrayList<>();
			}

			List<String> finalSubTopicsResponse = subTopicsResponse;
			if (subTopicsResponse != null && !CollectionUtils.isEmpty(subTopicsResponse)) {
				if (request.getSubtopicIds().stream().anyMatch(subtopic -> !finalSubTopicsResponse.contains(subtopic)))
					throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
							Translator.toLocale("subject.or.subtopic.mapping.do.not.exist", null));
			}

			List<AssignTeacher> assignTeacherEntities = new ArrayList<>();

			/*
			 * create a list of assign teacher entities before saving them. This is done
			 * because we need to have different rows for different combinations of
			 * teacherId - grade - section - subject -subtopic request . We can have
			 * multiple sections and subtopics in the request
			 */
			if (!request.getSectionIds().isEmpty() && !request.getSubtopicIds().isEmpty()) {
				for (String section : request.getSectionIds()) {
					for (String subtopic : request.getSubtopicIds()) {
						boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, section, subtopic,
								request.getGradeId(), request.getSubjectId());

						AssignTeacher assignTeacher = new AssignTeacher();
						assignTeacher = teacherMapper.mapAssignTeacherRequestToEntity(request, assignTeacher);
						assignTeacher.setSectionId(section);
						assignTeacher.setSubtopicId(subtopic);
						assignTeacher.setTeacherId(teacherId);
						if (!alreadyExists)
							assignTeacherEntities.add(assignTeacher);
					}
				}
			} else if (!request.getSectionIds().isEmpty()) { // where the section not exists for a grade.
				for (String section : request.getSectionIds()) {
					boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, section, null,
							request.getGradeId(), request.getSubjectId());

					AssignTeacher assignTeacher = new AssignTeacher();
					assignTeacher = teacherMapper.mapAssignTeacherRequestToEntity(request, assignTeacher);
					assignTeacher.setSectionId(section);
					assignTeacher.setTeacherId(teacherId);
					if (!alreadyExists)
						assignTeacherEntities.add(assignTeacher);
				}
			} else if (!request.getSubtopicIds().isEmpty()) { // where the subject with subtopic
				for (String subtopic : request.getSubtopicIds()) {
					boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, null, subtopic,
							request.getGradeId(), request.getSubjectId());

					AssignTeacher assignTeacher = new AssignTeacher();
					assignTeacher = teacherMapper.mapAssignTeacherRequestToEntity(request, assignTeacher);
					assignTeacher.setSubtopicId(subtopic);
					assignTeacher.setTeacherId(teacherId);
					if (!alreadyExists)
						assignTeacherEntities.add(assignTeacher);
				}
			} else {
				boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, null, null, request.getGradeId(),
						request.getSubjectId());
				AssignTeacher assignTeacher = new AssignTeacher();
				assignTeacher = teacherMapper.mapAssignTeacherRequestToEntity(request, assignTeacher);
				assignTeacher.setTeacherId(teacherId);
				if (!alreadyExists)
					assignTeacherEntities.add(assignTeacher);
			}

			assignTeacherEntities = assignTeacherRepo.saveAll(assignTeacherEntities);
			List<TeacherAssignResponse> responseList = teacherMapper
					.mapAssignTeacherEntityToResponse(assignTeacherEntities);
			setMasterDataResponseForAssignTeacher(responseList);

			return responseList;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.assigned.subjects.failed", null));
		}
	}

	@Override
	public List<GradeAccessInfoResponseDto> gradeAccessInformation(String teacherId) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

			List<GradeAccessInfoResponseDto> response = new ArrayList<>();
			Teachers teachers = teacherRepo.getById(teacherId);

			List<TeacherAssignProjection> projectionList = assignTeacherRepo
					.assignedSubjectGradeAndSectionByTeacherId(teacherId);

			if (!CollectionUtils.isEmpty(projectionList)) {

				// Separating the list of subject, grade and section
				List<String> subjectIds = assignTeacherRepo.getSubjectIdsByTeacherId(teacherId);

				List<String> gradeIds = projectionList.stream().map(TeacherAssignProjection::getGradeId)
						.collect(Collectors.toList());

				List<String> sectionIds = projectionList.stream()
						.filter(item -> !StringUtils.isEmpty(item.getSectionId()))
						.map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.toList());

				// master-feign call
				LMSResponse<List<SubjectsMinResponseDto>> masterSubjectResponse = !CollectionUtils.isEmpty(subjectIds)
						? mastersFeignClient.getAllSubjectsByIds(subjectIds)
						: null;
				List<SubjectsMinResponseDto> subjectDtoList = masterSubjectResponse != null
						? masterSubjectResponse.getData()
						: null;

				LMSResponse<List<GradesResponseDto>> masterGradeResponse = !CollectionUtils.isEmpty(gradeIds)
						? mastersFeignClient.getAllGradesByIds(gradeIds)
						: null;
				List<GradesResponseDto> gradeDtoList = masterGradeResponse != null ? masterGradeResponse.getData()
						: null;

				LMSResponse<List<SectionsResponseDto>> masterSectionResponse = !CollectionUtils.isEmpty(sectionIds)
						? mastersFeignClient.getAllSectionsByIds(sectionIds)
						: null;
				List<SectionsResponseDto> sectionDtoList = masterSectionResponse != null
						? masterSectionResponse.getData()
						: null;

				if (!CollectionUtils.isEmpty(subjectDtoList)) {
					for (SubjectsMinResponseDto subjectDto : subjectDtoList) {
						GradeAccessInfoResponseDto responseDto = new GradeAccessInfoResponseDto(
								subjectDto.getSubjectId(), subjectDto.getSubject(), subjectDto.isSkilledSubject());

						// listing the grades, if the section not exist student-count will find for
						// grade.
						List<String> gradesList = assignTeacherRepo.getGradeByTeacherAndSubject(teacherId,
								subjectDto.getSubjectId());
						if (!CollectionUtils.isEmpty(gradesList)) {
							List<GradeAccessResponseDto> grades = new ArrayList<>();
							for (String gradeId : gradesList) {
								Optional<GradesResponseDto> gradeDto = gradeDtoList.stream()
										.filter(item -> item.getId().equals(gradeId)).findAny();
								if (gradeDto.isPresent()) {
									GradeAccessResponseDto gradeAccessDto = new GradeAccessResponseDto(
											gradeDto.get().getId(), gradeDto.get().getGrade());

									// listing the section with student count
									List<String> sectionsList = assignTeacherRepo.getSectionByTeacherSubjectAndGrade(
											teacherId, subjectDto.getSubjectId(), gradeDto.get().getId());
									if (!CollectionUtils.isEmpty(sectionsList)) {
										List<SectionAccessResponseDto> sections = new ArrayList<>();
										for (String sectionId : sectionsList) {
											Optional<SectionsResponseDto> sectionDto = sectionDtoList.stream()
													.filter(item -> item.getId().equals(sectionId)).findAny();
											if (sectionDto.isPresent()) {
												long countStudent = studentsRepository.findCountByFilters(
														teachers.getSchools().getId(), teachers.getBranches().getId(),
														gradeDto.get().getId(), sectionDto.get().getId());
												sections.add(new SectionAccessResponseDto(sectionDto.get().getId(),
														sectionDto.get().getSection(), countStudent));
											}
											gradeAccessDto.setSections(sections);
										}
									} else
										gradeAccessDto.setStudentCount(
												studentsRepository.findCountByFilters(teachers.getSchools().getId(),
														teachers.getBranches().getId(), gradeDto.get().getId(), null));

									grades.add(gradeAccessDto);
								}
							}
							responseDto.setGrades(grades);
						}
						response.add(responseDto);
					}
				}
			}

			return response;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.assigned.subjects.failed", null));
		}
	}

	@Override
	public Boolean toggleActiveMappedSubjects(String id, boolean active) {
		try {
			if (!assignTeacherRepo.existsById(id))
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("assign.teacher.id.not.exist", null));

			AssignTeacher assignTeacher = assignTeacherRepo.getById(id);
			assignTeacher.setModifiedAt(Instant.now().getEpochSecond());
			assignTeacher.setLastModifiedBy(jwtUtil.currentLoginUser());
			assignTeacher.setActive(active);
			assignTeacher = assignTeacherRepo.save(assignTeacher);
			return assignTeacher.isActive();

		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("toggle.active.teacher.assigned.subjects.failed", null));
		}
	}

	/**
	 * Fetch all subjects assigned to a teacher/coordinator/principal
	 *
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	@Override
	public List<SubjectsMinResponseDto> listAllSubjectsByTeacher(String teacherId, String gradeId, String sectionId,
			String menuName) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

			if (!StringUtils.isBlank(menuName)) {
				if (!Enums.getIfPresent(SchoolMenus.class, menuName).isPresent())
					throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("specify.menu", null));
			}

			Teachers teachers = teacherRepo.getById(teacherId);

			if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR) {
				String coordinatorId = !StringUtils.isEmpty(teachers.getCoordinatorTypeId())
						? teachers.getCoordinatorTypeId()
						: null;

				log.info("COORDINATOR's assigned subjects are listing...");
				if (!StringUtils.isEmpty(gradeId)) {
					// master call to check the assigned grades & compare with request
					boolean isAssignedGrades = false;
					LMSResponse<List<String>> mResponse = mastersFeignClient.getGradeListByCoordinatorId(coordinatorId);
					if (mResponse != null && !CollectionUtils.isEmpty(mResponse.getData())) {
						isAssignedGrades = mResponse.getData().stream().anyMatch(item -> item.contains(gradeId));
						if (!isAssignedGrades)
							throw new USException(ErrorCodes.NO_CONTENT,
									Translator.toLocale("grade.access.not.provide.user", null));
					}
				}
			}

			return fromTeacherAssessEntity(teachers, gradeId, sectionId, menuName);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.assigned.subjects.failed", null));
		}
	}

	/**
	 * Filtering the subjects based on teacherId, gradeId or sectionId. Login person
	 * can be TEACHER, COORDINATOR, PRINCIPAL.
	 * 
	 * @param teachers
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	private List<SubjectsMinResponseDto> fromTeacherAssessEntity(Teachers teachers, String gradeId, String sectionId,
			String menuName) {
		try {
			List<SubjectsMinResponseDto> response = new ArrayList<>();
			List<SubjectAndSubTopicIds> toMasterService = new ArrayList<>();
			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();			
			// if it is teacher take only assigned subject and sub-topics			
			if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
				List<String> subjectIds = assignTeacherRepo.getAllSubjectIdsByFilters(teachers.getId(), gradeId,
						sectionId);
				if (!CollectionUtils.isEmpty(subjectIds)) {
					subjectIds.forEach(item -> {
						List<String> subTopicIds = assignTeacherRepo.getAllSubTopicIdsByFilters(teachers.getId(),
								gradeId, sectionId, item);
						toMasterService.add(new SubjectAndSubTopicIds(item, subTopicIds,gradeId));
					});					
				}
			} else {
				// if it is principal or coordinator take the subject & sub-topics based
				// on filter
				List<TeacherAssignProjection> assignProjection = assignTeacherRepo.findAllSubjectsAndSubtopics(schoolId,
						branchId, gradeId, sectionId);

				if (!CollectionUtils.isEmpty(assignProjection)) {
					// reading into a Map interface
					Map<String, List<String>> subjectSubTopicsMap = new HashMap<>();
					for (TeacherAssignProjection projection : assignProjection) {
						if (!subjectSubTopicsMap.containsKey(projection.getSubjectId())) {
							List<String> subTopics = new ArrayList<>();
							subTopics.add(projection.getSubTopicId());
							subjectSubTopicsMap.put(projection.getSubjectId(), subTopics);
						} else {
							subjectSubTopicsMap.get(projection.getSubjectId()).add(projection.getSubTopicId());
						}
					}

					// From Map interface bring the data to feign call's list of objects
					if (!CollectionUtils.isEmpty(subjectSubTopicsMap)) {
						for (Map.Entry<String, List<String>> item : subjectSubTopicsMap.entrySet())
							toMasterService.add(new SubjectAndSubTopicIds(item.getKey(), item.getValue(),gradeId));
					}
				}else{//if no subject is assign to teacher in that case fetch plan featured subject
					if (!StringUtils.isBlank(menuName)) {
						// based on the menu standing remove the subjects by plan_template.
						String planId = branchPlanRepo.findPlanIdByBranchId(branchId);
						String columnName = SchoolMenus.valueOf(menuName).getPlanTemplateColumn();
						LMSResponse<List<String>> featuredSubjects = mastersFeignClient
								.listOfSubjectByPuchasedPlanFeature(planId, gradeId, columnName);

						// setting the value to response, if there is any matching found
						if (featuredSubjects != null && !CollectionUtils.isEmpty(featuredSubjects.getData())) {
							for (String subjectId : featuredSubjects.getData()) {
								// Assuming no subtopics or empty list when coming from plan feature
								toMasterService.add(new SubjectAndSubTopicIds(subjectId, new ArrayList<>(), gradeId));
							}
						}
							

					}
				}
			}

			if (!CollectionUtils.isEmpty(toMasterService)) {
				// make a call to master-service
				LMSResponse<List<SubjectsMinResponseDto>> masterResponse = mastersFeignClient
						.getAllSubjectsBySelectedIds(toMasterService);
				if (masterResponse != null && !CollectionUtils.isEmpty(masterResponse.getData())) {

					if (!StringUtils.isBlank(menuName)) {
						// based on the menu standing remove the subjects by plan_template.
						String planId = branchPlanRepo.findPlanIdByBranchId(branchId);
						String columnName = SchoolMenus.valueOf(menuName).getPlanTemplateColumn();
						LMSResponse<List<String>> featuredSubjects = mastersFeignClient
								.listOfSubjectByPuchasedPlanFeature(planId, gradeId, columnName);

						// setting the value to response, if there is any matching found
						if (featuredSubjects != null && !CollectionUtils.isEmpty(featuredSubjects.getData()))
							response = masterResponse.getData().stream()
									.filter(item -> featuredSubjects.getData().contains(item.getSubjectId()))
									.collect(Collectors.toList());

					} else
						response = masterResponse.getData();

					if (!CollectionUtils.isEmpty(response))
						log.info("Completed the Subject/Subtopics retrieving!");
				}
			}

			return response;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.assigned.subjects.failed", null));
		}
	}

	/**
	 * Fetching the grade-section combo from assign-teacher table.
	 * assignedGradesFromPlan
	 * 
	 * @param teacherId
	 * @return
	 */
	@Override
	public List<GradesSectionFeignResponseDto> gradesFromTeacherAccessEntity(String teacherId, String menuName) {
		try {
			Map<String, Integer> gradeMap = createGradeMap();
			Teachers teachers = teacherRepo.getById(teacherId);
			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();
			List<GradesSectionFeignResponseDto> response = new ArrayList<>();

			// Fetching all assigned grades of academic staff
			List<String> assignedGrades = assignTeacherRepo.getAllGradeIdsByTeacherId(teacherId);
			String planId = branchPlanRepo.findPlanIdByBranchId(teachers.getBranches().getId());
			LMSResponse<List<String>> planMasterResponse = mastersFeignClient.assignedGradesFromPlan(planId);		
			// Filter assignedGrades by planMasterResponse if available
			if (planMasterResponse != null && !CollectionUtils.isEmpty(planMasterResponse.getData())) {
				assignedGrades = assignedGrades.stream()
											.filter(planMasterResponse.getData()::contains)
											.distinct()
											.collect(Collectors.toList());
			}

			// If Principal or Coordinator check the grades in school. If it is coordinator
			// need to check in the CoordinatorType from master-service.
			if (teachers.getAcademicStaffProfile() != AcademicStaffProfile.TEACHER) {
				log.info("Find the grades by default set for principal and coordinator. Seting up the grade-section");
				String coordinatorId = !StringUtils.isEmpty(teachers.getCoordinatorTypeId())
						? teachers.getCoordinatorTypeId()
						: null;
				
				LMSResponse<List<String>> gradeMasterResponse = null;

				// calling master service to get the assigned grade to the coordinator
				if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR)
					gradeMasterResponse = mastersFeignClient.getGradeListByCoordinatorId(coordinatorId);

				// calling master service to get the assigned grade to the plan
				if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.PRINCIPAL)
					gradeMasterResponse = planMasterResponse;//mastersFeignClient.assignedGradesFromPlan(planId);


				LMSResponse<List<String>> filteredResponse = new LMSResponse<>();
				filteredResponse.setData(
					gradeMasterResponse.getData().stream()
						.filter(planMasterResponse.getData()::contains)
						.distinct()
						.collect(Collectors.toList())
				);
				gradeMasterResponse = filteredResponse;
					
				// Finding the grades under school & branch and checking the common grades from
				// master-service with grade-section mapping.
				List<String> gradesInSchool = gradeSectionMappingRepository.findAllActiveGradesBySchoolBranch(schoolId,
						branchId);
				List<String> filteredGrades = null;
				if (!CollectionUtils.isEmpty(gradesInSchool)) {
					log.info("Checking the grades from master-service exists in school and branch");
					if (gradeMasterResponse != null && !CollectionUtils.isEmpty(gradeMasterResponse.getData())) {
						filteredGrades = gradeMasterResponse.getData().stream().filter(gradesInSchool::contains)
								.distinct().collect(Collectors.toList());
					}
				}

				// Adding all the grades and filtering w/o duplicate grades.
				if (!CollectionUtils.isEmpty(filteredGrades)) {
					List<String> tempGrades = new ArrayList<>();
					tempGrades.addAll(assignedGrades);
					tempGrades.addAll(filteredGrades);
					assignedGrades = !CollectionUtils.isEmpty(tempGrades) ? filteredGrades.stream()
							.filter(gradeId -> !StringUtils.isBlank(gradeId)).distinct().collect(Collectors.toList())
							: filteredGrades;
				}
			}

			// Setting the Grade-Section combination
			if (!CollectionUtils.isEmpty(assignedGrades)) {
				// creating the object to master-service feign call.
				List<GradeSectionResponseDto> toMasterService = new ArrayList<>();

				// if AcademicStaffProfile.TEACHER then only assigned grades & section visible.
				if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
					assignedGrades.stream().forEach(gradeId -> {
						List<String> assignedSections = assignTeacherRepo.getAllSectionsByGradeAndTeacherId(teacherId,
								gradeId);
						toMasterService.add(new GradeSectionResponseDto(gradeId, assignedSections));
					});
				} else {
					// If AcademicStaffProfile is not TEACHER, list down the sections mapped with
					// the grades.
					assignedGrades.stream().forEach(gradeId -> {
						List<String> assignedSections = gradeSectionMappingRepository
								.findAllSectionByGradeIdBranchAndSchool(gradeId, branchId, schoolId);
						toMasterService.add(new GradeSectionResponseDto(gradeId, assignedSections));
					});
				}

				// master-feign call
				if (!CollectionUtils.isEmpty(toMasterService)) {
					LMSResponse<List<GradesSectionFeignResponseDto>> assignedGradeResponse = mastersFeignClient
							.assignedGradesAndSectionForAcademicStaffs(toMasterService);
					if (assignedGradeResponse != null && !CollectionUtils.isEmpty(assignedGradeResponse.getData()))
						response = assignedGradeResponse.getData();
				}
			}
			// Before convert
			for (GradesSectionFeignResponseDto dto : response) {
				log.info("Grade sorting before");
				log.info(dto.getGrade() + " : " + dto.getId());

			}

			// Sorting the response list based on the gradeMap values
			response.sort(Comparator.comparingInt(dto -> gradeMap.get(dto.getGrade())));

			// Printing the sorted response list
			// After convert
			log.info("======================================");
			for (GradesSectionFeignResponseDto dto : response) {
				log.info("Grade sorting after");
				log.info(dto.getGrade() + " : " + dto.getId());
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT,
					Translator.toLocale("teacher.assigned.grade.fetch.failed", null));
		}
	}

	private Map<String, Integer> createGradeMap() {
		Map<String, Integer> gradeMap = new HashMap<>();
		// Adding key-value pairs to the map
		gradeMap.put("Nursery", 1);
		gradeMap.put("Junior Kg", 2);
		gradeMap.put("Senior Kg", 3);
		gradeMap.put("Grade 1", 4);
		gradeMap.put("Grade 2", 5);
		gradeMap.put("Grade 3", 6);
		gradeMap.put("Grade 4", 7);
		gradeMap.put("Grade 5", 8);
		gradeMap.put("Grade 6", 9);
		gradeMap.put("Grade 7", 10);
		gradeMap.put("Grade 8", 11);
		gradeMap.put("Grade 9", 12);
		gradeMap.put("Grade 10", 13);
		gradeMap.put("Grade 11", 14);
		gradeMap.put("Grade 12", 15);
		gradeMap.put("Grade 13", 16);
		gradeMap.put("SEM11", 17);
		gradeMap.put("Test grade", 18);
		gradeMap.put("10TH", 19);
		gradeMap.put("nursery", 20);
		gradeMap.put("Sem02", 21);
		gradeMap.put("TEST", 22);
		gradeMap.put("test", 23);
		return gradeMap;
	}

	@Override
	public Integer getCountOfTeacherUploads(String teacherId, String subjectId) {
		try {
			Integer response = 0;
			List<GradesSubjectModel> gradeSubjectResponse = assignTeacherRepo.getGradeSubjectModelForTeacher(teacherId,
					subjectId);
			if (!gradeSubjectResponse.isEmpty()) {
				LMSResponse<List<String>> lmsChapterIds = mastersFeignClient
						.getAllChapterIdsByGradesSubjects(gradeSubjectResponse);
				List<String> chapterIds = lmsChapterIds != null ? lmsChapterIds.getData() : new ArrayList<>();
				LMSResponse<Integer> lmsResponse = !chapterIds.isEmpty()
						? contentFeign.getCountOfTeacherUploads(chapterIds)
						: null;
				response = lmsResponse != null ? lmsResponse.getData() : 0;
			}
			return response;

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.upload.content.count.failed", null));
		}
	}

	@Override
	public Integer getCountOfStudents(String teacherId, String subjectId) {

		if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

		try {
			return assignTeacherRepo.getCountOfStudents(teacherId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.student.count.success", null));
		}
	}

	@Override
	public TeacherSubjectMappingFeignResponse getTeacherSubjectMappingMinResponse(String teacherId, String subjectId) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("teacher.id.not.found", null));

			Teachers teacher = teacherRepo.getById(teacherId);
			String boardId = teacher.getSchools().getBoardId();
			List<GradesSubjectModel> subjects = assignTeacherRepo.getGradeSubjectModelForTeacher(teacherId, subjectId);
			TeacherSubjectMappingFeignResponse response = new TeacherSubjectMappingFeignResponse();
			response.setSubjects(subjects);
			response.setBoardId(boardId);
			return response;

		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.quiz.count.success", null));
		}
	}

	@Override
	public List<TeacherNameResponse> getTeacherNameResponseByIds(List<String> teacherIds) {
		try {
			return teacherRepo.getTeacherName(teacherIds);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.names.failed", null));
		}
	}

	@Override
	public Boolean checkIfTeacherExistsById(String teacherId) {
		try {
			return teacherRepo.existsByIdAndDeleted(teacherId, false);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.get.by.id.failed", null));
		}
	}

	private void setMasterDataResponseForAssignTeacher(List<TeacherAssignResponse> response) {
		List<String> gradeIds = response.stream().map(TeacherAssignResponse::getGradeId).collect(Collectors.toList());
		List<String> sectionIds = response.stream().map(TeacherAssignResponse::getSectionId)
				.collect(Collectors.toList());
		List<String> subjectIds = response.stream().map(TeacherAssignResponse::getSubjectId)
				.collect(Collectors.toList());
		List<String> subtopics = response.stream().map(TeacherAssignResponse::getSubtopicId)
				.collect(Collectors.toList());

		List<GradesResponseDto> gradeResponseList = !gradeIds.isEmpty()
				? mastersFeignClient.getAllGradesByIds(gradeIds).getData()
				: new ArrayList<>();
		Map<String, String> gradeMap = gradeResponseList.stream()
				.collect(Collectors.toMap(GradesResponseDto::getId, GradesResponseDto::getGrade));

		List<SectionsResponseDto> sectionResponseList = sectionIds != null && !CollectionUtils.isEmpty(sectionIds)
				? mastersFeignClient.getAllSectionsByIds(sectionIds).getData()
				: new ArrayList<>();
		Map<String, String> sectionMap = sectionResponseList.stream()
				.collect(Collectors.toMap(SectionsResponseDto::getId, SectionsResponseDto::getSection));

		List<SubjectsMinResponseDto> subjectsResponseList = !subjectIds.isEmpty()
				? mastersFeignClient.getAllSubjectsByIds(subjectIds).getData()
				: new ArrayList<>();
		Map<String, String> subjectMap = subjectsResponseList.stream()
				.collect(Collectors.toMap(SubjectsMinResponseDto::getSubjectId, SubjectsMinResponseDto::getSubject));

		List<SubTopicsMinResponseDto> subtopicsResponseList = subtopics != null && !CollectionUtils.isEmpty(subtopics)
				? mastersFeignClient.getAllSubTopicsByIds(subtopics).getData()
				: new ArrayList<>();
		Map<String, String> subtopicMap = subtopicsResponseList.stream()
				.collect(Collectors.toMap(SubTopicsMinResponseDto::getId, SubTopicsMinResponseDto::getSubTopic));

		response.forEach(data -> {
			data.setGrade(gradeMap.get(data.getGradeId()));
			data.setSection(sectionMap.get(data.getSectionId()));
			data.setSubject(subjectMap.get(data.getSubjectId()));
			data.setSubtopic(subtopicMap.get(data.getSubtopicId()));
		});

	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto checkTheMappingForConcept(String id, String operationType) {
		if (!teacherRepo.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			boolean isExits = false;
			String message = Translator.toLocale("confirmation.api.default.message", null);
			List<Long> usages = assignTeacherRepo.findTheMappingForTeacher(id);
			if (!CollectionUtils.isEmpty(usages)) {
				isExits = usages.stream().anyMatch(count -> count > 0);
				if (isExits)
					message = Translator.toLocale("confirmation.api.permission.denied", null);
				else {
					message = (OperationType.DELETE == OperationType.valueOf(operationType))
							? Translator.toLocale("confirmation.api.delete", null)
							: Translator.toLocale("confirmation.api.toggle.active", null);
				}
			}
			return new ConfirmationApiResponseDto(isExits, message);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("confirmation.api.failed", null));
		}
	}

	/**
	 * Checking the mapping of coordinator type.
	 * 
	 * @param coordinatorTypeId
	 * @return
	 */
	@Override
	public boolean checkTheMappingForCoordinatoryType(String coordinatorTypeId) {

		try {
			if (StringUtils.isEmpty(coordinatorTypeId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("please.provide.madatory.field", null));

			boolean isExists = false;

			List<Long> coordinatoryList = teacherRepo.findTheMappingorCoordinatorType(coordinatorTypeId);
			if (!CollectionUtils.isEmpty(coordinatoryList))
				isExists = coordinatoryList.stream().anyMatch(item -> item > 0);

			return isExists;
		} catch (USException use) {
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("mapping.api.failed", null));
		}
	}

	/**
	 * Find the mapping for subject, this is the feign API method.
	 * 
	 * @param subjectId
	 * @return
	 */
	@Override
	public boolean checkTheMappingForSubject(String subjectId) {

		try {
			if (StringUtils.isEmpty(subjectId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("please.provide.madatory.field", null));

			boolean isExists = false;

			List<Long> subjectList = assignTeacherRepo.findTheMappingForSubject(subjectId);
			if (!CollectionUtils.isEmpty(subjectList))
				isExists = subjectList.stream().anyMatch(item -> item > 0);

			return isExists;
		} catch (USException use) {
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("mapping.not.found", null));
		}
	}

	/**
	 * Find the mapping for sub-topic, this is the feign API method.
	 * 
	 * @param subTopicId
	 * @return
	 */
	@Override
	public boolean checkTheMappingForSubTopic(String subTopicId) {

		try {
			if (StringUtils.isEmpty(subTopicId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("please.provide.madatory.field", null));

			boolean isExists = false;

			List<Long> subjectList = assignTeacherRepo.findTheMappingForSubTopic(subTopicId);
			if (!CollectionUtils.isEmpty(subjectList))
				isExists = subjectList.stream().anyMatch(item -> item > 0);

			return isExists;
		} catch (USException use) {
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("mapping.not.found", null));
		}
	}

	/**
	 * This is feign call for finding section or grade for a particular teacher to
	 * content-service. return only assigned section
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @param subtopicId
	 * @param teacherId
	 * @return
	 */
	@Override
	public GradeSectionResponseDto getGradeOrSectionToTeacherForQuizRelease(String schoolId, String branchId,
			String gradeId, String subjectId, String subtopicId, String teacherId) {
		try {
			GradeSectionResponseDto response = new GradeSectionResponseDto(gradeId);
			List<String> sectionsUnderGrade = gradeSectionMappingRepository.findSections(gradeId, branchId, schoolId);
			if (!CollectionUtils.isEmpty(sectionsUnderGrade)) {
				List<String> assignSection = assignTeacherRepo.getSectionByTeacherSubjectGradeAndSubtopic(teacherId,
						subjectId, gradeId, subtopicId);
				if (!CollectionUtils.isEmpty(assignSection))
					response.setSectionIds(
							sectionsUnderGrade.stream().filter(assignSection::contains).collect(Collectors.toList()));
			}
			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT,
					Translator.toLocale("teacher.assigned.section.or.grade.listing.failed", null));
		}
	}

	/**
	 * Teacher access for coordinator and principal
	 * <p>
	 * 
	 * 
	 * @return
	 */
	@Override
	public PaginatedResponse<AcademicStaffResponseDto> currentTeacherAccessForCoordinatorORPrincipal(int pageNumber,
			int pageSize, String schoolId, String branchId, String teacherId, String search, Boolean active) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.found", null));

			Teachers teachers = teacherRepo.getById(teacherId);
			List<String> commonGradeList = null;
			// Getting grades data information
			List<String> gradeIds = gradeSectionMappingRepository.findAllActiveGradesBySchoolBranch(schoolId, branchId);

			if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				// calling feign client from master-service and getting list of grade Ids
				LMSResponse<List<String>> mGradeResponse = mastersFeignClient
						.getGradeListByCoordinatorId(teachers.getCoordinatorTypeId());
				if (mGradeResponse != null && !CollectionUtils.isEmpty(mGradeResponse.getData())) {
					commonGradeList = mGradeResponse.getData().stream().filter(gradeIds::contains)
							.collect(Collectors.toList());
				}
			}
			Long totalElements = 0L;
			Integer totalPages = 0;
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;
			Pageable pageable = PageRequest.of(pageNumber, pageSize);
			List<AcademicStaffResponseDto> response = new ArrayList<>();

			List<String> gradeIdForQuery = !CollectionUtils.isEmpty(commonGradeList) ? commonGradeList : gradeIds;

			Page<TeachersProjection> paginations = !CollectionUtils.isEmpty(gradeIdForQuery)
					? teacherRepo.findTeachersByGradesForPrincipalORCoordinator(teacherId, searchFormat, schoolId,
							branchId, gradeIdForQuery, active, pageable)
					: teacherRepo.findTeachersWithOutGradesForPrincipal(teacherId, searchFormat, schoolId, branchId,
							active, pageable);

			if (!CollectionUtils.isEmpty(paginations.getContent())) {

				totalElements = paginations.getTotalElements();
				totalPages = paginations.getTotalPages();
				List<String> coordinatorTypeList = paginations.getContent().stream()
						.filter(item -> !StringUtils.isBlank(item.getCoordinatorTypeId()))
						.map(TeachersProjection::getCoordinatorTypeId).distinct().collect(Collectors.toList());
				List<CoordinatorTypeResponseDto> coordinatorTypeData = (!CollectionUtils.isEmpty(coordinatorTypeList))
						? mastersFeignClient.feignAllCoordinatorTypeByIds(coordinatorTypeList).getData()
						: null;
				paginations.getContent().forEach(page -> {
					AcademicStaffResponseDto responseDto = new AcademicStaffResponseDto(page);

					if (coordinatorTypeData != null && !coordinatorTypeData.isEmpty()) {
						Optional<CoordinatorTypeResponseDto> coordinatorTypeDto = coordinatorTypeData.stream()
								.filter(ct -> ct.getId().equals(responseDto.getCoordinatorTypeId())).findAny();

						if (coordinatorTypeDto.isPresent())
							responseDto.setCoordinatorType(coordinatorTypeDto.get().getCoordinatorType());
					}
					response.add(responseDto);

				});

			}

			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("teacher.get.list.failed", null));
		}
	}

	/**
	 * Fetching the grade-section based students-count from assign-teacher and
	 * grade-section-mapping tables * assignedGrades or not assigned for principal
	 * and coordinator
	 * 
	 * @param teacherId
	 * @return
	 */
	@Override
	public List<GradeAccessInfoResponseDto> schoolStrength(String teacherId) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.id.not.found", null));

			List<GradeAccessInfoResponseDto> response = new ArrayList<>();
			Teachers teachers = teacherRepo.getById(teacherId);
			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();

			List<GradeAccessResponseDto> accessibleGradeList = new ArrayList<>();

			List<String> gradeIdForQuery = null;
			// Getting grades data information
			List<String> activeGradeIds = gradeSectionMappingRepository.findAllActiveGradesBySchoolBranch(schoolId,
					branchId);			
			LMSResponse<List<String>> gradeMasterResponse = null;
			String planId= branchPlanRepo.findPlanIdByBranchId(teachers.getBranches().getId());
				//@pankaj @for principal 24-02-2025
			gradeMasterResponse = mastersFeignClient.assignedGradesFromPlan(planId);
			if (AcademicStaffProfile.COORDINATOR == teachers.getAcademicStaffProfile()) {
				// calling feign client from master-service and getting list of grade Ids
				LMSResponse<List<String>> mGradeResponse = mastersFeignClient
						.getGradeListByCoordinatorId(teachers.getCoordinatorTypeId());
				
				LMSResponse<List<String>> filteredResponse = new LMSResponse<>();
				filteredResponse.setData(
					mGradeResponse.getData().stream()
						.filter(gradeMasterResponse.getData()::contains)
						.distinct()
						.collect(Collectors.toList())
				);
				mGradeResponse = filteredResponse;
				if (mGradeResponse != null && !CollectionUtils.isEmpty(mGradeResponse.getData())) {
					gradeIdForQuery = mGradeResponse.getData().stream().filter(activeGradeIds::contains)
							.collect(Collectors.toList());
				}
			} else{
				// planId= branchPlanRepo.findPlanIdByBranchId(teachers.getBranches().getId());ß
				//@pankaj @for principal 24-02-2025
				// gradeMasterResponse = mastersFeignClient.assignedGradesFromPlan(planId);
				if (!CollectionUtils.isEmpty(activeGradeIds)) {
					log.info("Checking the grades from master-service exists in school and branch");
					if (gradeMasterResponse != null && !CollectionUtils.isEmpty(gradeMasterResponse.getData())) {
						gradeIdForQuery = gradeMasterResponse.getData().stream().filter(activeGradeIds::contains)
								.distinct().collect(Collectors.toList());
					}
				}
			}
				// gradeIdForQuery = activeGradeIds;

			// finding the grade-section combo
			List<TeacherAssignProjection> gradeSectionMapping = gradeSectionMappingRepository
					.findAllActiveGradesBySchoolBranchIds(schoolId, branchId);

			List<String> mappingSectionIds = gradeSectionMapping.stream()
					.filter(item -> !StringUtils.isEmpty(item.getSectionId()))
					.map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.toList());

			// master feign call for principal and coordinator
			LMSResponse<List<GradesResponseDto>> plMasterGradeResponse = !CollectionUtils.isEmpty(gradeIdForQuery)
					? mastersFeignClient.getAllGradesByIds(gradeIdForQuery)
					: null;
			List<GradesResponseDto> plGradeDtoList = plMasterGradeResponse != null ? plMasterGradeResponse.getData()
					: null;

			LMSResponse<List<SectionsResponseDto>> plMasterSectionResponse = !CollectionUtils.isEmpty(mappingSectionIds)
					? mastersFeignClient.getAllSectionsByIds(mappingSectionIds)
					: null;
			List<SectionsResponseDto> plSectionDtoList = plMasterSectionResponse != null
					? plMasterSectionResponse.getData()
					: null;

			// principal related listing the grades, if the section not exist student-count
			// will find for grade
			if (!CollectionUtils.isEmpty(gradeIdForQuery)) {
				GradeAccessInfoResponseDto responseDto = new GradeAccessInfoResponseDto();

				for (String plGradeId : gradeIdForQuery) {
					Optional<GradesResponseDto> commonGradeDto = plGradeDtoList.stream()
							.filter(item -> item.getId().equals(plGradeId)).findAny();

					if (commonGradeDto.isPresent()) {
						GradeAccessResponseDto gradeAccessDto = new GradeAccessResponseDto(commonGradeDto.get().getId(),
								commonGradeDto.get().getGrade());

						if (gradeSectionMappingRepository.existsBySectionByFilter(schoolId, branchId, plGradeId)) {
							List<SectionAccessResponseDto> accessSectionList = new ArrayList<>();
							List<String> extractedSectionList = gradeSectionMapping.stream()
									.filter(item -> item.getGradeId().equals(plGradeId))
									.map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.toList());

							for (String plSectionId : extractedSectionList) {
								Optional<SectionsResponseDto> commonSectionDto = plSectionDtoList.stream()
										.filter(item -> item.getId().equals(plSectionId)).findAny();
								if (commonSectionDto.isPresent()) {
									long pCountStudent = studentsRepository.findCountByPrincipalGradeAndSections(
											schoolId, branchId, plGradeId, plSectionId);
									accessSectionList.add(new SectionAccessResponseDto(commonSectionDto.get().getId(),
											commonSectionDto.get().getSection(), pCountStudent));
								}
							}
							gradeAccessDto.setSections(accessSectionList);

						} else
							gradeAccessDto.setStudentCount(studentsRepository.findCountByPrincipalGradeAndSections(
									schoolId, branchId, commonGradeDto.get().getId(), null));

						accessibleGradeList.add(gradeAccessDto);
					}
				}
				Comparator<GradeAccessResponseDto> gradeComparator = (g1, g2) -> {
					String grade1 = g1.getGrade();
					String grade2 = g2.getGrade();

					// Extracting the numeric part of the grade
					int num1 = extractGradeNumber(grade1);
					int num2 = extractGradeNumber(grade2);

					return Integer.compare(num1, num2);
				};
				accessibleGradeList.sort(gradeComparator);
				responseDto.setGrades(accessibleGradeList);
				response.add(responseDto);
			}

			// finding the assigned subjects and finding the student under the grade OR
			// section
			if (assignTeacherRepo.existsByTeacherIdAndDeleted(teacherId, false)) {
				List<TeacherAssignProjection> teacherAccessItems = assignTeacherRepo
						.findAllAssignedItemsByTeacher(teacherId);
				if (!CollectionUtils.isEmpty(teacherAccessItems)) {
					List<String> subjectIdList = teacherAccessItems.stream()
							.filter(item -> !StringUtils.isBlank(item.getSubjectId()))
							.map(TeacherAssignProjection::getSubjectId).distinct().collect(Collectors.toList());

					LMSResponse<List<SubjectsMinResponseDto>> masterSubjectResponse = !CollectionUtils
							.isEmpty(subjectIdList) ? mastersFeignClient.getAllSubjectsByIds(subjectIdList) : null;
					List<SubjectsMinResponseDto> subjectDtoList = masterSubjectResponse != null
							? masterSubjectResponse.getData()
							: null;

					if (!CollectionUtils.isEmpty(subjectDtoList)) {
						for (SubjectsMinResponseDto subjectDto : subjectDtoList) {
							GradeAccessInfoResponseDto assignedResponseDto = new GradeAccessInfoResponseDto(
									subjectDto.getSubjectId(), subjectDto.getSubject(), subjectDto.isSkilledSubject());
							Map<String, List<String>> gradeSectionMap = new HashMap<>();

							for (TeacherAssignProjection projection : teacherAccessItems) {
								if (subjectDto.getSubjectId().equals(projection.getSubjectId())) {
									List<String> sectionValues = new ArrayList<>();
									if (!gradeSectionMap.containsKey(projection.getGradeId())) {
										sectionValues.add(projection.getSectionId());
										gradeSectionMap.put(projection.getGradeId(), sectionValues);
									} else {
										gradeSectionMap.get(projection.getGradeId()).add(projection.getSectionId());
									}
								}
							}

							if (!CollectionUtils.isEmpty(gradeSectionMap)) {
								List<GradeAccessResponseDto> gradeAccessList = new ArrayList<>();
								for (Map.Entry<String, List<String>> gsMap : gradeSectionMap.entrySet()) {

									Optional<GradesResponseDto> commonGradeDto = plGradeDtoList.stream()
											.filter(item -> item.getId().equals(gsMap.getKey())).findAny();
									if (commonGradeDto.isPresent()) {
										GradeAccessResponseDto gradeAccess = new GradeAccessResponseDto(
												commonGradeDto.get().getId(), commonGradeDto.get().getGrade());
										List<SectionAccessResponseDto> sectionAccessList = new ArrayList<>();
										if (!CollectionUtils.isEmpty(gsMap.getValue())) {
											for (String sectionAccessId : gsMap.getValue()) {
												Optional<SectionsResponseDto> commonSectionDto = plSectionDtoList
														.stream().filter(item -> item.getId().equals(sectionAccessId))
														.findAny();
												if (commonSectionDto.isPresent()) {
													long studentCount = studentsRepository
															.findCountByPrincipalGradeAndSections(schoolId, branchId,
																	gsMap.getKey(), sectionAccessId);
													SectionAccessResponseDto sectionAccessResponseDto = new SectionAccessResponseDto(
															commonSectionDto.get().getId(),
															commonSectionDto.get().getSection(), studentCount);
													sectionAccessList.add(sectionAccessResponseDto);
												}
											}
										} else {
											long studentCount = studentsRepository.findCountByPrincipalGradeAndSections(
													schoolId, branchId, gsMap.getKey(), null);
											gradeAccess.setStudentCount(studentCount);
										}
										gradeAccessList.add(gradeAccess);
									}
								}
								assignedResponseDto.setGrades(gradeAccessList);
							}
							response.add(assignedResponseDto);
						}
					}
				}
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.principal.students.counted.failed", null));
		}
	}

	/**
	 * Return the response of teacher access. Will refactor this below code later.
	 * 
	 * @param teacherId
	 * @return
	 */
	@Override
	public List<TeacherAccessResponseDto> getAllAssignedAccessOfTeacher(String teacherId) {
		try {
			List<TeacherAccessResponseDto> response = new ArrayList<>();
			List<TeacherAssignProjection> projections = assignTeacherRepo.findAllAccessOfTeacher(teacherId);
			if (!CollectionUtils.isEmpty(projections)) {
				List<GradesResponseDto> mGradeList = null;
				List<String> gradeIds = projections.stream().filter(item -> !StringUtils.isBlank(item.getGradeId()))
						.map(TeacherAssignProjection::getGradeId).distinct().collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(gradeIds)) {
					LMSResponse<List<GradesResponseDto>> mGradeResponse = mastersFeignClient
							.getAllGradesByIds(gradeIds);
					mGradeList = mGradeResponse != null ? mGradeResponse.getData() : new ArrayList<>();
				}

				List<SectionsResponseDto> mSectionList = null;
				List<String> sectionIds = projections.stream().filter(item -> !StringUtils.isBlank(item.getSectionId()))
						.map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(sectionIds)) {
					LMSResponse<List<SectionsResponseDto>> mSectionResponse = mastersFeignClient
							.getAllSectionsByIds(sectionIds);
					mSectionList = mSectionResponse != null ? mSectionResponse.getData() : new ArrayList<>();
				}

				List<SubjectsMinResponseDto> mSubjectList = null;
				List<String> subjectIds = projections.stream().filter(item -> !StringUtils.isBlank(item.getSubjectId()))
						.map(TeacherAssignProjection::getSubjectId).distinct().collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(subjectIds)) {
					LMSResponse<List<SubjectsMinResponseDto>> mSubjectResponse = mastersFeignClient
							.getAllSubjectsByIds(subjectIds);
					mSubjectList = mSubjectResponse != null ? mSubjectResponse.getData() : new ArrayList<>();
				}

				List<SubTopicsMinResponseDto> mSubTopicList = null;
				List<String> subTopicIds = projections.stream()
						.filter(item -> !StringUtils.isBlank(item.getSubTopicId()))
						.map(TeacherAssignProjection::getSubTopicId).distinct().collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(subTopicIds)) {
					LMSResponse<List<SubTopicsMinResponseDto>> mSubTopicResponse = mastersFeignClient
							.getAllSubTopicsByIds(subTopicIds);
					mSubTopicList = mSubTopicResponse != null ? mSubTopicResponse.getData() : new ArrayList<>();
				}

				// Setting the response
				if (!CollectionUtils.isEmpty(mGradeList)) {
					// First setting the grade
					for (GradesResponseDto gradeDto : mGradeList) {
						TeacherAccessResponseDto responseDto = new TeacherAccessResponseDto(gradeDto.getId(),
								gradeDto.getGrade());

						List<String> matchingSubjects = projections.stream()
								.filter(item -> item.getGradeId().equals(gradeDto.getId())
										&& !StringUtils.isBlank(item.getSubjectId()))
								.map(TeacherAssignProjection::getSubjectId).distinct().collect(Collectors.toList());

						// Second setting subjects
						if (!CollectionUtils.isEmpty(matchingSubjects) && !CollectionUtils.isEmpty(mSubjectList)) {
							List<SubjectAccessResponseDto> subjects = new ArrayList<>();
							for (SubjectsMinResponseDto subjectDto : mSubjectList) {
								boolean isMatchingSubject = matchingSubjects.stream()
										.anyMatch(item -> item.contains(subjectDto.getSubjectId()));
								if (isMatchingSubject) {
									SubjectAccessResponseDto matchSubjectDto = new SubjectAccessResponseDto(
											subjectDto.getSubjectId(), subjectDto.getSubject());

									// Find the sections for the subject which has no sub-topic.
									List<String> matchingSections = projections.stream()
											.filter(item -> item.getGradeId().equals(gradeDto.getId())
													&& item.getSubjectId().equals(matchSubjectDto.getSubjectId())
													&& StringUtils.isBlank(item.getSubTopicId()))
											.map(TeacherAssignProjection::getSectionId).distinct()
											.collect(Collectors.toList());

									if (!CollectionUtils.isEmpty(matchingSections)
											&& !CollectionUtils.isEmpty(mSectionList)) {
										List<SectionAccessResponseDto> sectionsUnderSubject = mSectionList.stream()
												.filter(mSection -> matchingSections.contains(mSection.getId()))
												.map(item -> new SectionAccessResponseDto(item.getId(),
														item.getSection()))
												.collect(Collectors.toList());
										matchSubjectDto.setSectionsForSubject(sectionsUnderSubject);
									} else {
										// find out the sub-topic then set the sections.
										List<TeacherAssignProjection> sectionSubTopicProjection = projections.stream()
												.filter(item -> item.getGradeId().equals(gradeDto.getId())
														&& item.getSubjectId().equals(matchSubjectDto.getSubjectId())
														&& !StringUtils.isBlank(item.getSubTopicId()))
												.distinct().collect(Collectors.toList());

										if (!CollectionUtils.isEmpty(sectionSubTopicProjection)) {
											Map<String, Set<String>> subTopicSectionsMap = new HashMap<>();
											for (TeacherAssignProjection subTopicSection : sectionSubTopicProjection) {
												if (!subTopicSectionsMap.containsKey(subTopicSection.getSubTopicId())) {
													Set<String> sections = new HashSet<>();
													if (!StringUtils.isBlank(subTopicSection.getSectionId()))
														sections.add(subTopicSection.getSectionId());
													subTopicSectionsMap.put(subTopicSection.getSubTopicId(), sections);
												} else
													subTopicSectionsMap.get(subTopicSection.getSubTopicId())
															.add(subTopicSection.getSectionId());
											}

											if (!CollectionUtils.isEmpty(subTopicSectionsMap)) {
												List<SubtopicAccessResponseDto> subTopicsAccess = new ArrayList<>();
												for (Map.Entry<String, Set<String>> item : subTopicSectionsMap
														.entrySet()) {

													if (!CollectionUtils.isEmpty(mSubTopicList)) {
														Optional<SubTopicsMinResponseDto> optionalSubTopic = mSubTopicList
																.stream()
																.filter(subT -> subT.getId().equals(item.getKey()))
																.findAny();
														if (optionalSubTopic.isPresent()) {
															SubtopicAccessResponseDto subTopicAccessDto = new SubtopicAccessResponseDto(
																	optionalSubTopic.get().getId(),
																	optionalSubTopic.get().getSubTopic());
															if (!CollectionUtils.isEmpty(item.getValue())
																	&& !CollectionUtils.isEmpty(mSectionList)) {
																List<SectionAccessResponseDto> sectionForSubTopic = mSectionList
																		.stream()
																		.filter(mSection -> item.getValue()
																				.contains(mSection.getId()))
																		.map(mSecItem -> new SectionAccessResponseDto(
																				mSecItem.getId(),
																				mSecItem.getSection()))
																		.collect(Collectors.toList());
																subTopicAccessDto
																		.setSectionsForSubTopic(sectionForSubTopic);
															}
															subTopicsAccess.add(subTopicAccessDto);
															matchSubjectDto.setSubTopics(subTopicsAccess);
														}
													}

												}
											}
										}
									}
									subjects.add(matchSubjectDto);
								}
							}
							responseDto.setSubjects(subjects);
						}
						response.add(responseDto);
					}
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.principal.students.counted.failed", null));
		}
	}

	/**
	 * This is a feign call for teacher service. Get count of student.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param subjectId
	 * @param gradeId
	 * @param sectionId
	 * @param subtopicId
	 * @return
	 */
	@Override
	public Integer getStudentsCount(String teacherId, String schoolId, String branchId, String subjectId,
			String gradeId, String sectionId, String subtopicId) {

		try {
			int studentCount = assignTeacherRepo.getStudentsCountByFilters(schoolId, branchId, gradeId, sectionId);
			return studentCount;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.student.count.failed", null));
		}
	}

	/**
	 * This is a feign call for teacher service to get assigned students
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param subjectId
	 * @param gradeId
	 * @param sectionId
	 * @param subtopicId
	 * @return
	 */
	@Override
	public List<StudentMinResponseDto> getStudentDetails(String teacherId, String schoolId, String branchId,
			String subjectId, String gradeId, String sectionId, String subtopicId) {

		try {
			List<StudentsProjection> stuResponse = assignTeacherRepo.getStudentsListByFilters(schoolId, branchId,
					gradeId, sectionId);

			if (stuResponse == null || stuResponse.isEmpty()) {
				log.info("No students assigned for this teacher");
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null));
			}

			List<StudentMinResponseDto> responses = stuResponse.stream()
					.map(studentProjection -> new StudentMinResponseDto(studentProjection.getId(),
							studentProjection.getFirstName(), studentProjection.getLastName()))
					.collect(Collectors.toList());

			return responses;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.student.details.failed", null));
		}
	}

	public void checkingDuplicateTeacherAccess(String teacherId, TeacherAssignRequest request) {
		try {
			// checking duplicate teacher access
			if (!request.getSectionIds().isEmpty() && !request.getSubtopicIds().isEmpty()) {
				for (String section : request.getSectionIds()) {
					for (String subtopic : request.getSubtopicIds()) {
						boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, section, subtopic,
								request.getGradeId(), request.getSubjectId());

						if (alreadyExists) {
							throw new USException(ErrorCodes.CONFLICT,
									Translator.toLocale("teacher.section.subTopic.already.exit", null));
						}
					}
				}
			} else if (!request.getSectionIds().isEmpty()) {
				for (String section : request.getSectionIds()) {
					boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, section, null,
							request.getGradeId(), request.getSubjectId());

					if (alreadyExists) {
						throw new USException(ErrorCodes.CONFLICT,
								Translator.toLocale("teacher.section.already.exit", null));
					}
				}
			} else if (!request.getSubtopicIds().isEmpty()) {
				for (String subtopic : request.getSubtopicIds()) {
					boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, null, subtopic,
							request.getGradeId(), request.getSubjectId());

					if (alreadyExists) {
						throw new USException(ErrorCodes.CONFLICT,
								Translator.toLocale("teacher.subTopic.already.exit", null));
					}
				}
			} else {
				boolean alreadyExists = assignTeacherRepo.existsByFilters(teacherId, null, null, request.getGradeId(),
						request.getSubjectId());

				if (alreadyExists) {
					throw new USException(ErrorCodes.CONFLICT,
							Translator.toLocale("teacher.grade.subject.already.exit", null));
				}
			}
		} catch (USException ex) {
			log.error(ExceptionUtils.getStackTrace(ex));
			throw new USException(ex.getErrorCode(), ex.getMessage());
		}
	}

	/**
	 * Editing teacher access based on grade, subject and section, subTopic. This is
	 * mainlly used for Principal dashboard Teacher Access
	 */
	@Override
	public List<TeacherAssignResponse> updateTeacherAccess(String teacherId, TeacherAssignRequest request) {
		try {
			if (!teacherRepo.existsByIdAndDeleted(teacherId, false))
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("teacher.id.not.found", null));

			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();

			Teachers teacher = teacherRepo.getById(teacherId);
			String schoolId = teacher.getBranches().getSchools().getId();
			String branchId = teacher.getBranches().getId();

			// check if sections and grades mapping exist
			List<SectionsResponseDto> sectionData = gradeSectionMappingService
					.getAllSectionByGradesSchoolAndBranch(null, request.getGradeId(), branchId, schoolId);

			List<String> sectionIds = sectionData.stream().map(SectionsResponseDto::getId).collect(Collectors.toList());

			if (sectionIds != null && !CollectionUtils.isEmpty(sectionIds)) {
				if (request.getSectionIds().stream().anyMatch(section -> !sectionIds.contains(section)))
					throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
							Translator.toLocale("gs.mapping.corrupted.data", null));
			}

			// check if subject and subtopics mapping exist
			LMSResponse<SubjectsResponseDto> subjectLMSResponse = mastersFeignClient
					.getSubjectsById(request.getSubjectId());
			List<String> subTopicsResponse = new ArrayList<>();

			if (subjectLMSResponse != null) {
				List<SubTopicBulkInnerResponseDto> subtopics = subjectLMSResponse.getData().getSubTopics();
				subTopicsResponse = subtopics != null
						? subtopics.stream().filter(SubTopicBulkInnerResponseDto::isActive)
								.map(SubTopicBulkInnerResponseDto::getId).collect(Collectors.toList())
						: new ArrayList<>();
			}

			List<String> finalSubTopicsResponse = subTopicsResponse;
			if (subTopicsResponse != null && !CollectionUtils.isEmpty(subTopicsResponse)) {
				if (request.getSubtopicIds().stream().anyMatch(subtopic -> !finalSubTopicsResponse.contains(subtopic)))
					throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
							Translator.toLocale("subject.or.subtopic.mapping.do.not.exist", null));
			}

			/*
			 * edit a list of assign teacher entities. This is done because we need to have
			 * different rows for different combinations of teacherId - grade - section -
			 * subject -subtopic request . We can have multiple sections and subtopics in
			 * the request
			 */
			int deleteStatus = 0;
			if (!request.getSectionIds().isEmpty() && !request.getSubtopicIds().isEmpty()) {
				for (String section : request.getSectionIds()) {
					for (String subtopic : request.getSubtopicIds()) {
						deleteStatus = assignTeacherRepo.deleteTeacherAccess(teacherId, currentTime, currentUser,
								section, subtopic, request.getGradeId(), request.getSubjectId());
					}
				}
			} else if (!request.getSectionIds().isEmpty()) {
				for (String section : request.getSectionIds()) {
					deleteStatus = assignTeacherRepo.deleteTeacherAccess(teacherId, currentTime, currentUser, section,
							null, request.getGradeId(), request.getSubjectId());
				}
			} else if (!request.getSubtopicIds().isEmpty()) {
				for (String subtopic : request.getSubtopicIds()) {
					deleteStatus = assignTeacherRepo.deleteTeacherAccess(teacherId, currentTime, currentUser, null,
							subtopic, request.getGradeId(), request.getSubjectId());
				}
			} else {
				deleteStatus = assignTeacherRepo.deleteTeacherAccess(teacherId, currentTime, currentUser, null, null,
						request.getGradeId(), request.getSubjectId());
			}

			List<TeacherEditAccessResponseDto> assignTeacherEntities = assignTeacherRepo
					.getCureentTeacherAccess(teacherId, request.getSubjectId(), request.getGradeId(), null, null);
			List<TeacherAssignResponse> response = assignTeacherEntities.stream()
					.map(assignTeacherEntitie -> modelMapper.map(assignTeacherEntitie, TeacherAssignResponse.class))
					.collect(Collectors.toList());

			setMasterDataResponseForAssignTeacher(response);

			return response;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("edit.teacher.access.subjects.failed", null));
		}
	}

	/**
	 * This is a feign call for teacher formal assessment service to get student
	 * Details
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	@Override
	public List<StudentFormativeMinResponseDto> getFormativeStudentDetailsFilterBySection(String schoolId,
			String branchId, String gradeId, String sectionId) {

		try {
			List<StudentsProjection> response = assignTeacherRepo.getStudentsNameList(schoolId, branchId, gradeId,
					sectionId);

			List<StudentFormativeMinResponseDto> responseDtoList = !CollectionUtils.isEmpty(response)
					? response.stream().map(Projection -> {
						StudentFormativeMinResponseDto responseDto = new StudentFormativeMinResponseDto();
						responseDto.setId(Projection.getId());
						responseDto.setFirstName(Projection.getFirstName());
						responseDto.setLastName(Projection.getLastName());
						return responseDto;
					}).collect(Collectors.toList())
					: new ArrayList<>();

			return responseDtoList;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.formative.assessment.student.details.failed", null));
		}
	}

	/**
	 * This is a feign call for teacher service to get assigned students
	 *
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param subjectId
	 * @param gradeId
	 * @param sectionId
	 * @param subtopicId
	 * @return
	 */
	@Override
	public List<StudentNameDetails> getStudentNameDetails(String teacherId, String schoolId, String branchId,
			String subjectId, String gradeId, String sectionId, String subtopicId) {

		try {
			List<StudentsProjection> response = assignTeacherRepo.getStudentsNameList(schoolId, branchId, gradeId,
					sectionId);
			List<StudentNameDetails> responseDtoList = !CollectionUtils.isEmpty(response)
					? response.stream().map(Projection -> {
						StudentNameDetails responseDto = new StudentNameDetails();
						responseDto.setId(Projection.getId());
						responseDto.setFirstName(Projection.getFirstName());
						responseDto.setLastName(Projection.getLastName());
						return responseDto;
					}).collect(Collectors.toList())
					: null;

			return responseDtoList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.teacher.student.name.details.failed", null));
		}
	}

	/**
	 * Teacher list for Principal's or coordinator's Dashboard
	 * 
	 * @param gradeId
	 * @return
	 */
	@Override
	public List<AllTypeUserMinResponseDto> getAllTeachersForPrincipalOrCoodinator(String gradeId) {
		try {
			String username = jwtUtil.currentLoginUser();
			List<AllTypeUserMinResponseDto> response = null;

			Teachers teachers = teacherRepo.findByUserNameIgnoreCase(username);
			if (teachers == null || teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER)
				throw new USException(ErrorCodes.ACCESS_DENIED,
						Translator.toLocale("teachers.resource.reserver", null));

			List<String> gradeIds = new ArrayList<>();
			if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR) {
				LMSResponse<List<String>> coordinatorGrades = mastersFeignClient
						.getGradeListByCoordinatorId(teachers.getCoordinatorTypeId());
				gradeIds = coordinatorGrades != null && !CollectionUtils.isEmpty(coordinatorGrades.getData())
						? coordinatorGrades.getData()
						: new ArrayList<>();
			}

			/**
			 * Setting the response according to the principal or coordinator. If it is
			 * coordinator check with the assigned-grade if try to filter by gradeId.
			 */
			if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.PRINCIPAL)
				response = teacherRepo.findStaffsMinDetailsForPrincipal(teachers.getSchools().getId(),
						teachers.getBranches().getId(), gradeId);
			else {
				String selectedGrade = !StringUtils.isBlank(gradeId)
						&& gradeIds.stream().anyMatch(item -> item.contains(gradeId)) ? gradeId : null;
				response = !StringUtils.isBlank(selectedGrade)
						? teacherRepo.findStaffsMinDetailsForPrincipal(teachers.getSchools().getId(),
								teachers.getBranches().getId(), gradeId)
						: teacherRepo.findStaffsMinDetailsWithGradeIds(teachers.getSchools().getId(),
								teachers.getBranches().getId(), gradeIds);
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teachers.listing.for.dashboard.failed", null));
		}
	}

	/**
	 * This is feign call for teacher service for homework assignment
	 * 
	 * @param teacherId
	 * @return
	 */
	@Override
	public TeacherDetailsForFeignResponseDto getTeacherDetailsForFeign(String teacherId) {
		try {
			return teacherRepo.findTeachersMinDetails(teacherId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.details.failed", null));
		}
	}

	/**
	 * feign call for teacher service
	 * 
	 * @param teacherIds
	 * @return
	 */
	@Override
	public List<TeacherDetailsForFeignResponseDto> getAllTeacherDetailsForFeign(List<String> teacherIds) {
		try {
			return teacherRepo.getAllTeachersForFeign(teacherIds);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.details.failed", null));
		}
	}

	/**
	 * Find the id, name and username for subjective paper. Feign call to
	 * content-service
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public UserMinDetails getUserMiniDetails(String id) {
		try {
			return teacherRepo.getUserMinDetails(id);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.by.id.failed", null));
		}
	}

	/**
	 * For pagination response in subjective papers. Feign to content-service.
	 * 
	 * @param ids
	 * @return
	 */
	@Override
	public List<UserMinDetails> getAllUserMiniDetails(List<String> ids) {
		try {
			return teacherRepo.getAllUsersMinDetails(ids);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.all.failed", null));
		}
	}

	@Override
	public List<String> getteacherIdsListDetails(String schoolId, String gradeId, String sectionId) {
		try {
			List<String> response = new ArrayList<>();
			List<String> assignSection = assignTeacherRepo.getTeacherIdsByGradeAndSection(schoolId, gradeId, sectionId);
			if (!CollectionUtils.isEmpty(assignSection)) {
				response = assignSection;
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.get.list.failed", null));
		}
	}

	/**
	 * This api is update the sectionIds
	 * 
	 * @param oldSectionId
	 * @param newSectionId
	 * @return
	 */
	@Override
	public Boolean updateSectionIds(String oldSectionId, String newSectionId) {
		teacherRepo.executeUpdateSectionIdsFunction(oldSectionId, newSectionId);
		return true;
	}

	/*
	 * @Override public List<String> getTeachersAndAssignedSections() { List<String>
	 * sectionIds = null; try { Teachers teacher =
	 * teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser()); if (teacher
	 * != null) { List<TeacherAssignProjection> projectionList = assignTeacherRepo
	 * .assignedSubjectGradeAndSectionByTeacherId(teacher.getId());
	 * 
	 * if (!CollectionUtils.isEmpty(projectionList)) { sectionIds =
	 * projectionList.stream().filter(item ->
	 * !StringUtils.isEmpty(item.getSectionId()))
	 * .map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.
	 * toList()); } } return sectionIds; } catch (Exception e) {
	 * log.error(ExceptionUtils.getStackTrace(e)); throw new
	 * USException(ErrorCodes.INTERNAL_SERVER_ERROR,
	 * Translator.toLocale("teacher.details.failed", null)); } }
	 */

	/*
	 * @Override public List<String> getTeachersAndAssignedSections(String gradeId,
	 * String subjectId) { List<String> sectionIds = null; try { Teachers teacher =
	 * teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());
	 * System.out.println("teacher.id"+teacher != null ? teacher.getId() :""); if
	 * (teacher.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
	 * sectionIds =
	 * assignTeacherRepo.getAllSectionsByGradeAndTeacherId(teacher.getId(),
	 * gradeId); } else if (teacher.getAcademicStaffProfile() ==
	 * AcademicStaffProfile.PRINCIPAL) { List<AllTypeUserMinResponseDto>
	 * teacherListRsponse = teacherRepo.findStaffsMinDetailsForPrincipal(
	 * teacher.getSchools().getId(), teacher.getBranches().getId(), gradeId);
	 * List<String> teacherIdList =
	 * teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
	 * .collect(Collectors.toList());
	 * 
	 * List<TeacherAssignProjection> projectionList = assignTeacherRepo
	 * .assignedSubjectGradeAndSectionByTeacherIds(teacherIdList, gradeId,
	 * subjectId);
	 * 
	 * if (!CollectionUtils.isEmpty(projectionList)) { sectionIds =
	 * projectionList.stream().filter(item ->
	 * !StringUtils.isEmpty(item.getSectionId()))
	 * .map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.
	 * toList()); }
	 * 
	 * } else if (teacher.getAcademicStaffProfile() ==
	 * AcademicStaffProfile.COORDINATOR) { LMSResponse<List<String>>
	 * coordinatorGrades = mastersFeignClient
	 * .getGradeListByCoordinatorId(teacher.getCoordinatorTypeId()); List<String>
	 * gradeIds = coordinatorGrades != null &&
	 * !CollectionUtils.isEmpty(coordinatorGrades.getData()) ?
	 * coordinatorGrades.getData() : new ArrayList<>(); String selectedGrade =
	 * !StringUtils.isBlank(gradeId) && gradeIds.stream().anyMatch(item ->
	 * item.contains(gradeId)) ? gradeId : null; List<AllTypeUserMinResponseDto>
	 * teacherListRsponse = !StringUtils.isBlank(selectedGrade) ?
	 * teacherRepo.findStaffsMinDetailsForPrincipal( teacher.getSchools().getId(),
	 * teacher.getBranches().getId(), gradeId) : null; List<String> teacherIdList =
	 * teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
	 * .collect(Collectors.toList());
	 * 
	 * List<TeacherAssignProjection> projectionList = assignTeacherRepo
	 * .assignedSubjectGradeAndSectionByTeacherIds(teacherIdList, gradeId,
	 * subjectId);
	 * 
	 * if (!CollectionUtils.isEmpty(projectionList)) { sectionIds =
	 * projectionList.stream().filter(item ->
	 * !StringUtils.isEmpty(item.getSectionId()))
	 * .map(TeacherAssignProjection::getSectionId).distinct().collect(Collectors.
	 * toList()); }
	 * 
	 * } return sectionIds; } catch (Exception e) {
	 * log.error(ExceptionUtils.getStackTrace(e)); throw new
	 * USException(ErrorCodes.INTERNAL_SERVER_ERROR,
	 * Translator.toLocale("teacher.details.failed", null)); } }
	 */
	@Override
	public List<TeacherAssignmentResponse> getTeachersAndAssignedSections(String gradeId, String subjectId) {
		try {
			List<TeacherAssignmentResponse> responseList = new ArrayList<>();

			Teachers teacher = teacherRepo.findByUserNameIgnoreCase(jwtUtil.currentLoginUser());
			System.out.println("teacher.id" + teacher != null ? teacher.getId() : "");
			if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {

				List<String> sectionIds = assignTeacherRepo.assignTeacherSectionsList(teacher.getId(), gradeId,
						subjectId);
				TeacherAssignmentResponse response = new TeacherAssignmentResponse();
				response.setTeacherId(teacher.getId());
				response.setSectionId(sectionIds);
				response.setTeacher(true);
				responseList.add(response);
				// setTeacherSectionList(gradeId, responseList, teacher.getId());
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.PRINCIPAL) {
				List<AllTypeUserMinResponseDto> teacherListRsponse = teacherRepo.findStaffsMinDetailsForPrincipal(
						teacher.getSchools().getId(), teacher.getBranches().getId(), gradeId);
				List<String> teacherIdList = teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
						.collect(Collectors.toList());

				for (String teacherId : teacherIdList) {
					setTeacherSectionList(gradeId, responseList, teacherId);
				}

			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR) {
				LMSResponse<List<String>> coordinatorGrades = mastersFeignClient
						.getGradeListByCoordinatorId(teacher.getCoordinatorTypeId());
				List<String> gradeIds = coordinatorGrades != null
						&& !CollectionUtils.isEmpty(coordinatorGrades.getData()) ? coordinatorGrades.getData()
								: new ArrayList<>();
				String selectedGrade = !StringUtils.isBlank(gradeId)
						&& gradeIds.stream().anyMatch(item -> item.contains(gradeId)) ? gradeId : null;
				List<AllTypeUserMinResponseDto> teacherListRsponse = !StringUtils.isBlank(selectedGrade)
						? teacherRepo.findStaffsMinDetailsForPrincipal(
								teacher.getSchools().getId(), teacher.getBranches().getId(), gradeId)
						: null;
				List<String> teacherIdList = teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
						.collect(Collectors.toList());
				for (String teacherId : teacherIdList) {
					setTeacherSectionList(gradeId, responseList, teacherId);
				}

			}
			return responseList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.details.failed", null));
		}
	}

	private void setTeacherSectionList(String gradeId, List<TeacherAssignmentResponse> responseList, String teacherId) {
		List<String> sectionIds;
		sectionIds = assignTeacherRepo.getAllSectionsByGradeAndTeacherId(teacherId, gradeId);
		TeacherAssignmentResponse response = new TeacherAssignmentResponse();
		response.setTeacherId(teacherId);
		response.setSectionId(sectionIds);
		response.setTeacher(true);
		responseList.add(response);
	}

	@SuppressWarnings("unused")
	@Override
	public TeacherResponseDto getTeacherByUserName(String username) {
		try {
			TeachersProjection teacherProjection = teacherRepo.getTeachersByUserName(username);
			TeacherResponseDto teachers = new TeacherResponseDto(teacherProjection);
			return teachers != null ? teachers : null;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.details.failed", null));
		}
	}

	@Override
	public List<TeacherResponseDto> getAssignedTeacherList(String gradeId) {
		try {
			List<AllTypeUserMinResponseDto> teacherListRsponse = null;
			List<Teachers> teacherList = new ArrayList<>();
			List<String> gradeIds = new ArrayList<>();
			String userName = jwtUtil.currentLoginUser();
			Teachers teacher = teacherRepo.findByUserNameIgnoreCase(userName);
			log.debug(teacher + "teacher");
			if (teacher == null) {
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("teacher.not.valid", null));
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
				teacherList.add(teacher);
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.PRINCIPAL) {
				teacherListRsponse = teacherRepo.findStaffsMinDetailsForPrincipal(teacher.getSchools().getId(),
						teacher.getBranches().getId(), gradeId);
				List<String> teacherIdList = teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
						.collect(Collectors.toList());
				teacherList.addAll(teacherRepo.findAllById(teacherIdList));
			} else if (teacher.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR) {
				LMSResponse<List<String>> coordinatorGrades = mastersFeignClient
						.getGradeListByCoordinatorId(teacher.getCoordinatorTypeId());
				gradeIds = coordinatorGrades != null && !CollectionUtils.isEmpty(coordinatorGrades.getData())
						? coordinatorGrades.getData()
						: new ArrayList<>();
				String selectedGrade = !StringUtils.isBlank(gradeId)
						&& gradeIds.stream().anyMatch(item -> item.contains(gradeId)) ? gradeId : null;
				teacherListRsponse = !StringUtils.isBlank(selectedGrade)
						? teacherRepo.findStaffsMinDetailsForPrincipal(teacher.getSchools().getId(),
								teacher.getBranches().getId(), gradeId)
						: teacherRepo.findStaffsMinDetailsWithGradeIds(teacher.getSchools().getId(),
								teacher.getBranches().getId(), gradeIds);
				List<String> teacherIdList = teacherListRsponse.stream().map(AllTypeUserMinResponseDto::getId)
						.collect(Collectors.toList());
				teacherList.addAll(teacherRepo.findAllById(teacherIdList));
			}
			// Convert List<Teachers> to List<TeacherResponseDto>
			List<TeacherResponseDto> teacherResponse = teacherList.stream().map(this::convertToTeacherResponseDto)
					.collect(Collectors.toList());

			return teacherResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("teacher.details.failed", null));
		}
	}

	private TeacherResponseDto convertToTeacherResponseDto(Teachers teacher) {
		TeacherResponseDto dto = new TeacherResponseDto();
		dto.setId(teacher.getId());
		dto.setFirstName(teacher.getFirstName());
		dto.setLastName(teacher.getLastName());
		dto.setEmail(teacher.getEmail());
		return dto;
	}

	@Override
	public JsonNode getAssignGrades(String teacherId, String menuName) {
		try {
			Map<String, Integer> gradeMap = createGradeMap();
			Teachers teachers = teacherRepo.getById(teacherId);
			if (teachers == null) {
				throw new USException(ErrorCodes.NO_CONTENT,
						Translator.toLocale("teacher.assigned.grade.fetch.failed", null));
			}
			String schoolId = teachers.getSchools().getId();
			String branchId = teachers.getBranches().getId();

			// Fetching all assigned grades of academic staff
			List<String> assignedGrades = assignTeacherRepo.getAllGradeIdsByTeacherId(teacherId);

			// If Principal or Coordinator check the grades in school. If it is coordinator
			// need to check in the CoordinatorType from master-service.
			if (teachers.getAcademicStaffProfile() != AcademicStaffProfile.TEACHER) {
				log.info("Find the grades by default set for principal and coordinator. Seting up the grade-section");
				String coordinatorId = !StringUtils.isEmpty(teachers.getCoordinatorTypeId())
						? teachers.getCoordinatorTypeId()
						: null;
				String planId = branchPlanRepo.findPlanIdByBranchId(teachers.getBranches().getId());
				LMSResponse<List<String>> gradeMasterResponse = null;

				// calling master service to get the assigned grade to the coordinator
				if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.COORDINATOR)
					gradeMasterResponse = mastersFeignClient.getGradeListByCoordinatorId(coordinatorId);

				// calling master service to get the assigned grade to the plan
				if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.PRINCIPAL)
					gradeMasterResponse = mastersFeignClient.assignedGradesFromPlan(planId);

				// Finding the grades under school & branch and checking the common grades from
				// master-service with grade-section mapping.
				List<String> gradesInSchool = gradeSectionMappingRepository.findAllActiveGradesBySchoolBranch(schoolId,
						branchId);
				List<String> filteredGrades = null;
				if (!CollectionUtils.isEmpty(gradesInSchool)) {
					log.info("Checking the grades from master-service exists in school and branch");
					if (gradeMasterResponse != null && !CollectionUtils.isEmpty(gradeMasterResponse.getData())) {
						filteredGrades = gradeMasterResponse.getData().stream().filter(gradesInSchool::contains)
								.distinct().collect(Collectors.toList());
					}
				}

				// Adding all the grades and filtering w/o duplicate grades.
				if (!CollectionUtils.isEmpty(filteredGrades)) {
					List<String> tempGrades = new ArrayList<>();
					tempGrades.addAll(assignedGrades);
					tempGrades.addAll(filteredGrades);
					assignedGrades = !CollectionUtils.isEmpty(tempGrades) ? filteredGrades.stream()
							.filter(gradeId -> !StringUtils.isBlank(gradeId)).distinct().collect(Collectors.toList())
							: filteredGrades;
				}
			}

			// Setting the Grade-Section combination
			if (!CollectionUtils.isEmpty(assignedGrades)) {
				// creating the object to master-service feign call.
				List<GradeSectionResponseDto> toMasterService = new ArrayList<>();

				// if AcademicStaffProfile.TEACHER then only assigned grades & section visible.
				if (teachers.getAcademicStaffProfile() == AcademicStaffProfile.TEACHER) {
					assignedGrades.stream().forEach(gradeId -> {
						List<String> assignedSections = assignTeacherRepo.getAllSectionsByGradeAndTeacherId(teacherId,
								gradeId);
						toMasterService.add(new GradeSectionResponseDto(gradeId, assignedSections));
					});
				} else {
					// If AcademicStaffProfile is not TEACHER, list down the sections mapped with
					// the grades.
					assignedGrades.stream().forEach(gradeId -> {
						List<String> assignedSections = gradeSectionMappingRepository
								.findAllSectionByGradeIdBranchAndSchool(gradeId, branchId, schoolId);
						toMasterService.add(new GradeSectionResponseDto(gradeId, assignedSections));
					});
				}
				ObjectMapper objectMapper = new ObjectMapper();
				// master-feign call
				if (!CollectionUtils.isEmpty(toMasterService)) {
					LMSResponse<List<GradesSectionFeignResponseDto>> assignedGradeResponse = mastersFeignClient
							.assignedGradesAndSectionForAcademicStaffs(toMasterService);
					if (assignedGradeResponse != null && !CollectionUtils.isEmpty(assignedGradeResponse.getData())) {
						JsonNode root = objectMapper.valueToTree(assignedGradeResponse.getData());
						for (JsonNode gradeNode : root) {
							((ObjectNode) gradeNode).put("gradeId", gradeNode.get("id").asText());
							((ObjectNode) gradeNode).remove("id");
							JsonNode sectionsNode = gradeNode.get("sections");
							if (sectionsNode != null) {
								for (JsonNode sectionNode : sectionsNode) {
									((ObjectNode) sectionNode).put("sectionId", sectionNode.get("id").asText());
									((ObjectNode) sectionNode).remove("id");
								}
							}
						}
						return root;
					}
				}
			}
			return new ObjectMapper().createObjectNode();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT,
					Translator.toLocale("teacher.assigned.grade.fetch.failed", null));
		}
	}

	private int extractGradeNumber(String grade) {
		try {
			return Integer.parseInt(grade.replaceAll("\\D+", ""));
		} catch (NumberFormatException e) {
			return Integer.MAX_VALUE; // Handle cases where grade does not contain a number
		}
	}

}
