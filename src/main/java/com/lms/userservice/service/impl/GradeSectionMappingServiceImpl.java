package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.dto.UpdateSectionRequestDTO;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.GradeSectionMapping;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.SectionData;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.content.ContentFeignClient;
import com.lms.userservice.feign.master.AcademicYearResponseDto;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.feign.student.StudentFeignClient;
import com.lms.userservice.feign.teacher.TeacherFeignClient;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.GradeSectionMappingRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.request.dto.DiffGradeSectionRequestDto;
import com.lms.userservice.request.dto.GradeSectionMapRequestDto;
import com.lms.userservice.request.dto.GradeSectionPutRequestDto;
import com.lms.userservice.request.dto.GradeSectionRequestDto;
import com.lms.userservice.request.dto.ToggleGradeSectionRequestDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.GradeSectionGetResponseDto;
import com.lms.userservice.response.dto.GradeSectionMapResponseDto;
import com.lms.userservice.response.dto.GradeSectionMappingResponseDto;
import com.lms.userservice.response.dto.GradeSectionMinResponseDto;
import com.lms.userservice.response.dto.ProfileResponseDto;
import com.lms.userservice.response.dto.SectionDataResponseDto;
import com.lms.userservice.service.GradeSectionMappingService;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * All the crud for the Grades-SEctions mapping
 *
 * <AUTHOR> C Achari
 * @since 1.0.2
 *
 */
@Slf4j
@Service("GradeSectionMappingService")
public class GradeSectionMappingServiceImpl implements GradeSectionMappingService {

	@Autowired
	private JwtUtil jwtUtil;

	private Long currentTime = new Date().getTime();

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private GradeSectionMappingRepository gsmRepository;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private StudentsRepository studentsRepository;
	
	@Autowired
	private UsersRepository usersRepository;
	
	@Autowired
	private MastersFeignClient masterFeignClient;

	@Autowired
	private UsersRoleMappingRepository usersRoleMappingRepository;
	
	@Autowired
	private UserService userService;

	@Autowired
	private ContentFeignClient contentFeignClient;
	
	@Autowired
	private TeacherFeignClient teacherFeignClient;

	@Autowired
	private StudentFeignClient studentFeignClient;


	/**
	 * Get the enumerator {@link com.lms.userservice.enums.SectionData SectionData}
	 * into List
	 *
	 * @return
	 */
	@Override
	public List<SectionDataResponseDto> getAllSectionData() {
		try {
			List<SectionDataResponseDto> response = new ArrayList<>();
			// converting enumerator into list
			List<SectionData> eumList = new ArrayList<>(EnumSet.allOf(SectionData.class));
			eumList.forEach(item -> response.add(new SectionDataResponseDto(item.getCode(), item.getName())));
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("section.data.get.all.failed", null));
		}
	}

	/**
	 * Grade-Section mapping is going through the following points
	 * <p>
	 * <blockquote>
	 *
	 * <pre>
	 * 1. There will be Grade without section (NO_SECTION)
	 *    eg., class 11th or 12th can be stand without any division like A, B etc
	 *
	 * 2. The entire branch of school can be exist with same sections (SAME_SECTION)
	 *    eg., class from LKG to 12th there is only divisions like A, B etc
	 *  	means select section A, B for entire branch's grade
	 *
	 * 3. The branch of school can have different sections for each grade (DIFFERENT_SECTIONS)
	 *    eg., class LKG can have division A, B or C, at the same time class 10 has A & B division
	 * </pre>
	 *
	 * </blockquote>
	 *
	 * According to the grade and section list size the rows will be create.
	 * <p>
	 * Academic year is adding. Academic year details will return if the entity
	 * contains academic year id data.
	 *
	 * @param request
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public List<GradeSectionMapResponseDto> createGradeSectionMapping(GradeSectionMapRequestDto request) {
		log.info("Start to map grade and section...");
		if (!Enums.getIfPresent(SectionData.class, request.getSectionData()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("section.data.invalid", null));

		if (request.getSectionData().equals(SectionData.DIFFERENT_SECTIONS.getCode())) {
			if (!diffGradeSectionChecking(request.getDiffGradeSection())) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.corrupted.data", null));
			}
		} else {
			if (!gradeSectionChecking(request.getGradeSection(), request.getSectionData())) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.corrupted.data", null));
			}
		}

		if (!schoolRepository.existsById(request.getSchoolId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (!branchRepository.existsById(request.getBranchId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		if (!branchRepository.existsBySchoolsIdAndDeletedAndId(request.getSchoolId(), false, request.getBranchId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.school.relation", null));

		try {
			log.info("All checking completed.");
			String currentUser = jwtUtil.currentLoginUser();
			Schools schools = schoolRepository.getById(request.getSchoolId());
			Branches branches = branchRepository.getById(request.getBranchId());
			AcademicYearResponseDto academicYear = null;
			LMSResponse<AcademicYearResponseDto> academicResponse = mastersFeignClient.getLatestAcademicYear();
			if (!ObjectUtils.isEmpty(academicResponse)) {
				log.info("Return the latest academic year data.");
				academicYear = academicResponse.getData();
			}
			List<String> savedMapId = new ArrayList<>();

			if (request.getSectionData().equals(SectionData.DIFFERENT_SECTIONS.toString())) {
				log.info("DIFFERENT_SECTIONS : Grade-section data is mapping.");
				for (DiffGradeSectionRequestDto dto : request.getDiffGradeSection()) {
					for (String sec : dto.getSectionId()) {
						boolean isExisting = gsmRepository
								.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeleted(request.getSchoolId(),
										request.getBranchId(), dto.getGradeId(), sec, false);
						if (!isExisting) {
							GradeSectionMapping gradeSectionMapping = new GradeSectionMapping();
							gradeSectionMapping.setSchools(schools);
							gradeSectionMapping.setBranches(branches);
							gradeSectionMapping.setSectionData(SectionData.valueOf(request.getSectionData()));
							gradeSectionMapping.setGradeId(dto.getGradeId());
							gradeSectionMapping.setSectionId(sec);
							gradeSectionMapping.setCreatedBy(currentUser);
							gradeSectionMapping.setActive(request.isActive());
							gradeSectionMapping.setAcademicYearId(
									!ObjectUtils.isEmpty(academicYear) ? academicYear.getId() : null);
							gradeSectionMapping = gsmRepository.save(gradeSectionMapping);
							if (!StringUtils.isEmpty(gradeSectionMapping.getId())) {
								log.info("DIFFERENT_SECTIONS : Grade-section data mapping is created.");
								savedMapId.add(gradeSectionMapping.getId());
							}
						}
					}
				}
			} else if (request.getSectionData().equals(SectionData.SAME_SECTION.toString())) {
				log.info("SAME_SECTION : Grade-section data is mapping.");
				for (String grade : request.getGradeSection().getGradeId()) {
					for (String section : request.getGradeSection().getSectionId()) {
						boolean isExisting = gsmRepository
								.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeleted(request.getSchoolId(),
										request.getBranchId(), grade, section, false);
						if (!isExisting) {
							GradeSectionMapping gradeSectionMapping = new GradeSectionMapping();
							gradeSectionMapping.setSchools(schools);
							gradeSectionMapping.setBranches(branches);
							gradeSectionMapping.setSectionData(SectionData.valueOf(request.getSectionData()));
							gradeSectionMapping.setGradeId(grade);
							gradeSectionMapping.setSectionId(section);
							gradeSectionMapping.setCreatedBy(currentUser);
							gradeSectionMapping.setAcademicYearId(
									!ObjectUtils.isEmpty(academicYear) ? academicYear.getId() : null);
							gradeSectionMapping.setActive(request.isActive());
							gradeSectionMapping = gsmRepository.save(gradeSectionMapping);
							if (!StringUtils.isEmpty(gradeSectionMapping.getId())) {
								log.info("SAME_SECTION : Grade-section data mapping is created.");
								savedMapId.add(gradeSectionMapping.getId());
							}
						}
					}
				}
			} else {
				log.info("NO_SECTION : Grade-section data is mapping.");
				for (String grade : request.getGradeSection().getGradeId()) {
					Long existingCount = gsmRepository.countBySchoolBrachGrade(request.getSchoolId(),
							request.getBranchId(), grade);
					if (existingCount <= 0L) {
						GradeSectionMapping gradeSectionMapping = new GradeSectionMapping();
						gradeSectionMapping.setSchools(schools);
						gradeSectionMapping.setBranches(branches);
						gradeSectionMapping.setSectionData(SectionData.valueOf(request.getSectionData()));
						gradeSectionMapping.setGradeId(grade);
						gradeSectionMapping.setCreatedBy(currentUser);
						gradeSectionMapping.setActive(request.isActive());
						gradeSectionMapping
								.setAcademicYearId(!ObjectUtils.isEmpty(academicYear) ? academicYear.getId() : null);
						gradeSectionMapping = gsmRepository.save(gradeSectionMapping);
						if (!StringUtils.isEmpty(gradeSectionMapping.getId())) {
							// the section column has to be null
							log.info("NO_SECTION : Grade-section data mapping is created.");
							savedMapId.add(gradeSectionMapping.getId());
						}
					}
				}
			}
			log.info("Grade-section data mapping is completed...");
			return returnResponseForMapping(savedMapId, request.getSchoolId(), request.getBranchId(), academicYear);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			if (e.getMessage().equals(Translator.toLocale("gs.mapping.conflict", null))) {
				throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("gs.mapping.conflict", null));
			} else {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("gs.mapping.create.failed", null));
			}
		}
	}

	/**
	 * SectionData.NO_SECTION : request shouldn't pass section list
	 * SectionData.SAME_SECTION : request must pass section list
	 * 
	 * @param request
	 * @param sectionData
	 * @return
	 */
	@SuppressWarnings("all")
	private boolean gradeSectionChecking(GradeSectionRequestDto request, String sectionData) {
		if (CollectionUtils.isEmpty(request.getGradeId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.grade.mandatory", null));

		try {
			boolean isCorrect = true;
			if (sectionData.equals(SectionData.SAME_SECTION.toString())) {
				isCorrect = (CollectionUtils.isEmpty(request.getSectionId())) ? false : true;
			} else {
				isCorrect = (!CollectionUtils.isEmpty(request.getSectionId())) ? false : true;
			}

			return isCorrect;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.checking.failed", null));
		}
	}

	/**
	 * @param request
	 * @return
	 */
	@SuppressWarnings("all")
	private boolean diffGradeSectionChecking(List<DiffGradeSectionRequestDto> request) {
		try {
			boolean isCorrect = true;
			if (!request.isEmpty()) {
				for (DiffGradeSectionRequestDto dto : request) {
					boolean isGradeIdNull = StringUtils.isEmpty(dto.getGradeId()) ? false : true;
					boolean isSectionIdNull = (CollectionUtils.isEmpty(dto.getSectionId())
							|| dto.getSectionId().size() <= 0) ? false : true;
					isCorrect = (!isGradeIdNull || !isSectionIdNull) ? false : true;
					if (!isCorrect) {
						break;
					}
				}
			} else {
				isCorrect = false;
			}
			return isCorrect;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.checking.failed", null));
		}
	}

	@SuppressWarnings("all")
	private List<GradeSectionMapResponseDto> returnResponseForMapping(List<String> idList, String schoolId,
			String branchId, AcademicYearResponseDto academicYear) {
		log.info("Generating Grade-section data mapping response is started...");
		if (idList.isEmpty()) {
			throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("gs.mapping.conflict", null));
		}
		try {
			log.info("All mandatory entities are calling.");
			Schools schools = schoolRepository.getById(schoolId);
			Branches branches = branchRepository.getById(branchId);

			List<GradeSectionMapResponseDto> response = new ArrayList<>();

			List<GradesResponseDto> gradeList = new ArrayList<>();
			List<SectionsResponseDto> sectionList = new ArrayList<>();

			List<String> gradeIds = new ArrayList<>();
			List<String> sectionIds = new ArrayList<>();

			if (!idList.isEmpty()) {
				gradeIds = gsmRepository.findAllGradeByIds(idList);
				sectionIds = gsmRepository.findAllSectionByIds(idList);
			}

			log.info("master-service feign calls");
			if (!gradeIds.isEmpty()) {
				log.info("Fetched all grade data");
				gradeList = mastersFeignClient.getAllGradesByIds(gradeIds).getData();
			}

			if (!sectionIds.isEmpty()) {
				log.info("Fetched all section data");
				sectionList = mastersFeignClient.getAllSectionsByIds(sectionIds).getData();
			}

			List<GradeSectionMapping> dataList = gsmRepository.findMapingByIdIn(idList);

			if (!dataList.isEmpty()) {
				log.info("Fetched all data, which saved right now.");
				for (GradeSectionMapping mapping : dataList) {
					GradeSectionMapResponseDto gsmDTO = new GradeSectionMapResponseDto();
					gsmDTO.setId(mapping.getId());
					gsmDTO.setSchoolId(schools.getId());
					gsmDTO.setSchool(schools.getName());
					gsmDTO.setSchoolCode(schools.getCode());
					gsmDTO.setBranchId(branches.getId());
					gsmDTO.setBranch(branches.getName());
					gsmDTO.setSectionData(mapping.getSectionData());
					gsmDTO.setActive(mapping.isActive());

					if (!ObjectUtils.isEmpty(academicYear)) {
						log.info("Setting the academic year too.");
						gsmDTO.setAcademicYearId(academicYear.getId());
						gsmDTO.setAcademicYear(academicYear.getAcademicYear());
					}

					log.info("Traverse through each list to find the object with match by the given id");
					if (!gradeList.isEmpty()) {
						Optional<GradesResponseDto> gradeDto = gradeList.stream()
								.filter(g -> g.getId().equals(mapping.getGradeId())).findAny();
						if (gradeDto.isPresent()) {
							log.info("Setting the matching value of grade.");
							gsmDTO.setGradeId(gradeDto.get().getId());
							gsmDTO.setGradeName(gradeDto.get().getGrade());
						}
					}

					if (!sectionList.isEmpty()) {
						Optional<SectionsResponseDto> sectionDto = sectionList.stream()
								.filter(s -> s.getId().equals(mapping.getSectionId())).findAny();
						if (sectionDto.isPresent()) {
							log.info("Setting the matching value of section.");
							gsmDTO.setSectionId(sectionDto.get().getId());
							gsmDTO.setSectionName(sectionDto.get().getSection());
						}
					}
					response.add(gsmDTO);
				}
			}
			log.info("Generating Grade-section data mapping response is completed...");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.response.create.failed", null));
		}
	}

	// *GET ALL*//
	/**
	 * Get Grade-sEction mapping
	 *
	 * @param schoolId
	 * @param branchId
	 * @param academicYearId
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public List<GradeSectionGetResponseDto> getAllMappingBySchoolAndBranch(String schoolId, String branchId) {
		try {
			List<GradeSectionGetResponseDto> response = new ArrayList<>();
			log.info("Get all GradeId by schoolId, branchId and acdemicYearId");
			List<String> gradeIdList = gsmRepository.findAllGradesBySchoolBranch(schoolId, branchId);

			List<GradeSectionMinResponseDto> sectionAndIdList = new ArrayList<>();
			List<String> sectionId = new ArrayList<>();

			List<GradesResponseDto> gradeList = new ArrayList<>();
			List<SectionsResponseDto> sectionList = new ArrayList<>();

			log.info("master-service feign calls");

			if (!gradeIdList.isEmpty()) {
				log.info("Fetched all grade data");
				gradeList = mastersFeignClient.getAllGradesByIds(gradeIdList).getData();
			}

			log.info("Get all section by GradeId, branchId, schoolId");
			for (String gradeId : gradeIdList) {
				sectionAndIdList = gsmRepository.findAllSectionByGradeBranchSchool(gradeId, branchId, schoolId);
				if (!sectionAndIdList.isEmpty()) {
					sectionId = sectionAndIdList.stream().map(GradeSectionMinResponseDto::getSectionId)
							.collect(Collectors.toList());
				}
				if (!sectionId.isEmpty()) {
					log.info("Fetched all section data");
					sectionList = mastersFeignClient.getAllSectionsByIds(sectionId).getData();
				}

				log.info("Creating response...");
				GradeSectionGetResponseDto responseDto = new GradeSectionGetResponseDto();
				if (!gradeList.isEmpty()) {
					Optional<GradesResponseDto> gradeDto = gradeList.stream().filter(g -> g.getId().equals(gradeId))
							.findAny();
					if (gradeDto.isPresent()) {
						log.info("Setting the matching value of grade.");
						responseDto.setGradeId(gradeDto.get().getId());
						responseDto.setGradeName(gradeDto.get().getGrade());
					}
				}

				List<GradeSectionMinResponseDto> innerMapList = new ArrayList<>();
				List<GradeSectionMinResponseDto> existingSectionList = gsmRepository
						.findAllSectionByGradeBranchSchool(responseDto.getGradeId(), branchId, schoolId);
				if (!sectionList.isEmpty()) {
					boolean active = false;
					for (GradeSectionMinResponseDto minDto : existingSectionList) {
						Optional<SectionsResponseDto> sectionDto = sectionList.stream()
								.filter(s -> s.getId().equals(minDto.getSectionId())).findAny();
						if (sectionDto.isPresent()) {
							minDto.setSectionName(sectionDto.get().getSection());
							innerMapList.add(minDto);
							active = minDto.isActive();
						}
					}
					responseDto.setActive(active);
				}
				responseDto.setGradeSectionMapping(innerMapList);
				response.add(responseDto);
			}
			log.info("All grade-section mapping completed...");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.get.all.failed", null));
		}
	}

	// *UPDATE*//
	/**
	 * Update the Grade-Sections mapping
	 *
	 * @param request
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public GradeSectionMapResponseDto updateGradeSectionsMapping(GradeSectionPutRequestDto request) {
		log.info("Start to update grade and section mapping ...");

		try {
			if (!gsmRepository.existsByIdAndActiveAndDeleted(request.getId(), true, false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("gs.mapping.not.found", null));

			// gradeSectionMappingFinder(request); Remove to handle update section without student check

			if (!Enums.getIfPresent(SectionData.class, request.getSectionData()).isPresent())
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("section.data.invalid", null));

			boolean isExist = !StringUtils.isEmpty(request.getSectionId())
					? gsmRepository.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeletedAndIdNot(
							request.getSchoolId(), request.getBranchId(), request.getGradeId(), request.getSectionId(),
							false, request.getId())
					: gsmRepository.existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndIdNot(request.getSchoolId(),
							request.getBranchId(), request.getGradeId(), false, request.getId());
			if (isExist)
				throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("gs.mapping.already.exist", null));

			if (request.getSectionData().equals(SectionData.NO_SECTION.toString())
					&& !StringUtils.isEmpty(request.getSectionId())) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.corrupted.data", null));
			} else {
				if (StringUtils.isEmpty(request.getSectionId()))
					throw new USException(ErrorCodes.BAD_REQUEST,
							Translator.toLocale("gs.mapping.corrupted.data", null));
			}

			if (!schoolRepository.existsByIdAndDeletedAndActive(request.getSchoolId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

			if (!branchRepository.existsByIdAndDeletedAndActive(request.getBranchId(), false, true))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

			log.info("All checking completed.");
			GradeSectionMapResponseDto response = new GradeSectionMapResponseDto();
			String currentUser = jwtUtil.currentLoginUser();
			//1. Capture old section ID before update
        	String oldSectionId = gsmRepository.getById(request.getId()).getSectionId();
			int updated = gsmRepository.updateGradeSectionMapping(request.getSectionData(), request.getSectionId(),
					request.getGradeId(), currentTime, currentUser, request.getId());

			if (updated > 0) {
				log.info("Grade-Section mapping is updated successfully.");
				
				GradeSectionMapping gradeSectionMapping = gsmRepository.getById(request.getId());
				response.setId(gradeSectionMapping.getId());
				response.setSchoolId(gradeSectionMapping.getSchools().getId());
				response.setSchoolCode(gradeSectionMapping.getSchools().getCode());
				response.setSchool(gradeSectionMapping.getSchools().getName());
				response.setBranchId(gradeSectionMapping.getBranches().getId());
				response.setBranch(gradeSectionMapping.getBranches().getName());
				response.setSectionData(gradeSectionMapping.getSectionData());
				response.setActive(gradeSectionMapping.isActive());

				LMSResponse<GradesResponseDto> masterGradeResponse = mastersFeignClient
						.getGradesById(request.getGradeId());
				if (!ObjectUtils.isEmpty(masterGradeResponse)) {
					response.setGradeId(masterGradeResponse.getData().getId());
					response.setGradeName(masterGradeResponse.getData().getGrade());
				}

				if (!StringUtils.isEmpty(request.getSectionId())) {
					LMSResponse<SectionsResponseDto> masterSectionResponse = mastersFeignClient
							.getSectionsById(request.getSectionId());
					if (!ObjectUtils.isEmpty(masterSectionResponse)) {
						response.setSectionId(masterSectionResponse.getData().getId());
						response.setSectionName(masterSectionResponse.getData().getSection());
					}
				}

				//Now call function to update other places other tables
				gsmRepository.executeUpdateSectionIdsFunction(oldSectionId, request.getSectionId(),request.getSchoolId(),request.getBranchId(),request.getGradeId(),response.getSectionName());
				//
				UpdateSectionRequestDTO updateSectionRequestDTO = new UpdateSectionRequestDTO();
				updateSectionRequestDTO.setSchoolId(request.getSchoolId());
				updateSectionRequestDTO.setBranchId(request.getBranchId());
				updateSectionRequestDTO.setGradeId(request.getGradeId());
				updateSectionRequestDTO.setOldSectionId(oldSectionId);
				updateSectionRequestDTO.setNewSectionId(request.getSectionId());
				updateSectionRequestDTO.setSection(response.getSectionName());

				// call feign client
				updateSectionForAll(updateSectionRequestDTO);
				log.info("Grade-Section mapping is updated successfully on feign side also.");
			} else
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("gs.mapping.update.failed", null));

			return response;
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.update.failed", null));
		}
	}

	/**
	 * find out any Student related with this grade and section combination
	 * 
	 * @param request
	 */
	private void gradeSectionMappingFinder(GradeSectionPutRequestDto request) {
		try {
			GradeSectionMapping gradeSectionMapping = gsmRepository.getById(request.getId());
			boolean isStudentExists = !StringUtils.isEmpty(request.getSectionId())
					? studentsRepository.existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndSectionId(
							request.getSchoolId(), request.getBranchId(), gradeSectionMapping.getGradeId(), false,
							gradeSectionMapping.getSectionId())
					: studentsRepository.existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndSectionIdIsNull(
							request.getSchoolId(), request.getBranchId(), gradeSectionMapping.getGradeId(), false);

			if (isStudentExists)
				throw new USException(ErrorCodes.CONFLICT,
						Translator.toLocale("student.grade.section.mapping.found", null));

		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		}
	}

	/**
	 * Enable/Disable Toggle the active field status
	 *
	 */
	@Override
	public boolean updateActiveFieldByGradeIdBranchIdAndSchoolId(ToggleGradeSectionRequestDto request) {
		if (!gsmRepository.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeleted(request.getSchoolId(),
				request.getBranchId(), request.getGradeId(), request.getSectionId(), false)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.id.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			gsmRepository.updateActiveBySectionIdAndGradeIdAndBrachIdAndSchoolId(request.getSectionId(),
					request.getGradeId(), request.getBranchId(), request.getSchoolId(), request.isActive(), currentTime,
					currentUser);
			List<Boolean> activeList = gsmRepository.findActiveByGradeIdBrachIdAndSchoolId(request.getGradeId(),
					request.getBranchId(), request.getSchoolId());
			return (!CollectionUtils.isEmpty(activeList) && (activeList.size() == 1)
					&& (activeList.get(0) == request.isActive()));
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.failed", null));
		}
	}

	/**
	 * Before delete a grade confirming whether the specific grade is mapped with
	 * any section(table grade_section_mapping)
	 *
	 * @param gradeId
	 * @return
	 */
	@Override
	public Boolean getCountOfGradeSectionsByGradeId(String gradeId) {
		try {
			Long gradeCount = gsmRepository.findCountOfSectionsByGradeId(gradeId);
			return gradeCount > 0;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapped.with.grade.failed", null));
		}
	}

	/**
	 * Before delete a section confirming whether the specific section has any
	 * mapping in the table grade_section_mapping.
	 *
	 * @param sectionId
	 * @return
	 */
	@Override
	public Boolean getCountOfGradeSectionsBySectionId(String sectionId) {
		try {
			Long sectionCount = gsmRepository.findCountOfSectionsBySectionId(sectionId);
			return sectionCount > 0;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapped.with.section.failed", null));
		}
	}

	/**
	 * Get all the grades assigned to a specific school and branch.
	 *
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	@Override
	public List<GradesResponseDto> getAllGradesBySchoolAndBranch(String branchId, String schoolId) {
	    validateBranchAndSchool(branchId, schoolId);
	    
	    try {
	        String currentUser = jwtUtil.currentLoginUser();
	        Users userDetails = usersRepository.findByUserName(currentUser);
	        if (userDetails != null) {
	            List<String> createdUserRoles = usersRoleMappingRepository.findAllRolesByUserId(userDetails.getId());
	            List<RolesFeignDto> currentUserRoles = masterFeignClient.getRolesByIds(createdUserRoles).getData();

	            boolean isCoordinator = currentUserRoles.stream()
	                    .anyMatch(role -> "COORDINATOR".equalsIgnoreCase(role.getRole()));

	            List<String> gradeIdList = gsmRepository.findAllActiveGradesBySchoolBranch(schoolId, branchId);

	            if (isCoordinator) {
	                return getGradesForCoordinator(currentUser, gradeIdList);
	            } else {
	                return mastersFeignClient.getAllGradesByIds(gradeIdList).getData();
	            }
	        }
	    } catch (Exception e) {
	        log.error(ExceptionUtils.getStackTrace(e));
	        throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
	                Translator.toLocale("gs.mapped.with.grade.failed", null));
	    }

	    return Collections.emptyList();
	}

	private void validateBranchAndSchool(String branchId, String schoolId) {
	    if (!branchRepository.existsById(branchId)) {
	        throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));
	    }

	    if (!schoolRepository.existsById(schoolId)) {
	        throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));
	    }

	    if (!branchRepository.existsBySchoolsIdAndDeletedAndId(schoolId, false, branchId)) {
	        throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.school.relation", null));
	    }
	}

	private List<GradesResponseDto> getGradesForCoordinator(String currentUser, List<String> gradeIdList) {
	    List<String> roles = Collections.singletonList("COORDINATOR");
	    ProfileResponseDto response = userService.getUsersByUserName(currentUser, roles);

	    if (response != null) {
	        LMSResponse<List<String>> mGradeResponse = mastersFeignClient
	                .getGradeListByCoordinatorId(response.getTeacher().getCoordinatorTypeId());

	        if (mGradeResponse != null && !CollectionUtils.isEmpty(mGradeResponse.getData())) {
	            List<String> commonGradeList = gradeIdList.stream()
	                    .filter(mGradeResponse.getData()::contains)
	                    .collect(Collectors.toList());

	            return mastersFeignClient.getAllGradesByIds(commonGradeList).getData();
	        }
	    }

	    return Collections.emptyList();
	}


	/**
	 * <li>Fetch all the sections by gradeId, branch and school. GradeId is
	 * optional</li>
	 * <li>By search checking the section is exist via fgien call</li>
	 *
	 * @param search
	 * @param gradeId
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	@Override
	public List<SectionsResponseDto> getAllSectionByGradesSchoolAndBranch(String search, String gradeId,
			String branchId, String schoolId) {
		if (!StringUtils.isEmpty(gradeId) && !gsmRepository.existsByGradeIdAndDeleted(gradeId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("gs.mapping.not.found", null));

		if (!branchRepository.existsByIdAndDeleted(branchId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		if (!schoolRepository.existsByIdAndDeleted(schoolId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (!branchRepository.existsBySchoolsIdAndDeletedAndId(schoolId, false, branchId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.school.relation", null));

		try {
			LMSResponse<List<SectionsResponseDto>> masterResponse = null;

			if (!StringUtils.isEmpty(search)) // searching the section exist in master-data.
				masterResponse = mastersFeignClient.getAllSections(search);
			else {
				List<String> sectionList = gsmRepository.findAllSectionByGradeIdBranchAndSchool(gradeId, branchId,
						schoolId);
				masterResponse = mastersFeignClient.getAllSectionsByIds(sectionList);
			}
			return !ObjectUtils.isEmpty(masterResponse) ? masterResponse.getData() : new ArrayList<>();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapped.with.section.failed", null));
		}
	}

	/**
	 * Get the last modification done on the table grade_section_mapping. Return
	 * format example is 25 Jun 2022 | 10:30 AM
	 *
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return gsmRepository.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	@Override
	public boolean deleteActiveFieldByGradeIdBranchIdAndSchoolId(ToggleGradeSectionRequestDto request) {
		if (!gsmRepository.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeleted(request.getSchoolId(),
				request.getBranchId(), request.getGradeId(), request.getSectionId(), false)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.id.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			gsmRepository.deleteBySectionIdAndGradeIdAndBrachIdAndSchoolId(request.getSectionId(), request.getGradeId(),
					request.getBranchId(), request.getSchoolId(), currentTime, currentUser);
			List<Boolean> activeList = gsmRepository.findDeletedByGradeIdBrachIdAndSchoolId(request.getGradeId(),
					request.getBranchId(), request.getSchoolId());
			return (!CollectionUtils.isEmpty(activeList) && (activeList.size() == 1)
					&& (activeList.get(0) == request.isActive()));
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.delete.failed", null));
		}
	}

	@Override
	public List<GradeSectionMappingResponseDto> getMappingsBySchoolIdAndBranchId(String schoolId, String branchId) {
		try {
			List<GradeSectionMapping> gsmList = gsmRepository.findBySchoolsIdAndBranchesIdAndDeleted(schoolId, branchId,
					false);
			List<String> gradeIds = gsmList.stream().distinct().map(GradeSectionMapping::getGradeId)
					.collect(Collectors.toList());
			List<String> sectionIds = gsmList.stream().distinct().map(GradeSectionMapping::getSectionId)
					.collect(Collectors.toList());
			List<GradesResponseDto> gradeList = null;
			List<SectionsResponseDto> sectionList = null;
			if (!gradeIds.isEmpty()) {
				log.info("Fetched all grade data");
				gradeList = mastersFeignClient.getAllGradesByIds(gradeIds).getData();
			}
			if (!sectionIds.isEmpty()) {
				log.info("Fetched all section data");
				sectionList = mastersFeignClient.getAllSectionsByIds(sectionIds).getData();
			}
			List<GradeSectionMappingResponseDto> responseList = new ArrayList<>();
			for (GradeSectionMapping gsm : gsmList) {
				GradeSectionMappingResponseDto response = new GradeSectionMappingResponseDto(gsm.getId(),
						gsm.getGradeId(), null, gsm.getSectionId(), "", gsm.getSectionData(), gsm.isActive(), 0);
				if (gradeList != null && !gradeList.isEmpty()) {
					Optional<GradesResponseDto> gradeDto = gradeList.stream()
							.filter(g -> g.getId().equals(gsm.getGradeId())).findAny();
					if (gradeDto.isPresent()) {
						log.info("Setting the matching value of grade.");
						response.setGrade(gradeDto.get().getGrade());
						response.setSortOrder(gradeDto.get().getSortOrder());
					}
				}
				if (sectionList != null && !sectionList.isEmpty()) {
					Optional<SectionsResponseDto> sectionDto = sectionList.stream()
							.filter(s -> s.getId().equals(gsm.getSectionId())).findAny();
					if (sectionDto.isPresent()) {
						log.info("Setting the matching value of section.");
						response.setSection(sectionDto.get().getSection());
					}
				}
				responseList.add(response);
			}
			Comparator<GradeSectionMappingResponseDto> compareByGradeAndSection = Comparator
					.comparing(GradeSectionMappingResponseDto::getSortOrder)
					.thenComparing(GradeSectionMappingResponseDto::getSection);
			responseList = responseList.stream().sorted(compareByGradeAndSection).collect(Collectors.toList());
			return responseList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.get.all.failed", null));
		}
	}

	@Override
	public boolean deleteById(String id) {
		if (gsmRepository.existsByIdAndDeleted(id, true)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.id.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			gsmRepository.deleteById(id, currentTime, currentUser);
			return gsmRepository.existsByIdAndActiveAndDeleted(id, false, true); 
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.delete.failed", null));
		}
	}

	@Override
	public boolean toggleActiveStatusById(String id, boolean active) {
		if (gsmRepository.existsByIdAndDeleted(id, true)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.id.not.found", null));
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			gsmRepository.toggleActiveById(id, active, currentTime, currentUser);
			return gsmRepository.existsByIdAndActiveAndDeleted(id, active, false);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("gs.mapping.delete.failed", null));
		}
	}

	@Override
	public Boolean existsByIdAndDeleted(String mappingId, boolean deleted) {
		return gsmRepository.existsByIdAndDeleted(mappingId, deleted);
	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto confirmationApi(String id, String operationType) {
		if (!gsmRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			GradeSectionMapping gsMapping = gsmRepository.getById(id);
			String schoolId = gsMapping.getSchools().getId();
			String branchId = gsMapping.getBranches().getId();

				// Check if any child branches are active
				long activeSectionCount = gsmRepository.findMappingForSection(gsMapping.getSectionId(),
						gsMapping.getGradeId(), schoolId, branchId);
				if (activeSectionCount > 0)
					throw new USException(ErrorCodes.INVALID_OPERATION,
							Translator.toLocale("gs.deactivate.has.active.student.teacher", null));		
			
			// checking any student in mapped Grade-Section.
//			boolean isExists = studentsRepository.checkGradeSectionHasMapping(schoolId, branchId, gsMapping.getGradeId(),
//					gsMapping.getSectionId());
//			String message = CommonUtilities.confirmatioAPIMessages(isExists, operationType);
				
				String message = Translator.toLocale("confirmation.api.toggle.active", null);			
			return new ConfirmationApiResponseDto(activeSectionCount > 0, message);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		}catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("confirmation.api.failed", null));
		}
	}

	/**
	 * This is created as a feign for master-service (confirmation-api of
	 * GradesController).
	 * 
	 * @param gradeId
	 * @return
	 */
	@Override
	public boolean checkTheGradeHasMapping(String gradeId) {
		if (StringUtils.isEmpty(gradeId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("please.provide.madatory.field", null));

		try {
			boolean response = false;
			List<Long> mappingList = gsmRepository.findMappingForGrade(gradeId);
			if (!CollectionUtils.isEmpty(mappingList))
				response = mappingList.stream().anyMatch(item -> item > 0);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("mapping.not.found", null));
		}
	}

	@Override
	public List<String> getAllSectionsForPrincipal(String schoolId, String branchId, String gradeId) {
		try {
			return gsmRepository.findAllSectionByGradeIdBranchAndSchool(gradeId, branchId, schoolId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("mapping.not.found", null));
		}
	}

	@Override
	public List<String> getSectionByGradeId(String gradeId) {

		if (StringUtils.isEmpty(gradeId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("please.provide.madatory.field", null));
		try {
			return gsmRepository.findSectionByGradeId(gradeId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("mapping.not.found", null));
		}

	}
	
	public void updateSectionForAll(UpdateSectionRequestDTO request) {
		try{
			Boolean studentSectionUpdate =studentFeignClient.updateSection(request);
			Boolean teacherSectionUpdate =teacherFeignClient.updateSection(request);
			Boolean contentSectionUpdate = contentFeignClient.updateSection(request);
			log.info("Section update done for student = "+studentSectionUpdate+" teacher = "+teacherSectionUpdate+ " content = "+contentSectionUpdate);
		}catch(Exception e){
			log.error(ExceptionUtils.getStackTrace(e));
		}       

    }
	
}
