package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.ReadingPassport;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Students;
import com.lms.userservice.entity.Teachers;
import com.lms.userservice.entity.Users;
import com.lms.userservice.entity.UsersCheckInHistory;
import com.lms.userservice.entity.UsersInstitutionMapping;
import com.lms.userservice.entity.UsersRoleMapping;
import com.lms.userservice.enums.ChangeAndSharePersona;
import com.lms.userservice.enums.CommunicationAction;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.LoginMedium;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.IrMenuResponseDto;
import com.lms.userservice.feign.master.LanguagesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.MenuSubMenuResponseDto;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.feign.master.StudentCategoriesResponseDto;
import com.lms.userservice.feign.master.SubMenuResponseDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.request.dto.ForgetPasswordNumberOtpRequestDto;
import com.lms.userservice.request.dto.ForgetPasswordOtpRequestDto;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.projection.AdministrationProjection;
import com.lms.userservice.projection.SchoolsProjection;
import com.lms.userservice.projection.StudentsProjection;
import com.lms.userservice.projection.TeachersProjection;
import com.lms.userservice.projection.UsersProjection;
import com.lms.userservice.projection.UsersWebMobCountProjection;
import com.lms.userservice.repository.AdministrationRepository;
import com.lms.userservice.repository.AssignTeacherRepository;
import com.lms.userservice.repository.BranchCommunicationRepository;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.ReadingPassportRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.repository.UsersCheckInHistoryRepository;
import com.lms.userservice.repository.UsersInstitutionMappingRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.request.dto.Access;
import com.lms.userservice.request.dto.AuthRequestDto;
import com.lms.userservice.request.dto.ChangePasswordRequestDto;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.EmailRequestDto;
import com.lms.userservice.request.dto.ForgotPasswordRequestDto;
import com.lms.userservice.request.dto.Grades;
import com.lms.userservice.request.dto.ResetPasswordWithoutTokenRequestDto;
import com.lms.userservice.request.dto.SchoolsBranchsRequestDto;
import com.lms.userservice.request.dto.ShareDetailsRequestDto;
import com.lms.userservice.request.dto.SmsAlertBody;
import com.lms.userservice.request.dto.UpdateMobileNumberRequestDto;
import com.lms.userservice.request.dto.UserRolesRequestDto;
import com.lms.userservice.request.dto.UsersRequestDto;
import com.lms.userservice.request.dto.UsersResetPasswordDto;
import com.lms.userservice.request.dto.ValidateTokenRequestDto;
import com.lms.userservice.request.dto.VerifyOtpRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;
import com.lms.userservice.response.dto.AdministrationResponseDto;
import com.lms.userservice.response.dto.AuthResponseDto;
import com.lms.userservice.response.dto.BranchUsersWebMobCountResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.EmailResponseDto;
import com.lms.userservice.response.dto.PlanTemplateImplResponseDto;
import com.lms.userservice.response.dto.ProfileResponseDto;
import com.lms.userservice.response.dto.SchoolsBranchsMinResponseDto;
import com.lms.userservice.response.dto.SchoolsBranchsResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.response.dto.SmsGatewayRequestDto;
import com.lms.userservice.response.dto.SmsSendResponseDTO;
import com.lms.userservice.response.dto.StudentsResponseDto;
import com.lms.userservice.response.dto.TeacherResponseDto;
import com.lms.userservice.response.dto.UserDetailsResponseDto;
import com.lms.userservice.response.dto.UserMinResponseDto;
import com.lms.userservice.response.dto.UserRolesResponseDto;
import com.lms.userservice.response.dto.UsersFeignDto;
import com.lms.userservice.response.dto.UsersResponseDto;
import com.lms.userservice.response.dto.UsersRoleResponseDto;
import com.lms.userservice.response.dto.UsersUseWebMobCountResponseDto;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.DateUtilities;
import com.lms.userservice.util.EncryptionAndDecryption;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.JwtUtil;
import com.lms.userservice.util.SMSUtils;

import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.impl.DefaultClaims;
import lombok.extern.slf4j.Slf4j;

/**
 * The {@code UserServiceImpl} implements {@code UserService}<br>
 * {@code @Service} is a stereotypical annotation used for Service Layer<br>
 * {@code Slf4j} is a Logger annotation obtained from Lombok dependency for
 * logging the requests and responses
 * 
 * <AUTHOR>
 */

@Slf4j
@Service("userService")
public class UserServiceImpl implements UserService {

	@Autowired
	private JwtUtil jwtUtil;

	@Autowired
	private AuthenticationManager authenticationManager;

	BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private UsersRoleMappingRepository urMappingRepository;

	@Autowired
	private ModelMapper modelMapper;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private UsersInstitutionMappingRepository uiRepository;

	@Autowired
	private MastersFeignClient masterFeignClient;

	@Autowired
	private NotificationFeignClient notificationFeign;

	@Autowired
	private StudentsRepository studentsRepository;

	@Autowired
	private TeacherRepository teacherRepository;

	@Autowired
	private AdministrationRepository adminRepository;

	@Autowired
	private AssignTeacherRepository assignTeacherRepository;

	@Autowired
	private BranchPlanMappingsRepository branchPlanMappingsRepository;

	@Autowired
	private UsersCheckInHistoryRepository usersCheckInHistoryRepo;

	@Autowired
	private BranchCommunicationRepository branchCommunicationRepository;

	@Autowired
	ReadingPassportRepository readingPassportRespository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	/**
	 * Register the user details
	 * 
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	@Override
	@SuppressWarnings("all")
	public UsersResponseDto createuser(UsersRequestDto request) {
		log.info("Register the user started");

		if (usersRepository.existsByUserName(request.getUserName())) {
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("user.already.exist", null) + ", by this name");
		}
		try {
			String currentUser = jwtUtil.currentLoginUser();
			UsersResponseDto response = new UsersResponseDto();
			String encodedPassword = passwordEncoder.encode(request.getPassword());

			log.info("Users details mapped");
			Users users = modelMapper.map(request, Users.class);
			users.setPassword(encodedPassword);
			users.setCreatedBy(currentUser);
			users = usersRepository.save(users);

			if (!StringUtils.isEmpty(users.getId())) {
				log.info("Users details registered successfully!");
				response = modelMapper.map(users, UsersResponseDto.class);
				if (!CollectionUtils.isEmpty(request.getUserRoles())) {
					log.info("Roles are assigning to the user");
					response.setUserRoles(addRoles(users, request.getUserRoles()));
				}
			}
			log.info("Users registration completed!");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.create.failed", null));
		}
	}

	@Override
	@SuppressWarnings("all")
	public UsersResponseDto getuserById(String id) {
		log.info("Get Users details by Id started");

		if (!usersRepository.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}
		try {
			UsersResponseDto response = usersRepository.findUsersById(id);
			if (urMappingRepository.existsByUsersId(response.getId())) {
				List<String> roleIds = urMappingRepository.findAllRolesByUserId(response.getId());
				List<RolesFeignDto> rolesDto = masterFeignClient.getRolesByIds(roleIds).getData();
				response.setRoles(rolesDto);
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.by.id.failed", null));
		}
	}

	@Override
	public List<UsersResponseDto> getAllUsers() {
		log.info("Get all user details started");
		List<UsersResponseDto> userResponse = new ArrayList<>();
		try {
			log.info("Get all users details in progress...");
			usersRepository.findAll().forEach(user -> userResponse.add(modelMapper.map(user, UsersResponseDto.class)));
			if (!userResponse.isEmpty()) {
				log.info("All users details fetched successfully!");
			}
			log.info("Get all user details completed");
			return userResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.all.failed", null));
		}
	}

	@Override
	public PaginatedResponse<UsersResponseDto> getUsersByPagniation(int pageNumber, int pageSize, boolean sortOrder,
			String sortBy, String search, String roleId, Boolean active) {
		log.info("User filtering and searching started");
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			List<UsersResponseDto> usersList = new ArrayList<>();
			String sortByField = FieldMappers.usersApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortByField));

			log.info("Paginated data in progress");
			Page<UsersProjection> projectionList = usersRepository.findAllUserBySearchAndFilter(false, searchFormat,
					roleId, pageable, active);
			if (!projectionList.getContent().isEmpty()) {
				totalElements = projectionList.getTotalElements();
				totalPages = projectionList.getTotalPages();
				projectionList.getContent().forEach(user -> {
					UsersResponseDto responseDto = new UsersResponseDto(user);
					if (urMappingRepository.existsByUsersId(responseDto.getId())) {
						List<String> roleIds = urMappingRepository.findAllRolesByUserId(responseDto.getId());

						LMSResponse<List<RolesFeignDto>> roleResponse = masterFeignClient.getRolesByIds(roleIds);
						if (!ObjectUtils.isEmpty(roleResponse) && !CollectionUtils.isEmpty(roleResponse.getData()))
							responseDto.setRoles(roleResponse.getData());
					}
					usersList.add(responseDto);
				});
			}
			log.info("User filtering and searching completed");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), usersList.size(),
					usersList);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.list.failed", null));
		}
	}

	@Override
	@SuppressWarnings("all")
	public UsersResponseDto getuserByEmail(String email) {
		log.info("Get Users details by email started");
		if (StringUtils.isEmpty(email))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.id.not.found", null));

		if (!usersRepository.existsByEmailAndDeleted(email, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		UsersResponseDto userResponse = new UsersResponseDto();
		try {
			log.info("Fetching user's details in progress...");
			Users users = usersRepository.findByEmail(email).orElseThrow(Exception::new);
			userResponse = modelMapper.map(users, UsersResponseDto.class);
			if (!StringUtils.isEmpty(userResponse.getId())) {
				log.info("Users details fetched successfully!");
			}
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.by.id.failed", null));
		}
		log.info("Get Users details completed");
		return userResponse;
	}

	@Override
	public UsersResponseDto updateUser(String id, UsersRequestDto userRequest) {
		log.info("Update the Users started");

		if (!usersRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			UsersResponseDto response = null;
			log.info("Users details mapped");
			Users users = getUsersById(id);
			modelMapper.map(userRequest, users);
			users.setModifiedAt(currentTime);
			users.setLastModifiedBy(currentUser);
			if (!StringUtils.isBlank(userRequest.getPassword())) {
				String encodedPassword = passwordEncoder.encode(userRequest.getPassword());
				users.setPassword(encodedPassword);
			}
			users = usersRepository.save(users);
			response = modelMapper.map(users, UsersResponseDto.class);
			if (!userRequest.getUserRoles().isEmpty()) {
				log.info("Roles are assigning to the user");
				response.setUserRoles(addRoles(users, userRequest.getUserRoles()));
			}
			log.info("User Update completed!");
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.update.failed", null));
		}
	}

	@Override
	public UsersResponseDto resetPassword(String id, UsersResetPasswordDto request) {

		log.info("Reset password: Get Users details by Id started.");
		if (StringUtils.isEmpty(id)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.id.not.found", null));
		}

		if (!usersRepository.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}

		UsersResponseDto userResponse = new UsersResponseDto();
		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			log.info("Users details mapped");
			Users users = getUsersById(id);
			if (!StringUtils.isEmpty(users.getId())) {
				String encodedPassword = passwordEncoder.encode(request.getPassword());
				users.setPassword(encodedPassword);
				users.setModifiedAt(currentTime);
				users.setLastModifiedBy(currentUser);
				users = usersRepository.save(users);
				log.info("Password Reset successfully!");
				userResponse = modelMapper.map(users, UsersResponseDto.class);
			}
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("password.reset.failed", null));
		}
		log.info("Password Update completed!");

		return userResponse;
	}

	/**
	 * Followed by the forgot-password from the UI, no need of the token
	 * 
	 * @param userId  (This is an encrypted value)
	 * @param request
	 * @return
	 */
	@Override
	public UsersResponseDto withoutTokenResetPassword(ResetPasswordWithoutTokenRequestDto request) {
		String id = EncryptionAndDecryption.decrypt(request.getUserId());
		if (!usersRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			UsersResponseDto userResponse = new UsersResponseDto();
			log.info("Users details mapped");
			Users users = getUsersById(id);
			if (!StringUtils.isBlank(users.getId())) {
				String encodedPassword = passwordEncoder.encode(request.getPassword());
				users.setPassword(encodedPassword);
				users.setModifiedAt(currentTime);
				users.setLastModifiedBy(users.getUserName());
				users = usersRepository.save(users);
				log.info("Password Reset successfully!");
				userResponse = modelMapper.map(users, UsersResponseDto.class);
			}
			log.info("Password Update completed!");
			return userResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("password.reset.failed", null));
		}
	}

	/**
	 * Add the roles to user
	 * 
	 * @param users
	 * @param userRoles
	 * @return
	 */
	@Override
	public List<UserRolesResponseDto> addRoles(Users users, List<UserRolesRequestDto> userRoles) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			List<UserRolesResponseDto> response = new ArrayList<>();
			Long roleMappingCount = urMappingRepository.countByUsersId(users.getId());
			if (roleMappingCount != null && roleMappingCount > 0) {
				urMappingRepository.deleteByUsersId(users.getId());
			}
			userRoles.forEach(roles -> {
				UsersRoleMapping urMapping = new UsersRoleMapping(users, roles.getRoleId());
				urMapping.setCreatedBy(currentUser);
				urMapping = urMappingRepository.save(urMapping);
				response.add(new UserRolesResponseDto(urMapping.getId(), urMapping.getRoleId(), urMapping.isActive()));
			});
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.role.add.failed", null));
		}
	}

	/**
	 * Once the user/admin/teacher or student deleted, then remove user-role mapping
	 * too.
	 * 
	 * @param userId
	 * @return
	 */
	@Override
	public Long deleteUsersRoleMapping(String userId) {
		try {
			Long deleted = 0L;
			if (urMappingRepository.countByUsersId(userId) > 0) {
				deleted = urMappingRepository.deleteByUsersId(userId);
			}
			return deleted;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.role.delete.failed", null));
		}
	}

	/**
	 * To map the Users with Schools and Branches
	 * 
	 * @param users
	 * @param institutions
	 * @return
	 */
	@Override
	public List<SchoolsBranchsResponseDto> addInstitution(Users users, List<SchoolsBranchsRequestDto> institutions) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			List<SchoolsBranchsResponseDto> response = new ArrayList<>();

			if (uiRepository.countByUsersId(users.getId()) > 0) {
				uiRepository.deleteByUsersId(users.getId());
			}

			for (SchoolsBranchsRequestDto institution : institutions) {
				Schools schools = schoolRepository.getById(institution.getSchoolId());
				List<String> branchList = Arrays.asList(institution.getBranchesId());
				List<BranchesMinDataResponseDto> branchMinList = new ArrayList<>();

				for (String branch : branchList) {
					Branches branches = branchRepository.getById(branch);

					// Will avoid request if combo of user, school, branch is already exist
					if (!uiRepository.existsByUsersIdAndBranchesIdAndSchoolsId(users.getId(), branches.getId(),
							schools.getId())) {
						UsersInstitutionMapping userInstitute = new UsersInstitutionMapping();
						userInstitute.setUsers(users);
						userInstitute.setSchools(schools);
						userInstitute.setBranches(branches);
						userInstitute.setCreatedBy(currentUser);
						userInstitute = uiRepository.save(userInstitute);
						if (!StringUtils.isEmpty(userInstitute.getId())) {
							branchMinList.add(new BranchesMinDataResponseDto(branches.getId(), branches.getName(),
									branches.isActive()));
						}
					}
				}
				response.add(new SchoolsBranchsResponseDto(schools.getId(), schools.getCode(), schools.getName(),
						branchMinList));
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.institute.add.failed", null));
		}
	}

	/**
	 * Create the response for addInstitution
	 * 
	 * @param schools
	 * @param branchMinList
	 * @return
	 */
	private List<SchoolsBranchsResponseDto> createInstitutionResponse(Schools schools,
			List<BranchesMinDataResponseDto> branchMinList) {
		try {
			List<SchoolsBranchsResponseDto> response = new ArrayList<>();
			SchoolsBranchsResponseDto schoolsBranches = new SchoolsBranchsResponseDto(schools.getId(),
					schools.getName(), schools.getCode(), branchMinList);
			response.add(schoolsBranches);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.institute.return.failed", null));
		}
	}

	/**
	 * Once the user/admin/teacher or student deleted, then remove user-institution
	 * mapping too.
	 * 
	 * @param userId
	 * @return
	 */
	@Override
	public Long deleteUsersInstitutionMapping(String userId) {
		try {
			Long deleted = 0L;
			if (uiRepository.countByUsersId(userId) > 0) {
				deleted = uiRepository.deleteByUsersId(userId);
			}
			return deleted;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.inst.delete.failed", null));
		}
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	@Modifying
	@Transactional
	public Boolean deleteUserById(String id) {
		if (!usersRepository.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}
		try {
			Long currentTime = new Date().getTime();
			if (urMappingRepository.countByUsersId(id) > 0) {
				urMappingRepository.updateUserMappingByUserId(false, true, currentTime, id);
			}
			Users users = usersRepository.getById(id);
			users.setActive(false);
			users.setDeleted(true);
			users.setModifiedAt(currentTime);
			users = usersRepository.save(users);
			return users.isDeleted();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.role.add.failed", null));
		}
	}

	public Users getUsersById(String id) throws USException {
		return usersRepository.findById(id)
				.orElseThrow(() -> new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null)));
	}

	/**
	 * Get user details by user name This method wrote for master-service token
	 * validation.
	 * 
	 * @param username
	 * @return
	 * @since 0.0.1
	 */
	@Override
	public UsersFeignDto getUsersByUsernameForFeign(String username) throws USException {
		log.info("Get Users details by username started");

		if (!usersRepository.existsByUserName(username)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}
		try {
			return usersRepository.findUsersByUserNameForFeign(username);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.by.id.failed", null));
		}
	}

	/**
	 * AuthResponseDto
	 * 
	 * @return
	 * @since 0.0.1
	 */
	@Override
	public AuthResponseDto authentication(AuthRequestDto authRequestDto) {
		if (!usersRepository.existsByUserNameIgnoreCaseAndDeletedAndActive(authRequestDto.getUsername(), false, true)) {
			throw new USException(ErrorCodes.ACCESS_DENIED, Translator.toLocale("access.denied", null));
		}
		Users users;
		try {
			users = usersRepository.findByUserNameIgnoreCase(authRequestDto.getUsername());
			authenticationManager.authenticate(
					new UsernamePasswordAuthenticationToken(users.getUserName(), authRequestDto.getPassword()));
			authRequestDto.setUsername(users.getUserName());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.authentication.failed", null));
		}

		AuthResponseDto authResponse = new AuthResponseDto();
		String username = authRequestDto.getUsername();
		String token = jwtUtil.generateToken(authRequestDto.getUsername());
		authResponse.setToken(token);
		authResponse.setUserName(username);
		if (StringUtils.isEmpty(username))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.not.found", null));

		if (!usersRepository.existsByUserName(username))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			log.info("Users details mapped");
//			Users users = usersRepository.findByUserName(username);
			users.setLastLoginTime(currentTime);
			users = usersRepository.save(users);
			if (!StringUtils.isEmpty(users.getId())) {
				authResponse.setId(users.getId());
				authResponse.setFirstName(users.getFirstName());
				authResponse.setLastName(users.getLastName());
				authResponse.setEmail(users.getEmail());
				authResponse.setPhoneNumber(users.getPhoneNumber());
				List<String> roles = urMappingRepository.findAllRolesByUserId(users.getId());
				ReadingPassport readingPassport = readingPassportRespository.findByUserId(users.getId());
				log.info("access------>" + readingPassport);

				// making a feign call to master-service
				if (!CollectionUtils.isEmpty(roles)) {
					List<RolesFeignDto> userRoles = masterFeignClient.getRolesByIds(roles).getData();
					authResponse.setRoles(userRoles);
					List<MenuSubMenuResponseDto> menus = masterFeignClient.getAllRolesMenuMappingByRoles(roles)
							.getData();
					authResponse.setMenus(menus);
					List<IrMenuResponseDto> irmenus = new ArrayList<>();
					IrMenuResponseDto irmenu = new IrMenuResponseDto();
					irmenu.setOrder(1);
					irmenu.setKey("courses");
					irmenu.setTitle("Courses");
					irmenu.setRoute("/courses");
					irmenu.setIcon("video");
					irmenu.setRoles("CONTENT_ADMIN");
					irmenus.add(irmenu);

					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(2);
					irmenu.setKey("question-bank");
					irmenu.setTitle("Question Bank");
					irmenu.setRoute("/question-bank");
					irmenu.setIcon("Scroll");
					irmenu.setRoles("CONTENT_ADMIN");
					irmenus.add(irmenu);

					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(3);
					irmenu.setKey("assessments");
					irmenu.setTitle("Assessments");
					irmenu.setRoute("/assessments");
					irmenu.setRoles("CONTENT_ADMIN");
					irmenu.setIcon("list-checks");
					irmenus.add(irmenu);

					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(4);
					irmenu.setKey("study");
					irmenu.setTitle("Study");
					irmenu.setRoute("/study");
					irmenu.setIcon("book-open-text");
					irmenu.setRoles("STUDENT");
					irmenus.add(irmenu);

					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(5);
					irmenu.setKey("gradingModel");
					irmenu.setTitle("Grading Model");
					irmenu.setRoute("/grading-model");
					irmenu.setIcon("graduation-cap");
					irmenu.setRoles("CONTENT_ADMIN");
					irmenus.add(irmenu);

					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(6);
					irmenu.setKey("quiz-POC");
					irmenu.setTitle("Quiz POC");
					irmenu.setRoute("/quiz-poc");
					irmenu.setIcon("Student");
					irmenu.setRoles("STUDENT");
					irmenus.add(irmenu);
					
					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(7);
					irmenu.setKey("ir-grading");
					irmenu.setTitle("IR Grading");
					irmenu.setRoute("/ir-grading");
					irmenu.setIcon("ir-grading");
					irmenu.setRoles("CONTENT_ADMIN");
					irmenus.add(irmenu);

					
					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(8);
					irmenu.setKey("student");
					irmenu.setTitle("Student");
					irmenu.setRoute("/student");
					irmenu.setIcon("users-four");
					irmenu.setRoles("TEACHER");
					irmenus.add(irmenu);
					
					irmenu = new IrMenuResponseDto();
					irmenu.setOrder(9);
					irmenu.setKey("my-activity");
					irmenu.setTitle("My Activity");
					irmenu.setRoute("/my-activity");
					irmenu.setIcon("trend-up");
					irmenu.setRoles("STUDENT");
					irmenus.add(irmenu);

					authResponse.setIrMenu(irmenus);

					// Currently one one role is assigned to each users. The below set of codes are
					// for find the purchased plan feature.
					String roleName = userRoles.get(0).getRole();
					if (roleName.equals("TEACHER") || roleName.equals("COORDINATOR") || roleName.equals("PRINCIPAL")
							|| roleName.equals("STUDENT")) {
						log.info("Only for academic-staffs and students.");

						Teachers teachers = !roleName.equals("STUDENT")
								? teacherRepository.findByUserNameIgnoreCase(username)
								: null;
						Students students = roleName.equals("STUDENT")
								? studentsRepository.findByUserNameIgnoreCase(username)
								: null;
								if(students != null){
									authResponse.setStudentId(students.getId());
								}
						
						String planId = null;

						// Finding the grades for academic staffs and students, for principal no need of
						// grades.
						List<String> gradeIds = new ArrayList<>();
						List<String> subjectIds = new ArrayList<>();
						// ObjectMapper objectMapper = new ObjectMapper();
						// try {
						// // Parse the JSON string into a JsonNode object
						// String access = readingPassport.getAccess();
						// log.info("before access:" + access);
						// access = access.replace("\\", "");
						// log.info("after access:" + access);

						// // Map the JSON string to model objects
						// GradesContainer gradesContainer = objectMapper.readValue(access, new
						// TypeReference<GradesContainer>() {});

						// // Access gradeIds and sectionIds from the model objects
						// List<Grade> grades = gradesContainer.getGrades();
						// List<String> rpGradeSections = new A
						// for (Grade grade : grades) {

						// log.info("Grade: " + grade.getGrade());
						// log.info("Grade ID: " + grade.getGradeId());
						// for (Section section : grade.getSections()) {
						// log.info("Section: " + section.getSection());
						// log.info("Section ID: " + section.getSectionId());

						// }
						// }
						// } catch (Exception e) {
						// e.printStackTrace();
						// }

						if (teachers != null) {
							planId = branchPlanMappingsRepository.findPlanIdByBranchId(teachers.getBranches().getId());
							if (teachers.getAcademicStaffProfile().getCode().equals("TEACHER")) {
								gradeIds = assignTeacherRepository.getAllGradeIdsByTeacherId(teachers.getId());
								subjectIds = assignTeacherRepository.getSubjectIdsByTeacherId(teachers.getId());

								if (readingPassport == null) {
									menus = removeMenuItem(menus, "Reading Passport");
								} else if (!readingPassport.isLibrarian() && readingPassport.getAccess() != null) {
									boolean haveAccess = false;
									ObjectMapper mapper = new ObjectMapper();
									mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
									Access access = mapper.readValue(readingPassport.getAccess(),
											new TypeReference<Access>() {
											});
									// Access access = mapper.convertValue(readingPassport.getAccess(),
									// Access.class);
									if (access.getGrades() != null && !access.getGrades().isEmpty()) {
										for (Grades grade : access.getGrades()) {
											if (gradeIds.contains(grade.getGradeId())) {
												haveAccess = true;
												break;
											}
										}
									}
									if (!haveAccess) {
										menus = removeMenuItem(menus, "Reading Passport");
									}

								}
							}

							if (teachers.getAcademicStaffProfile().getCode().equals("COORDINATOR")) {
								LMSResponse<List<String>> gradestByCoordinatorTypeId = masterFeignClient
										.getGradesByCoordinatorId(teachers.getCoordinatorTypeId());
								if (gradestByCoordinatorTypeId != null
										&& !CollectionUtils.isEmpty(gradestByCoordinatorTypeId.getData()))
									gradeIds = gradestByCoordinatorTypeId.getData();
							}
						}

						if (students != null) {
							planId = branchPlanMappingsRepository.findPlanIdByBranchId(students.getBranches().getId());
							gradeIds.add(students.getGradeId());
						}

						LMSResponse<List<PlanTemplateImplResponseDto>> planPurchasedResponse = masterFeignClient
								.purchasedPlanTemplate(planId, gradeIds, roleName, subjectIds);
						if (planPurchasedResponse != null && !CollectionUtils.isEmpty(planPurchasedResponse.getData()))
							authResponse.setPurchasedFeatures(planPurchasedResponse.getData());

						if (authResponse.getPurchasedFeatures() == null
								|| (authResponse.getPurchasedFeatures().isEmpty())) {
							boolean haveAccess = false;

							if (readingPassport != null) {
								// GradesContainer gradesContainer =
								// objectMapper.readValue(readingPassport.getAccess(), new
								// TypeReference<GradesContainer>() {});
								ObjectMapper mapper = new ObjectMapper();
								mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
								// GradesContainer gradesContainer =
								// objectMapper.readValue(readingPassport.getAccess(), new
								// TypeReference<GradesContainer>() {});
								Access access = mapper.readValue(readingPassport.getAccess(),
										new TypeReference<Access>() {
										});
								// Access access = mapper.convertValue(readingPassport.getAccess(),
								// Access.class);

								if (readingPassport.isLibrarian()
										|| (access.getGrades() != null && !access.getGrades().isEmpty())) {

									List<PlanTemplateImplResponseDto> ptR = new ArrayList<>();
									PlanTemplateImplResponseDto dto = new PlanTemplateImplResponseDto();
									dto.setName("Reading Passport");
									dto.setToggleStatus(true);
									dto.setType("Menu");
									ptR.add(dto);
									authResponse.setPurchasedFeatures(ptR);

									MenuSubMenuResponseDto menu = new MenuSubMenuResponseDto();
									menu.setMenu("Dashboard");
									menu.setMenuId("ff808181854f743801854f76a37d0000");
									menus.clear();
									menus.add(menu);

									menu = new MenuSubMenuResponseDto();
									menu.setMenu("Reading Passport");
									menu.setMenuId("402892888e0e01bb018e50431e4800e0");
									List<SubMenuResponseDto> subMenus = new ArrayList<>();

									SubMenuResponseDto subMenu = new SubMenuResponseDto();
									subMenu.setSubMenuName("Reading Passport List");
									subMenu.setSubMenuId("402892888e0e01bb018e75dcec0701b7");
									subMenu.setMenuId("402892888e0e01bb018e50431e4800e0");
									subMenus.add(subMenu);

									subMenu = new SubMenuResponseDto();
									subMenu.setSubMenuId("402892888e0e01bb018e75dd58c901b8");
									subMenu.setSubMenuName("Monthly Avid Reader");
									subMenu.setMenuId("402892888e0e01bb018e50431e4800e0");
									subMenus.add(subMenu);

									subMenu = new SubMenuResponseDto();
									subMenu.setSubMenuId("402892888e0e01bb018e75dd9ab701b9");
									subMenu.setSubMenuName("Performance Report");
									subMenu.setMenuId("402892888e0e01bb018e50431e4800e0");
									subMenus.add(subMenu);
									menu.setSubMenus(subMenus);

									menus.add(menu);

								}
							}

						}
					}
				} else
					throw new USException(ErrorCodes.ACCESS_DENIED,
							Translator.toLocale("none.active.role.login.denied", null));

				// User login history is saving
				if (!StringUtils.isBlank(authRequestDto.getLoginMedium())) {
					boolean webLogin = LoginMedium.WEB.getCode().equals(authRequestDto.getLoginMedium());
					boolean mobLogin = LoginMedium.MOB.getCode().equals(authRequestDto.getLoginMedium());

					UsersCheckInHistory usersCheckInHistory = null;
					if (webLogin)
						usersCheckInHistory = new UsersCheckInHistory(users, username, webLogin, mobLogin, currentTime,
								null, authRequestDto.getMacAddress(), true);

					if (mobLogin)
						usersCheckInHistory = new UsersCheckInHistory(users, username, webLogin, mobLogin, null,
								currentTime, authRequestDto.getMacAddress(), true);

					if (usersCheckInHistory != null) {
						usersCheckInHistory.setCreatedBy(users.getCreatedBy());
						usersCheckInHistoryRepo.save(usersCheckInHistory);
					}
				}

			}
			return authResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			ExceptionUtils.getRootCauseStackTrace(e);
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.authentication.failed", null));
		}
	}

	private List<MenuSubMenuResponseDto> removeMenuItem(List<MenuSubMenuResponseDto> menus, String menu) {
		List<MenuSubMenuResponseDto> menusNew = new ArrayList<MenuSubMenuResponseDto>();

		for (MenuSubMenuResponseDto menuSubMenuResponseDto : menusNew) {
			if (!menu.equalsIgnoreCase(menuSubMenuResponseDto.getMenu())) {
				menusNew.add(menuSubMenuResponseDto);
			}
		}
		return menusNew;

	}

	/**
	 * AuthResponseDto using refresh token
	 * 
	 * @return
	 * @since 0.0.1
	 */
	@Override
	public AuthResponseDto authenticationByRefreshToken(HttpServletRequest request) {

		DefaultClaims claims = (io.jsonwebtoken.impl.DefaultClaims) request.getAttribute("claims");

		Map<String, Object> expectedMap = getMapFromIoJsonwebtokenClaims(claims);
		AuthResponseDto authResponse = new AuthResponseDto();
		String token = jwtUtil.reGenerateToken(expectedMap, expectedMap.get("sub").toString());
		String username = jwtUtil.extractUsername(token);
		authResponse.setToken(token);
		authResponse.setUserName(username);
		if (StringUtils.isEmpty(username)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.not.found", null));
		}

		if (!usersRepository.existsByUserName(username)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}

		return authResponse;
	}

	public Map<String, Object> getMapFromIoJsonwebtokenClaims(DefaultClaims claims) {
		Map<String, Object> expectedMap = new HashMap<String, Object>();
		for (Map.Entry<String, Object> entry : claims.entrySet()) {
			expectedMap.put(entry.getKey(), entry.getValue());
		}
		return expectedMap;
	}

	/**
	 * User's data is taking by the help of userName & email or mobile will use if
	 * after client discussion.
	 * <li>This method will not work, the structure of the sending the email has
	 * changed
	 * <li>
	 * 
	 * @param requestDto
	 * @return
	 */
	public EmailResponseDto forgotPasswordWithEmailOrUserName(ForgotPasswordRequestDto requestDto) {
		try {
			UsersResponseDto userDto = usersRepository.findByUserNameOrEmailOrMobile(requestDto.getUserName(),
					requestDto.getEmailOrMobile());
			String userId = EncryptionAndDecryption.encrypt(userDto.getId());
			String resetPasswordUrl = masterFeignClient.getResetPasswordWithBaseUrl(requestDto.getLmsEnv()).getData();
			String resetLink = resetPasswordUrl + "?userId=" + userId;
			// return notificationFeign.resetpassword(new
			// EmailRequestDto(userDto.getEmail(), resetLink)).getData();
			EmailResponseDto response = new EmailResponseDto();
			response.setMessage(resetLink);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("password.reset.failed", null));
		}
	}

	/**
	 * Token will get validate before each and every request, which needed the
	 * token.
	 * 
	 * @param token
	 * @return
	 */
	@Override
	public UsersResponseDto tokenValidation(ValidateTokenRequestDto request) {
		if (StringUtils.isEmpty(request.getToken()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("token.mandatory", null));
		try {
			String username = jwtUtil.extractUsername(request.getToken());
			return usersRepository.findUsersByUserName(username);
		} catch (USException e) {
			throw new USException(e.getErrorCode(), e.getMessage());
		} catch (AccessDeniedException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.ACCESS_DENIED, Translator.toLocale("access.denied", null));
		} catch (MalformedJwtException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("malformed.Jwt", null));
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("token.validate.failed", null));
		}
	}

	/**
	 * To sent the reset-password link to given email. This API no need of JWT
	 * token.
	 *
	 */
	@Override
	public EmailRequestDto forgotPassword(String email, String lmsEnv, String userName) {
		if (!Enums.getIfPresent(LMSEnvironment.class, lmsEnv).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("specify.application.environment", null));

		try {
			UsersResponseDto userResponse = usersRepository.findByUserNameOrEmailOrMobile(userName, email);
			String userId = EncryptionAndDecryption.encrypt(userResponse.getId());
			String resetPasswordUrl = masterFeignClient.getResetPasswordWithBaseUrl(lmsEnv).getData();
			String resetLink = resetPasswordUrl + "?userId=" + userId;
			return new EmailRequestDto(resetLink, email);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("email.sent.failed", null));
		}

	}

	/**
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType) {
		if (!usersRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			boolean isExits = false;
			String message = "There is no mapping related to this user currently.";
			Users users = usersRepository.getById(id);
			List<Long> mappingCounts = usersRepository.findAllMappingCountOfUser(id);
			if (!mappingCounts.isEmpty()) {
				isExits = mappingCounts.stream().anyMatch(count -> count > 0);
				if (isExits) {
					if (OperationType.DELETE == OperationType.valueOf(operationType))
						message = "Some data are connected with this user. Deleting may cause some loss of data. "
								+ "Do you still want to delete " + users.getUserName();
					else
						message = "Some data are connected with this user. "
								+ "Changing the active status may cause some constrain violation issues. "
								+ "Do you still want to change the active status of " + users.getUserName();
				} else
					return message;
			}
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	@Override
	@SuppressWarnings("all")
	public UsersResponseDto getuserByPhoneNumber(String phoneNumber) {
		log.info("Get Users details by phonenumber started");
		if (StringUtils.isEmpty(phoneNumber)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.id.not.found", null));
		}

		if (!usersRepository.existsByPhoneNumber(phoneNumber)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}

		UsersResponseDto userResponse = new UsersResponseDto();
		try {
			log.info("Get user's details by mobile in progress...");
			Users users = usersRepository.findByPhoneNumber(phoneNumber).orElseThrow(Exception::new);
			userResponse = modelMapper.map(users, UsersResponseDto.class);
			if (!StringUtils.isEmpty(userResponse.getId())) {
				log.info("Users details fetched successfully!");
			}
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.get.by.id.failed", null));
		}
		log.info("Get Users details completed");
		return userResponse;
	}

	/**
	 * Remove the +91 once the twilio account get upgrade or after SMS gateway
	 * implemented. To send SMS through the twilio the toPhoneNumber has to be
	 * verified in the twilio account(if the account is free trial)
	 *
	 */
	@Override
	@SuppressWarnings("all")
	public String forgotPasswordMobile(String phoneNumber, String userName) {
		if (!usersRepository.existsByPhoneNumber(phoneNumber))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			String otp = CommonUtilities.generateRandumNumber(4);
			Users users = usersRepository.findUserByPhoneNumber(phoneNumber, userName);
			users.setOtp(otp);
			usersRepository.save(users);

			/**
			 * Use it local if you have free twilio account, register the phoneNumber in
			 * twilio to send the SMS
			 */
			// SmsAlertBody smsAlertBody = new SmsAlertBody(otp + " is your Azvasa
			// verification code.", "+91" + phoneNumber);

			/**
			 * Below smsAlertBody will give error if phoneNumber not verified(if the twilio
			 * a/c is free)
			 */
			SmsAlertBody smsAlertBody = new SmsAlertBody(otp + " is your Azvasa verification code.", phoneNumber);

			SmsAlertBody smsAlertResponse = notificationFeign.sendSms(smsAlertBody).getData();
			return (!StringUtils.isBlank(smsAlertResponse.getMessage())
					&& !StringUtils.isBlank(smsAlertResponse.getPhoneNumber())) ? "OTP send successfully" : null;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.otp.send.failed", null));
		}
	}

	/**
	 * Check verify the user with phone number and the otp, then return user details
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public UsersResponseDto verifyOtp(VerifyOtpRequestDto request) {
		if (!usersRepository.existsByPhoneNumberAndOtp(request.getPhoneNumber(), request.getOtp()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		try {
			Users users = usersRepository.findUserByPhoneNumberAndOtp(request.getPhoneNumber(), request.getOtp());
			users.setOtp(null);
			users = usersRepository.save(users);
			return modelMapper.map(users, UsersResponseDto.class);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, null);
		}
	}

	/**
	 * After verify the OTP , resetting the password. This API no need of token
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public UsersResponseDto mobileResetPasswordWithoutToken(ResetPasswordWithoutTokenRequestDto request) {
		if (!usersRepository.existsById(request.getUserId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			log.info("Finding the user by id.");
			Users users = usersRepository.getById(request.getUserId());
			String encryptedPassword = passwordEncoder.encode(request.getPassword());
			users.setPassword(encryptedPassword);
			users.setLastModifiedBy(users.getUserName());
			users.setModifiedAt(currentTime);
			users = usersRepository.save(users);
			log.info("Password updated successfully.");
			return modelMapper.map(users, UsersResponseDto.class);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, null);
		}
	}

	/**
	 * Get the last modification done on the table users return format example will
	 * be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return usersRepository.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	@Override
	public List<UserMinResponseDto> getAllUsersMappedToRole(String roleId) {
		try {
			return usersRepository.getAllUsersMappedToRole(roleId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	/**
	 * Generate user name for persons like school-admin, management, admin-uers
	 * [master/super/content/revision-Admin or reviewer]
	 * 
	 * @param firstName
	 * @param lastName
	 * @return
	 */
	@Override
	public String generateUserName(String firstName, String lastName) {
		try {
			long count = usersRepository.countByFirstNameAndLastName(firstName, lastName);
			return (count > 0) ? (firstName + "." + lastName + "." + count) : (firstName + "." + lastName);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.name.generate.failed", null));
		}
	}

	@Override
	public ProfileResponseDto getUsersByUserName(String username, List<String> roles) {

		if (!usersRepository.existsByUserName(username)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		}
		try {
			ProfileResponseDto response = new ProfileResponseDto();
			response.setUserName(username);
			if (!CollectionUtils.isEmpty(roles)) {
				roles.forEach(role -> {
					if (role.toLowerCase().equals("student")) {
						log.info("Students Role | finding by username.");
						response.setStudent(getSudentProfile(username));
					} else if (role.toLowerCase().equals("teacher") || role.toLowerCase().equals("principal")
							|| role.toLowerCase().equals("coordinator")) {
						log.info("Teacher Role | finding by username.");
						TeachersProjection teacherProjection = teacherRepository.getTeachersByUserName(username);
						TeacherResponseDto teacher = new TeacherResponseDto(teacherProjection);
						response.setTeacher(teacher);
					} else if (role.toLowerCase().contains("admin") || role.toLowerCase().contains("management")) {
						log.info("Administration Role | finding by username.");
						AdministrationProjection administrationProjection = adminRepository
								.getAdminByUserName(username);
						AdministrationResponseDto administration = new AdministrationResponseDto(
								administrationProjection);
						response.setAdministration(administration);
					} else {
						log.info("AdminUser Role | finding by username.");
						UsersProjection usersProjection = usersRepository.getUserByUserName(username);
						AdminUsersResponseDto adminUser = new AdminUsersResponseDto(usersProjection);
						response.setAdminUsers(adminUser);
					}
				});
			}
			return response;

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.get.by.username.failed", null));
		}
	}

	/**
	 * Get the student profile by user_name
	 * 
	 * @param userName
	 * @return
	 */
	private StudentsResponseDto getSudentProfile(String userName) {
		try {
			StudentsProjection projection = studentsRepository.findStudentByUserName(userName);
			StudentsResponseDto response = new StudentsResponseDto(projection);
			if (!StringUtils.isBlank(response.getGradeId())) {
				GradesResponseDto grades = masterFeignClient.getGradesById(response.getGradeId()).getData();
				response.setGrade(grades.getGrade());
			}
			if (!StringUtils.isBlank(response.getSectionId())) {
				SectionsResponseDto sections = masterFeignClient.getSectionsById(response.getSectionId()).getData();
				response.setSection(sections.getSection());
			}

			// *** setting the first/second language started ***//
			Set<String> languageSet = new HashSet<>();
			if (!StringUtils.isBlank(projection.getFirstLanguageId()))
				languageSet.add(projection.getFirstLanguageId());
			if (!StringUtils.isBlank(projection.getSecondLanguageId()))
				languageSet.add(projection.getSecondLanguageId());

			List<LanguagesResponseDto> languageList = new ArrayList<>();
			if (!CollectionUtils.isEmpty(languageSet)) {
				List<String> ids = languageSet.stream().collect(Collectors.toList());
				LMSResponse<List<LanguagesResponseDto>> fromMaster = masterFeignClient.getLanguagesByIds(ids);
				languageList = fromMaster != null && !CollectionUtils.isEmpty(fromMaster.getData())
						? fromMaster.getData()
						: new ArrayList<>();
			}

			if (!CollectionUtils.isEmpty(languageList)) {
				Optional<LanguagesResponseDto> languageDto;
				if (!StringUtils.isBlank(projection.getFirstLanguageId())) {
					languageDto = languageList.stream()
							.filter(item -> item.getId().equals(projection.getFirstLanguageId())).findFirst();
					if (languageDto.isPresent()) {
						response.setFirstLanguageId(languageDto.get().getId());
						response.setFirstLanguage(languageDto.get().getLanguage());
					}
				}

				if (!StringUtils.isBlank(projection.getSecondLanguageId())) {
					languageDto = languageList.stream()
							.filter(item -> item.getId().equals(projection.getSecondLanguageId())).findFirst();
					if (languageDto.isPresent()) {
						response.setSecondLanguageId(languageDto.get().getId());
						response.setSecondLanguage(languageDto.get().getLanguage());
					}
				}
			}
			// *** setting the first/second language ended ***//

			if (!StringUtils.isBlank(response.getStudentCategoryId())) {
				StudentCategoriesResponseDto studentCategory = masterFeignClient
						.getStudentCategoryById(response.getStudentCategoryId()).getData();
				response.setStudentCategory(studentCategory.getStudentCategory());
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	@Override
	public Long countRoleIdForDeletion(String roleId) {
		try {
			return urMappingRepository.countRoleIdForDeletion(roleId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("faild.to.check.role", null));
		}

	}

	@Override
	public List<String> getUserIdsByRoleId(String roleId) {
		try {
			return urMappingRepository.findAllUserIdsByRoleId(roleId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("faild.to.check.role", null));
		}
	}

	/**
	 * Share the user details via email, WhatsApp or SMS. Currently only Email is
	 * integrating WhatsApp and SMS are paid.
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public ShareDetailsResponseDto shareUserDetails(ShareDetailsRequestDto request) {
		try {
			if (!usersRepository.existsByIdAndDeleted(request.getUserId(), false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("User data not found", null));

			if (!Enums.getIfPresent(ChangeAndSharePersona.class, request.getPersona()).isPresent()
					|| !Enums.getIfPresent(LMSEnvironment.class, request.getLmsEnv()).isPresent())
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("invalid.data", null));

			ShareDetailsResponseDto response = null;

			if (request.getPersona().equals(ChangeAndSharePersona.ADMINISTRATION.getCode()))
				response = adminRepository.findByUsernameForShareDetails(request.getUsername());

			if (request.getPersona().equals(ChangeAndSharePersona.ADMIN_USERS.getCode()))
				response = usersRepository.findByUsernameForShareDetails(request.getUsername());

			if (request.getPersona().equals(ChangeAndSharePersona.ACADEMIC_STAFF.getCode()))
				response = teacherRepository.findByUsernameForShareDetails(request.getUsername());

			if (request.getPersona().equals(ChangeAndSharePersona.STUDENT.getCode()))
				response = studentsRepository.findByUsernameForShareDetails(request.getUsername());

			String resetPasswordUrl = null;
			LMSResponse<String> masterResponse = masterFeignClient.changePasswordWithBaseUrl(request.getLmsEnv());
			if (!ObjectUtils.isEmpty(masterResponse))
				resetPasswordUrl = masterResponse.getData();

			if (!StringUtils.isBlank(resetPasswordUrl)) {
				response.setBaseFEUrl(resetPasswordUrl.substring(0, resetPasswordUrl.indexOf("#")));
				String userId = EncryptionAndDecryption.encrypt(request.getUserId());
				response.setPasswordResetUrl(resetPasswordUrl + "?userId=" + userId);
			}

			// Sending the SMS.
			/**
			 * If the ChangeAndSharePersona either STUDENT or an ACADEMIC_STAFF, then find
			 * out their branch is allowed to send SMS. Temporally commenting this
			 * functionality due to absence of UI.
			 */
			SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(response.getFirstName(),
					response.getUserName(), response.getMobile(), response.getEmail(),
					CommunicationAction.SHARE_ID.getCode());
			LMSResponse<Boolean> notificationResponse = null;

//			if ((ChangeAndSharePersona.STUDENT.getCode().equals(request.getPersona())
//					|| ChangeAndSharePersona.ACADEMIC_STAFF.getCode().equals(request.getPersona()))) {
//				String branchId = ChangeAndSharePersona.STUDENT.getCode().equals(request.getPersona())
//						? studentsRepository.getBranchByUsername(response.getUserName())
//						: teacherRepository.getBranchByUsername(response.getUserName());
//
//				boolean smsAllowed = branchCommunicationRepository.checkSMSAllowedForBranchByUsername(branchId,
//						CommunicationAction.SHARE_ID.getCode());
//				notificationResponse = smsAllowed ? notificationFeign.sendSMSToUser(smsGatewayRequestDto) : null;
//			} else // other Users
			notificationResponse = notificationFeign.sendSMSToUser(smsGatewayRequestDto);

			if (notificationResponse != null && notificationResponse.getData())
				log.info("Shared details via SMS.");

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.details.share.failed", null));
		}
	}

	/**
	 * Update password by admin to entire application.
	 * <ol>
	 * <li>Check with persona and bring the username, email, firstName</li>
	 * <li>Update the password by username to users table</li>
	 * <li>Set the baseUrl, password reset url and password to response body</li>
	 * </ol>
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public CreateUserEmailRequestDto changePasswordBySuperAdmin(ChangePasswordRequestDto request) {
		try {
			Long currentTime = new Date().getTime();

			if (!usersRepository.existsByIdAndDeleted(request.getUserId(), false))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("User data not found", null));

			if (!Enums.getIfPresent(ChangeAndSharePersona.class, request.getPersona()).isPresent()
					|| !Enums.getIfPresent(LMSEnvironment.class, request.getLmsEnv()).isPresent())
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("invalid.data", null));

			CreateUserEmailRequestDto response = null;

			if (request.getPersona().equals(ChangeAndSharePersona.ADMINISTRATION.getCode())) {
				log.info("Checking admin exist by this username");
				if (adminRepository.existsByUserNameAndDeleted(request.getUsername(), false))
					response = adminRepository.findByUsernameForUpdatePassByAdmin(request.getUsername());
				else
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("admin.not.found", null));
			}

			if (request.getPersona().equals(ChangeAndSharePersona.ADMIN_USERS.getCode())) {
				log.info("Checking admin_user exist by this username");
				if (usersRepository.existsByUserNameAndDeleted(request.getUsername(), false))
					response = usersRepository.findByUsernameForUpdatePassByAdmin(request.getUsername());
				else
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
			}

			if (request.getPersona().equals(ChangeAndSharePersona.ACADEMIC_STAFF.getCode())) {
				log.info("Checking academic_staffs exist by this username");
				if (teacherRepository.existsByUserNameAndDeleted(request.getUsername(), false))
					response = teacherRepository.findByUsernameForUpdatePassByAdmin(request.getUsername());
				else
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("teacher.not.found", null));
			}

			if (request.getPersona().equals(ChangeAndSharePersona.STUDENT.getCode())) {
				log.info("Checking student exist by this username");
				if (studentsRepository.existsByUserNameAndDeleted(request.getUsername(), false))
					response = studentsRepository.findByUsernameForUpdatePassByAdmin(request.getUsername());
				else
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null));
			}

			if (response != null && !StringUtils.isEmpty(response.getUserName())) {
				String currentUser = jwtUtil.currentLoginUser();
				String encryptedPass = CommonUtilities.passwordEncryptor(request.getPassword());
				int changed = usersRepository.setPasswordById(request.getUserId(), currentTime, currentUser,
						encryptedPass);
				log.info(changed > 0 ? "Password updated by admin" : "failed to update password by admin.");

				if (changed > 0) {
					log.info("Password updated, setting rest of the data to response.");

					response.setTypeOfEmailSend("UPDATE_PASS_BY_ADMIN"); // need for the AOPConfig.java
					response.setUserId(request.getUserId());
					response.setPassword(request.getPassword());

					String resetPasswordUrl = null;
					LMSResponse<String> masterResponse = masterFeignClient
							.changePasswordWithBaseUrl(request.getLmsEnv());
					if (masterResponse != null)
						resetPasswordUrl = masterResponse.getData();

					if (!StringUtils.isBlank(resetPasswordUrl)) {
						response.setBaseFEUrl(resetPasswordUrl.substring(0, resetPasswordUrl.indexOf("#")));
						String userId = EncryptionAndDecryption.encrypt(request.getUserId());
						response.setForgotPasswordEmailLink(resetPasswordUrl + "?userId=" + userId);
					}

					// sending the sms.
					/**
					 * If the ChangeAndSharePersona either STUDENT or an ACADEMIC_STAFF, then find
					 * out their branch is allowed to send SMS. Temporally commenting this
					 * functionality due to absence of UI.
					 */
					Users users = usersRepository.findByUserName(response.getUserName());
					String lastModifiedAt = DateUtilities.fromLongMilliToString(users.getModifiedAt());
					SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(users.getFirstName(),
							users.getUserName(), users.getPhoneNumber(), users.getEmail(), lastModifiedAt,
							CommunicationAction.UPDATE_PASSWORD.getCode());
					LMSResponse<Boolean> notificationResponse = null;

//					if ((ChangeAndSharePersona.STUDENT.getCode().equals(request.getPersona())
//							|| ChangeAndSharePersona.ACADEMIC_STAFF.getCode().equals(request.getPersona()))) {
//						
//						String branchId = ChangeAndSharePersona.STUDENT.getCode().equals(request.getPersona())
//								? studentsRepository.getBranchByUsername(response.getUserName())
//								: teacherRepository.getBranchByUsername(response.getUserName());
//
//						boolean smsAllowed = branchCommunicationRepository.checkSMSAllowedForBranchByUsername(branchId,
//								CommunicationAction.UPDATE_PASSWORD.getCode());
//						notificationResponse = smsAllowed ? notificationFeign.sendSMSToUser(smsGatewayRequestDto)
//								: null;
//					} else // other Users
					notificationResponse = notificationFeign.sendSMSToUser(smsGatewayRequestDto);

					if (notificationResponse != null && notificationResponse.getData())
						log.info("Password changed, inform user via SMS.");

				} else
					throw new USException(ErrorCodes.PARTIAL_CONTENT,
							Translator.toLocale("password.update.failed", null));
			}

			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("password.update.failed", null));
		}
	}

	/**
	 * Generate otp to change Mobile number
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public String generateOtp(UpdateMobileNumberRequestDto request) {
		if (!usersRepository.existsByUserNameAndPhoneNumberAndDeleted(request.getUserName(), request.getMobileNumber(),
				false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		try {
			// Generate the random otp.
			Long currentTime = new Date().getTime();
			String otp = CommonUtilities.generateRandumNumber(4);
			Users users = usersRepository.findByUserName(request.getUserName());
			users.setOtp(otp);
			users.setModifiedAt(currentTime);
			users.setLastModifiedBy(jwtUtil.currentLoginUser());
			usersRepository.save(users);
			log.info("Otp sent successfully.");
			return otp;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("failed to send OTP", null));
		}
	}

	/**
	 * Verify otp and change the Mobile number.
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public UsersResponseDto verifyOtpToChangeMobileNumber(UpdateMobileNumberRequestDto request) {
		if (!Enums.getIfPresent(ChangeAndSharePersona.class, request.getUserRole()).isPresent())
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("invalid.data", null));
		if (!usersRepository.existsByUserNameAndDeleted(request.getUserName(), false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		try {
			Long currentTime = new Date().getTime();
			Users users = usersRepository.findByUserName(request.getUserName());
			if (!users.getOtp().equals(request.getOtp()))
				throw new USException(ErrorCodes.UNAUTHORIZED, Translator.toLocale("user.otp.verified.failed", null));
			users.setPhoneNumber(request.getMobileNumber());
			users.setOtp(null); // Clear the OTP field after successful verification
			users.setModifiedAt(currentTime);
			users.setLastModifiedBy(jwtUtil.currentLoginUser());
			users = usersRepository.save(users);

			if (request.getUserRole().equals(ChangeAndSharePersona.STUDENT.getCode())) {
				// Update the mobile number in the Student table
				Students student = studentsRepository.findByUserNameIgnoreCase(request.getUserName());
				student.setMobile(request.getMobileNumber());
				student.setModifiedAt(currentTime);
				student.setLastModifiedBy(jwtUtil.currentLoginUser());
				studentsRepository.save(student);
			} else if (request.getUserRole().equals(ChangeAndSharePersona.ACADEMIC_STAFF.getCode())) {
				// Update the mobile number in the Teacher table
				Teachers teacher = teacherRepository.findByUserNameIgnoreCase(request.getUserName());
				teacher.setMobile(request.getMobileNumber());
				teacher.setModifiedAt(currentTime);
				teacher.setLastModifiedBy(jwtUtil.currentLoginUser());
				teacherRepository.save(teacher);
			}
			log.info("Mobile number updated successfully.");
			return modelMapper.map(users, UsersResponseDto.class);
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.mobileNumber.update.failed", null));
		}
	}

	/**
	 * Extract the user_name which is created based on either phone_number or email.
	 * The {@code extractor} can hold either phone number or email.
	 */
	@Override
	public List<String> getUserNameExtractingList(String extractor) {
		try {
			return usersRepository.getAllExtractingUserName(extractor);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.details.fetch.failed", null));
		}
	}

	/**
	 * This Is feign call for master service to get users details.
	 * 
	 * @param roleIds
	 * @return
	 */
	@Override
	public List<UsersRoleResponseDto> getAllAdminUsersRole(List<String> roleIds) {
		try {
			List<UsersRoleResponseDto> response = new ArrayList<>();
			for (String role : roleIds) {
				List<UserDetailsResponseDto> userResponseList = usersRepository.getAllUsersRole(role);
				response.add(new UsersRoleResponseDto(role, userResponseList.size(), userResponseList));
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.details.fetch.failed", null));
		}
	}

	/**
	 * This API is used for count Web User, Mob User, both users with most use.
	 * 
	 * @param userId
	 * @param userName
	 * @return
	 */
	@Override
	public List<UsersUseWebMobCountResponseDto> getUserWebMobCount() {
		try {
			List<UsersUseWebMobCountResponseDto> response = new ArrayList<>();
			String currentUser = jwtUtil.currentLoginUser();
			Users users = usersRepository.findByUserName(currentUser);

			List<SchoolsProjection> projection = null;
			// find out the current user has any entry in user-institution-mapping
			List<SchoolsBranchsMinResponseDto> uimResponse = null;
			if (uiRepository.checkingUserIdExists(users.getId()))
				uimResponse = uiRepository.findSchoolBranchsForAdmin(users.getId());
			else if (!users.isAdminUser())
				throw new USException(ErrorCodes.UNAUTHORIZED,
						Translator.toLocale("api.is.accessible.for.admin.user", null));

			if (!CollectionUtils.isEmpty(uimResponse)) {
				// finding details for school-admin or management.
				List<String> schoolIds = uimResponse.stream().map(SchoolsBranchsMinResponseDto::getSchoolId).distinct()
						.collect(Collectors.toList());
				projection = schoolRepository.findSchoolsForAdministration(schoolIds);
			} else
				// finding details for Super-Admin
				projection = schoolRepository.findSchoolsForAdminUsers();

			// traversing through the projection
			if (projection != null) {
				List<UsersWebMobCountProjection> userCountProjection = null;

				// find out the branches
				if (!CollectionUtils.isEmpty(uimResponse)) {
					List<String> branchIds = uimResponse.stream().map(SchoolsBranchsMinResponseDto::getBranchId)
							.distinct().collect(Collectors.toList());
					userCountProjection = usersCheckInHistoryRepo.findAllCountForAdministration(branchIds);
				} else
					userCountProjection = usersCheckInHistoryRepo.findAllCountForSuperAdmin();// for super-admin

				// Traversing through the schools to set the response
				for (SchoolsProjection schoolProjection : projection) {
					UsersUseWebMobCountResponseDto responseDto = new UsersUseWebMobCountResponseDto(schoolProjection);

					List<BranchUsersWebMobCountResponseDto> countList = null;
					if (!CollectionUtils.isEmpty(uimResponse)) {
						List<String> branchIds = uimResponse.stream()
								.filter(item -> item.getSchoolId().equals(responseDto.getSchoolId()))
								.map(SchoolsBranchsMinResponseDto::getBranchId).distinct().collect(Collectors.toList());
						countList = userCountProjection.stream()
								.filter(item -> branchIds.contains(item.getBranchId())
										&& item.getSchoolId().equals(responseDto.getSchoolId()))
								.map(BranchUsersWebMobCountResponseDto::new).distinct().collect(Collectors.toList());

					} else {
						// super-admin
						countList = userCountProjection.stream()
								.filter(item -> item.getSchoolId().equals(responseDto.getSchoolId()))
								.map(BranchUsersWebMobCountResponseDto::new).distinct().collect(Collectors.toList());
					}

					responseDto.setUsersCount(countList);
					response.add(responseDto);
				}

			}
			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.check.in.history.total.count.failed", null));
		}
	}

	/**
	 * Feign call to content service to get the first/last name by username
	 * 
	 * @param userNames
	 * @return
	 */
	@Override
	public List<UserMinResponseDto> getAllUsersByUserNames(List<String> userNames) {
		try {
			return usersRepository.findAllUsersByUserNames(userNames);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.name.failed", null));
		}
	}

	@Override
	public UsersResponseDto getUserDetailsForFeign(String username) {
		UsersResponseDto response = null;
		try {
			if (StringUtils.isNotBlank(username)) {
				response = usersRepository.findUsersByUserName(username);
				List<String> roleIds = urMappingRepository.findAllRolesByUserId(response.getId());
				List<RolesFeignDto> rolesDto = masterFeignClient.getRolesByIds(roleIds).getData();
				response.setRoles(rolesDto);
			}
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.name.failed", null));
		}
		return response;
	}

	@Override
	public String userSendOtp(@Valid ForgetPasswordNumberOtpRequestDto request) {
		if (!usersRepository.existsByUserNameIgnoreCaseAndDeletedAndActive(request.getUserName(), false, true))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		String msg = "";
		try {
			// Generate the random otp.
			Long currentTime = new Date().getTime();
			String otp = CommonUtilities.generateRandumNumber(4);
			Users users = usersRepository.findByUserName(request.getUserName());
			if (request.getPhoneNumber() != null && !request.getPhoneNumber().isEmpty()
					&& (request.getPhoneNumber().equals(users.getPhoneNumber()))) {
				users.setOtpType("Mobile");
			} else if (request.getEmail() != null && !request.getEmail().isEmpty()
					&& (request.getEmail().equals(users.getEmail()))) {
				users.setOtpType("Email");
			} else {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("user.otp.send.failed.emailOrPhone", null));
			}
			users.setForgetPasswordOtpCode(otp);
			users.setOtpSendedAt(currentTime);
			users.setLastModifiedBy(jwtUtil.currentLoginUser());
			users = usersRepository.save(users);
			log.info("save successfully.");
			// SMS & Mail send logic
			if (users.getOtpType().equalsIgnoreCase("Mobile")) {
				SmsSendResponseDTO smsSendResponseDTO = new SmsSendResponseDTO();
				smsSendResponseDTO.setUserName(users.getUserName());
				smsSendResponseDTO.setMobileNumber(users.getPhoneNumber());
				smsSendResponseDTO.setOtpCode(users.getForgetPasswordOtpCode());
				smsSendResponseDTO.setUserMail(users.getEmail());
				// LMSResponse<String> notificationResponse =
				// notificationFeignClient.sendSmsUser(smsSendResponseDTO);
				String notificationResponse = SMSUtils.sendSms(smsSendResponseDTO);
				log.info("sms response:" + notificationResponse);
				msg = "Mobile otp sent successfully.";
			} else if (users.getOtpType().equalsIgnoreCase("Email")) {
				SmsSendResponseDTO smsSendResponseDTO = new SmsSendResponseDTO();
				smsSendResponseDTO.setUserName(users.getUserName());
				smsSendResponseDTO.setMobileNumber(users.getPhoneNumber());
				smsSendResponseDTO.setOtpCode(users.getForgetPasswordOtpCode());
				smsSendResponseDTO.setUserMail(users.getEmail());
				notificationFeignClient.sendEmailUser(smsSendResponseDTO);
				msg = "Email otp sent successfully.";
			}
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.otp.send.failed", null));
		}
		return msg;
	}

	@Override
	public String userVerifyOtp(@Valid ForgetPasswordOtpRequestDto request) {
		if (!usersRepository.existsByUserNameIgnoreCaseAndDeletedAndActive(request.getUserName(), false, true))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
		try {
			String response = null;
			Long currentTime = new Date().getTime();
			Users users = usersRepository.findByUserName(request.getUserName());

			// Calculate the time difference in milliseconds
			long timeDifference = currentTime - users.getOtpSendedAt();

			// Check if the OTP has expired (more than 10 minutes)
			if (timeDifference > 600000) { // 600000 milliseconds = 10 minutes
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("otp.is.expired", null));
			} else if (!users.getForgetPasswordOtpCode().equals(request.getOtp())) {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("otp.not.matching", null));
			} else {
				users.setLastModifiedBy(jwtUtil.currentLoginUser());
				users = usersRepository.save(users);
				String userId = EncryptionAndDecryption.encrypt(users.getId());
				response = userId + "#OTP verified successfully";
			}
			return response;
		} catch (USException use) {
			log.error(ExceptionUtils.getStackTrace(use));
			throw new USException(use.getErrorCode(), use.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.otp.verified.failed", null));
		}
	}

	@Override
	public List<String> getUserNameExtractingListWithUserType(String extractor, String userType) {
		try {

			String roleId = masterFeignClient.getOnlyIdByRole(userType).getData();
			if (roleId == null)
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.Type.not.found", null));
			return usersRepository.getAllExtractingUserNameWithRoleId(extractor, roleId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("user.details.fetch.failed", null));
		}
	}

}
