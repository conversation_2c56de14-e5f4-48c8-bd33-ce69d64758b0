package com.lms.userservice.service.impl;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.multipart.MultipartFile;

import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.FileExtensions;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.response.dto.BulkUploadResponseDto;
import com.lms.userservice.service.FileUploadService;
import com.lms.userservice.service.StudentsService;
import com.lms.userservice.service.TeacherService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("FileUploadService")
public class FileUploadServiceImpl implements FileUploadService {

	@Autowired
	private StudentsService studentsService;

	@Autowired
	private TeacherService teacherService;

	/**
	 * Upload and save the file temporarily, then from the file according to the
	 * {@link com.lms.userservice.enums.FileTypes FileTypes} and
	 * {@link com.lms.userservice.enums.FileExtensions FileExtensions}.
	 * <p>
	 * If the file read completely to the database then file and folder will be
	 * deleted.
	 * 
	 * @param file
	 * @param fileTypes
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public BulkUploadResponseDto uploadFile(MultipartFile file, FileTypes fileTypes) {
		String uploadPath = checkFolderExist("upload-temp");
		try {

			Path root = Paths.get(uploadPath);
			Files.copy(file.getInputStream(), root.resolve(file.getOriginalFilename()));

			String fileName = file.getOriginalFilename();
			String extension = FilenameUtils.getExtension(file.getOriginalFilename());
			String filePath = uploadPath + "/" + fileName;

			BulkUploadResponseDto response = null;
			if (FileTypes.STUDENTS.equals(fileTypes)) {
				response = studentsService.saveStudentsFromFile(filePath,
						FileExtensions.valueOf(extension.toUpperCase()), fileTypes);
			} else if (FileTypes.TEACHERS.equals(fileTypes))
				response = teacherService.readAndSaveTeachersFromFile(filePath,
						FileExtensions.valueOf(extension.toUpperCase()), fileTypes);

			return response;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, "Could not store the file.");
		} finally {
			deleteAll(uploadPath);
		}
	}

	/**
	 * After all the process done this method will be deleted the temporary file.
	 * Just to free up the space
	 * 
	 */
	public void deleteAll(String uploadPath) {
		try {
			// delete the temporarily created folder and content.
			FileSystemUtils.deleteRecursively(Paths.get(uploadPath).toFile());

			// Only clear the uploaded files
			// FileUtils.cleanDirectory(new File(uploadPath));
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, "Deleting the remporary file failed");
		}
	}

	/**
	 * Check the directory is exist, if not will create new one.
	 * 
	 * @param folderPath
	 * @return
	 */
	public static String checkFolderExist(String folderPath) {
		File directory = new File(folderPath);
		if (!directory.exists()) {
			directory.mkdir();
		}
		return directory.getAbsolutePath();
	}
}
