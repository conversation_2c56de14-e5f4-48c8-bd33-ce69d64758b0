package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.lms.userservice.request.dto.*;
import com.lms.userservice.util.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.google.common.base.Enums;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.BranchPlanMappings;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.StudentSubjectMapping;
import com.lms.userservice.entity.Students;
import com.lms.userservice.entity.StudentsProfileHistory;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.CommunicationAction;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.FileExtensions;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.enums.Gender;
import com.lms.userservice.enums.LMSEnvironment;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.content.ContentFeignClient;
import com.lms.userservice.feign.master.AcademicYearResponseDto;
import com.lms.userservice.feign.master.CsvFileResponseDto;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.LanguagesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.PlansResponseDto;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.feign.master.StudentCategoriesResponseDto;
import com.lms.userservice.feign.master.SubjectGroupsSubjectMappingsMinResponseDto;
import com.lms.userservice.feign.master.SubjectGroupsSubjectMappingsResponseDto;
import com.lms.userservice.feign.master.SubjectsResponseDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.feign.student.StudentFeignClient;
import com.lms.userservice.feign.student.StudentQuizCountSubmitDateResoponseDto;
import com.lms.userservice.mapper.StudentMapper;
import com.lms.userservice.model.ImportedFiles;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.model.StudentsFile;
import com.lms.userservice.projection.StudentsProjection;
import com.lms.userservice.repository.AssignTeacherRepository;
import com.lms.userservice.repository.BranchCommunicationRepository;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.GradeSectionMappingRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentSubjectMappingsRepository;
import com.lms.userservice.repository.StudentsProfileHistoryRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersRoleMappingRepository;
import com.lms.userservice.response.dto.AbsenteesResponseDto;
import com.lms.userservice.response.dto.BatchReceptionistRequestDto;
import com.lms.userservice.response.dto.BulkUploadResponseDto;
import com.lms.userservice.response.dto.ChangeProfileResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.FormalStudentWiseGetName;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.SmsGatewayRequestDto;
import com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto;
import com.lms.userservice.response.dto.StudentDetailsMinResponseDto;
import com.lms.userservice.response.dto.StudentDetailsResponseDto;
import com.lms.userservice.response.dto.StudentMinDetailsResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentSubjectGroupSubjectMappingsResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingDto;
import com.lms.userservice.response.dto.StudentSubjectMappingsMinResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingsResponseDto;
import com.lms.userservice.response.dto.StudentsMinResponseDto;
import com.lms.userservice.response.dto.StudentsResponseDto;
import com.lms.userservice.response.dto.TokensResponseDto;
import com.lms.userservice.service.StudentsService;
import com.lms.userservice.service.TokensService;
import com.lms.userservice.service.UserService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> C Achari
 *
 */
@Slf4j
@Service("StudentsService")
public class StudentsServiceImpl implements StudentsService {

	@Autowired
	private JwtUtil jwtUtil;

	@Autowired
	private ModelMapper modelMapper;

	@Autowired
	private StudentsRepository studentsRepository;

	@Autowired
	private UsersRepository userRepository;

	@Autowired
	private TokensService tokensService;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private UserService userService;

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private StudentSubjectMappingsRepository studentSubjectMappingsRepository;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private ContentFeignClient contentFeignClient;

	@Autowired
	private StudentFeignClient studentFeignClient;

	@Autowired
	private BranchPlanMappingsRepository branchPlanMappingsRepository;

	@Autowired
	private GradeSectionMappingRepository gradeSectionMappingRepository;

	@Autowired
	private StudentsProfileHistoryRepository profileHistoryRepo;

	@Autowired
	private StudentMapper studentMapper;

	@Autowired
	private UsersRoleMappingRepository usersRoleMappingRepository;

	@Autowired
	private BranchCommunicationRepository branchCommunicationRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	@Autowired
	private AssignTeacherRepository assignTeacherRepo;

	/**
	 * Register the student details by school admin
	 *
	 * @param request
	 * @return
	 */
	@Override
	@Transactional
	@SuppressWarnings("all")
	public StudentsResponseDto createStudent(StudentsRequestDto request) {
		log.info("Student Registration started...");
		// studentCreateChecking(request);
		try {
			log.info("All checking completed, student Registration going on.");
			String currentUser = jwtUtil.currentLoginUser();
			StudentsResponseDto response = null;
			String roleId = mastersFeignClient.getOnlyIdByRole("student").getData();

			Schools school = schoolRepository.getById(request.getSchool());
			Branches branch = branchRepository.getById(request.getBranch());

			Students students = modelMapper.map(request, Students.class);
			String userName = generateUserName(request.getFirstName(), request.getLastName(), branch.getBranchCode());
			students.setUserName(userName);
			students.setSchools(school);
			students.setBranches(branch);
			students.setCreatedBy(currentUser);

			students = studentsRepository.save(students);

			String password = CommonUtilities.generatePassayPassword();
			if (!StringUtils.isEmpty(students.getId())) {
				log.info("Student created and mapping to the users");
				Users users = modelMapper.map(students, Users.class);
				users.setPhoneNumber(students.getMobile());
				users.setCreatedBy(currentUser);
				users.setPassword(CommonUtilities.passwordEncryptor(password));
				users = userRepository.save(users);
				if (!StringUtils.isEmpty(users.getId())) {
					log.info("User is mapping to the users-role");
					List<UserRolesRequestDto> userRoles = new ArrayList<>();
					userRoles.add(new UserRolesRequestDto(roleId, true));
					userService.addRoles(users, userRoles);
				}
				/**
				 * sending the sms. check whether the branch is allowed to send the sms
				 * <p>
				 * Currently no UI has been implemented for this checking. So the condition is
				 * commenting.
				 */
//				if(branchCommunicationRepository.checkSmsAllowed(branch.getId(), CommunicationAction.USER_CREATION.getCode())) {
				if (request.getEmail() != null && !request.getEmail().isEmpty() && !request.getEmail().isBlank()) {
					SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(students.getFirstName(),
							userName, students.getMobile(), students.getEmail(),
							CommunicationAction.USER_CREATION.getCode());
					LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
					if (notificationLms != null && notificationLms.getData())
						log.info("SMS send to student.");
				}
//				}
			}
			response = new StudentsResponseDto(studentsRepository.findStudentById(students.getId()));
			response.setPassword(password);
			if (!StringUtils.isBlank(students.getGradeId())) {
				GradesResponseDto grades = mastersFeignClient.getGradesById(students.getGradeId()).getData();
				response.setGrade(grades.getGrade());
			}
			if (!StringUtils.isBlank(students.getSectionId())) {
				SectionsResponseDto sections = mastersFeignClient.getSectionsById(students.getSectionId()).getData();
				response.setSection(sections.getSection());
			}
			if (!StringUtils.isBlank(students.getFirstLanguageId())
					&& !StringUtils.isBlank(students.getSecondLanguageId())) {
				LanguagesResponseDto languages = mastersFeignClient.getLanguagesById(students.getFirstLanguageId())
						.getData();
				response.setFirstLanguage(languages.getLanguage());
				languages = mastersFeignClient.getLanguagesById(response.getSecondLanguageId()).getData();
				response.setSecondLanguage(languages.getLanguage());
			}
			if (!StringUtils.isBlank(students.getStudentCategoryId())) {
				StudentCategoriesResponseDto studentCategory = mastersFeignClient
						.getStudentCategoryById(students.getStudentCategoryId()).getData();
				response.setStudentCategory(studentCategory.getStudentCategory());
			}
			response.setTypeOfEmailSend("CREATE");
			response.setLmsEnv(request.getLmsEnv());
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("student.create.failed", null));
		}
	}

	/**
	 * Get Student details by id
	 *
	 * @param id
	 * @return
	 * @since 0.0.1
	 */
	@Override
	@SuppressWarnings("all")
	public StudentsResponseDto getStudentById(String id) {
		log.info("Get student details by Id started...");
		if (StringUtils.isEmpty(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("student.id.not.found", null));

		if (!studentsRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null));

		try {
			StudentsProjection projection = studentsRepository.findStudentById(id);
			StudentsResponseDto response = new StudentsResponseDto(projection);
			if (!StringUtils.isBlank(response.getGradeId())) {
				GradesResponseDto grades = mastersFeignClient.getGradesById(response.getGradeId()).getData();
				response.setGrade(grades.getGrade());
			}
			if (!StringUtils.isBlank(response.getSectionId())) {
				SectionsResponseDto sections = mastersFeignClient.getSectionsById(response.getSectionId()).getData();
				response.setSection(sections.getSection());
			}

			if (!StringUtils.isBlank(response.getStudentCategoryId())) {
				StudentCategoriesResponseDto studentCategory = mastersFeignClient
						.getStudentCategoryById(response.getStudentCategoryId()).getData();
				response.setStudentCategory(studentCategory.getStudentCategory());
			}

			// *** setting the first/second language started ***//
			Set<String> languageSet = new HashSet<>();
			if (!StringUtils.isBlank(projection.getFirstLanguageId()))
				languageSet.add(projection.getFirstLanguageId());
			if (!StringUtils.isBlank(projection.getSecondLanguageId()))
				languageSet.add(projection.getSecondLanguageId());

			List<LanguagesResponseDto> languageList = new ArrayList<>();
			if (!CollectionUtils.isEmpty(languageSet)) {
				List<String> ids = languageSet.stream().collect(Collectors.toList());
				LMSResponse<List<LanguagesResponseDto>> fromMaster = mastersFeignClient.getLanguagesByIds(ids);
				languageList = fromMaster != null && !CollectionUtils.isEmpty(fromMaster.getData())
						? fromMaster.getData()
						: new ArrayList<>();
			}

			if (!CollectionUtils.isEmpty(languageList)) {
				Optional<LanguagesResponseDto> languageDto;
				if (!StringUtils.isBlank(projection.getFirstLanguageId())) {
					languageDto = languageList.stream()
							.filter(item -> item.getId().equals(projection.getFirstLanguageId())).findFirst();
					if (languageDto.isPresent()) {
						response.setFirstLanguageId(languageDto.get().getId());
						response.setFirstLanguage(languageDto.get().getLanguage());
					}
				}

				if (!StringUtils.isBlank(projection.getSecondLanguageId())) {
					languageDto = languageList.stream()
							.filter(item -> item.getId().equals(projection.getSecondLanguageId())).findFirst();
					if (languageDto.isPresent()) {
						response.setSecondLanguageId(languageDto.get().getId());
						response.setSecondLanguage(languageDto.get().getLanguage());
					}
				}
			}
			// *** setting the first/second language ended ***//

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * Get Student details by UserName
	 *
	 * @param userName
	 * @return
	 * @since 0.0.1
	 */
	@Override
	@SuppressWarnings("all")
	public StudentsResponseDto getStudentByUserName(String userName) {
		log.info("Get student details by Id started...");
		if (StringUtils.isEmpty(userName)) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("student.id.not.found", null));
		}

		if (!studentsRepository.existsByUserName(userName)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null));
		}

		try {
			StudentsProjection projection = studentsRepository.findStudentByUserName(userName);
			StudentsResponseDto response = new StudentsResponseDto(projection);
			if (!StringUtils.isBlank(response.getGradeId())) {
				GradesResponseDto grades = mastersFeignClient.getGradesById(response.getGradeId()).getData();
				response.setGrade(grades.getGrade());
			}
			if (!StringUtils.isBlank(response.getSectionId())) {
				SectionsResponseDto sections = mastersFeignClient.getSectionsById(response.getSectionId()).getData();
				response.setSection(sections.getSection());
			}
			if (!StringUtils.isBlank(response.getFirstLanguageId())
					&& !StringUtils.isBlank(response.getSecondLanguageId())) {
				LanguagesResponseDto languages = mastersFeignClient.getLanguagesById(response.getFirstLanguageId())
						.getData();
				response.setFirstLanguage(languages.getLanguage());
				languages = mastersFeignClient.getLanguagesById(response.getSecondLanguageId()).getData();
				response.setSecondLanguage(languages.getLanguage());
			}
			if (!StringUtils.isBlank(response.getStudentCategoryId())) {
				StudentCategoriesResponseDto studentCategory = mastersFeignClient
						.getStudentCategoryById(response.getStudentCategoryId()).getData();
				response.setStudentCategory(studentCategory.getStudentCategory());
			}
			String planId = null;
			if (!StringUtils.isBlank(response.getBranchId())) {
				planId = branchPlanMappingsRepository.findPlanIdByBranchId(response.getBranchId());
				response.setPlanId(planId);
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * @param id
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	@Override
	public StudentsResponseDto updateStudent(String id, StudentsRequestDto request) {
		log.info("Student Updation started...");

		if (StringUtils.isEmpty(id))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("student.id.not.found", null));

		if (!studentsRepository.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			StudentsResponseDto response = null;
			Students existingStudent = studentsRepository.getById(id);
			modelMapper.map(request, existingStudent);
			existingStudent.setModifiedAt(currentTime);
			existingStudent.setLastModifiedBy(currentUser);
			existingStudent = studentsRepository.save(existingStudent);
			log.info("Student details updated successfully!");

			// update in users repository
			if (!StringUtils.isEmpty(existingStudent.getId())) {

				if (!usersRepository.existsByUserName(existingStudent.getUserName())) {
					throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));
				}
				Users users = userRepository.findByUserName(existingStudent.getUserName());
				users.setEmail(request.getEmail());
				users.setPhoneNumber(request.getMobile());
				users.setModifiedAt(currentTime);
				users.setLastModifiedBy(currentUser);
				userRepository.save(users);
				log.info("Student details updated successfully in user table!");
			}

			response = new StudentsResponseDto(studentsRepository.findStudentById(existingStudent.getId()));
			if (!StringUtils.isBlank(existingStudent.getGradeId())) {
				GradesResponseDto grades = mastersFeignClient.getGradesById(existingStudent.getGradeId()).getData();
				response.setGrade(grades.getGrade());
			}
			if (!StringUtils.isBlank(existingStudent.getSectionId())) {
				SectionsResponseDto sections = mastersFeignClient.getSectionsById(existingStudent.getSectionId())
						.getData();
				response.setSection(sections.getSection());
			}
			if (!StringUtils.isBlank(existingStudent.getFirstLanguageId())
					&& !StringUtils.isBlank(existingStudent.getSecondLanguageId())) {
				LanguagesResponseDto languages = mastersFeignClient
						.getLanguagesById(existingStudent.getFirstLanguageId()).getData();
				response.setFirstLanguage(languages.getLanguage());
				languages = mastersFeignClient.getLanguagesById(existingStudent.getSecondLanguageId()).getData();
				response.setSecondLanguage(languages.getLanguage());
			}
			if (!StringUtils.isBlank(existingStudent.getStudentCategoryId())) {
				StudentCategoriesResponseDto studentCategory = mastersFeignClient
						.getStudentCategoryById(existingStudent.getStudentCategoryId()).getData();
				response.setStudentCategory(studentCategory.getStudentCategory());
			}
			if (!StringUtils.isEmpty(existingStudent.getId())) {
				/**
				 * sending the sms. check whether the branch is allowed to send the sms
				 * <p>
				 * Temporally commenting the condition due to the absence of UI.
				 */
//				if (branchCommunicationRepository.checkSmsAllowed(existingStudent.getBranches().getId(),
//						CommunicationAction.UPDATE_PROFILE.getCode())) {
				SmsGatewayRequestDto smsGatewayRequestDto = new SmsGatewayRequestDto(existingStudent.getFirstName(),
						existingStudent.getUserName(), existingStudent.getMobile(), existingStudent.getEmail(),
						CommunicationAction.UPDATE_PROFILE.getCode());
				LMSResponse<Boolean> notificationLms = notificationFeignClient.sendSMSToUser(smsGatewayRequestDto);
				if (notificationLms != null && notificationLms.getData())
					log.info("SMS send to student for update.");
//				}
			}

			// find the created person name and role name
			Users updateUser = usersRepository.findByUserName(currentUser);
			if (updateUser != null) {
				List<String> createdUserRoles = usersRoleMappingRepository.findAllRolesByUserId(updateUser.getId());
				List<RolesFeignDto> currentUserRoles = mastersFeignClient.getRolesByIds(createdUserRoles).getData();
				response.setRoleNameOfAdmin(currentUserRoles.get(0).getRole());
				response.setAdminName(updateUser.getFirstName() + " " + updateUser.getLastName());
			}
			response.setLmsEnv(request.getLmsEnv());
			response.setTypeOfEmailSend("UPDATE");

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.update.failed", null));
		}
	}

	/**
	 * Read from the csv or excel file then write the data to the database
	 *
	 * @param filePath
	 * @param extension
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	@Transactional
	public BulkUploadResponseDto saveStudentsFromFile(String filePath, FileExtensions extension, FileTypes fileTypes) {
		log.info("Student's details data reading started");
		try {

			List<StudentsResponseDto> successData = new ArrayList<>();
			List<StudentsFile> failedData = new ArrayList<>();
			String message = null;

			String currentUser = jwtUtil.currentLoginUser();
			String roleId = mastersFeignClient.getOnlyIdByRole("student").getData();
			log.info("Reading from the file");
			ImportedFiles importedFiles = FileOperations.readFromFile(filePath, fileTypes, extension);

			if (!CollectionUtils.isEmpty(importedFiles.getStudentsFile())) {
				log.info("File read from CSV and set to the List");
				int originalSize = importedFiles != null && !CollectionUtils.isEmpty(importedFiles.getStudentsFile())
						? importedFiles.getStudentsFile().size()
						: 0;

				/**
				 * Validating the data and keeping in the failed response. Only accepting the
				 * correct data.
				 */
				List<StudentsFile> validationFailed = validationForCSV(importedFiles.getStudentsFile());
				List<StudentsFile> processingData = !CollectionUtils.isEmpty(validationFailed)
						? processingData = importedFiles.getStudentsFile().stream()
								.filter(item -> !validationFailed.contains(item)).distinct()
								.collect(Collectors.toList())
						: importedFiles.getStudentsFile();

				if (!CollectionUtils.isEmpty(validationFailed))// keeping the failed data.
					failedData = validationFailed;

				List<String> gradeNameList = importedFiles.getStudentsFile().stream()
						.filter(item -> !StringUtils.isEmpty(item.getGrade())).map(StudentsFile::getGrade).distinct()
						.collect(Collectors.toList());

				List<String> sectionNameList = importedFiles.getStudentsFile().stream()
						.filter(item -> !StringUtils.isEmpty(item.getSection())).map(StudentsFile::getSection)
						.distinct().collect(Collectors.toList());

				List<String> studentCatogaryNameList = importedFiles.getStudentsFile().stream()
						.filter(item -> !StringUtils.isEmpty(item.getStudentCategory()))
						.map(StudentsFile::getStudentCategory).distinct().collect(Collectors.toList());

				Set<String> fLanguageNameList = importedFiles.getStudentsFile().stream()
						.filter(item -> !StringUtils.isEmpty(item.getFirstLanguage()))
						.map(StudentsFile::getFirstLanguage).collect(Collectors.toSet());

				Set<String> sLanguageNameList = importedFiles.getStudentsFile().stream()
						.filter(item -> !StringUtils.isEmpty(item.getSecondLanguage()))
						.map(StudentsFile::getSecondLanguage).collect(Collectors.toSet());

				Set<String> langNameList = new HashSet<>();
				langNameList.addAll(fLanguageNameList);
				langNameList.addAll(sLanguageNameList);

				LMSResponse<CsvFileResponseDto> masterResponseDto = mastersFeignClient.getALLCsvTypeOfIdsByNames(
						langNameList, gradeNameList, sectionNameList, studentCatogaryNameList);
				CsvFileResponseDto csvResponseDto = masterResponseDto != null ? masterResponseDto.getData() : null;

				// Get all the Plan and it's Grades from master-service
				// LMSResponse<Map<String, List<String>>> getAllPlanAndItsGrades()
				LMSResponse<Map<String, List<String>>> planGradeResponse = mastersFeignClient.getAllPlanAndItsGrades();
				Map<String, List<String>> planGradeMap = planGradeResponse != null
						&& !CollectionUtils.isEmpty(planGradeResponse.getData()) ? planGradeResponse.getData() : null;

				// Data setting to Database
				for (StudentsFile record : processingData) {
					StringBuilder remarks = new StringBuilder();

					log.info("checking the StudentsFile has all mandatory fields.");
					if (!record.isNull()) {
						Schools schools = schoolRepository.getByCode(record.getSchoolCode());
						Branches branches = branchRepository.getByNameAndSchoolsAndDeleted(record.getBranchName(),
								schools, false);
						BranchPlanMappings planMapping = branchPlanMappingsRepository
								.findByBranchesAndDeletedAndActive(branches, false, true);
						boolean isGradeExists = false;// to check grade is belongs to plan of the branch

						Students students = modelMapper.map(record, Students.class);
						students.setDob(!StringUtils.isBlank(record.getDob())
								? DateUtilities.convertStringToLocalDate(record.getDob())
								: null);
						students.setAdmissionDate(!StringUtils.isBlank(record.getAdmissionDate())
								? DateUtilities.convertStringToLocalDate(record.getAdmissionDate())
								: null);

						String upperGender = record.getGender().toUpperCase();
						students.setGender(Gender.valueOf(upperGender));
						students.setSchools(schools);
						students.setBranches(branches);
						students.setCreatedBy(currentUser);

						// setting the master-details to students
						boolean isSectionExists = false; // check the given section exists under the grade
						if (csvResponseDto != null) {
							String gradeId = null;
							Optional<GradesResponseDto> optGrade = csvResponseDto.getGrades().stream()
									.filter(gradeData -> gradeData.getGrade().equals(record.getGrade())).findAny();
							if (optGrade.isPresent()) {
								gradeId = optGrade.get().getId();
								students.setGradeId(gradeId);
								// checking the grade belongs to the plan
								if (!CollectionUtils.isEmpty(planGradeMap) && planMapping != null)
									isGradeExists = planGradeMap.get(planMapping.getPlanId()).contains(gradeId);

							}

							Optional<SectionsResponseDto> optSection = csvResponseDto.getSections().stream()
									.filter(sectionData -> sectionData.getSection().equals(record.getSection()))
									.findAny();
							if (optSection.isPresent()) {
								isSectionExists = gradeSectionMappingRepository
										.existsBySchoolsAndBranchesAndGradeIdAndSectionIdAndDeletedAndActive(schools,
												branches, gradeId, optSection.get().getId(), false, true);
								students.setSectionId(optSection.get().getId());
							}

							if (csvResponseDto != null && csvResponseDto.getStudentCategories() != null) {
								Optional<StudentCategoriesResponseDto> optStudentCategory = csvResponseDto
										.getStudentCategories().stream().filter(categoryData -> categoryData
												.getStudentCategory().equals(record.getStudentCategory()))
										.findAny();
								if (optStudentCategory.isPresent())
									students.setStudentCategoryId(optStudentCategory.get().getId());
							}

							if (!StringUtils.isBlank(record.getFirstLanguage())) {
								Optional<LanguagesResponseDto> optFirstLanguage = csvResponseDto.getLanguages().stream()
										.filter(langData -> langData.getLanguage().equals(record.getFirstLanguage()))
										.findAny();
								if (optFirstLanguage.isPresent())
									students.setFirstLanguageId(optFirstLanguage.get().getId());
							}

							if (!StringUtils.isBlank(record.getSecondLanguage())) {
								Optional<LanguagesResponseDto> optSecondLanguage = csvResponseDto.getLanguages()
										.stream()
										.filter(langData -> langData.getLanguage().equals(record.getSecondLanguage()))
										.findAny();
								if (optSecondLanguage.isPresent())
									students.setSecondLanguageId(optSecondLanguage.get().getId());
							}
						}

						// create user-name
						// System.out.println("record.getFirstName():"+record.getFirstName());
						// System.out.println("record.getLastName():"+record.getLastName());
						if (schools != null && branches != null && isGradeExists && isSectionExists) {
							String userName = generateUserName(record.getFirstName(), record.getLastName(),
									branches.getBranchCode());
							students.setUserName(userName);
							students = studentsRepository.save(students); // saving the student

						} else {
							String schoolMessage = schools == null ? "Unable to find the school." : null;
							String branchMessage = branches == null ? "Unable to find the branch." : null;
							String gradeMessage = !isGradeExists ? "The grade not in the plan." : null;
							String sectionMessage = !isSectionExists ? "The section not mapped with the grade" : null;
							if (!StringUtils.isBlank(schoolMessage))
								remarks.append(
										remarks != null && remarks.length() > 0 ? schoolMessage : ", " + schoolMessage);
							if (!StringUtils.isBlank(branchMessage))
								remarks.append(
										remarks != null && remarks.length() > 0 ? branchMessage : ", " + branchMessage);
							if (!StringUtils.isBlank(gradeMessage))
								remarks.append(
										remarks != null && remarks.length() > 0 ? gradeMessage : ", " + gradeMessage);
							if (!StringUtils.isBlank(sectionMessage))
								remarks.append(remarks != null && remarks.length() > 0 ? sectionMessage
										: ", " + sectionMessage);
							record.setRemarks(remarks);
							record.setFailed(true);
							failedData.add(record);
						}

						if (!StringUtils.isEmpty(students.getId())) {
							log.info("Student created successfully, details mapping to the Users DB Table");
							Users users = modelMapper.map(students, Users.class);
							users.setPhoneNumber(students.getMobile());
							users.setUserName(students.getUserName());
							String password = CommonUtilities.generatePassayPassword();
							users.setPassword(CommonUtilities.passwordEncryptor(password));
							users.setCreatedBy(currentUser);
							users = userRepository.save(users);
							if (!StringUtils.isEmpty(users.getId())) {
								log.info("Student registered as users, role mapping intitated...");
								List<UserRolesRequestDto> userRoles = new ArrayList<>();
								userRoles.add(new UserRolesRequestDto(roleId, true));
								userService.addRoles(users, userRoles);
							}
							StudentsResponseDto studentsResponse = new StudentsResponseDto(
									studentsRepository.findStudentById(students.getId()));
							if (!StringUtils.isBlank(students.getGradeId())) {
								GradesResponseDto grades = mastersFeignClient.getGradesById(students.getGradeId())
										.getData();
								studentsResponse.setGrade(grades.getGrade());
							}
							if (!StringUtils.isBlank(students.getSectionId())) {
								SectionsResponseDto sections = mastersFeignClient
										.getSectionsById(students.getSectionId()).getData();
								studentsResponse.setSection(sections.getSection());
							}
							if (!StringUtils.isBlank(studentsResponse.getFirstLanguageId())
									&& !StringUtils.isBlank(studentsResponse.getSecondLanguageId())) {
								LanguagesResponseDto languages = mastersFeignClient
										.getLanguagesById(students.getFirstLanguageId()).getData();
								studentsResponse.setFirstLanguage(languages.getLanguage());
								languages = mastersFeignClient.getLanguagesById(students.getSecondLanguageId())
										.getData();
								studentsResponse.setSecondLanguage(languages.getLanguage());
							}
							if (!StringUtils.isBlank(students.getStudentCategoryId())) {
								StudentCategoriesResponseDto studentCategory = mastersFeignClient
										.getStudentCategoryById(students.getStudentCategoryId()).getData();
								studentsResponse.setStudentCategory(studentCategory.getStudentCategory());
							}

							StudentsResponseDto response = new StudentsResponseDto(
									studentsRepository.findStudentById(students.getId()));
							response.setPassword(password);
							if (!StringUtils.isBlank(students.getGradeId())) {
								GradesResponseDto grades = mastersFeignClient.getGradesById(students.getGradeId())
										.getData();
								response.setGrade(grades.getGrade());
							}
							if (!StringUtils.isBlank(students.getSectionId())) {
								SectionsResponseDto sections = mastersFeignClient
										.getSectionsById(students.getSectionId()).getData();
								response.setSection(sections.getSection());
							}
							if (!StringUtils.isBlank(students.getFirstLanguageId())
									&& !StringUtils.isBlank(students.getSecondLanguageId())) {
								LanguagesResponseDto languages = mastersFeignClient
										.getLanguagesById(students.getFirstLanguageId()).getData();
								response.setFirstLanguage(languages.getLanguage());
								languages = mastersFeignClient.getLanguagesById(response.getSecondLanguageId())
										.getData();
								response.setSecondLanguage(languages.getLanguage());
							}
							if (!StringUtils.isBlank(students.getStudentCategoryId())) {
								StudentCategoriesResponseDto studentCategory = mastersFeignClient
										.getStudentCategoryById(students.getStudentCategoryId()).getData();
								response.setStudentCategory(studentCategory.getStudentCategory());
							}
							response.setTypeOfEmailSend("CREATE");
							response.setLmsEnv("DEV");
							sendEmail(response);
							log.info("Student imported to the system!");
							successData.add(studentsResponse);
						}
					}
				}

				// Set the message in response body
				int successSize = !CollectionUtils.isEmpty(successData) ? successData.size() : 0;
				int failedSize = !CollectionUtils.isEmpty(failedData) ? failedData.size() : 0;
				if (successSize > 0 && failedSize > 0)
					message = "Out of " + originalSize + " " + successSize + " data have been saved, " + failedSize
							+ " failed to save.";
				else if (successSize > 0 && failedSize == 0)
					message = "Out of " + originalSize + ", all were successfully saved.";
				else
					message = "Out of " + originalSize + ", all failed to save.";

			} else
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("read.csv.file.failed", null));

			return new BulkUploadResponseDto(successData, failedData, message);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("student.create.failed", null));
		}
	}

	private void sendEmail(StudentsResponseDto dto) {
		try {

			CreateUserEmailRequestDto requestDto = new CreateUserEmailRequestDto();

			BeanUtils.copyProperties(requestDto, dto);

			String userName = requestDto.getUserName();
			Users users = userRepository.findByUserName(userName);
			requestDto.setToEmail(users.getEmail());
			String userId = EncryptionAndDecryption.encrypt(requestDto.getUserId());
			String resetPasswordUrl = mastersFeignClient.getResetPasswordWithBaseUrl(requestDto.getLmsEnv()).getData();
			requestDto.setForgotPasswordEmailLink(resetPasswordUrl + "?userId=" + userId);
			requestDto.setBaseFEUrl(resetPasswordUrl.substring(0, resetPasswordUrl.indexOf("#")));
			log.info("Sending email started...");

			try {
				if (requestDto.getToEmail() != null && !requestDto.getToEmail().isBlank()
						&& !requestDto.getToEmail().isEmpty()) {
					if (requestDto.getTypeOfEmailSend().equals("CREATE"))
						notificationFeignClient.userCreated(requestDto);
					else
						notificationFeignClient.editUser(requestDto);
				}
			} catch (Exception e) {
				throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
						Translator.toLocale("user.create.email.failed", null));
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.info("Unable send Email: " + e.getMessage());
		}
	}

	/**
	 * Checking the validation of CSV files, if any record not pass the condition
	 * return it.
	 * 
	 * @param request
	 * @return
	 */
	private List<StudentsFile> validationForCSV(List<StudentsFile> request) {
		try {
			List<StudentsFile> failedRecords = new ArrayList<>();
			for (StudentsFile record : request) {
				StringBuilder remarks = new StringBuilder();

//				if (StringUtils.isBlank(record.getFirstName())) {
//					String firstNameMandatory = "First name is mandatory.";
//					remarks.append(remarks != null && remarks.length() > 0 ? firstNameMandatory : ", " + firstNameMandatory);
//				} else {
//					boolean firstNameValid = CommonUtilities.validationByRegex(Constants.INDIAN_NAME_IN_ENG,
//							record.getFirstName());
//					if (!firstNameValid) {
//						String firstNameValidation = "Name format is not proper, please check the first name given.";
//						remarks.append(remarks != null && remarks.length() > 0 ? firstNameValidation : ", " + firstNameValidation);
//					}
//				}

//				if (StringUtils.isBlank(record.getLastName())) {
//					String lastNameMandatory = "Last name is mandatory.";
//					remarks.append(remarks != null && remarks.length() > 0 ? lastNameMandatory : ", " + lastNameMandatory);
//				} else {
//					boolean lastNameValid = CommonUtilities.validationByRegex(Constants.INDIAN_NAME_IN_ENG,
//							record.getLastName());
//					if (!lastNameValid) {
//						String lastNameValidation = "Name format is not proper, please check the last name given.";
//						remarks.append(remarks != null && remarks.length() > 0 ? lastNameValidation : ", " + lastNameValidation);
//					}
//				}

				// Validation stoped on 10_04_2024
				/*
				 * if (StringUtils.isBlank(record.getEmail())) { String emailMandatory =
				 * "Email is mandatory."; remarks.append(remarks != null && remarks.length() > 0
				 * ? emailMandatory : ", " + emailMandatory); } else { boolean emailValid =
				 * CommonUtilities.validationByRegex(Constants.EMAIL_REGEX, record.getEmail());
				 * if (!emailValid) { String emailValidation = "Please check the given email.";
				 * remarks.append( remarks != null && remarks.length() > 0 ? emailValidation :
				 * ", " + emailValidation); } }
				 */

				if (StringUtils.isBlank(record.getMobile())) {
					String mobileMandatory = "Mobile number is mandatory.";
					remarks.append(remarks != null && remarks.length() > 0 ? mobileMandatory : ", " + mobileMandatory);
				} else {
					boolean mobileValid = CommonUtilities.validationByRegex(Constants.PHONE_REGEX, record.getMobile());
					if (!mobileValid) {
						String mobileValidation = "Please provide the Indian mobile number.";
						remarks.append(
								remarks != null && remarks.length() > 0 ? mobileValidation : ", " + mobileValidation);
					}
				}

//				if (StringUtils.isBlank(record.getGender())) {
//					String genderMandatory = "Gender is mandatory.";
//					remarks.append(remarks != null && remarks.length() > 0 ? genderMandatory : ", " + genderMandatory);
//				} else {
//					boolean genderValid = Enums.getIfPresent(Gender.class, record.getGender()).isPresent();
//					if (!genderValid) {
//						String genderValidation = "Please provide the gender in capital letters.";
//						remarks.append(remarks != null && remarks.length() > 0 ? genderValidation : ", " + genderValidation);
//					}
//				}

				// Validation stoped_05_04_2024
//				if (StringUtils.isBlank(record.getDob())) {
//					String dobMandatory = "Date of birth is mandatory.";
//					remarks.append(remarks != null && remarks.length() > 0 ? dobMandatory : ", " + dobMandatory);
//				} else {
//					boolean dobValid = CommonUtilities.validationByRegex(Constants.DD_MM_YYYY, record.getDob());
//					if (!dobValid) {
//						String dobValidation = "Please provide the date of birth format in dd-MM-yyyy.";
//						remarks.append(remarks != null && remarks.length() > 0 ? dobValidation : ", " + dobValidation);
//					}
//
//					boolean isExpiredDate = DateUtilities.checkIfTheDateHasPassed(record.getDob());
//					if (!isExpiredDate) {
//						String expiredDate = "Sorry the given date of birth is not an expired date.";
//						remarks.append(remarks != null && remarks.length() > 0 ? expiredDate : ", " + expiredDate);
//					}
//				}

				// Validation stoped on 10_04_2024
				/*
				 * if (StringUtils.isBlank(record.getAdmissionDate())) { String
				 * admissionMandatory = "Admission date is mandatory."; remarks.append( remarks
				 * != null && remarks.length() > 0 ? admissionMandatory : ", " +
				 * admissionMandatory); } else { boolean admissionValid =
				 * CommonUtilities.validationByRegex(Constants.DD_MM_YYYY,
				 * record.getAdmissionDate()); if (!admissionValid) { String admissionValidation
				 * = "Please provide the admission date format in dd-MM-yyyy.";
				 * remarks.append(remarks != null && remarks.length() > 0 ? admissionValidation
				 * : ", " + admissionValidation); } }
				 */

				if (StringUtils.isBlank(record.getSchoolCode())) {
					String schoolMandatory = "School code is mandatory and make sure existing code is providing.";
					remarks.append(remarks != null && remarks.length() > 0 ? schoolMandatory : ", " + schoolMandatory);
				}

				if (StringUtils.isBlank(record.getBranchName())) {
					String branchMandatory = "Branch name is mandatory and branch-school realtion exists.";
					remarks.append(remarks != null && remarks.length() > 0 ? branchMandatory : ", " + branchMandatory);
				}

				// setting the failed records
				if (remarks != null && remarks.length() > 0) {
					record.setRemarks(remarks);
					record.setFailed(true);
					failedRecords.add(record);
				}
			}
			return failedRecords;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("csv.validation.failed", null));
		}
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	public Boolean deleteStudent(String id) {
		log.info("Student details delete started.");
		if (!studentsRepository.existsById(id)) {
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("student.not.found", null));
		}

		try {
			Long currentTime = new Date().getTime();
			boolean response = false;
			String currentUser = jwtUtil.currentLoginUser();
			Students students = studentsRepository.getById(id);
			int deleteStatus = studentsRepository.setDeleteStatus(id, currentTime, currentUser);
			if (deleteStatus > 0) {
				log.info("Student details marked as deleted.");

				log.info("Deleting the student from users table");
				Users users = usersRepository.findByUserName(students.getUserName());
				String email = users.getEmail() + "-" + currentTime;
				String mobile = users.getPhoneNumber() + "-" + currentTime;

				int deleted = usersRepository.setDeleteStatus(users.getId(), currentTime, currentUser, email, mobile);
				log.info(deleted > 0 ? "User's status set into deleted" : "failed to set status into deleted");

				log.info("Delete the user-role mapping");
				Long userRole = userService.deleteUsersRoleMapping(users.getId());

				if (deleteStatus > 0 && deleted > 0 && userRole > 0) {
					log.info("All relationship with student removed.");
					response = true;
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("student.delete.failed", null));
		}
	}

	/**
	 * Get the student by id, findById return as Optional to avoid and catch the
	 * exception properly.
	 *
	 * @param id
	 * @return
	 * @throws Exception
	 * @since 0.0.1
	 */
	public Students getStudentsById(String id) {
		return studentsRepository.findById(id).orElseThrow(
				() -> new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null)));
	}

	/**
	 * Using {@code firstName}, {@code lastName} and {@code firstName} create a
	 * userId, if the userId already exist in the user collection it'll incremented
	 * by sequence. Each part of userId separated by {@code .}
	 *
	 * @param firstName
	 * @param lastName
	 * @param branchCode
	 * @return
	 */
	private String generateUserName(String firstName, String lastName, String branchCode) {
		try {
			String[] fname = firstName.split(" ");
			String firstNames = fname[0];
			System.out.println(firstNames);
			String lowerCaseFirstName = firstNames.toLowerCase();
			System.out.println("Lowercase first name: " + lowerCaseFirstName);
			String[] lname = lastName.split(" ");
			String lastNames = lname[0];
			System.out.println(lastNames);
			String lowerCaseLastName = lastNames.toLowerCase();
			System.out.println("Lowercase last name: " + lowerCaseLastName);
			String userName = lowerCaseFirstName + "." + lowerCaseLastName + "." + branchCode.toLowerCase();
			;
			Long userCount = userRepository.countByStudentUsername(userName);

			if (userCount != null && userCount > 0)
				userName = lowerCaseFirstName + "." + lowerCaseLastName + "." + branchCode.toLowerCase() + "."
						+ Long.toString(userCount);

			return userName;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.USER_ID_GENERATION_FAILED);
		}
	}

	/**
	 * Self registration by the student with token. After the registration user will
	 * be mapped with the token.
	 *
	 * check for the roles too whether it is exist
	 *
	 * @param request
	 * @return
	 */
	@Override
	@Transactional
	public StudentsResponseDto selfRegistrationByStudent(StudentsRequestDto request) {
		log.info("Student Registration started...");

		try {
			String roleId = mastersFeignClient.getOnlyIdByRole("student").getData();
			Schools school = schoolRepository.getById(request.getSchool());
			Branches branch = branchRepository.getById(request.getBranch());
			String userName = generateUserName(request.getFirstName(), request.getLastName(), branch.getBranchCode());

			Students students = modelMapper.map(request, Students.class);
			students.setUserName(userName);
			students.setSchools(school);
			students.setBranches(branch);
			students = studentsRepository.save(students);

			if (!StringUtils.isEmpty(students.getId())) {
				log.info("Student details registered successfully!");
				String studentId = students.getId();

				Users users = modelMapper.map(students, Users.class);
				users.setPhoneNumber(students.getMobile());
				String password = CommonUtilities.generatePassayPassword();
				users.setPassword(CommonUtilities.passwordEncryptor(password));
				users = userRepository.save(users);
				if (!StringUtils.isEmpty(users.getId())) {
					log.info("Student registered as users, role mapping intitated...");
					List<UserRolesRequestDto> userRoles = new ArrayList<>();
					userRoles.add(new UserRolesRequestDto(roleId, true));
					userService.addRoles(users, userRoles);
				}

				log.info("Mapping with the token, if the student has provided token by the school.");
				if (!StringUtils.isEmpty(request.getToken())) {
					TokensResponseDto tokensResponseDto = tokensService
							.updateTokenDuringRegistration(request.getToken(), users.getId());
					if (!tokensResponseDto.getTokenDetails().isEmpty()) {
						boolean isExist = tokensResponseDto.getTokenDetails().stream()
								.anyMatch(token -> token.getUserId().contains(studentId));
						if (isExist) {
							log.info("User mapped with token successfully");
						} else {
							log.info("failed to map the user with token.");
						}
					}
				}
			}
			StudentsProjection projection = studentsRepository.findStudentById(students.getId());
			StudentsResponseDto response = new StudentsResponseDto(projection);
			if (!StringUtils.isBlank(students.getGradeId())) {
				GradesResponseDto grades = mastersFeignClient.getGradesById(students.getGradeId()).getData();
				response.setGrade(grades.getGrade());
			}
			if (!StringUtils.isBlank(students.getSectionId())) {
				SectionsResponseDto sections = mastersFeignClient.getSectionsById(students.getSectionId()).getData();
				response.setSection(sections.getSection());
			}
			if (!StringUtils.isBlank(students.getFirstLanguageId())
					&& !StringUtils.isBlank(students.getSecondLanguageId())) {
				LanguagesResponseDto languages = mastersFeignClient.getLanguagesById(students.getFirstLanguageId())
						.getData();
				response.setFirstLanguage(languages.getLanguage());
				languages = mastersFeignClient.getLanguagesById(students.getSecondLanguageId()).getData();
				response.setSecondLanguage(languages.getLanguage());
			}
			if (!StringUtils.isBlank(students.getStudentCategoryId())) {
				StudentCategoriesResponseDto studentCategory = mastersFeignClient
						.getStudentCategoryById(students.getStudentCategoryId()).getData();
				response.setStudentCategory(studentCategory.getStudentCategory());
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.self.registration.failed", null));
		}
	}

	/**
	 *
	 * @param request
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public PaginatedResponse<StudentsResponseDto> getAllStudentsByPagination(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String sectionId, String gradeId, String branchId, String schoolId,
			String search, Boolean active) {
		try {
			List<StudentsResponseDto> response = new ArrayList<>();

			String sortKey = FieldMappers.studentsApiFieldMapper(sortBy);
			Long totalElements = 0L;
			Integer totalPages = 0;
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortKey));
			Page<StudentsProjection> pagination = studentsRepository.findAllStudentsByPagination(sectionId, gradeId,
					branchId, schoolId, searchFormat, active, pageable);

			if (!pagination.getContent().isEmpty()) {

				log.info("Feign call to student-service. Get the total quiz attempted and last submission date.");
				List<String> studentIds = pagination.getContent().stream().map(StudentsProjection::getId).distinct()
						.collect(Collectors.toList());
				LMSResponse<List<StudentQuizCountSubmitDateResoponseDto>> studentServiceResponse = studentFeignClient
						.getTheLastSubmissionDateAndCountByIds(studentIds);
				List<StudentQuizCountSubmitDateResoponseDto> quizCountSubmitDateList = studentServiceResponse != null
						? studentServiceResponse.getData()
						: new ArrayList<>();

				log.info(
						"Get unique value of section and grade from the pagination and call master-service to get details.");
				Set<String> sectionSet = pagination.getContent().stream().map(StudentsProjection::getSectionId)
						.collect(Collectors.toSet());
				List<String> listSectionIds = sectionSet.stream().collect(Collectors.toList());
				List<SectionsResponseDto> sectionList = mastersFeignClient.getAllSectionsByIds(listSectionIds)
						.getData();

				Set<String> gradeSet = pagination.getContent().stream().map(StudentsProjection::getGradeId)
						.collect(Collectors.toSet());
				List<String> listGradeIds = gradeSet.stream().collect(Collectors.toList());
				List<GradesResponseDto> gradeList = mastersFeignClient.getAllGradesByIds(listGradeIds).getData();

				totalElements = pagination.getTotalElements();
				totalPages = pagination.getTotalPages();

				pagination.getContent().forEach(pages -> {
					log.info("Setting data");
					StudentsResponseDto responseDto = new StudentsResponseDto(pages);

					// setting the quiz count and last-submission of the unit-quiz.
					if (!CollectionUtils.isEmpty(quizCountSubmitDateList)) {
						Optional<StudentQuizCountSubmitDateResoponseDto> quizCountSubmitDateDto = quizCountSubmitDateList
								.stream().filter(item -> item.getStudentId().equals(responseDto.getId())).findAny();
						if (quizCountSubmitDateDto.isPresent()) {
							responseDto.setNumberOfQuiz(quizCountSubmitDateDto.get().getNumberOfQuizAttempted());
							responseDto.setLastUnitQuizSubmission(
									quizCountSubmitDateDto.get().getLastUnitQuizSubmission());
						}
					}

					if (!sectionList.isEmpty()) {
						Optional<SectionsResponseDto> sectionDto = sectionList.stream()
								.filter(s -> s.getId().equals(responseDto.getSectionId())).findAny();
						if (sectionDto.isPresent()) {
							log.info("Setting the matching value of section.");
							responseDto.setSection(sectionDto.get().getSection());
						}
					}

					if (!gradeList.isEmpty()) {
						Optional<GradesResponseDto> gradeDto = gradeList.stream()
								.filter(g -> g.getId().equals(responseDto.getGradeId())).findAny();
						if (gradeDto.isPresent()) {
							log.info("Setting the matching value of grade.");
							responseDto.setGrade(gradeDto.get().getGrade());
						}
					}
					if (!StringUtils.isBlank(responseDto.getFirstLanguageId())
							&& !StringUtils.isBlank(responseDto.getSecondLanguageId())) {
						LanguagesResponseDto languages = mastersFeignClient
								.getLanguagesById(responseDto.getFirstLanguageId()).getData();
						responseDto.setFirstLanguage(languages.getLanguage());
						languages = mastersFeignClient.getLanguagesById(responseDto.getSecondLanguageId()).getData();
						responseDto.setSecondLanguage(languages.getLanguage());
					}
					if (!StringUtils.isBlank(responseDto.getStudentCategoryId())) {
						StudentCategoriesResponseDto studentCategory = mastersFeignClient
								.getStudentCategoryById(responseDto.getStudentCategoryId()).getData();
						responseDto.setStudentCategory(studentCategory.getStudentCategory());
					}
					response.add(responseDto);
				});
				if (!response.isEmpty()) {
					log.info("All students fetched.");
				}
			}
			log.info("Get all students by pagination completed...");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("failed.to.filter.students.detail", null));
		}
	}

	@Override
	public Boolean updateActiveField(String id, boolean active) {
		if (!studentsRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("student.id.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			Students student = studentsRepository.getById(id);
			student.setActive(active);
			student.setModifiedAt(currentTime);
			student.setLastModifiedBy(jwtUtil.currentLoginUser());
			student = studentsRepository.save(student);

			log.info("Toggle the student from users table");
			Users users = usersRepository.findByUserName(student.getUserName());

			int toggled = usersRepository.setActiveStatus(users.getId(), active, currentTime, currentUser);
			log.info(toggled > 0 ? "User's active-status toggled" : "failed to toggled the active status.");

			return (student.isActive() == active) && (toggled > 0);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.failed", null));
		}
	}

	/**
	 * Checking the user by user_name then call the user_role, institute mapping.
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType) {
		if (!studentsRepository.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.id.not.found", null));
		}

		try {
			boolean isExits = false;
			String message = "There is no mapping related to this student currently.";
			Students students = studentsRepository.getById(id);
			Users users = userRepository.findByUserName(students.getUserName());
			List<Long> mappingCounts = userRepository.findAllMappingCountOfUser(users.getId());
			if (!mappingCounts.isEmpty()) {
				isExits = mappingCounts.stream().anyMatch(count -> count > 0);
				if (isExits) {
					if (OperationType.DELETE == OperationType.valueOf(operationType)) {
						message = "Some data are connected with this student. Deleting may cause some loss of data. "
								+ "Do you still want to delete " + students.getFirstName() + " "
								+ students.getLastName();
					} else {
						message = "Some data are connected with this student. "
								+ "Changing the active status may cause some constrain violation issues. "
								+ "Do you still want to change the active status of " + students.getFirstName() + " "
								+ students.getLastName();
					}
				} else {
					return message;
				}
			}
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	/**
	 * Confirm Students Mapped data by Grade Id before delete
	 *
	 * @param gradeId
	 * @return
	 */
	@Override
	public Boolean getCountOfStudentsByGradeId(String gradeId) {
		try {
			Boolean response = false;
			Long gradeCount = studentsRepository.findCountOfStudentsByGradeId(gradeId);
			response = gradeCount > 0;
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("school.branch.mapped.with.board.failed", null));
		}
	}

	/**
	 * Confirm Students mapped data by section Id before delete
	 *
	 * @param sectionId
	 * @return
	 */
	@Override
	public Boolean getCountOfStudentsBySectionId(String sectionId) {
		try {
			Boolean response = false;
			Long studentsCount = studentsRepository.findCountOfStudentBySectionId(sectionId);
			response = studentsCount > 0;
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.mapped.with.section.failed", null));
		}
	}

	/**
	 * Get the last modification done on the table students return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 *
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return studentsRepository.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	@Override
	public List<NameCommonResponseDto> getAllStudentsByLanguage(String languageId) {
		try {
			return studentsRepository.getStudentsByLanguageId(languageId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("students.mappings.by.language.fetch.failed", null));
		}
	}

	@Override
	public List<NameCommonResponseDto> getStudentsByStudentCategory(String studentCategoryId) {
		try {
			return studentsRepository.getStudentsByStudentCategory(studentCategoryId);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("students.mappings.by.student.category.fetch.failed", null));
		}
	}

	private void studentCreateChecking(StudentsRequestDto request) {
		if (!schoolRepository.existsById(request.getSchool()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (!branchRepository.existsById(request.getBranch()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		if (!branchRepository.existsBySchoolsIdAndDeletedAndId(request.getSchool(), false, request.getBranch()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.school.relation", null));

		if (StringUtils.isBlank(request.getLmsEnv())
				|| !Enums.getIfPresent(LMSEnvironment.class, request.getLmsEnv()).isPresent()) {
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("specify.application.environment", null));
		}

		boolean mappingExist = false;
		if (StringUtils.isEmpty(request.getSectionId())) {
			log.info("Checking the grade-section mapping using gradeId, branchId and schoolId");
			mappingExist = gradeSectionMappingRepository
					.existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndActiveAndSectionIdIsNull(request.getSchool(),
							request.getBranch(), request.getGradeId(), false, true);
		} else {
			log.info("Checking the grade-section mapping using sectionId, gradeId, branchId and schoolId");
			mappingExist = gradeSectionMappingRepository
					.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeletedAndActive(request.getSchool(),
							request.getBranch(), request.getGradeId(), request.getSectionId(), false, true);
		}

		if (!mappingExist)
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("gs.mapping.not.exist", null));

	}

	/**
	 * @param request
	 * @return
	 */
	@Override
	@Transactional
	public StudentSubjectMappingsResponseDto mapSubjectsToStudent(StudentSubjectMappingRequestDto request) {
		if (!studentsRepository.existsById(request.getStudentId())) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.id.not.found", null));
		}
		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			StudentsResponseDto studentsResponse = getStudentById(request.getStudentId());
			AcademicYearResponseDto academicYearResponseDto = mastersFeignClient
					.getAcademicYearsById(request.getAcademicYearId()).getData();
			StudentSubjectMappingsResponseDto response = new StudentSubjectMappingsResponseDto(studentsResponse,
					academicYearResponseDto.getId(), academicYearResponseDto.getAcademicYear());
			List<String> mappingIds = new ArrayList<>();
			for (StudentSubjectMappingsMappingRequestDto studentSubjectMappingsMapping : request
					.getStudentSubjectMappingsMappingsList()) {
				StudentSubjectMapping studentSubjectMappings = new StudentSubjectMapping();
				studentSubjectMappings.setStudent(studentsRepository.getById(request.getStudentId()));
				studentSubjectMappings.setAcademicYearsId(request.getAcademicYearId());
				studentSubjectMappings.setId(studentSubjectMappingsMapping.getId());
				studentSubjectMappings.setSubjectGroupSubjectMappingsId(
						studentSubjectMappingsMapping.getSubjectGroupsSubjectMappingsId());
				studentSubjectMappings = studentSubjectMappingsRepository.save(studentSubjectMappings);
				SubjectGroupsSubjectMappingsResponseDto subjectGroupsSubjectMappings = mastersFeignClient
						.getSubjectGroupsSubjectMappingsById(
								studentSubjectMappingsMapping.getSubjectGroupsSubjectMappingsId())
						.getData();
				mappingIds.add(studentSubjectMappings.getId());
				response.getSubjectGroupSubjectMappings().add(new StudentSubjectGroupSubjectMappingsResponseDto(
						studentSubjectMappings.getId(), subjectGroupsSubjectMappings));
			}
			studentSubjectMappingsRepository.deleteDataByStudentIdAndIdsNotInAndAcademicYearsId(currentUser,
					currentTime, request.getStudentId(), mappingIds, request.getAcademicYearId());
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.subject.groups.subject.mapping.failed", null));
		}
	}

	/**
	 * @param studentId
	 * @param academicYearId
	 * @return
	 */
	@Override
	public StudentSubjectMappingsResponseDto getStudentWithSubjects(String studentId, String academicYearId) {
		if (!studentsRepository.existsById(studentId)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.id.not.found", null));
		}
		try {
			StudentsResponseDto studentsResponse = getStudentById(studentId);
			AcademicYearResponseDto academicYearResponseDto = mastersFeignClient.getAcademicYearsById(academicYearId)
					.getData();
			StudentSubjectMappingsResponseDto response = new StudentSubjectMappingsResponseDto(studentsResponse,
					academicYearResponseDto.getId(), academicYearResponseDto.getAcademicYear());
			response.getSubjectGroupSubjectMappings().addAll(
					studentSubjectMappingsRepository.findByStudentIdAndAcademicYearId(studentId, academicYearId));
			Set<String> subjectGroupSubjectMappingsIdSet = response.getSubjectGroupSubjectMappings().stream()
					.map(StudentSubjectGroupSubjectMappingsResponseDto::getSubjectGroupSubjectMappingId)
					.collect(Collectors.toSet());
			List<SubjectGroupsSubjectMappingsResponseDto> subjectGroupsSubjectMappingsResponseList = mastersFeignClient
					.getAllSubjectGroupsSubjectMappingsByIds(new ArrayList<>(subjectGroupSubjectMappingsIdSet))
					.getData();
			response.getSubjectGroupSubjectMappings().forEach(res -> {
				Optional<SubjectGroupsSubjectMappingsResponseDto> ssgsmDto = subjectGroupsSubjectMappingsResponseList
						.stream().filter(mapping -> mapping.getId().equals(res.getSubjectGroupSubjectMappingId()))
						.findAny();
				if (!ssgsmDto.isEmpty()) {
					res.setSubjectGroupId(ssgsmDto.get().getSubjectGroupId());
					res.setSubjectGroupTitle(ssgsmDto.get().getSubjectGroupTitle());
					res.setSubjectGroupCode(ssgsmDto.get().getSubjectGroupCode());
					res.setSubjectId(ssgsmDto.get().getSubjectId());
					res.setSubject(ssgsmDto.get().getSubject());
				}
			});

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.subject.groups.subject.mapping.failed", null));
		}
	}

	/**
	 * @param studentId
	 * @return
	 */
	@Override
	public StudentSubjectMappingsMinResponseDto getStudentWithSubjectsAndSubTopics(String studentId) {
		if (!studentsRepository.existsById(studentId)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.id.not.found", null));
		}
		try {
			String academicYearId = studentSubjectMappingsRepository.getCurrentAcademicYear();
			StudentsResponseDto studentsResponse = getStudentById(studentId);
			AcademicYearResponseDto academicYearResponseDto = mastersFeignClient.getAcademicYearsById(academicYearId)
					.getData();
			StudentSubjectMappingsMinResponseDto response = new StudentSubjectMappingsMinResponseDto(studentsResponse,
					academicYearResponseDto.getId(), academicYearResponseDto.getAcademicYear());
			List<StudentSubjectMappingDto> studentSubjectMappingList = studentSubjectMappingsRepository
					.findMinByStudentIdAndAcademicYearId(studentId, academicYearId);
			Set<String> subjectGroupSubjectMappingsIdSet = studentSubjectMappingList.stream()
					.map(StudentSubjectMappingDto::getSubjectSubjectMappingId).collect(Collectors.toSet());
			List<SubjectGroupsSubjectMappingsMinResponseDto> subjectGroupsSubjectMappingsResponseList = mastersFeignClient
					.getAllSubjectGroupsSubjectMappingsAndSubTopicsByIds(
							new ArrayList<>(subjectGroupSubjectMappingsIdSet))
					.getData();
			response.setSubjects(subjectGroupsSubjectMappingsResponseList);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.subject.groups.subject.mapping.failed", null));
		}
	}

	/**
	 * @param studentId
	 * @return
	 */
	@Override
	public List<SubjectsResponseDto> getAllSubjectsByStudentId(String studentId) {
		if (!studentsRepository.existsById(studentId)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.id.not.found", null));
		}
		try {
			List<SubjectsResponseDto> response = new ArrayList<>();
			Students students = studentsRepository.getById(studentId);
			String schoolId = students.getSchools().getId();
			String branchId = students.getBranches().getId();
			String planId = branchPlanMappingsRepository.findPlanIdByBranchId(branchId);
			String boardId = students.getBranches().getBoardId();
			String gradeId = students.getGradeId();
			String sectionId = students.getSectionId();
			// String academicYearId;

			if (!StringUtils.isEmpty(planId)) {
				LMSResponse<List<SubjectsResponseDto>> subjectResponse = mastersFeignClient
						.subjectListFromPlanTemplate(gradeId, planId);

				if (!ObjectUtils.isEmpty(subjectResponse)) {
					response = subjectResponse.getData();

					if (!CollectionUtils.isEmpty(response)) {
						response.forEach(subject -> {

							if (!CollectionUtils.isEmpty(subject.getSubTopics())) {
								// Subject with sub_topic
								subject.getSubTopics().forEach(subtopic -> {
									LMSResponse<Integer> masterServiceResponse = mastersFeignClient
											.chapterCount(boardId, gradeId, subject.getId(), subtopic.getId());
									if (!ObjectUtils.isEmpty(masterServiceResponse))
										subtopic.setChapterCount(masterServiceResponse.getData());

									LMSResponse<Integer> contentServiceResponse = contentFeignClient
											.getQuizReleaseCount(boardId, schoolId, branchId, gradeId, sectionId, null,
													subject.getId(), subtopic.getId(), null);
									if (!ObjectUtils.isEmpty(contentServiceResponse))
										subtopic.setQuizCount(contentServiceResponse.getData());

								});
							} else {
								// Subject without sub_topic
								LMSResponse<Integer> masterServiceResponse = mastersFeignClient.chapterCount(boardId,
										gradeId, subject.getId(), null);
								if (!ObjectUtils.isEmpty(masterServiceResponse))
									subject.setChapterCount(masterServiceResponse.getData());

								LMSResponse<Integer> contentServiceResponse = contentFeignClient.getQuizReleaseCount(
										boardId, schoolId, branchId, gradeId, sectionId, null, subject.getId(), null,
										null);
								if (!ObjectUtils.isEmpty(contentServiceResponse))
									subject.setQuizCount(contentServiceResponse.getData());
							}

						});
					}
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.subject.groups.subject.mapping.failed", null));
		}
	}

	/**
	 * Checking plan is exist by master-feign call
	 * 
	 * @param planId
	 * @return
	 */
	private PlansResponseDto planByFeignCall(String planId) {
		try {
			LMSResponse<PlansResponseDto> gradeResponse = mastersFeignClient.getPlansById(planId);
			if (!ObjectUtils.isEmpty(gradeResponse)) {
				return gradeResponse.getData();
			} else {
				log.error("Grade not pesent in the system");
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("grade.not.found", null));
			}
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		}

	}

	/**
	 * Checking grade is exist by master-feign call
	 * 
	 * @param gradeId
	 * @return
	 */
	private GradesResponseDto gradeByFeignCall(String gradeId) {
		try {
			LMSResponse<GradesResponseDto> gradeResponse = mastersFeignClient.getGradesById(gradeId);
			if (!ObjectUtils.isEmpty(gradeResponse)) {
				return gradeResponse.getData();
			} else {
				log.error("Grade not pesent in the system");
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("grade.not.found", null));
			}
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		}
	}

	@Override
	public Map<String, String> getUserIds(String sectionId, String gradeId, String branchId, String schoolId,
			String search) {
		try {
			Map<String, String> response = new HashMap<>();
			List<Object[]> ids = studentsRepository.getUserIds(sectionId, gradeId, branchId, schoolId, search);
			ids.forEach(new Consumer<Object[]>() {
				@Override
				public void accept(Object[] result) {
					response.put(result[0].toString(), result[1].toString());
				}
			});
			return response;
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(e.getErrorCode(), e.getMessage());
		}
	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto checkTheMappingForConcept(String id, String operationType) {
		if (!studentsRepository.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.id.not.found", null));

		try {
			boolean isExits = false;
			String message = Translator.toLocale("confirmation.api.default.message", null);

			LMSResponse<Long> studentReponse = studentFeignClient.getStudentsCount(id);
			if (studentReponse != null)
				isExits = studentReponse.getData() != null && studentReponse.getData() > 0;

			if (isExits)
				message = Translator.toLocale("confirmation.api.permission.denied", null);
			else {
				message = (OperationType.DELETE == OperationType.valueOf(operationType))
						? Translator.toLocale("confirmation.api.delete", null)
						: Translator.toLocale("confirmation.api.toggle.active", null);
			}

			return new ConfirmationApiResponseDto(isExits, message);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("confirmation.api.failed", null));
		}
	}

	/**
	 * Changing the student profile by the gradeId and sectionId
	 * 
	 * @param request
	 * @return
	 */
	@Override
	@Transactional
	public ChangeProfileResponseDto changeStudentProfile(ChangeProfileRequestDto request) {
		try {
			log.info("Student profile Process Updation started...");
			checkTheRequestForChageProfile(request);

			AcademicYearResponseDto academicYear = null;

			//TODO yearend process not completed
			// if (request.isYearEndProcess()) {
				LMSResponse<AcademicYearResponseDto> masterResponse = mastersFeignClient.getLatestAcademicYear();
				if (!ObjectUtils.isEmpty(masterResponse))
					academicYear = masterResponse.getData();
			// }

			List<String> studentUserIds = new ArrayList<>();

			List<StudentMinDetailsResponseDto> studentMinList = new ArrayList<>();
			for (String studentId : request.getStudents()) {
				Students students = studentsRepository.getById(studentId);
				students.setGradeId(request.getToGradeId());
				students.setSectionId(request.getToSectionId());
				students.setYearEndProcess(request.isYearEndProcess());
				students.setIsPromoted(request.isYearEndProcess());

				String studentUserId = userRepository.findUserIdByUserName(students.getUserName());

				studentUserIds.add(studentUserId);
				studentUserIds.add(studentId);

				students = studentsRepository.save(students);

				if (students.getGradeId().equals(request.getToGradeId())) {
					log.info("Grade changed, updating to the history table.");
					StudentsProfileHistory history = modelMapper.map(request, StudentsProfileHistory.class);
					history.setStudents(students);
					if (academicYear != null)
						history.setAcademicYearId(academicYear.getId());
					history = profileHistoryRepo.save(history);
					if (!StringUtils.isEmpty(history.getId()))
						studentMinList.add(new StudentMinDetailsResponseDto(students.getId(), students.getFirstName(),
								students.getLastName(), students.getUserName(), students.isActive()));
				}
			}

			log.info("studentUserIds: " + studentUserIds);

			if(studentUserIds != null && !studentUserIds.isEmpty()){
				StudentExamReqDto   studentExamRequestDto = new StudentExamReqDto(studentUserIds, "dummy", 
				request.getSchoolId(), request.getBranchId(),
				request.getFromGradeId(),   request.getToSectionId(), academicYear.getId(),"");

				LMSResponse<String> lmsResponse =  studentFeignClient.handleStudentsSectionTransfer(studentExamRequestDto);
				log.info("Transfer Response: " + lmsResponse);
			}
			

			return !CollectionUtils.isEmpty(studentMinList) ? createResponseForProfileHistory(request, studentMinList)
					: null;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.profile.id.not.found", null));
		}
	}

	/**
	 * checking grade, section mapping exists
	 * 
	 * @param request
	 */
	private void checkTheRequestForChageProfile(ChangeProfileRequestDto request) {
		try {
			if (request.isYearEndProcess() == request.isSameYear())
				throw new USException(ErrorCodes.BAD_REQUEST,
						Translator.toLocale("student.year.end.process.and.same.year", null));

			boolean mappingExist = false;
			if (StringUtils.isEmpty(request.getToSectionId())) {
				log.info("Checking school, branch and grade combination where section is null");
				mappingExist = gradeSectionMappingRepository
						.existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndActiveAndSectionIdIsNull(
								request.getSchoolId(), request.getBranchId(), request.getToGradeId(), false, true);
			} else {
				log.info("Checking school, branch, grade and section combination");
				mappingExist = gradeSectionMappingRepository
						.existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeletedAndActive(request.getSchoolId(),
								request.getBranchId(), request.getToGradeId(), request.getToSectionId(), false, true);
			}

			if (!mappingExist)
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("gs.mapping.not.exist", null));

		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		}
	}

	/**
	 * Creating the response for change profile api
	 * 
	 * @param request
	 * @param studentMinList
	 * @return
	 */
	private ChangeProfileResponseDto createResponseForProfileHistory(ChangeProfileRequestDto request,
			List<StudentMinDetailsResponseDto> studentMinList) {
		try {
			log.info("Creating the response");
			ChangeProfileResponseDto response = new ChangeProfileResponseDto();
			response.setStudents(studentMinList);

			if (!StringUtils.isEmpty(request.getToGradeId())) {
				GradesResponseDto gradeDto = null;
				LMSResponse<GradesResponseDto> masterRersponse = mastersFeignClient
						.getGradesById(request.getToGradeId());
				if (!ObjectUtils.isEmpty(masterRersponse)) {
					gradeDto = masterRersponse.getData();
					response.setToGradeId(gradeDto.getId());
					response.setToGradeName(gradeDto.getGrade());
				}
			}

			if (!StringUtils.isEmpty(request.getToSectionId())) {
				SectionsResponseDto sectionDto = null;
				LMSResponse<SectionsResponseDto> sectionResponse = mastersFeignClient
						.getSectionsById(request.getToSectionId());
				if (!ObjectUtils.isEmpty(sectionResponse)) {
					sectionDto = sectionResponse.getData();
					response.setToSectionId(sectionDto.getId());
					response.setToSectionName(sectionDto.getSection());
				}
			}

			Branches branches = branchRepository.getById(request.getBranchId());
			response.setBranchId(branches.getId());
			response.setBranchName(branches.getName());

			Schools schools = schoolRepository.getById(request.getSchoolId());
			response.setSchoolId(schools.getId());
			response.setSchoolName(schools.getName());
			response.setSameYear(request.isSameYear());
			response.setYearEndProcess(request.isYearEndProcess());

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("response.create.failed", null));
		}
	}

	/**
	 * Re-Release quiz and pass the students list and getting exam status
	 * <p>
	 * 
	 * @param studentIds
	 * @param studentName
	 * @param examAttended OR not
	 * @return
	 */
	@Override
	public PaginatedResponse<StudentsResponseDto> getStudentsExaminationStatus(int pageNumber, int pageSize,
			String schoolId, String branchId, String gradeId, String quizId, String search, String sectionId) {
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			Pageable pageable = PageRequest.of(pageNumber, pageSize);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;
			List<StudentsResponseDto> response = new ArrayList<>();

			// Getting student data information
			Boolean active=true;
			Page<StudentsProjection> studentPagination = studentsRepository.findAllStudentsByPagination(sectionId,
					gradeId, branchId, schoolId, searchFormat, active, pageable);

			if (!CollectionUtils.isEmpty(studentPagination.getContent())) {
				List<String> studentIds = studentPagination.getContent().stream().map(StudentsProjection::getId)
						.collect(Collectors.toList());

				// calling feign client from student-service getting exam status
				LMSResponse<List<String>> studentsExaminationResponse = studentFeignClient.getStudentsExamAttendedORNot(
						new StudentExamRequestDto(studentIds, quizId, schoolId, branchId, gradeId, sectionId));
				List<String> studentExamResponseList = studentsExaminationResponse != null
						? studentsExaminationResponse.getData()
						: new ArrayList<>();

				for (StudentsProjection studentsProjection : studentPagination.getContent()) {
					StudentsResponseDto responseDto = studentMapper
							.mapStudentProjectionToResponseDto(studentsProjection);
					boolean isExamAttended = !CollectionUtils.isEmpty(studentExamResponseList)
							? studentExamResponseList.stream().anyMatch(item -> item.equals(studentsProjection.getId()))
							: false;
					responseDto.setExamAttended(isExamAttended);
					response.add(responseDto);
				}
			}

			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("failed.to.filter.students.detail", null));
		}
	}

	/**
	 * This is a feign call for sending notification from the
	 * release-quizzes(content-service)
	 * 
	 * @param sectionId
	 * @param gradeId
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	@Override
	public List<String> getAllUserIdForStudent(String sectionId, String gradeId, String branchId, String schoolId) {
		try {
			return studentsRepository.findUserIdsForStudent(sectionId, gradeId, branchId, schoolId);
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.user.id.listing.failed", null));
		}
	}

	/**
	 * This is a feign call for sending notification from content-service for quiz
	 * Re-release.
	 * 
	 * @param studentsId
	 * @return
	 */
	@Override
	public List<String> getAllUserIdByStudentIds(List<String> studentsId) {
		try {
			return studentsRepository.findUserIdsByStudentIds(studentsId);
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.user.id.listing.failed", null));
		}
	}

	/**
	 * Find the student count. This is a feign call to student-service.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	@Override
	public long countOfStudentsByFilters(String schoolId, String branchId, String gradeId, String sectionId) {
		try {
			return studentsRepository.findCountByPrincipalGradeAndSections(schoolId, branchId, gradeId, sectionId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.count.failed", null));
		}
	}

	/**
	 * De-Activate the student profile by the branchId and schoolId
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public Boolean deActivateStudentProfile(DeActivateProfileRequestDto request) {
		log.info("Student details update started.");
		if (!schoolRepository.existsByIdAndDeletedAndActive(request.getSchoolId(), false, true))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (!branchRepository.existsByIdAndDeletedAndActive(request.getBranchId(), false, true))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		if (!studentsRepository.existsBySchoolsIdAndBranchesIdAndDeletedAndActiveAndIdIn(request.getSchoolId(),
				request.getBranchId(), false, true, request.getStudents()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.branch.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			boolean response = false;

			String currentUser = jwtUtil.currentLoginUser();
			int deActivateStatus = studentsRepository.deActiveStudentProfile(request.isActive(), currentUser,
					currentTime, request.getBranchId(), request.getSchoolId(), request.getStudents());
			if (deActivateStatus > 0) {
				log.info("student details marked as de-activated ");
				response = true;
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.profile.updated.failed", null));
		}
	}

	/**
	 * This api is used for for getting studentId and fullName.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	@Override
	public List<StudentsMinResponseDto> getStudentsDetail(String schoolId, String branchId, String gradeId,
			String sectionId) {
		try {
			List<StudentsProjection> projectionList = studentsRepository.findStudentDetailsByFilters(schoolId, branchId,
					gradeId, sectionId);
			return projectionList.stream().map(projection -> {
				String fullName = projection.getFirstName() + " " + projection.getLastName();
				return new StudentsMinResponseDto(projection.getId(), fullName);
			}).collect(Collectors.toList());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * This is a feign call for sending notification from content-service for quiz
	 * Re-release.
	 * 
	 * @param studentsId
	 * @return
	 */
	@Override
	public String getUserIdByStudentId(String studentId) {
		try {
			return studentsRepository.findUserIdByStudentId(studentId);
		} catch (USException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.user.id.listing.failed", null));
		}
	}

	/**
	 * This api is used for getting student count using sectionId.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */

	@Override
	public Integer getStudentSectionCount(String schoolId, String branchId, String gradeId, String sectionId) {

		if (!studentsRepository.existsBySectionIdAndDeleted(sectionId, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("section.id.not.found", null));
		try {
			return studentsRepository.getCountOfStudentsSection(schoolId, branchId, gradeId, sectionId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.student.section.count.success", null));
		}

	}

	/**
	 * This is a feign call for getting students count by grade id
	 *
	 *
	 * @param gradeId
	 * @param schoolId
	 * @param sectionId
	 * @return
	 */
	@Override
	public Long getStudentCountByGrade(String gradeId, String schoolId, String sectionId) {
		try {
			return studentsRepository.getStudentCountByGradeIdSection(gradeId, schoolId, sectionId);
		} catch (Exception ex) {
			log.error(ExceptionUtils.getStackTrace(ex));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.count.by.grade.id.failed", null));
		}
	}

	/**
	 * Find the userId for different schools. Feign call to content-service to
	 * notify the PQ release.
	 * 
	 * @param sectionIds
	 * @param gradeIds
	 * @param branchIds
	 * @param schoolIds
	 * @return
	 */
	@Override
	public List<String> getUserIdOfStudentsFromDifferentSchools(List<String> sectionIds, List<String> gradeIds,
			List<String> branchIds, List<String> schoolIds) {
		try {
			return !CollectionUtils.isEmpty(sectionIds)
					? studentsRepository.findUserIdsForDifferentSchoolsWithSection(sectionIds, gradeIds, branchIds,
							schoolIds)
					: studentsRepository.findUserIdsForDifferentSchools(gradeIds, branchIds, schoolIds);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT,
					Translator.toLocale("student.userid.from.different.schools.failed", null));
		}
	}

	/**
	 * THis is feign call for students access details.
	 * 
	 * @param userName
	 * @return
	 */
	@Override
	public StudentAssignedMinDetailResponseDto getStudentAssignedDetails(String userName) {
		try {
			StudentAssignedMinDetailResponseDto responseDto = studentsRepository.getStudentsByStudentIds(userName);
			if (responseDto == null)
				throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.details.fetch.failed", null));

			String planId = branchRepository.getByPlanId(responseDto.getBoardId(), responseDto.getBranchId(),
					responseDto.getSchoolId());

			StudentAssignedMinDetailResponseDto response = new StudentAssignedMinDetailResponseDto(responseDto.getId(),
					responseDto.getBoardId(), responseDto.getSchoolId(), responseDto.getSchool(),
					responseDto.getBranchId(), responseDto.getBranch(), responseDto.getGradeId(),
					responseDto.getSectionId(), planId);

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * This is a feign call for getting student details by user name
	 *
	 *
	 * @return
	 */
	@Override
	public StudentDetailsResponseDto getStudentDetailsByName(String userName) {
		try {
			StudentDetailsResponseDto response = new StudentDetailsResponseDto();
			StudentsProjection projection = studentsRepository.getStudentDetailsByName(userName);
			if (projection != null) {
				response = new StudentDetailsResponseDto(projection);
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.user.name.details.failed", null));
		}
	}

	public List<FormalStudentWiseGetName> getFormalWiseStudentName(String teacherId, String gradeId, String schoolId,
			String branchId, String sectionId, List<String> studentIds) {

		try {

			List<StudentsProjection> response = studentsRepository.getFormalStudentDetailsByName(schoolId, branchId,
					sectionId, gradeId, studentIds);

			List<FormalStudentWiseGetName> responseDtoList = !CollectionUtils.isEmpty(response)
					? response.stream().map(studentsProjection -> {
						FormalStudentWiseGetName responseDto = new FormalStudentWiseGetName();
						responseDto.setStudentId(studentsProjection.getId());
						responseDto.setStudentName(
								studentsProjection.getFirstName() + " " + studentsProjection.getLastName());
						return responseDto;
					}).collect(Collectors.toList())
					: null;

			return responseDtoList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.user.name.details.failed", null));
		}

	}

	@Override
	public List<StudentMinResponseDto> getStudentListInSection(String schoolId, String branchId, String gradeId,
			String sectionId) {
		try {

			List<StudentsProjection> response = studentsRepository.getStudentListInSection(schoolId, branchId, gradeId,
					sectionId);

					log.info("studentsRepository.getStudentListInSection: " + response);

			List<StudentMinResponseDto> studentDetailsResponse = !CollectionUtils.isEmpty(response)
					? response.stream().map(studentsProjection -> {
						StudentMinResponseDto responseDto = new StudentMinResponseDto();
						responseDto.setId(studentsProjection.getId());
						responseDto.setFirstName(studentsProjection.getFirstName());
						responseDto.setLastName(studentsProjection.getLastName());
						return responseDto;
					}).collect(Collectors.toList())
					: null;
					log.info("studentDetailsResponse.studentDetailsResponse: " + studentDetailsResponse);
			return studentDetailsResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.details.fetch.failed", null));
		}

	}

	/**
	 * get the student details. This is a feign call to Teacher-service.
	 * 
	 * @param boardId
	 * @param gradeId
	 * @return
	 */
	@Override
	public List<StudentMinResponseDto> getStudentListInGrade(String boardId, String gradeId) {
		try {

			List<StudentsProjection> response = studentsRepository.getStudentListInGrade(boardId, gradeId);

			List<StudentMinResponseDto> studentDetailsResponse = !CollectionUtils.isEmpty(response)
					? response.stream().map(studentsProjection -> {
						StudentMinResponseDto responseDto = new StudentMinResponseDto();
						responseDto.setId(studentsProjection.getId());
						responseDto.setFirstName(studentsProjection.getFirstName());
						responseDto.setLastName(studentsProjection.getLastName());
						return responseDto;
					}).collect(Collectors.toList())
					: null;
			return studentDetailsResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * This is a feign call for getting student details by user name
	 *
	 *
	 * @return
	 */
	@Override
	public StudentDetailsMinResponseDto findStudentDetails(String studentId) {
		try {
			return studentsRepository.findStudentDetails(studentId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * This is a content service feign call for getting student's details
	 *
	 *
	 * @return
	 */
	@Override
	public List<BatchReceptionistRequestDto> findingStudentsDetails(String boardId, String schoolId, String branchId,
			String gradeId, String sectionId, List<String> studentIds) {
		try {
			return CollectionUtils.isEmpty(studentIds)
					? studentsRepository.gettingAllStudentsDetails(boardId, schoolId, branchId, gradeId, sectionId)
					: studentsRepository.gettingStudentsDetails(boardId, schoolId, branchId, gradeId, sectionId,
							studentIds);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	/**
	 * This is a feign call from Teacher-Service to find out the absentees list.
	 * {@code requestDto.getStudentIds()} is empty then the whole student will be
	 * list down.
	 * 
	 * @param requestDto
	 * @return
	 */
	@Override
	public List<AbsenteesResponseDto> getAllAbsenteesDetails(AbsenteesRequestDto request) {
		try {
			return !CollectionUtils.isEmpty(request.getStudentIds())
					? studentsRepository.absenteesDetails(request.getSchoolId(), request.getBranchId(),
							request.getGradeId(), request.getSectionId(), request.getStudentIds())
					: studentsRepository.absenteesDetailsWithoutIds(request.getSchoolId(), request.getBranchId(),
							request.getGradeId(), request.getSectionId());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("student.absentees.details.failed", null));
		}
	}

	/**
	 * This api is used for for getting studentId and fullName.
	 * 
	 * @param schoolId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	@Override
	public List<StudentsMinResponseDto> getAvidStudentsDetails(String schoolId, String gradeId, String sectionId,String branchId) {
		try {
			List<StudentsProjection> projectionList = studentsRepository.findAvidStudentDetailsByFilters(schoolId,
					gradeId, sectionId,branchId);
			log.info("list:"+projectionList.size());
			return projectionList.stream().map(projection -> {
				String fullName = projection.getFirstName() + " " + projection.getLastName();
				return new StudentsMinResponseDto(projection.getId(), fullName);
			}).collect(Collectors.toList());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.fetch.failed", null));
		}
	}

	@Override
	public Boolean getStudentProfileIRDetails(String studentId, StudentRequestIRDTO request) {
		log.info("Student Updation started...");

		if (StringUtils.isEmpty(studentId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("student.id.not.found", null));

		if (!studentsRepository.existsById(studentId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("student.not.found", null));

		try {
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			Students existingStudent = studentsRepository.getById(studentId);
			existingStudent.setIsPromoted(
					request.getIsPromoted() != null ? request.getIsPromoted() : existingStudent.getIsPromoted());
			existingStudent.setYearEndProcess(request.getYearEndProcess() != null ? request.getYearEndProcess()
					: existingStudent.getYearEndProcess());
			existingStudent
					.setAddress(request.getAddress() != null ? request.getAddress() : existingStudent.getAddress());
			existingStudent
					.setLearningGeneration(request.getLearningGeneration() != null ? request.getLearningGeneration()
							: existingStudent.getLearningGeneration());
			existingStudent.setGeographicalType(request.getGeographicalType() != null ? request.getGeographicalType()
					: existingStudent.getGeographicalType());
			existingStudent.setGradeLevel(
					request.getGradeLevel() != null ? request.getGradeLevel() : existingStudent.getGradeLevel());
			existingStudent.setDiagnosticTest(request.getDiagnosticTest() != null ? request.getDiagnosticTest()
					: existingStudent.getDiagnosticTest());
			existingStudent.setModifiedAt(currentTime);
			existingStudent.setLastModifiedBy(currentUser);
			existingStudent = studentsRepository.save(existingStudent);
			log.info("Student details updated successfully!");

			return true;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("student.details.update.failed", null));
		}
	}

	@Override
	public PaginatedResponse<StudentsResponseDto> getAllStudentSetionAndGradeBased(int pageNumber, int pageSize,
			String teacherId, String irStatus, String schoolId, String branchId, boolean sortOrder,String sortBy) {
		List<StudentsResponseDto> response = new ArrayList<>();
		Page<StudentsProjection> projectionList = null;
		log.info("getAllStudentSetionAndGradeBased method start.....");
		try {
			// Fetching all assigned grades of academic staff
			Long totalElements = 0L;
			Integer totalPages = 0;
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortBy));

			List<String> assignedGrades = assignTeacherRepo.getAllGradeIdsByTeacherId(teacherId);
			List<String> assignedSections = assignTeacherRepo.getAllSectionsByGradeAndTeacherId(teacherId);

			if (assignedGrades != null && assignedSections != null) {
				if (irStatus != null && !irStatus.isEmpty()) {
					projectionList = studentsRepository.findAllStudentsBysectionsAndGradesAndIrstatus(irStatus,assignedGrades,
							assignedSections, schoolId, branchId, pageable);
				} else {
					projectionList = studentsRepository.findAllStudentsBysectionsAndGrades(assignedGrades,
							assignedSections, schoolId, branchId, pageable);
				}
			} else {
				projectionList = studentsRepository.findAllStudents(pageable);
			}
			if (projectionList != null) {
				for (StudentsProjection studentsProjection : projectionList.getContent()) {
					StudentsResponseDto responseDto = studentMapper
							.mapStudentProjectionToResponseDto(studentsProjection);
					
					//get grade
					LMSResponse<GradesResponseDto> garedLMSResponse = mastersFeignClient.getGradesById(responseDto.getGradeId());
					GradesResponseDto gradeResponse = garedLMSResponse != null ? garedLMSResponse.getData() : null;
					if(gradeResponse != null) {
						responseDto.setGrade(gradeResponse.getGrade());				
					}
					
					//get section
					LMSResponse<SectionsResponseDto> sectionLmsResponse = responseDto.getSectionId() != null
							? mastersFeignClient.getSectionsById(responseDto.getSectionId())
							: null;
					SectionsResponseDto sectionResponse = sectionLmsResponse != null ? sectionLmsResponse.getData() : null;
					if(sectionResponse != null) {
						responseDto.setSection(sectionResponse.getSection());
					}					
					response.add(responseDto);
				}
			}
			log.info("getAllStudentSetionAndGradeBased method end.....");
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("failed.to.filter.students.detail", null));
		}
	}

//	@Override
//	public Boolean updateStudentEntityIrStatus() {
//		try {
//			Long currentTime = new Date().getTime();
//			String currentUser = jwtUtil.currentLoginUser();
//			Students existingStudent = studentsRepository.findByUserNameIgnoreCase(currentUser);
//			existingStudent.setModifiedAt(currentTime);
//			existingStudent.setLastModifiedBy(currentUser);
//			existingStudent.setIrStatus("ONBOARD");
//			studentsRepository.save(existingStudent);
//			log.info("Student details updated successfully!");
//			return true;
//		} catch (Exception e) {
//			log.error(ExceptionUtils.getStackTrace(e));
//			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
//					Translator.toLocale("student.details.update.failed", null));
//		}
	//}

	@Override
	public Boolean updateStudentEntityIrStatus(String studentId) {
	    try {
	        Long currentTime = new Date().getTime();
	        // String currentUser = jwtUtil.currentLoginUser();
	        Optional<Students> existingStudentOpt = studentsRepository.findById(studentId);
	        
	        if (existingStudentOpt.isPresent()) {
	        	int updated = studentsRepository.updateIrStatus(studentId);
	        	log.info("updated:"+updated);           
	        	log.info("Student details updated successfully for studentId: {}", studentId);
	            return true;
	        }else {
	            log.info("Student with studentId: {} not found", studentId);
	            return false;
	        }
	    } catch (Exception e) {
	        log.error("Error updating student details for studentId: {}", studentId, e);
	        return false;
	    }
	}
	
}
