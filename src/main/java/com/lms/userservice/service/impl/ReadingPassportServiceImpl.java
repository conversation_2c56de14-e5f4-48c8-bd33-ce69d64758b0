package com.lms.userservice.service.impl;

import java.util.Date;
import java.util.List;
import java.util.*;

import javax.validation.Valid;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.ReadingPassport;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.repository.ReadingPassportRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.request.dto.ReadingPassportRequest;
import com.lms.userservice.request.dto.ReadingPassportResponse;
import com.lms.userservice.service.ReadingPassportService;
import com.lms.userservice.util.JwtUtil;

import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class ReadingPassportServiceImpl implements ReadingPassportService {

	@Autowired
	ReadingPassportRepository readingPassportRespository;
	
	@Autowired
	private JwtUtil jwtUtil;
	
	@Autowired
	private UsersRepository usersRepository;

	
	private Long currentTime = new Date().getTime();
	
	@Override
	public ReadingPassportResponse createOrUpdateReadingPassport(String id,@Valid ReadingPassportRequest dto) {
		log.info("---Start of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
		ReadingPassportResponse response=new ReadingPassportResponse();
		try {
			if(StringUtils.isNotBlank(id)) {
            ReadingPassport rp=readingPassportRespository.findByUserId(id);
            if(rp!=null) {
            	//ReadingPassport updateEntity=new ReadingPassport();
            	rp.setLibrarian(dto.isLibrarian());
    			ObjectMapper mapper=new ObjectMapper();
    			String currentUser = jwtUtil.currentLoginUser();
    			rp.setModifiedAt(currentTime);
    			rp.setLastModifiedBy(currentUser);
    			String access = mapper.writeValueAsString(dto.getAccess());
    			rp.setAccess(access);
    			rp.setUserType(dto.getUserType());
    			rp.setSchoolId(dto.getSchoolId());
    			rp.setBranchId(dto.getBranchId());
    			readingPassportRespository.save(rp);
    			response.setId(rp.getId());
    			response.setUserId(id);
    			response.setUserType(rp.getUserType());
    			response.setLibrarian(dto.isLibrarian());
    			response.setAccess(access);
    			response.setSchoolId(rp.getSchoolId());
    			response.setBranchId(rp.getBranchId());
    			//response.setAccess(dto.getAccess());
			}
            else {
            	ReadingPassport entity=new ReadingPassport();
    			//Users users = usersRepository.findByUserName(JwtUtil.getUsername());
    			entity.setUserId(id);
    			entity.setUserType(dto.getUserType());
    			entity.setLibrarian(dto.isLibrarian());
    			ObjectMapper mapper=new ObjectMapper();
    			String access = mapper.writeValueAsString(dto.getAccess());
    			entity.setAccess(access);
    			entity.setSchoolId(dto.getSchoolId());
    			entity.setBranchId(dto.getBranchId());
    			readingPassportRespository.save(entity);
    			response.setId(entity.getId());
    			response.setUserId(id);
    			response.setUserType(dto.getUserType());
    			response.setLibrarian(dto.isLibrarian());
    			response.setAccess(access);
    			response.setSchoolId(dto.getSchoolId());
    			response.setBranchId(dto.getBranchId());
            }
			}
		}
		catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("readingpassport.response.failed", null));
		}
		return response;

	}

	@Override
	public ReadingPassportResponse getReadingPassport(String id) {
		log.info("---Start of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
		ReadingPassportResponse response=new ReadingPassportResponse();
		try {
			if(StringUtils.isNotBlank(id)) {
	         ReadingPassport getIdDetails=readingPassportRespository.findByUserId(id);
			 if(getIdDetails != null){
				 response.setId(getIdDetails.getId());
				 response.setUserId(getIdDetails.getUserId());
				 response.setUserType(getIdDetails.getUserType());
				 response.setLibrarian(getIdDetails.isLibrarian());
				 ObjectMapper objectMapper = new ObjectMapper();
				 String accessJsonString = getIdDetails.getAccess() != null ? objectMapper.writeValueAsString(getIdDetails.getAccess()) : "";				 
				 response.setAccess(accessJsonString);
				 response.setSchoolId(getIdDetails.getSchoolId());
				 response.setBranchId(getIdDetails.getBranchId());
			 }

			}
		}
		catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("readingpassport.response.failed", null));
		}
		return response;
	}

	@SuppressWarnings("all")
	@Override
	public List<ReadingPassportResponse> getAllAccessBySchoolIdAndBranchId(String schoolId, String branchId) {
		List<ReadingPassportResponse> responseList = new ArrayList<>();
		try {
			List<ReadingPassport> readingPassportList = readingPassportRespository.findBySchoolIdAndBranchId(schoolId,
					branchId);
			if (readingPassportList != null) {
				for (ReadingPassport readingPassport : readingPassportList) {
					ReadingPassportResponse response = new ReadingPassportResponse();
					response.setId(readingPassport.getId());
					response.setUserId(readingPassport.getUserId());
					response.setUserType(readingPassport.getUserType());
					response.setLibrarian(readingPassport.isLibrarian());
					ObjectMapper objectMapper = new ObjectMapper();
					String accessJsonString = readingPassport.getAccess() != null
							? objectMapper.writeValueAsString(readingPassport.getAccess())
							: "";
					response.setAccess(accessJsonString);
					response.setSchoolId(readingPassport.getSchoolId());
					response.setBranchId(readingPassport.getBranchId());
					responseList.add(response);
				}
			}
			return responseList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("readingpassport.response.failed", null));
		}

	}

}
