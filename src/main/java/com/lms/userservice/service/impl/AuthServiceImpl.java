package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.lms.userservice.entity.Users;
import com.lms.userservice.repository.UsersRepository;

/**
 * <AUTHOR>
 * The {@code AuthServiceImpl} implements {@code UserDetailsService}
 * <br>
 * {@code @Service} is a stereotypical annotation used for Service Layer
 * <br> {@code Slf4j} is a Logger annotation obtained from Lombok dependency for logging the requests and responses
 */

@Service("authService")
public class AuthServiceImpl implements UserDetailsService{

	    @Autowired
	    private UsersRepository repo;
	    
	    @Override
	    public UserDetails loadUserByUsername(String userName) throws UsernameNotFoundException {
	    	 Users user = repo.findByUserName(userName);
	    	 List<SimpleGrantedAuthority> authorities = new ArrayList<>();
	         return new org.springframework.security.core.userdetails.User(user.getUserName(), user.getPassword(), authorities);
	    }
	    
	    
	    
}
