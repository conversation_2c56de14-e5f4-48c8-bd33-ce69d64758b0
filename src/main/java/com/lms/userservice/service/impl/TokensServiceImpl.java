package com.lms.userservice.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Tokens;
import com.lms.userservice.entity.Users;
import com.lms.userservice.entity.UsersTokenMapping;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.RolesFeignDto;
import com.lms.userservice.mapper.TokenMapper;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.projection.TokenListProjection;
import com.lms.userservice.projection.TokenUserMappingProjection;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.TokensRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.repository.UsersTokenMappingRepository;
import com.lms.userservice.request.dto.TokensRequestDto;
import com.lms.userservice.response.dto.TokenDetailsResponseDto;
import com.lms.userservice.response.dto.TokenListResponseDto;
import com.lms.userservice.response.dto.TokensResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;
import com.lms.userservice.service.TokensService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("TokensService")
public class TokensServiceImpl implements TokensService {

	@Autowired
	private JwtUtil jwtUtil;

	private Long currentTime = new Date().getTime();

	@Autowired
	private TokensRepository tokensRepository;

	@Autowired
	private ModelMapper modelMapper;

	@Autowired
	private TokenMapper tokenMapper;

	@Autowired
	private SchoolRepository schoolRepository;

	@Autowired
	private BranchRepository branchRepository;

	@Autowired
	private UsersRepository userRepository;

	@Autowired
	private UsersTokenMappingRepository usersTokenMapRepo;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	/**
	 * Tokens will be created according to the {@code numberOfTokens}.
	 * <p>
	 * eg., {@code numberOfTokens} = 3 it'll create new 3 documents with unique
	 * tokenId
	 * 
	 * <p>
	 * if {@code multiUser} is false then {@code numberOfUsersPerToken} will set to
	 * 1
	 * 
	 * <p>
	 * {@code numberOfUsersPerToken} means how many users can use one tokenId
	 * 
	 * <p>
	 * check the role is exist if it is not throw error
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public List<TokensResponseDto> createTokens(TokensRequestDto request) {
		log.info("Tokens started to create");
		if (request.isMultiUser() && request.getNumberOfUsersPerToken() <= 0)
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("number.of.users", null));

		if (!schoolRepository.existsById(request.getSchoolId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (!branchRepository.existsBySchoolsIdAndDeletedAndId(request.getSchoolId(), false, request.getBranchId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		try {
			String currentUser = jwtUtil.currentLoginUser();
			List<TokensResponseDto> response = new ArrayList<>();

			Schools school = schoolRepository.getById(request.getSchoolId());
			Branches branch = branchRepository.getById(request.getBranchId());

			// according to the number of tokens will be created list of token id
			List<String> tokenIds = CommonUtilities.generateTokenId(request.getNumberOfTokens(), school.getCode());

			if (!tokenIds.isEmpty()) {
				tokenIds.forEach(item -> {
					Tokens tokens = modelMapper.map(request, Tokens.class);
					tokens.setToken(item);
					tokens.setBranches(branch);
					tokens.setSchools(school);
					tokens.setCreatedBy(currentUser);
					if (!request.isMultiUser()) {
						tokens.setNumberOfUsersPerToken(1);
					}
					tokens = tokensRepository.save(tokens);
					if (!StringUtils.isEmpty(tokens.getId())) {
						log.info("Token created successfully, mapping to the reponse");
						TokensResponseDto tokenResponse = tokenMapper.mapTokenEntityToResponse(tokens);
						RolesFeignDto roleDto = mastersFeignClient.getRolesById(tokenResponse.getRoleId()).getData();
						tokenResponse.setRole(roleDto.getRole());
						response.add(tokenResponse);
					}
				});
			}
			log.info("Tokens create completed");
			return response;
		} catch (Exception e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("token.create.failed", null));
		}
	}

	@Override
	public TokensResponseDto getTokensByToken(String token) {
		try {
			Tokens tokens = tokensRepository.findByToken(token);
			TokensResponseDto response = tokenMapper.mapTokenEntityToResponse(tokens);
			List<TokenUserMappingProjection> tokenUserDetails = usersTokenMapRepo
					.findAllUsersByTokenId(response.getId());
			RolesFeignDto roleDto = mastersFeignClient.getRolesById(response.getRoleId()).getData();
			response.setRole(roleDto.getRole());
			if (!tokenUserDetails.isEmpty()) {
				response.setTokenDetails(tokenMapper.mapTokenUserMappingToTokenDetailsDto(tokenUserDetails));
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("get.token.failed", null));
		}
	}

	/**
	 * @param id
	 * @return
	 */
	@Override
	public Boolean deleteTokenById(String id) {

		if (StringUtils.isEmpty(id)) {
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("token.id.not.found", null));
		}

		if (!tokensRepository.existsById(id)) {
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("token.not.found", null));
		}

		try {
			String currentUser = jwtUtil.currentLoginUser();
			Tokens tokens = tokensRepository.getById(id);
			tokens.setDeleted(true);
			tokens.setActive(false);
			tokens.setModifiedAt(currentTime);
			tokens.setLastModifiedBy(currentUser);
			tokens = tokensRepository.save(tokens);
			return tokens.isDeleted();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("delete.token.failed", null));
		}
	}

	/**
	 * During the self registration by the user will mapped to the given token if
	 * the {@code numberOfUsersPerToken} and {@code tokenUseCount} is equal then the
	 * token won't available to accept users.
	 * 
	 * @param token
	 * @return
	 */
	@Override
	public TokensResponseDto updateTokenDuringRegistration(String token, String userId) {
		if (!tokensRepository.existsByToken(token))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("token.not.found", null));

		if (!userRepository.existsById(userId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		Tokens tokens = tokensRepository.findByToken(token);
		LocalDate expiryDate = tokens.getExpiaryDate();

		if (expiryDate.isBefore(LocalDate.now(ZoneId.systemDefault()))) {
			throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("token.expired", null));
		}

		if (tokens.getNumberOfUsersPerToken().equals(tokens.getTokenUseCount())) {
			throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("token.usage.limit.exceeded", null));
		}

		try {
			String currentUser = jwtUtil.currentLoginUser();
			Users user = userRepository.getById(userId);
			Integer count = tokens.getTokenUseCount() == null ? 1 : (tokens.getTokenUseCount() + 1);
			List<UsersTokenMapping> tokenUserMappings = new ArrayList<>();

			// save user mapping to db
			UsersTokenMapping userMapping = new UsersTokenMapping();
			userMapping.setUsers(user);
			userMapping.setTokens(tokens);
			userMapping.setCreatedBy(currentUser);
			usersTokenMapRepo.save(userMapping);

			// set the mapping to the token
			if (!tokens.getUserTokenMappings().isEmpty()) {
				tokens.getUserTokenMappings().add(userMapping);
				tokens.setUserTokenMappings(tokens.getUserTokenMappings());
			} else {
				tokenUserMappings.add(userMapping);
				tokens.setUserTokenMappings(tokenUserMappings);
			}
			tokens.setTokenUseCount(count);
			tokens.setModifiedAt(currentTime);
			tokens.setLastModifiedBy(currentUser);
			tokens = tokensRepository.save(tokens);
			if (!tokens.getUserTokenMappings().isEmpty())
				log.info("User mapped with token");
			TokensResponseDto response = tokenMapper.mapTokenEntityToResponse(tokens);
			List<TokenUserMappingProjection> tokenUserDetails = usersTokenMapRepo
					.findAllUsersByTokenId(response.getId());
			RolesFeignDto roleDto = mastersFeignClient.getRolesById(response.getRoleId()).getData();
			response.setRole(roleDto.getRole());
			if (!tokenUserDetails.isEmpty()) {
				response.setTokenDetails(tokenMapper.mapTokenUserMappingToTokenDetailsDto(tokenUserDetails));
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			ExceptionUtils.printRootCauseStackTrace(e);
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("token.update.with.user.faild", null));
		}
	}

	/**
	 * Search by {@code tokenId}, filtering is enabled for below parameters, sort
	 * order by either ascending or descending for all fields.
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortOrder
	 * @param sortBy
	 * @return
	 */
	@Override
	public PaginatedResponse<TokenListResponseDto> getTokenByFilterSearchOrsorted(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String roleId, String userId, String branchId, String schoolId,
			String search, Boolean active) {
		try {
			List<TokenListResponseDto> response = new ArrayList<>();
			Long totalElements = 0L;
			Integer totalPages = 0;
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;
			String sortKey = FieldMappers.tokenApiFieldMapper(sortBy);
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(sortOrder ? Direction.ASC : Direction.DESC, sortKey));

			Page<TokenListProjection> paginations = tokensRepository.findTokenByPagination(roleId, userId, branchId,
					schoolId, searchFormat, pageable, active);
			if (!paginations.getContent().isEmpty()) {

				totalElements = paginations.getTotalElements();
				totalPages = paginations.getTotalPages();

				log.info("Taking the role's list and passing to the feign call");
				List<String> roleSet = paginations.getContent().stream().map(TokenListProjection::getRoleId)
						.collect(Collectors.toList());
				List<RolesFeignDto> rolesList = mastersFeignClient.getRolesByIds(roleSet).getData();

				paginations.getContent().forEach(pages -> {
					TokenListResponseDto responseDto = new TokenListResponseDto(pages);
					if (!rolesList.isEmpty()) {
						Optional<RolesFeignDto> roleDto = rolesList.stream()
								.filter(g -> g.getId().equals(responseDto.getRoleId())).findAny();
						if (roleDto.isPresent()) {
							log.info("Setting the matching value of role.");
							responseDto.setRole(roleDto.get().getRole());
						}
					}
					if (responseDto.getNoOfUsers() != null && responseDto.getNoOfUsers() > 0) {
						List<TokenUserMappingProjection> userDeatails = usersTokenMapRepo.findAllUsers(searchFormat,
								responseDto.getId());
						if (!userDeatails.isEmpty()) {
							responseDto.setUserDetails(tokenMapper.mapTokenUserMappingToTokenDetailsDto(userDeatails));
						}
					}
					response.add(responseDto);
				});
			}
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("get.all.token.failed", null));
		}
	}

	/**
	 * Get token use count for students, teachers, coordinators, principal
	 */
	@Override
	public UsersCountResponseDto getAllCounts(String tokenId, String roleId, String schoolId, String branchId,
			String userId) {
		UsersCountResponseDto response = modelMapper.map(
				usersTokenMapRepo.getTeachersTokenCount(tokenId, roleId, schoolId, branchId, userId),
				UsersCountResponseDto.class);
		response.setStudentCount(usersTokenMapRepo.getStudentsTokenCount(tokenId, roleId, schoolId, branchId, userId));
		return response;
	}

	@Override
	public boolean updateActiveField(String id, boolean active) {
		if (!tokensRepository.existsById(id)) {
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("token.not.found", null));
		}

		try {
			Boolean response = false;
			Tokens tokens = tokensRepository.getById(id);
			tokens.setActive(active);
			tokens.setModifiedAt(currentTime);
			tokens.setLastModifiedBy(jwtUtil.currentLoginUser());
			tokens = tokensRepository.save(tokens);
			response = (tokens.isActive() == active);
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.success", null));
		}
	}

	/**
	 * Get the user detail, who used the token
	 * 
	 * @param search  (by userName, firstName, lastName, email and phone number)
	 * @param tokenId (filter)
	 * @return
	 */
	@Override
	public List<TokenDetailsResponseDto> getUsedUserId(String search, String tokenId) {
		try {
			String searchFormat = !StringUtils.isEmpty(search) ? search.toLowerCase() : null;
			List<TokenDetailsResponseDto> response = new ArrayList<>();
			List<TokenUserMappingProjection> userDeatails = usersTokenMapRepo.findAllUsers(searchFormat, tokenId);
			if (userDeatails != null && !userDeatails.isEmpty()) {
				response = tokenMapper.mapTokenUserMappingToTokenDetailsDto(userDeatails);
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("used.user.id.listed.failed", null));
		}
	}

	/**
	 * Get the last modification done on the table tokens return format example will
	 * be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return tokensRepository.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

}
