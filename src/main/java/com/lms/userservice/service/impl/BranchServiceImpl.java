package com.lms.userservice.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.BranchCommunication;
import com.lms.userservice.entity.BranchPlanMappings;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.enums.CommunicationAction;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.enums.OperationType;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.content.ContentFeignClient;
import com.lms.userservice.feign.master.BoardsResponseDto;
import com.lms.userservice.feign.master.CitiesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.PlansResponseDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.feign.student.StudentFeignClient;
import com.lms.userservice.feign.teacher.TeacherFeignClient;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.projection.BranchesProjection;
import com.lms.userservice.projection.TeacherCountProjection;
import com.lms.userservice.repository.BranchCommunicationRepository;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.BranchRepository;
import com.lms.userservice.repository.SchoolRepository;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.request.dto.BranchCommunicationRequestDto;
import com.lms.userservice.request.dto.BranchPlansRequestDto;
import com.lms.userservice.request.dto.BranchRequestDto;
import com.lms.userservice.request.dto.CommunicationRequestDto;
import com.lms.userservice.response.dto.BranchCommunicationResponseDto;
import com.lms.userservice.response.dto.BranchMinResponseDto;
import com.lms.userservice.response.dto.BranchPlanResponseDto;
import com.lms.userservice.response.dto.BranchResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;
import com.lms.userservice.response.dto.SchoolCountDetailsResponseDto;
import com.lms.userservice.response.dto.SchoolDetailsResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;
import com.lms.userservice.service.BranchService;
import com.lms.userservice.util.FieldMappers;
import com.lms.userservice.util.JwtUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BranchServiceImpl implements BranchService {

	@Autowired
	private JwtUtil jwtUtil;

	private Long currentTime = new Date().getTime();

	@Autowired
	private BranchRepository branchRepo;

	@Autowired
	private SchoolRepository schoolRepo;

	@Autowired
	private ModelMapper modelMapper;

	@Autowired
	private BranchPlanMappingsRepository branchPlanMappingsRepo;

	@Autowired
	private MastersFeignClient masterFeign;

	@Autowired
	private ContentFeignClient contentFeign;

	@Autowired
	private StudentFeignClient studentFeign;

	@Autowired
	private TeacherFeignClient teacherFeign;

	@Autowired
	private StudentsRepository studentsRepository;

	@Autowired
	private TeacherRepository teacherRepository;

	@Autowired
	private BranchCommunicationRepository branchCommunicationRepository;

	@Autowired
	private UsersRepository usersRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;

	/**
	 * Create Branch
	 *
	 * @param branchRequest The Branch Request DTO Object
	 * @return
	 */
	@Override
	public BranchResponseDto addSchoolBranch(BranchRequestDto branchRequest) {
		log.info("Branches registration started...");
		if (!schoolRepo.existsByIdAndDeletedAndActive(branchRequest.getSchoolId(), false, true))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (branchRepo.existsByNameAndSchoolsIdAndDeleted(branchRequest.getName(), branchRequest.getSchoolId(), false))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("branch.already.exist", null) + " by this name.");

		if (branchRepo.existsByPocEmailAndSchoolsIdAndDeleted(branchRequest.getPocEmail(), branchRequest.getSchoolId(),
				false))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("branch.already.exist", null) + " by this email.");

		try {
			if (masterFeign.getAllBoardById(Collections.singletonList(branchRequest.getBoardId())).getData()
					.isEmpty()) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("board.does.not.exist", null));
			}

			if (masterFeign.getCityById(branchRequest.getCityId()) == null) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("city.does.not.exist", null));
			}

			String currentUser = jwtUtil.currentLoginUser();
			BranchResponseDto branchResponse = new BranchResponseDto();
			Schools schools = schoolRepo.getById(branchRequest.getSchoolId());
			Branches branches = modelMapper.map(branchRequest, Branches.class);
			branches.setSchools(schools);
			branches.setCreatedBy(currentUser);
			branches.setBranchCode(branchRequest.getBranchCode());	
			branches.setTestBranch(branchRequest.getTestBranch());		
			branches = branchRepo.save(branches);
			if (!StringUtils.isEmpty(branches.getId())) {
				branchResponse = new BranchResponseDto(branchRepo.findBranchById(branches.getId()));
			}
			branchResponse.setPlans(setBranchPlansFromDto(branchRequest.getPlans(), branches));
			CitiesResponseDto city = masterFeign.getCityById(branches.getCityId()).getData();
			branchResponse.setCityName(city.getCity());
			branchResponse.setBranchCode(branchRequest.getBranchCode());			
			log.info("Branches registered successfully");
			return branchResponse;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("branch.create.failed", null));
		}
	}

	/**
	 * Update Branch
	 *
	 * @param branchId      The Branch ID
	 * @param branchRequest The Branch Request DTO Object
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public BranchResponseDto updateSchoolBranch(String branchId, BranchRequestDto branchRequest) {
		log.info("Branches updation started...");
		if (StringUtils.isEmpty(branchId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.id.not.found", null));

		if (!schoolRepo.existsByIdAndDeletedAndActive(branchRequest.getSchoolId(), false, true))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

		if (!branchRepo.existsById(branchId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		if (branchRepo.existsByNameAndSchoolsIdAndDeletedAndIdNot(branchRequest.getName(), branchRequest.getSchoolId(),
				false, branchId))
			throw new USException(ErrorCodes.CONFLICT, Translator.toLocale("branch.already.exist", null));

		if (branchRepo.existsByPocEmailAndSchoolsIdAndDeletedAndIdNot(branchRequest.getPocEmail(),
				branchRequest.getSchoolId(), false, branchId))
			throw new USException(ErrorCodes.CONFLICT,
					Translator.toLocale("branch.already.exist", null) + " by this email.");

		try {
			if (masterFeign.getAllBoardById(Collections.singletonList(branchRequest.getBoardId())).getData()
					.isEmpty()) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("board.does.not.exist", null));
			}

			if (masterFeign.getCityById(branchRequest.getCityId()) == null) {
				throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("city.does.not.exist", null));
			}

			String currentUser = jwtUtil.currentLoginUser();
			BranchResponseDto branchResponse = new BranchResponseDto();
			Branches branches = branchRepo.getById(branchId);
			Schools schools = schoolRepo.getById(branchRequest.getSchoolId());
			modelMapper.map(branchRequest, branches);
			branches.setSchools(schools);
			branches.setBranchCode(branchRequest.getBranchCode());
			branches.setModifiedAt(currentTime);
			branches.setLastModifiedBy(currentUser);
			branches.setTestBranch(branchRequest.getTestBranch());
			branches = branchRepo.save(branches);
			if (!StringUtils.isEmpty(branches.getId())) {
				branchResponse = new BranchResponseDto(branchRepo.findBranchById(branches.getId()));
			}
			branchResponse.setPlans(setBranchPlansFromDto(branchRequest.getPlans(), branches));
			CitiesResponseDto city = masterFeign.getCityById(branches.getCityId()).getData();
			branchResponse.setCityName(city.getCity());
			branchResponse.setBranchCode(branchRequest.getBranchCode());
			branchResponse.setTestBranch(branchRequest.getTestBranch());
			log.info("Branches updated successfully");
			return branchResponse;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("branch.update.failed", null));
		}
	}

	/**
	 * Get Branches By ID
	 *
	 * @param branchId The Branch ID String
	 * @return Branches by Branch ID
	 */
	@Override
	public BranchResponseDto getSchoolBranchesById(String branchId) {
		log.info("Branch Details fetch started...");
		if (StringUtils.isEmpty(branchId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.id.not.found", null));

		if (!branchRepo.existsById(branchId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		try {
			log.info("GET Detail Branches fetched successfully");
			Branches id=branchRepo.getByIds(branchId);
			BranchResponseDto response = new BranchResponseDto(branchRepo.findBranchById(branchId));
			Branches branch = branchRepo.getById(branchId);
            response.setBranchCode(id.getBranchCode());
			List<BranchPlanResponseDto> branchPlanResponse = branchPlanMappingsRepo
					.findAllByBranchesAndDeleted(branch, false).stream()
					.map(plan -> modelMapper.map(plan, BranchPlanResponseDto.class)).collect(Collectors.toList());
			response.setPlans(branchPlanResponse);

			// setting the city name
			CitiesResponseDto city = masterFeign.getCityById(response.getCityId()).getData();
			response.setCityName(city.getCity());

			// setting the board name
			if (!StringUtils.isEmpty(branch.getBoardId())) {
				LMSResponse<BoardsResponseDto> masterResponse = masterFeign.getBoardsById(branch.getBoardId());
				if (!ObjectUtils.isEmpty(masterResponse))
					response.setBoard(masterResponse.getData().getBoard());
			}

			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.get.by.id.failed", null));
		}
	}

	/**
	 * Get All Branches by Pagination for SUPER_ADMIN, SCHOOL_ADMIN or MANAGEMENT.
	 * If the request param userId has data it'll be either SCHOOL_ADMIN or
	 * MANAGEMENT.
	 *
	 * @return Branches using Pagination
	 */
	@Override
	public PaginatedResponse<BranchResponseDto> getAllBranchesByPage(int pageNumber, int pageSize, String sortBy,
			boolean order, String search, String cityId, String locality, String boardId, String branchId,
			String schoolId, String userId, Boolean active) {
		log.info("Branch pagination fetch started...");
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			log.info("Branch pagination in progress...");
			String sortField = FieldMappers.branchApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			List<BranchResponseDto> response = new ArrayList<>();
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(order ? Direction.ASC : Direction.DESC, sortField));
			Page<BranchesProjection> pagedBranches = StringUtils.isEmpty(userId)
					? branchRepo.findAllBranchesByPagination(cityId, locality, boardId, searchFormat, branchId,
							schoolId, pageable, active)
					: branchRepo.forSchoolAdminFindAllBranchesByPagination(cityId, locality, boardId, searchFormat,
							branchId, schoolId, userId, pageable, active);

			if (!CollectionUtils.isEmpty(pagedBranches.getContent())) {
				log.info("Branch details listed, and setting to the response.");
				totalElements = pagedBranches.getTotalElements();
				totalPages = pagedBranches.getTotalPages();

				// feign call to master-service for board
				List<String> listOfBoard = pagedBranches.getContent().stream().map(BranchesProjection::getBoardId)
						.filter(item -> !StringUtils.isEmpty(item)).distinct().collect(Collectors.toList());
				List<BoardsResponseDto> boardResponse = new ArrayList<>();
				if (!CollectionUtils.isEmpty(listOfBoard)) {
					LMSResponse<List<BoardsResponseDto>> boardFeign = masterFeign.getAllBoardById(listOfBoard);
					if (!ObjectUtils.isEmpty(boardFeign) && !CollectionUtils.isEmpty(boardFeign.getData()))
						boardResponse = boardFeign.getData();
				}

				// feign call to master-service for city
				List<String> listOfcity = pagedBranches.getContent().stream().map(BranchesProjection::getCityId)
						.filter(item -> !StringUtils.isEmpty(item)).distinct().collect(Collectors.toList());
				List<CitiesResponseDto> cityResponse = new ArrayList<>();
				if (!CollectionUtils.isEmpty(listOfcity)) {
					LMSResponse<List<CitiesResponseDto>> cityFeign = masterFeign.getAllCitiesById(listOfcity);
					if (!ObjectUtils.isEmpty(cityFeign) && !CollectionUtils.isEmpty(cityFeign.getData()))
						cityResponse = cityFeign.getData();
				}

				for (BranchesProjection projection : pagedBranches.getContent()) {
					BranchResponseDto responseDto = new BranchResponseDto(projection);

					// setting the board from feign to response JsonProperties
					Optional<BoardsResponseDto> boardDto = boardResponse.stream()
							.filter(board -> board.getId().contains(projection.getBoardId())).findAny();
					if (boardDto.isPresent())
						responseDto.setBoard(boardDto.get().getBoard());

					// setting the city from feign to response JsonProperties
					Optional<CitiesResponseDto> cityDto = cityResponse.stream()
							.filter(city -> city.getId().contains(projection.getCityId())).findAny();
					if (cityDto.isPresent())
						responseDto.setCityName(cityDto.get().getCity());

					// setting the board_plan to response JsonProperties
					List<BranchPlanResponseDto> branchPlans = branchPlanMappingsRepo
							.findPlansByBranchId(responseDto.getId());
					responseDto.setPlans(branchPlans);

					response.add(responseDto);
				}
			}

			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("branch.get.all.failed", null));
		}
	}

	/**
	 * Set Branch Plans
	 *
	 * @param plans    The branch plans request DTO list
	 * @param branches The Branches entity
	 * @return
	 */
	private List<BranchPlanResponseDto> setBranchPlansFromDto(List<BranchPlansRequestDto> plans, Branches branches) {

		List<BranchPlanResponseDto> response = new ArrayList<>();
		if (!CollectionUtils.isEmpty(branchPlanMappingsRepo.findByBranches(branches))) {
			branchPlanMappingsRepo.deleteByBranches(branches);
		}

		plans.forEach(plan -> {
			BranchPlanMappings mapping = modelMapper.map(plan, BranchPlanMappings.class);
			mapping.setBranches(branches);
			mapping = branchPlanMappingsRepo.save(mapping);
			response.add(modelMapper.map(mapping, BranchPlanResponseDto.class));
		});
		return response;
	}

	/**
	 * Delete Branch (mark Branch as Deleted)
	 *
	 * @param branchId The Branch ID
	 * @return boolean
	 */
	@Override
	public Boolean removeSchoolBranch(String branchId) {
		if (!branchRepo.existsById(branchId)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));
		}

		try {
			String currentUser = jwtUtil.currentLoginUser();
			Branches branches = branchRepo.getById(branchId);
			branches.setModifiedAt(currentTime);
			branches.setDeleted(true);
			branches.setActive(false);
			branches.setLastModifiedBy(currentUser);
			branches = branchRepo.save(branches);
			if (branches.isDeleted()) {
				log.info("Branch marked as deleted");
			}
			return branches.isDeleted();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("branch.delete.failed", null));
		}
	}

	@Override
	public List<BranchesMinDataResponseDto> getAllBranches(String search, String schoolId, String userId) {
		try {
			List<BranchesMinDataResponseDto> responseList = new ArrayList<>();
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			List<BranchesProjection> projectionList = StringUtils.isEmpty(userId)
					? branchRepo.getBranches(searchFormat, schoolId)
					: branchRepo.getBranchesForSchoolAdmin(searchFormat, schoolId, userId);

			projectionList.forEach(projection -> responseList.add(
					new BranchesMinDataResponseDto(projection.getId(), projection.getName(), projection.getActive())));

			return responseList;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.get.all.plans.failed", null));
		}
	}

	@Override
	public Boolean updateActiveField(String id, boolean active) {
		if (!branchRepo.existsById(id))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		try {
			Branches branch = branchRepo.getById(id);
			Schools parentSchool = branch.getSchools();

			// Check if parent school is active
			if (active && !parentSchool.isActive())
				throw new USException(ErrorCodes.BAD_REQUEST,
						Translator.toLocale("branch.activate.parent.not.active", null));

			branch.setActive(active);
			branch.setModifiedAt(currentTime);
			branch.setLastModifiedBy(jwtUtil.currentLoginUser());
			branch = branchRepo.save(branch);
			return branch.isActive() == active;

		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.failed", null));
		}
	}

	@Override
	public List<BranchesMinDataResponseDto> getAllBranchMappingsForPlan(String planId) {
		try {
			return branchRepo.getAllBranchesByPlanId(planId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("get.all.branches.mapped.with.plans.failed", null));
		}
	}

	@Override
	public Boolean deleteAllBranchMappingsForPlan(String planId) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			branchPlanMappingsRepo.softDeleteByPlanId(currentUser, currentTime, planId);
			return true;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("delete.all.branches.mapped.with.plans.failed", null));
		}
	}

	@Override
	public Boolean updateActiveFieldOfAllBranchMappingsForPlan(String planId, boolean active) {
		try {
			String currentUser = jwtUtil.currentLoginUser();
			branchPlanMappingsRepo.updateActiveByPlanId(currentUser, currentTime, planId, active);
			return true;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("update.all.branches.mapped.with.plans.failed", null));
		}
	}

	/**
	 * Return the mapping tables related to the branch, check the count, if the
	 * count==0 return the default message or show the branch has mapping as in a
	 * message format.
	 *
	 * currently mapping entities :: branch_plan_mappings, grade_section_mapping,
	 * student, teacher, token, users_institution_mapping
	 *
	 * @param id
	 * @return
	 */
	@Override
	public String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType) {
		if (!branchRepo.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));
		}

		try {
			boolean isExits = false;
			String message = "There is no mapping related to this branch currently.";
			Branches branches = branchRepo.getById(id);
			List<Long> mappingCounts = branchRepo.findAllMappingCountOfBranch(id);
			if (!mappingCounts.isEmpty()) {
				isExits = mappingCounts.stream().anyMatch(count -> count > 0);
				if (isExits) {
					if (OperationType.DELETE == OperationType.valueOf(operationType)) {
						message = "Some data are connected with this branch. Deleting may cause some loss of data. "
								+ "Do you still want to delete the Branch, " + branches.getName();
					} else {
						message = "Some data are connected with this branch. "
								+ "Changing the active status may cause some constrain violation issues. "
								+ "Do you still want to change the active status of the branch, " + branches.getName();
					}
				} else {
					return message;
				}
			}
			return message;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("something.went.wrong", null));
		}
	}

	/**
	 * Confirm Branch Mapped data by Plan Id before delete
	 *
	 * @param planId
	 * @return
	 */
	@Override
	public boolean checkPlanHasAnyMapping(String planId) {
		try {
			Long planCount = branchRepo.findCountOfBranchByPlanId(planId);
			return planCount > 0;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.NO_CONTENT, Translator.toLocale("mapping.api.failed", null));
		}
	}

	/**
	 * Get the last modification done on the table branches return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 *
	 * @return
	 */
	@Override
	public String getLastModifiedAt() {
		try {
			return branchRepo.findLastModifiedAt();
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("last.modified.time.fetch.failed", null));
		}
	}

	/**
	 * Feign functionality for the CONTENT-SERVICE's quiz release
	 *
	 * @param ids
	 * @return
	 */
	@Override
	public List<BranchResponseDto> getAllBranchesByIds(List<String> ids) {
		try {
			List<BranchResponseDto> branchGetAllPageResponse = new ArrayList<>();
			List<BranchesProjection> branchesProjectionList = branchRepo.findAllBranchesByIds(ids);
			if (!branchesProjectionList.isEmpty()) {
				branchesProjectionList.forEach(projection -> {
					BranchResponseDto responseDto = new BranchResponseDto(projection);
					branchGetAllPageResponse.add(responseDto);
				});
			}
			return branchGetAllPageResponse;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branches.fetch.all.failed", null));
		}
	}

	@Override
	public Integer getMaxQuizRelease(String id) {
		if (!branchRepo.existsById(id)) {
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));
		}

		try {
			return branchRepo.getMaxQuizRelease(id);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.get.max-quiz-release.success", null));
		}
	}

	/**
	 * Confirmation API before active/de-active
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	@Override
	public ConfirmationApiResponseDto confirmationAPI(String id, String operationType) {
		if (!branchRepo.existsByIdAndDeleted(id, false))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("user.not.found", null));

		try {
			boolean isExits = false;
			String message = null;
			List<Long> userCountList = branchRepo.findUsages(id);

			LMSResponse<List<Long>> contentResponse = contentFeign.branchIds(id);
			List<Long> contentCountList = (contentResponse != null) ? contentResponse.getData() : null;

			LMSResponse<List<Long>> studntServiceResponse = studentFeign.branchIds(id);
			List<Long> studntServiceCountList = (studntServiceResponse != null) ? studntServiceResponse.getData()
					: null;

			LMSResponse<List<Long>> teacherServiceResponse = teacherFeign.branchIds(id);
			List<Long> teacherServiceCountList = (teacherServiceResponse != null) ? teacherServiceResponse.getData()
					: null;

			Long userCount = !CollectionUtils.isEmpty(userCountList)
					? userCountList.stream().mapToLong(Long::longValue).sum()
					: 0;

			Long contentCount = !CollectionUtils.isEmpty(contentCountList)
					? contentCountList.stream().mapToLong(Long::longValue).sum()
					: 0;
			Long studntServiceCount = !CollectionUtils.isEmpty(studntServiceCountList)
					? studntServiceCountList.stream().mapToLong(Long::longValue).sum()
					: 0;
			Long teacherServiceCount = !CollectionUtils.isEmpty(teacherServiceCountList)
					? teacherServiceCountList.stream().mapToLong(Long::longValue).sum()
					: 0;
			Long totalCount = userCount + contentCount + studntServiceCount + teacherServiceCount;

			if (totalCount > 0) {
				isExits = true;
				message = Translator.toLocale("confirmation.api.permission.denied", null);
			} else {
				message = (OperationType.DELETE == OperationType.valueOf(operationType))
						? Translator.toLocale("confirmation.api.delete", null)
						: Translator.toLocale("confirmation.api.toggle.active", null);
			}

			return new ConfirmationApiResponseDto(isExits, message);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("confirmation.api.failed", null));
		}
	}

	/**
	 * Coordinator, Principal, Teacher, Student Count's
	 *
	 */
	@Override
	public UsersCountResponseDto countByPersona(String schoolId, String branchId) {
		try {
			if (!branchRepo.existsById(branchId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

			if (!schoolRepo.existsById(schoolId))
				throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("school.not.found", null));

			Long studentCount = 0L;
			Long teacherCount = 0L;
			Long coordinatorCount = 0L;
			Long principalCount = 0L;

			studentCount = studentsRepository.findStudentCountWithSchoolAndBranch(schoolId, branchId);
			TeacherCountProjection staffProfileCount = teacherRepository.findAcademicStaffProfileCount(schoolId,
					branchId);

			if (staffProfileCount != null) {
				teacherCount = staffProfileCount.getTeacherCount();
				coordinatorCount = staffProfileCount.getCoordinatorCount();
				principalCount = staffProfileCount.getPrincipalCount();
			}

			return new UsersCountResponseDto(studentCount, teacherCount, coordinatorCount, principalCount);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.count.failed", null));
		}
	}

	@Override
	public BranchesMinDataResponseDto getBranchesById(String id) {
		try {
			BranchesMinDataResponseDto response = null;
			BranchesProjection projection = branchRepo.findBrancheById(id);
			if (projection != null) {
				response = new BranchesMinDataResponseDto(projection.getId(), projection.getName(),
						projection.getActive());
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.get.by.id.failed", null));
		}
	}

	/**
	 * Feign call to master-service for Dash-board, get schools by board
	 *
	 */
	@Override
	public List<SchoolCountDetailsResponseDto> getSchoolsDetails(List<String> boardIds) {
		try {
			List<SchoolCountDetailsResponseDto> response = new ArrayList<>();
			if (!CollectionUtils.isEmpty(boardIds)) {
				for (String boardId : boardIds) {
					List<BranchesProjection> projection = branchRepo.getAllSchoolDetails(boardId);
					if (!CollectionUtils.isEmpty(projection)) {
						List<SchoolDetailsResponseDto> schoolResponse = projection.stream()
								.map(SchoolDetailsResponseDto::new).collect(Collectors.toList());
						if (!CollectionUtils.isEmpty(schoolResponse)) {
							List<String> cityIds = schoolResponse != null
									? schoolResponse.stream().map(SchoolDetailsResponseDto::getCityId).distinct()
											.collect(Collectors.toList())
									: null;

							// master feign call for city name
							LMSResponse<List<CitiesResponseDto>> masterLmsResponse = cityIds != null
									? masterFeign.getAllCitiesById(cityIds)
									: null;
							List<CitiesResponseDto> cities = masterLmsResponse != null ? masterLmsResponse.getData()
									: null;

							if (!CollectionUtils.isEmpty(cities)) {
								schoolResponse.forEach(item -> {
									CitiesResponseDto city = cities.stream()
											.filter(cDto -> cDto.getId().equals(item.getCityId())).findFirst()
											.orElse(null);
									item.setLocality(
											city != null && !StringUtils.isBlank(city.getCity()) ? city.getCity()
													: null);
								});
							}
							response.add(
									new SchoolCountDetailsResponseDto(boardId, schoolResponse.size(), schoolResponse));
						}
					}
				}
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("school.details.failed", null));
		}
	}

	/**
	 * This API is used for to get Top 5 Branched based on no of students.
	 * 
	 * @return
	 */
	@Override
	public List<BranchMinResponseDto> getTopBranchesBasedOnStudent() {
		try {
			List<BranchesProjection> projections = branchRepo.findTopFiveBranchesByStudentCount();
			List<BranchMinResponseDto> response = null;

			if (!CollectionUtils.isEmpty(projections)) {
				response = projections.stream().map(BranchMinResponseDto::new).collect(Collectors.toList());

				// extracting the master id for feign calls
				List<String> boardIds = response.stream().map(BranchMinResponseDto::getBoardId).distinct()
						.collect(Collectors.toList());
				List<String> cityIds = response.stream().map(BranchMinResponseDto::getCityId).distinct()
						.collect(Collectors.toList());
				List<String> planIds = response.stream().map(BranchMinResponseDto::getPlanId).distinct()
						.collect(Collectors.toList());

				// master-feign calls
				LMSResponse<List<BoardsResponseDto>> boardLMSResponse = masterFeign.getAllBoardById(boardIds);
				List<BoardsResponseDto> boards = Optional.ofNullable(boardLMSResponse).map(LMSResponse::getData)
						.orElse(Collections.emptyList());

				LMSResponse<List<CitiesResponseDto>> citiLMSResponse = masterFeign.getAllCitiesById(cityIds);
				List<CitiesResponseDto> cities = Optional.ofNullable(citiLMSResponse).map(LMSResponse::getData)
						.orElse(Collections.emptyList());

				LMSResponse<List<PlansResponseDto>> planLMSResponse = masterFeign.getAllPlansByIds(planIds);
				List<PlansResponseDto> plans = Optional.ofNullable(planLMSResponse).map(LMSResponse::getData)
						.orElse(Collections.emptyList());

				// setting the master data
				response.forEach(item -> {
					if (!CollectionUtils.isEmpty(boards)) {
						BoardsResponseDto boardDto = boards.stream()
								.filter(bDto -> bDto.getId().equals(item.getBoardId())).findFirst().orElse(null);
						if (boardDto != null)
							item.setBoard(boardDto.getBoard());
					}

					if (!CollectionUtils.isEmpty(cities)) {
						CitiesResponseDto cityDto = cities.stream()
								.filter(cDto -> cDto.getId().equals(item.getCityId())).findFirst().orElse(null);
						if (cityDto != null)
							item.setCityName(cityDto.getCity());
					}

					if (!CollectionUtils.isEmpty(plans)) {
						PlansResponseDto planDto = plans.stream().filter(pDto -> pDto.getId().equals(item.getPlanId()))
								.findFirst().orElse(null);
						if (planDto != null)
							item.setPlan(planDto.getPlan());
					}
				});
			}
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.top.5.retrieve.failed", null));
		}
	}

	/**
	 * This api is used for student service to get the plan.
	 * 
	 * @param boardId
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	@Override
	public String getPlanByBranchSchool(String boardId, String branchId, String schoolId) {
		try {
			return branchRepo.getByPlanId(boardId, branchId, schoolId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.get.all.plans.failed", null));
		}
	}

	/**
	 * Add Branches and Communication Actions
	 * 
	 * @param branches The BranchCommunicationRequestDto DTO Request
	 * @return
	 */
	@Override
	public List<BranchCommunicationResponseDto> addBranchCommunication(BranchCommunicationRequestDto branchRequest) {
		if (StringUtils.isEmpty(branchRequest.getBranchId()))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.id.not.found", null));

		if (!branchRepo.existsById(branchRequest.getBranchId()))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		try {
			Branches branches = branchRepo.getById(branchRequest.getBranchId());
			return saveTheCommunicationToBranch(branchRequest, branches);
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("branch.communication.create.failed", null));
		}
	}

	/**
	 * Add Branches and Communication Actions
	 * 
	 * @param branches The BranchCommunicationRequestDto DTO Request
	 * @return
	 */
	private List<BranchCommunicationResponseDto> saveTheCommunicationToBranch(
			BranchCommunicationRequestDto branchRequest, Branches branches) {
		try {
			List<BranchCommunicationResponseDto> response = new ArrayList<>();
			String currentUser = jwtUtil.currentLoginUser();

			// checking the request
			verifyTheCommunication(branchRequest, branches);

			if (branchCommunicationRepository.findActiveStatus(branches.getId())) {
				List<BranchCommunication> branchCommunications = branchCommunicationRepository
						.findBybranchesId(branches.getId());
				branchCommunications.forEach(communication -> {
					String id = communication.getId();
					branchCommunicationRepository.deleteById(id);
				});
			}
			if (branchRequest.getCommunication() != null
					&& !CollectionUtils.isEmpty(branchRequest.getCommunication())) {
				for (CommunicationRequestDto communication : branchRequest.getCommunication()) {
					BranchCommunicationResponseDto responseDto = new BranchCommunicationResponseDto();
					BranchCommunication branchCommunication = modelMapper.map(communication, BranchCommunication.class);
					branchCommunication.setBranches(branches);
					branchCommunication.setCreatedBy(currentUser);
					branchCommunication = branchCommunicationRepository.save(branchCommunication);
					if (!StringUtils.isEmpty(branchCommunication.getId())) {
						responseDto = new BranchCommunicationResponseDto(
								branchCommunicationRepository.getCommunicationDetails(branchCommunication.getId()));
					}
					response.add(responseDto);
				}
			}
			return response;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		}
	}

	private void verifyTheCommunication(BranchCommunicationRequestDto branchRequest, Branches branches) {
		try {
			// checking request
			for (CommunicationRequestDto item : branchRequest.getCommunication()) {
				if (!item.isSms() && !item.isEmail()) {
					throw new USException(ErrorCodes.BAD_REQUEST,
							Translator.toLocale("branch.communication.sms.email.failed", null));
				}
				if (!(CommunicationAction.QUIZ_RELEASE.equals(item.getCommunicationAction())
						|| CommunicationAction.QUIZ_RESULT.equals(item.getCommunicationAction())
						|| CommunicationAction.SHARE_ID.equals(item.getCommunicationAction())
						|| CommunicationAction.UPDATE_PASSWORD.equals(item.getCommunicationAction())
						|| CommunicationAction.UPDATE_PROFILE.equals(item.getCommunicationAction())
						|| CommunicationAction.USER_CREATION.equals(item.getCommunicationAction()))) {
					throw new USException(ErrorCodes.BAD_REQUEST,
							Translator.toLocale("branch.communication.enum.failed", null));
				}
			}
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());
		}
	}

	/**
	 * Update Branches and Communication Actions
	 * 
	 * @param branches The BranchCommunicationRequestDto DTO Request
	 * @return
	 */
	@Override
	@SuppressWarnings("all")
	public List<BranchCommunicationResponseDto> editBranchCommunication(String branchId,
			BranchCommunicationRequestDto branchRequest) {
		if (StringUtils.isEmpty(branchId))
			throw new USException(ErrorCodes.BAD_REQUEST, Translator.toLocale("branch.id.not.found", null));

		if (!branchRepo.existsById(branchId))
			throw new USException(ErrorCodes.NOT_FOUND, Translator.toLocale("branch.not.found", null));

		try {
			List<BranchCommunicationResponseDto> response = new ArrayList<>();
			Long currentTime = new Date().getTime();
			String currentUser = jwtUtil.currentLoginUser();
			Branches branches = branchRepo.getById(branchId);

			// checking the request
			verifyTheCommunication(branchRequest, branches);

			List<BranchCommunication> branchCommunications = branchCommunicationRepository
					.findBybranchesId(branches.getId());
			for (CommunicationRequestDto request : branchRequest.getCommunication()) {
				branchCommunications.forEach(branchCommunication -> {
					if (branchCommunication.getCommunicationAction().equals(request.getCommunicationAction())) {
						BranchCommunicationResponseDto responseDto = new BranchCommunicationResponseDto();
						branchCommunication.setBranches(branches);
						branchCommunication.setSms(request.isSms());
						branchCommunication.setEmail(request.isEmail());
						branchCommunication.setModifiedAt(currentTime);
						branchCommunication.setLastModifiedBy(currentUser);
						branchCommunication = branchCommunicationRepository.save(branchCommunication);
						if (!StringUtils.isEmpty(branchCommunication.getId())) {
							responseDto = new BranchCommunicationResponseDto(
									branchCommunicationRepository.getCommunicationDetails(branchCommunication.getId()));
						}
						response.add(responseDto);
					}
				});
			}
			return response;
		} catch (USException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new USException(us.getErrorCode(), us.getMessage());

		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("branch.update.failed", null));
		}
	}

	/**
	 * Feign call using in subjective paper
	 * 
	 * @param schoolId
	 * @param branchId
	 * @return
	 */
	@Override
	public SchoolBranchResponseDto schoolBranchMinDetails(String schoolId, String branchId) {
		try {
			SchoolBranchResponseDto response = branchRepo.findShoolBranchMinDetails(schoolId, branchId);
			CitiesResponseDto city = masterFeign.getCityById(response.getCityId()).getData();
			response.setCity(city.getCity());
			return response;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("school.branches.details.failed", null));
		}
	}	
	
	@Override
	public List<String> getTestBranchList() {
		try {
			List<String> response = branchRepo.getTestBranchList();
			return response!= null ? response: null;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new USException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("fetch.test.branches.details.failed", null));
		}
	}

}
