package com.lms.userservice.service;

import java.util.List;

import javax.validation.Valid;

import com.lms.userservice.request.dto.ReadingPassportRequest;
import com.lms.userservice.request.dto.ReadingPassportResponse;
import com.lms.userservice.response.dto.TeacherResponseDto;

public interface ReadingPassportService {

	ReadingPassportResponse createOrUpdateReadingPassport(String id, @Valid ReadingPassportRequest dto);

	ReadingPassportResponse getReadingPassport(String id);

	List<ReadingPassportResponse> getAllAccessBySchoolIdAndBranchId(String schoolId, String branchId);

}
