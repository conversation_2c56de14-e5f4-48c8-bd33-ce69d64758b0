package com.lms.userservice.service;

import org.springframework.stereotype.Service;

import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AdminUsersRequestDto;
import com.lms.userservice.request.dto.ToggleActiveStatusRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;

@Service
public interface AdminUsersService {

	AdminUsersResponseDto createAdminUserAndSendEmail(AdminUsersRequestDto request);

	AdminUsersResponseDto updateAdminUser(AdminUsersRequestDto request);

	AdminUsersResponseDto getAdminUserById(String id);

	/**
	 * @see <a href=
	 *      "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/1e2b14a7-1d99-4606-9f48-fb04d9bb7e3c/">Add
	 *      Admin User</a>
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortOrder
	 * @param sortBy
	 * @param search
	 * @param roleId
	 * @return
	 */
	PaginatedResponse<AdminUsersResponseDto> getAllAdminUsersByPagniation(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String search, String roleId, Boolean active);

	Boolean toggleActiveStatus(ToggleActiveStatusRequestDto request);

	Boolean deleteAdminUser(String id);

	/**
	 * Confirmation API before active/de-active and delete
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	ConfirmationApiResponseDto checkTheMappingForConcept(String id, String operationType);

	Boolean toggleActiveStatusForMaster(String id, boolean active);

}
