package com.lms.userservice.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.lms.userservice.request.dto.QuizReleaseCheckingRequestDto;
import com.lms.userservice.response.dto.EnumsResponseDto;
import com.lms.userservice.response.dto.SchoolBranchTeacherNameResponseDto;

@Service
public interface MiscService {

	/**
	 * List the Coordinator type from the enumeration
	 * 
	 * @return
	 */
	List<EnumsResponseDto> listOfCoordinatorType();

	/**
	 * List the Gender from the enumeration
	 * 
	 * @return
	 */
	List<EnumsResponseDto> listOfGender();

	/**
	 * List the Operation Type from the enumeration, use if while do the
	 * confirmation API
	 * 
	 * @return
	 */
	List<EnumsResponseDto> listOfOperationType();

	/**
	 * @return the list of LMSEnvironment
	 */
	List<EnumsResponseDto> listOfApplicationEnvironment();
	
	/**
	 * Find the mapping of academicYearId. This API will be act as a feign call.
	 * 
	 * @param AcademicYearId
	 * @return
	 */

	boolean checkTheGradeSectionMappingForAcademicYearId(String academicYearId);

	/**
	 * Feign call from the teacher-service. Checking the relationships and
	 * teacher-access before trigger the teach-status.
	 *
	 * @param schoolId
	 * @param branchId
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	boolean fiegnCallFromTeacherServiceForTeachStatus(String schoolId, String branchId, String teacherId,
			String gradeId, String sectionId, String subjectId, String subTopicId);

	/**
	 * Get name to teacher-service for teach-status.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param startedById
	 * @param endedById
	 * @return
	 */
	SchoolBranchTeacherNameResponseDto getSchoolBranchAndTeachersName(String schoolId, String branchId,
			String startedById, String endedById);

	/**
	 * Menu names for Academic staffs or students
	 * 
	 * @return
	 */
	List<EnumsResponseDto> listOfSchoolMenus();

	/**
	 * Feign call for content-service. Before quiz release or re-release checking
	 * the teacher has access to give school, branch, grade, section, subject and
	 * sub-topics. Section is exist then checking will do with section or with
	 * grade.
	 * 
	 * @param request
	 */
	boolean checkingAccessProvideBeforeQuizRelease(QuizReleaseCheckingRequestDto request);

}
