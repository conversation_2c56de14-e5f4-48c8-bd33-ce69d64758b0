package com.lms.userservice.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.TokensRequestDto;
import com.lms.userservice.response.dto.TokenDetailsResponseDto;
import com.lms.userservice.response.dto.TokenListResponseDto;
import com.lms.userservice.response.dto.TokensResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;

@Service
public interface TokensService {

	/**
	 * Tokens will be created according to the {@code numberOfTokens}.
	 * <p>
	 * eg., {@code numberOfTokens} = 3 it'll create new 3 documents with unique
	 * tokenId
	 * 
	 * <p>
	 * if {@code multiUser} is false then {@code numberOfUsersPerToken} will set to
	 * 1
	 * 
	 * <p>
	 * {@code numberOfUsersPerToken} means how many users can use one tokenId
	 * 
	 * @param request
	 * @return
	 */
	List<TokensResponseDto> createTokens(TokensRequestDto request);

	/**
	 * et Tokens by generated Token
	 * 
	 * @param token
	 * @return
	 */
	TokensResponseDto getTokensByToken(String token);

	/**
	 * Search by {@code tokenId}, filtering is enabled for below parameters, sort
	 * order by either ascending or descending for all fields.
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortOrder
	 * @param sortBy
	 * @param roleCode
	 * @param tokenId
	 * @return
	 */
	PaginatedResponse<TokenListResponseDto> getTokenByFilterSearchOrsorted(int pageNumber, int pageSize,
			boolean sortOrder, String sortBy, String roleId, String userId, String branchId, String schoolId,
			String search, Boolean active);

	/**
	 * @param id
	 * @return
	 */
	Boolean deleteTokenById(String id);

	/**
	 * During the self registration by the user will mapped to the given token if
	 * the {@code numberOfUsersPerToken} and {@code tokenUseCount} is equal then the
	 * token won't available to accept users.
	 * 
	 * @param tokenId
	 * @param request
	 * @return
	 */
	TokensResponseDto updateTokenDuringRegistration(String tokenId, String userId);

	/**
	 * Get token counts for users
	 * 
	 * @param tokenId
	 * @param roleId
	 * @param schoolId
	 * @param branchId
	 * @param userId
	 * @return
	 */
	UsersCountResponseDto getAllCounts(String tokenId, String roleId, String schoolId, String branchId, String userId);

	/**
	 * Update active field to activate/deactivate token
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	boolean updateActiveField(String id, boolean active);

	/**
	 * Get the user detail, who used the token
	 * 
	 * @param search  (by userName, firstName, lastName, email and phone number)
	 * @param tokenId (filter)
	 * @return
	 */
	List<TokenDetailsResponseDto> getUsedUserId(String search, String tokenId);

	/**
	 * Get the last modification done on the table tokens return format example will
	 * be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();
}
