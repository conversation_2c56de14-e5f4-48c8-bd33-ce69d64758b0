package com.lms.userservice.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.request.dto.GradeSectionMapRequestDto;
import com.lms.userservice.request.dto.GradeSectionPutRequestDto;
import com.lms.userservice.request.dto.ToggleGradeSectionRequestDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.GradeSectionGetResponseDto;
import com.lms.userservice.response.dto.GradeSectionMapResponseDto;
import com.lms.userservice.response.dto.GradeSectionMappingResponseDto;
import com.lms.userservice.response.dto.SectionDataResponseDto;

@Service
public interface GradeSectionMappingService {

	/**
	 * Get the enumerator {@link com.lms.userservice.enums.SectionData SectionData}
	 * into List
	 * 
	 * @return
	 */
	List<SectionDataResponseDto> getAllSectionData();

	/**
	 * Grade-Section mapping is going through the following points
	 * <p>
	 * <blockquote>
	 * 
	 * <pre>
	 * 1. There will be Grade without section (NO_SECTION)
	 *    eg., class 11th or 12th can be stand without any division like A, B etc
	 *    
	 * 2. The entire branch of school can be exist with same sections (SAME_SECTION)
	 *    eg., class from LKG to 12th there is only divisions like A, B etc
	 *  	means select section A, B for entire branch's grade
	 *  
	 * 3. The branch of school can have different sections for each grade (DIFFERENT_SECTIONS)
	 *    eg., class LKG can have division A, B or C, at the same time class 10 has A & B division
	 * </pre>
	 * 
	 * </blockquote>
	 * 
	 * According to the grade and section list size the rows will be create.
	 * <p>
	 * 
	 * @param request
	 * @return
	 */
	List<GradeSectionMapResponseDto> createGradeSectionMapping(GradeSectionMapRequestDto request);

	/**
	 * Get Grade-sEction mapping
	 * 
	 * @param schoolId
	 * @param branchId
	 * @return
	 */
	List<GradeSectionGetResponseDto> getAllMappingBySchoolAndBranch(String schoolId, String branchId);

	/**
	 * Update the Grade-Sections mapping
	 * 
	 * @param request
	 * @return
	 */
	GradeSectionMapResponseDto updateGradeSectionsMapping(GradeSectionPutRequestDto request);

	/**
	 * Update active field to activate/deactivate mapping
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	boolean updateActiveFieldByGradeIdBranchIdAndSchoolId(ToggleGradeSectionRequestDto request);

	/**
	 * Get the Mapped data by Grade Id before marking the record as deleted
	 * 
	 * @param gradeId
	 * @return
	 */
	Boolean getCountOfGradeSectionsByGradeId(String gradeId);

	/**
	 * Get the Mapped data by Section Id before marking the record as deleted
	 * 
	 * @param sectionId
	 * @return
	 */
	Boolean getCountOfGradeSectionsBySectionId(String sectionId);

	/**
	 * Get all the grades mapped with the grades and sections.
	 * 
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	List<GradesResponseDto> getAllGradesBySchoolAndBranch(String branchId, String schoolId);

	/**
	 * Fetch all the sections by gradeId, branch and school
	 * 
	 * @param gradeId
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	List<SectionsResponseDto> getAllSectionByGradesSchoolAndBranch(String search, String gradeId, String branchId, String schoolId);

	/**
	 * Get the last modification done on the table students return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();
        /**
         * Delete grade section mapping
         * @param request
         * @return 
         */
        boolean deleteActiveFieldByGradeIdBranchIdAndSchoolId(ToggleGradeSectionRequestDto request);

        /**
         * 
         * @param schoolId
         * @param branchId
         * @return 
         */
        List<GradeSectionMappingResponseDto> getMappingsBySchoolIdAndBranchId(String schoolId, String branchId);

        /**
         * 
         * @param id
         * @return 
         */
        boolean deleteById(String id);

        /**
         * 
         * @param id
         * @param active
         * @return 
         */
        boolean toggleActiveStatusById(String id, boolean active);
        /**
         * Confirm before deletion or toggling active status
         * @param mappingId
         * @param deleted
         * @return 
         */
        Boolean existsByIdAndDeleted(String mappingId, boolean deleted);

        /**
        * Confirmation API before active/de-active and delete
        *
        * @param id
        * @param operationType
        * @return
        */
       ConfirmationApiResponseDto confirmationApi(String id, String operationType);

		/**
		 * This is created as a feign for master-service (confirmation-api of
		 * GradesController).
		 * 
		 * @param gradeId
		 * @return
		 */
		boolean checkTheGradeHasMapping(String gradeId);
		
		List<String> getAllSectionsForPrincipal(String schoolId, String branchId, String gradeId);

		List<String> getSectionByGradeId(String gradeId);

}
