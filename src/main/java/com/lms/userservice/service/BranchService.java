package com.lms.userservice.service;

import java.util.List;

import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.BranchCommunicationRequestDto;
import com.lms.userservice.request.dto.BranchRequestDto;
import com.lms.userservice.response.dto.BranchCommunicationResponseDto;
import com.lms.userservice.response.dto.BranchMinResponseDto;
import com.lms.userservice.response.dto.BranchResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;
import com.lms.userservice.response.dto.SchoolCountDetailsResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;

public interface BranchService {

	/**
	 * Get Branches by Branch ID
	 * 
	 * @param branchId the String Branch ID
	 * @return Branches by Branch ID
	 */
	public BranchResponseDto getSchoolBranchesById(String branchId);

	/**
	 * Get Branches List by Pagination
	 * 
	 * @return Branches response using Pagination
	 */
	public PaginatedResponse<BranchResponseDto> getAllBranchesByPage(int pageNumber, int pageSize, String sortBy,
			boolean order, String search, String cityId, String locality, String boardId, String branchId,
			String schoolId, String userId, Boolean active);

	/**
	 * Add Branches
	 * 
	 * @param branches The Branch DTO Request
	 * @return
	 */
	public BranchResponseDto addSchoolBranch(BranchRequestDto branches);

	/**
	 * Update Branches
	 * 
	 * @param branchId The Branch ID
	 * @param branch   The Branch DTO Request
	 * @return
	 */
	public BranchResponseDto updateSchoolBranch(String branchId, BranchRequestDto branch);

	/**
	 * Mark the Branch as Deleted
	 * 
	 * @param branchId The Branch ID
	 * @return
	 */
	public Boolean removeSchoolBranch(String branchId);

	/**
	 * Get all the branches
	 * 
	 * @param branchId
	 * @return
	 */
	public List<BranchesMinDataResponseDto> getAllBranches(String search, String schoolId, String userId);

	/**
	 * Update active field
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	public Boolean updateActiveField(String id, boolean active);

	/**
	 * List all branches which has mapping to the plan
	 * 
	 * @param planId
	 * @return
	 */
	public List<BranchesMinDataResponseDto> getAllBranchMappingsForPlan(String planId);

	/**
	 * Delete all branch plan mappings by planId
	 * 
	 * @param planId
	 * @return
	 */
	public Boolean deleteAllBranchMappingsForPlan(String planId);

	/**
	 * Update active field for all branch mappings for plan
	 * 
	 * @param planId
	 * @param active
	 * @return
	 */
	public Boolean updateActiveFieldOfAllBranchMappingsForPlan(String planId, boolean active);

	/**
	 * Return the mapping tables related to the branch, check the count, if the
	 * count==0 return the default message or show the branch has mapping as in a
	 * message format.
	 * 
	 * currently mapping entities :: branch_plan_mappings, grade_section_mapping,
	 * student, teacher, token, users_institution_mapping
	 * 
	 * @param id
	 * @return
	 */
	String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType);

	/**
	 * Get the Mapped data before marking the record as deleted
	 * 
	 * @param planId
	 * @return
	 */
	boolean checkPlanHasAnyMapping(String planId);

	/**
	 * Get the last modification done on the table branches return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();
        
        /**
	 * Feign functionality for the CONTENT-SERVICE's Quiz Release
	 * @param ids
	 * @return
	 */

        List<BranchResponseDto> getAllBranchesByIds(List<String> ids);

        /**
         * get maximum limit of quiz re-release
         * @param id
         * @return 
         */
        Integer getMaxQuizRelease(String id);

        /**
        * Confirmation API before active/de-active and delete
        *
        * @param id
        * @param operationType
        * @return
        */
       ConfirmationApiResponseDto confirmationAPI(String id, String operationType);
       
		/**
		 * Coordinator, Principal, Teacher, Student Count's
		 *
		 */
		UsersCountResponseDto countByPersona(String schoolId, String branchId);

		BranchesMinDataResponseDto getBranchesById(String id);
		
		List<SchoolCountDetailsResponseDto> getSchoolsDetails(List<String> boardIds);

		/**
		 * This API is used for to get Top 5 Branched based on no of students.
		 * @return
		 */
		List<BranchMinResponseDto> getTopBranchesBasedOnStudent();

		/**
		 * This api is used for student service to get the plan.
		 * @param boardId
		 * @param branchId
		 * @param schoolId
		 * @return
		 */
		String getPlanByBranchSchool(String boardId, String branchId, String schoolId);
		
		/**
		 * Add Branches and Communication Actions
		 * 
		 * @param branches The BranchCommunicationRequestDto DTO Request
		 * @return
		 */
		List<BranchCommunicationResponseDto> addBranchCommunication(BranchCommunicationRequestDto branchRequest);
		
		/**
		 * Update Branches and Communication Actions
		 * 
		 * @param branches The BranchCommunicationRequestDto DTO Request
		 * @return
		 */
		List<BranchCommunicationResponseDto> editBranchCommunication(String id,
				BranchCommunicationRequestDto branchRequest);

		/**
		 * Feign call using in subjective paper
		 * 
		 * @param schoolId
		 * @param branchId
		 * @return
		 */
		SchoolBranchResponseDto schoolBranchMinDetails(String schoolId, String branchId);

		public List<String> getTestBranchList();

}