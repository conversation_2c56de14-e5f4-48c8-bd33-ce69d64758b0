package com.lms.userservice.service;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.stereotype.Service;

import com.lms.userservice.entity.Students;
import com.lms.userservice.enums.FileExtensions;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.feign.master.SubjectsResponseDto;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AbsenteesRequestDto;
import com.lms.userservice.request.dto.ChangeProfileRequestDto;
import com.lms.userservice.request.dto.DeActivateProfileRequestDto;
import com.lms.userservice.request.dto.StudentRequestIRDTO;
import com.lms.userservice.request.dto.StudentSubjectMappingRequestDto;
import com.lms.userservice.request.dto.StudentsRequestDto;
import com.lms.userservice.response.dto.AbsenteesResponseDto;
import com.lms.userservice.response.dto.BatchReceptionistRequestDto;
import com.lms.userservice.response.dto.BulkUploadResponseDto;
import com.lms.userservice.response.dto.ChangeProfileResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.FormalStudentWiseGetName;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto;
import com.lms.userservice.response.dto.StudentDetailsMinResponseDto;
import com.lms.userservice.response.dto.StudentDetailsResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingsMinResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingsResponseDto;
import com.lms.userservice.response.dto.StudentsMinResponseDto;
import com.lms.userservice.response.dto.StudentsResponseDto;

/**
 * <AUTHOR> C Achari
 * @since 0.0.1
 */
@Service
public interface StudentsService {

	/**
	 * Register the student details by school admin
	 * 
	 * @param request
	 * @return
	 */
	StudentsResponseDto createStudent(StudentsRequestDto request);

	/**
	 * Get Student details by id
	 * 
	 * @param id
	 * @return
	 * @since 0.0.1
	 */
	StudentsResponseDto getStudentById(String id);

	/**
	 * Get Student details by username
	 * 
	 * @param userName
	 * @return
	 * @since 0.0.1
	 */
	StudentsResponseDto getStudentByUserName(String userName);

	/**
	 * Update a student details by id, To-Do other checking will do later
	 * 
	 * @param id
	 * @param request
	 * @return
	 * @since 0.0.1
	 */
	StudentsResponseDto updateStudent(String id, StudentsRequestDto request);

	/**
	 * Read from the csv or excel file then write the data to the database
	 * 
	 * @param filePath
	 * @param extension
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	BulkUploadResponseDto saveStudentsFromFile(String filePath, FileExtensions extension, FileTypes fileTypes);

	/**
	 * @param id
	 * @return
	 */
	Boolean deleteStudent(String id);

	/**
	 * 
	 * @param request
	 * @return
	 */
	StudentsResponseDto selfRegistrationByStudent(StudentsRequestDto request);

	/**
	 * Search, filtering, sort order and sorting done in one API. All the parameters
	 * taken as request body
	 * {@link com.lms.userservice.request.dto.StudentsPaginationRequestDto
	 * StudentsPaginationRequestDto}
	 * 
	 * @param pageNumber @param pageSize @param sortOrder @param sortBy @param
	 *                   search
	 * @param schoolName @param branchName @param gradeId @param sectionId
	 * @return
	 */
	PaginatedResponse<StudentsResponseDto> getAllStudentsByPagination(int pageNumber, int pageSize, boolean sortOrder,
			String sortBy, String sectionId, String gradeId, String branchId, String schoolId, String search,
			Boolean active);

	/**
	 * update active field activate/deactivate student
	 * 
	 * @param id
	 * @param active
	 * @return
	 */
	Boolean updateActiveField(String id, boolean active);

	/**
	 * Checking the user by user_name then call the user_role, institute mapping.
	 * 
	 * @param id
	 * @param operationType
	 * @return
	 */
	String checkTheMappingExistBeforeDeleteOrTogglingActive(String id, String operationType);

	/**
	 * Get the Students Mapped data by Grade Id before marking the record as deleted
	 * 
	 * @param gradeId
	 * @return
	 */
	Boolean getCountOfStudentsByGradeId(String gradeId);

	/**
	 * Get the Students Mapped data by Section Id before marking the record as
	 * deleted
	 * 
	 * @param sectionId
	 * @return
	 */
	Boolean getCountOfStudentsBySectionId(String sectionId);

	/**
	 * Get the last modification done on the table students return format example
	 * will be 25 Jun 2022 | 10:30 AM
	 * 
	 * @return
	 */
	String getLastModifiedAt();

	/**
	 * List all students with the language mapping for the given Id
	 * 
	 * @param languageId
	 * @return
	 */
	List<NameCommonResponseDto> getAllStudentsByLanguage(String languageId);

	/**
	 * List all students with student category mapping for given Id
	 * 
	 * @param studentCategoryId
	 * @return
	 */
	List<NameCommonResponseDto> getStudentsByStudentCategory(String studentCategoryId);

	/**
	 * Map subject mapping with a student
	 * 
	 * @param request
	 * @return
	 */
	public StudentSubjectMappingsResponseDto mapSubjectsToStudent(StudentSubjectMappingRequestDto request);

	/**
	 * Get student with subjects (subject mappings)
	 * 
	 * @param studentId
	 * @param academicYearId
	 * @return
	 */
	public StudentSubjectMappingsResponseDto getStudentWithSubjects(String studentId, String academicYearId);

	/**
	 * Get student with subjects and sub-topic list(subject mappings)
	 * 
	 * @param studentId
	 * @return
	 */
	public StudentSubjectMappingsMinResponseDto getStudentWithSubjectsAndSubTopics(String studentId);

	/**
	 * @param studentId
	 * @return
	 */
	List<SubjectsResponseDto> getAllSubjectsByStudentId(String studentId);

	/**
	 * Get User Ids of students for messaging and notification services
	 * 
	 * @param sectionId
	 * @param gradeId
	 * @param branchId
	 * @param schoolId
	 * @param search
	 * @return
	 */
	Map<String, String> getUserIds(String sectionId, String gradeId, String branchId, String schoolId, String search);

	/**
	 * Confirmation API before active/de-active and delete
	 *
	 * @param id
	 * @param operationType
	 * @return
	 */
	ConfirmationApiResponseDto checkTheMappingForConcept(String id, String operationType);

	/**
	 * Changing the student profile by the gradeId and sectionId
	 * 
	 * @param request
	 * @return
	 */
	ChangeProfileResponseDto changeStudentProfile(ChangeProfileRequestDto request);

	/**
	 * Re-Release quiz and pass the students list and getting exam status
	 * <p>
	 * 
	 * @param studentIds
	 * @param studentName
	 * @param examAttended OR not
	 * @return
	 */
	PaginatedResponse<StudentsResponseDto> getStudentsExaminationStatus(int pageNumber, int pageSize, String schoolId,
			String branchId, String gradeId, String quizId, String search, String sectionId);

	/**
	 * This is a feign call for sending notification from the
	 * release-quizzes(content-service)
	 * 
	 * @param sectionId
	 * @param gradeId
	 * @param branchId
	 * @param schoolId
	 * @return
	 */
	List<String> getAllUserIdForStudent(String sectionId, String gradeId, String branchId, String schoolId);

	/**
	 * This is a feign call for sending notification from content-service for quiz
	 * Re-release.
	 * 
	 * @param studentsId
	 * @return
	 */
	List<String> getAllUserIdByStudentIds(List<String> studentsId);

	/**
	 * Find the student count. This is a feign call to student-service.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	long countOfStudentsByFilters(String schoolId, String branchId, String gradeId, String sectionId);

	Boolean deActivateStudentProfile(DeActivateProfileRequestDto request);

	/**
	 * This api is used for for getting studentId and fullName.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	List<StudentsMinResponseDto> getStudentsDetail(String schoolId, String branchId, String gradeId, String sectionId);

	/**
	 * This is a feign call for sending notification from content-service for quiz
	 * Re-release.
	 * 
	 * @param studentsId
	 * @return
	 */
	String getUserIdByStudentId(String studentId);

	/**
	 * This api is used for getting student section count
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	Integer getStudentSectionCount(String schoolId, String branchId, String gradeId, String sectionId);

	/**
	 * This is a feign call for getting students count by grade id
	 *
	 *
	 * @param gradeId
	 * @param schoolId
	 * @param sectionId
	 * @return
	 */
	Long getStudentCountByGrade(String gradeId, String schoolId, String sectionId);

	/**
	 * Find the userId for different schools. Feign call to content-service to
	 * notify the PQ release.
	 * 
	 * @param sectionIds
	 * @param gradeIds
	 * @param branchIds
	 * @param schoolIds
	 * @return
	 */
	List<String> getUserIdOfStudentsFromDifferentSchools(List<String> sectionIds, List<String> gradeIds,
			List<String> branchIds, List<String> schoolIds);

	/**
	 * This is a feign call for getting student details by user name
	 *
	 *
	 * @return
	 */
	StudentDetailsResponseDto getStudentDetailsByName(String userName);

	/**
	 * THis is feign call for students access details.
	 * 
	 * @param userName
	 * @return
	 */
	StudentAssignedMinDetailResponseDto getStudentAssignedDetails(String userName);

	List<FormalStudentWiseGetName> getFormalWiseStudentName(String teacherId, String gradeId, String schoolId,
			String branchId, String sectionId, List<String> studentIds);

	/**
	 * get the student details. This is a feign call to Teacher-service.
	 * 
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param sectionId
	 * @return
	 */
	List<StudentMinResponseDto> getStudentListInSection(String schoolId, String branchId, String gradeId,
			String sectionId);

	/**
	 * get the student details. This is a feign call to Teacher-service.
	 * 
	 * @param boardId
	 * @param gradeId
	 * @return
	 */
	List<StudentMinResponseDto> getStudentListInGrade(String boardId, String gradeId);

	/**
	 * This is a feign call for getting student details by user name
	 *
	 *
	 * @return
	 */
	StudentDetailsMinResponseDto findStudentDetails(String studentId);

	/**
	 * This is a feign call for getting student details
	 *
	 *
	 * @return
	 */
	List<BatchReceptionistRequestDto> findingStudentsDetails(String boardId, String schoolId, String branchId,
			String gradeId, String sectionId, List<String> studentIds);

	/**
	 * This is a feign call from Teacher-Service to find out the absentees list.
	 * {@code requestDto.getStudentIds()} is empty then the whole student will be
	 * list down.
	 * 
	 * @param requestDto
	 * @return
	 */
	List<AbsenteesResponseDto> getAllAbsenteesDetails(AbsenteesRequestDto request);
	
	List<StudentsMinResponseDto> getAvidStudentsDetails(String schoolId, String gradeId, String sectionId, String branchId);

	Boolean getStudentProfileIRDetails(String studentId, StudentRequestIRDTO request);	

	PaginatedResponse<StudentsResponseDto> getAllStudentSetionAndGradeBased(int pageNumber, int pageSize,String teacherId, String irStatus,String schoolId, String branchId, boolean sortOrder,String sortBy);

	Boolean updateStudentEntityIrStatus(String studentId);
}
