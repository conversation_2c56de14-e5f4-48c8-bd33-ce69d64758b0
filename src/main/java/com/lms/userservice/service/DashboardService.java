package com.lms.userservice.service;

import java.util.List;

import com.lms.userservice.assignedTeacher.AssignedTeacherResponseDto;
import com.lms.userservice.assignedTeacher.ChapterTrackingResponseDto;
import com.lms.userservice.assignedTeacher.ChapterTrackingWithQuizResponseDto;
import com.lms.userservice.response.dto.BlueprintLevelResponse;
import com.lms.userservice.response.dto.ChaptersVSQuizzesRelasesResponseDto;
import com.lms.userservice.response.dto.DashboardResponseDto;
import com.lms.userservice.response.dto.DashboardUnitQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.GradeAccessInfoStudentsDetailsResponseDto;
import com.lms.userservice.response.dto.GradeSubjectScoreResponseDto;
import com.lms.userservice.response.dto.PrincipalGradeDetailedPerformanceResponseDto;
import com.lms.userservice.response.dto.PrincipalGradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.StudentLevelResponseDto;
import com.lms.userservice.response.dto.SyllabusGradeAccessResponseDto;
import com.lms.userservice.response.dto.TeacherFormativeAssessmentResponseDto;
import com.lms.userservice.response.dto.TeacherGradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.TotalVsComplatedResponseDto;

public interface DashboardService {
	/**
	 * Counting of student, grade, section, subject for
	 * teacher/coordinator/principal dashboard.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @return
	 */
	public DashboardResponseDto countToDashboard(String teacherId, String schoolId, String branchId, String gradeId,
			String subjectId, String subtopicId);

	/**
	 * This Api is used for coordinator counting chapters and quizzes, subject .
	 */
	public List<TotalVsComplatedResponseDto> countForChapterQuizzes(String teacherId, String boardId, String schoolId,
			String branchId, String gradeId, String subjectId, String subTopicId);

	public List<SyllabusGradeAccessResponseDto> percentageForSyllabus(String boardId, String schoolId, String branchId,
			String gradeId, String subjectId, String subTopicId);

	/**
	 * Dashboard Student level for teacher and coordinator.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @param sectionId
	 * @return
	 */
	List<StudentLevelResponseDto> getScoreRangeAndPercentageOfStudents(String teacherId, String boardId,
			String schoolId, String branchId, String gradeId, String subjectId, String subTopicId,
			String academicYearId);

	/**
	 * Find the count of total chapters, teaching completed chapter and
	 * released-quiz. Use this API mainly for the Dash-board.
	 * 
	 * @param teacherId
	 * @param boardId
	 * @param schoolId
	 * @param branchId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @param academicYearId
	 * @return
	 */
	ChaptersVSQuizzesRelasesResponseDto countForTotalChaptersReleasedQuizzes(String teacherId, String boardId,
			String schoolId, String branchId, String gradeId, String subjectId, String subTopicId,
			String academicYearId);

	/**
	 * This api is used for teacher dashboard unit quiz performance.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param boardId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicId
	 * @param chapterId
	 * @return
	 */
	DashboardUnitQuizPerformanceResponseDto getUnitQuizPerformance(String teacherId, String schoolId, String branchId,
			String boardId, String gradeId, String subjectId, String subTopicId, String chapterId);

	/**
	 * This api used for Teacher Dashboard Formative Assessment.
	 * 
	 * @param teacherId
	 * @param schoolId
	 * @param branchId
	 * @param boardId
	 * @param gradeId
	 * @param subjectId
	 * @param subTopicid
	 * @return
	 */
	List<TeacherFormativeAssessmentResponseDto> getTeacherFormativeAssessment(String teacherId, String boardId,
			String schoolId, String branchId, String gradeId, String subjectId, String subTopicId,
			String academicYearId);
	
	/**
	 * Fetching the grade-section based students-count from assign-teacher and
	 * grade-section-mapping tables * assignedGrades or not assigned for principal
	 * and coordinator, getting grades and students details.
	 * 
	 * @return
	 */
	GradeAccessInfoStudentsDetailsResponseDto getGradeAndStudentDetails(String teacherId, String gradeId);
	
	/**
	 * Fetching the grade-section based students Average score vs Global Score from
	 * grade-section-mapping tables * assignedGrades or not assigned for principal
	 * and coordinator dash-board.
	 * 
	 * @return
	 */
	PrincipalGradeWiseQuizPerformanceResponseDto getGradeWiseQuizPerformanceForPrincipal(String teacherId, String gradeId);

	/**
	 * Fetching the grade-section based students Average score vs Global Score from
	 * assignedGrade or not assigned for principal and coordinator dash-board.
	 * 
	 * @return
	 */
	PrincipalGradeDetailedPerformanceResponseDto getGradeDetailedPerformanceForPrincipal(String teacherId,
			String gradeId, String sectionId);

	/**
	 * This API is used for the Teacher Dashboard Grade-wise Quiz Performance.
	 * @param teacherId
	 * @param gradeId
	 * @return
	 */
	TeacherGradeWiseQuizPerformanceResponseDto getTeacherGradeWiseQuizPerformance(String teacherId, String gradeId);

	/**
	 * This api is used for Teacher dashboard chapter-wise quiz performance.
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	GradeSubjectScoreResponseDto getChapterWiseQuizPerformance(String teacherId, String gradeId, String sectionId,
			String subjectId, String subTopicId);

	long getGlobalStudentCount(String boardId, String gradeId);

	public GradeSubjectScoreResponseDto getChapterWiseQuizPerformanceDashboard(String teacherId, String gradeId,
			String subjectId);

	public List<BlueprintLevelResponse> getBlueprintLevelDetailsBybranchId(String branchId);

	public List<ChapterTrackingWithQuizResponseDto> getChapterWiseQuizPerformanceDashboardAdmin(String boardId, String schoolId, String branchId, String gradeId,
																								String subjectId);	

	
	List<GradeSubjectScoreResponseDto> getChapterWiseQuizPerformance(String gradeId, String sectionId,
			String subjectId, String subTopicId);

	public List<TeacherGradeWiseQuizPerformanceResponseDto> getGradeWiseQuizPerformance(String gradeId,String subjectId);
}
