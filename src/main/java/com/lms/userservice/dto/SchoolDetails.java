package com.lms.userservice.dto;

import javax.validation.constraints.NotBlank;

import com.lms.userservice.util.Constants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Using for get the object for the school details used in the Student entity.
 * 
 * <AUTHOR>
 * 
 * @since 11-Mar-2022
 * 
 * @see	com.lms.userservice.entity.Students
 * 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchoolDetails {
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolCode;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String schoolName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String branchName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String gradeName;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionId;
	
	@NotBlank(message = Constants.MANDATORY_FIELD)
	private String sectionName;
}
