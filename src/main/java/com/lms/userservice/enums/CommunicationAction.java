package com.lms.userservice.enums;

/**
 * <AUTHOR>
 *
 */
public enum CommunicationAction {

	USER_CREATION("User Creation", "USER_CREATION"), UPDATE_PASSWORD("Update Password", "UPDATE_PASSWORD"),
	SHARE_ID("Share id", "SHARE_ID"), QUIZ_RELEASE("Quiz Release", "QUIZ_RELEASE"),
	QUIZ_RESULT("Quiz Result", "QUIZ_RESULT"), UPDATE_PROFILE("Edit & Update Profile", "UPDATE_PROFILE");

	private String name;

	private String code;

	CommunicationAction(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
