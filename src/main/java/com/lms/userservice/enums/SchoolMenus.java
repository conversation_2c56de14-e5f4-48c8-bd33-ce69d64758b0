package com.lms.userservice.enums;

public enum SchoolMenus {
	
	TEACH("Teach", "TEACH","lesson_plan"),
	ASSIGN_QUIZ("Assign Quiz", "ASSIGN_QUIZ", "teacher_revision_module"),
	FORMAL_ASSESSMENT("Formal Assessment", "FORMAL_ASSESSMENT", "teacher_revision_module"),
	STUDENT_REVISION("Student Revision", "STUDENT_REVISION", "teacher_revision_module"),
	SUBJECTIVE_PAPERS("Subjective papers", "SUBJECTIVE_PAPERS", "assessment_module"),
	STUDY("Study", "STUDY", "student_booklet"),
	ENRICH("Enrich", "ENRICH", "student_revision_module");

	private String name;

	private String code;
	
	private String planTemplateColumn;
	
	SchoolMenus(String name, String code, String planTemplateColumn) {
		this.name = name;
		this.code = code;
		this.planTemplateColumn = planTemplateColumn;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
	
	public String getPlanTemplateColumn() {
		return planTemplateColumn;
	}
}
