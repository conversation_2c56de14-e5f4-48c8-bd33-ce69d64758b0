package com.lms.userservice.enums;

public enum StudentEncourageMessages {

	// quiz attempt rate

	QUIZ_ATTEMPT_LOWER("Let's Step it Up! The quiz attempts are low, please encourage students to attempt more quizzes.", 0, 60),

	QUIZ_ATTEMPT_HIGHER("Good work! Please encourage more students to attempt quizzes.", 60, 100),

	
	
	// based on quiz average score

	SCORE_FIRST_LEVEL("Let's Step it Up! The average scores are low, please ensure that student specific gaps are addressed.", 0, 50),

	SCORE_SECOND_LEVEL("Keep Going! Please continue to deliver excellence in your classes.", 50, 100);

	String message;
	Integer startingRate;
	Integer endingRate;

	private StudentEncourageMessages(String message, Integer startingRate, Integer endingRate) {
		this.message = message;
		this.startingRate = startingRate;
		this.endingRate = endingRate;
	}

	private StudentEncourageMessages() {
	}

	public String getMessage() {
		return message;
	}

	public Integer getStartingRate() {
		return startingRate;
	}

	public Integer getEndingRate() {
		return endingRate;
	}

}
