package com.lms.userservice.enums;

/**
 * <AUTHOR>
 *
 */
public enum LMSEnvironment {

	LOCAL("local", "LOCAL"), DEV("dev", "DEV"), TEST("test", "TEST"), UAT("uat", "UAT"),
	PRE_PROD("pre_prod", "PRE_PROD"), PROD("prod", "PROD");

	private String name;

	private String code;

	LMSEnvironment(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
