package com.lms.userservice.enums;

public enum AcademicStaffProfile {

	TEACH<PERSON>("Teacher", "TEACHER"), 
	COORDINATOR("Coordinator", "COORDINATOR"), 
	PRINCIPAL("Principal", "PRINCIPAL");

	private String name;

	private String code;

	AcademicStaffProfile(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
