package com.lms.userservice.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.databind.SerializationFeature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.userservice.service.impl.AuthServiceImpl;
import com.lms.userservice.util.JwtUtil;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;


/**
 * Processes an {@link JwtFilter} request.
 * 
 * Filter base class that aims to guarantee a single execution per request
 * dispatch, on any servlet container. It provides a {@link #doFilterInternal}
 * method with HttpServletRequest and HttpServletResponse arguments.
 * 
 * <AUTHOR>
 */

@Component
public class JwtFilter extends OncePerRequestFilter {

	
	/**
	 * Injecting the jwtUtil Dependency
	 */
	@Autowired
	private JwtUtil jwtUtil;
	
	
	/**
	 * Injecting the AuthServiceImpl Dependency
	 */
	@Autowired
	private AuthServiceImpl authService;

	@Override
	protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			FilterChain filterChain) throws ServletException, IOException {
		
		String authorizationHeader = httpServletRequest.getHeader("Authorization");

		String token = null;
		String userName = null;

		try {
			if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {

				token = authorizationHeader.substring(7);
				userName = jwtUtil.extractUsername(token);

			}

			if (userName != null && SecurityContextHolder.getContext().getAuthentication() == null) {

				UserDetails userDetails = authService.loadUserByUsername(userName);
				if (jwtUtil.validateToken(token, userDetails)) {

					UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(
							userDetails, null, userDetails.getAuthorities());
					usernamePasswordAuthenticationToken
							.setDetails(new WebAuthenticationDetailsSource().buildDetails(httpServletRequest));
					SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
				}
			}
			filterChain.doFilter(httpServletRequest, httpServletResponse);
		} catch (MalformedJwtException e) {
			httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
			httpServletResponse.getWriter().write(convertObjectToJson(e));
		} catch (ExpiredJwtException e) {
			String isExtendable = httpServletRequest.getHeader("isExtendable");
			String requestURL = httpServletRequest.getRequestURL().toString();
			if (isExtendable != null && isExtendable.equals("true") && requestURL.contains("extendtoken") && jwtUtil.canTokenBeExtended(e.getClaims())) {
                            allowForRefreshToken(e, httpServletRequest);
                            filterChain.doFilter(httpServletRequest, httpServletResponse);
                        } else {
                        	httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
                			httpServletResponse.getWriter().write(convertObjectToJson(e));
                        }
                            
		}catch (Exception e) {
                    	httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
			httpServletResponse.getWriter().write(convertObjectToJson(e));
		}
                
	}

	public String convertObjectToJson(Object object) throws JsonProcessingException {
		if (object == null) {
			return null;
		}
		ObjectMapper mapper = new ObjectMapper();
		mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
		return mapper.writeValueAsString(object);
	}
	
	private void allowForRefreshToken(ExpiredJwtException ex, HttpServletRequest request) {

		// create a UsernamePasswordAuthenticationToken with null values.
		UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(
				null, null, null);
		// After setting the Authentication in the context, we specify
		// that the current user is authenticated. So it passes the
		// Spring Security Configurations successfully.
		SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
		// Set the claims so that in controller we will be using it to create
		// new JWT
		request.setAttribute("claims", ex.getClaims());

	}

}
