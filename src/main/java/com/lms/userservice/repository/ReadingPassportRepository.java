package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.ReadingPassport;

import feign.Param;

@Repository
public interface ReadingPassportRepository extends JpaRepository<ReadingPassport, String> {

	@Query(value="select * from reading_passport_access where user_id=:userId",nativeQuery=true)
	ReadingPassport findByUserId(@Param("userId") String userId);
	
	@Query(value = "SELECT * FROM reading_passport_access WHERE school_id = :schoolId AND branch_id = :branchId", nativeQuery = true)
    List<ReadingPassport> findBySchoolIdAndBranchId(@Param("schoolId") String schoolId, @Param("branchId") String branchId);

}
