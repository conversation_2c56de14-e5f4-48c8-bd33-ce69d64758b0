package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.UsersTokenMapping;
import com.lms.userservice.projection.TeacherCountProjection;
import com.lms.userservice.projection.TokenUserMappingProjection;

@Repository
public interface UsersTokenMappingRepository extends JpaRepository<UsersTokenMapping, String> {

	@Query("SELECT utm.users.id as userId, utm.users.email as email , "
			+ "utm.users.userName as userName, utm.users.phoneNumber as phone "
			+ "FROM #{#entityName} utm WHERE utm.tokens.id =:tokenId ")
	List<TokenUserMappingProjection> getUserMappingByTokenId(String tokenId);

	@Query("SELECT COUNT(distinct s.id) FROM #{#entityName} utm JOIN "
			+ "com.lms.userservice.entity.Students s ON (utm.users.userName = s.userName) "
			+ "WHERE s.deleted IS FALSE AND utm.tokens.deleted IS FALSE AND "
			+ "(:tokenId IS NULL OR utm.tokens.id =:tokenId) AND (:roleId IS NULL OR utm.tokens.roleId =:roleId) "
			+ "AND (:schoolId IS NULL OR utm.tokens.schools.id=:schoolId) AND (:branchId IS NULL OR "
			+ "utm.tokens.branches.id =:branchId) AND (:userId IS NULL OR utm.users.id =:userId)")
	long getStudentsTokenCount(String tokenId,String roleId,String schoolId, String branchId, String userId);

	@Query(value = "SELECT COUNT(t.id) FILTER(WHERE t.academic_staff_profile='TEACHER' OR t.academic_staff_profile IS NULL) as teacherCount, "
			+ "COUNT(t.id) FILTER(WHERE t.academic_staff_profile='PRINCIPAL') as principalCount, "
			+ "COUNT(t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM user_token_mapping utm JOIN tokens tok on (utm.token_id = tok.id AND "
			+ "tok.deleted IS FALSE ) JOIN users u ON (utm.user_id = u.id) JOIN teachers t "
			+ "ON (u.user_name = t.user_name) WHERE t.deleted IS FALSE AND t.active IS TRUE AND tok.deleted IS FALSE AND tok.active IS TRUE AND "
			+ "(:tokenId IS NULL OR utm.token_id =cast(:tokenId AS text)) AND (:roleId IS NULL OR "
			+ "tok.role_id =cast(:roleId AS text)) AND (:schoolId IS NULL OR tok.school_id=cast(:schoolId AS text)) "
			+ "AND (:branchId IS NULL OR tok.branch_id =cast(:branchId AS text)) AND (:userId IS NULL OR "
			+ "u.id =cast(:userId AS text))",
			nativeQuery = true)
	TeacherCountProjection getTeachersTokenCount(String tokenId,String roleId,String schoolId, String branchId,
												 String userId);
	
	@Query(value = "SELECT u.id as userId, u.email as email, "
			+ "u.phone_number as phone, u.user_name as userName "
			+ "FROM user_token_mapping utm "
			+ "INNER JOIN tokens t on t.id=utm.token_id "
			+ "INNER JOIN users u on u.id=utm.user_id "
			+ "where utm.deleted=false AND utm.active=true "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (LOWER(u.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(u.phone_number) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(u.user_name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:tokenId as text) IS NULL) OR (t.id=cast(:tokenId as text)))", nativeQuery = true)
	List<TokenUserMappingProjection> findAllUsers(String search, String tokenId);
	
	@Query("SELECT utm.users.id as userId, utm.users.email as email, "
			+ "utm.users.phoneNumber as phone, utm.users.userName as userName "
			+ "FROM #{#entityName} utm "
			+ "INNER JOIN com.lms.userservice.entity.Tokens t on t.id=utm.tokens.id "
			+ "INNER JOIN com.lms.userservice.entity.Users u on u.id=utm.users.id "
			+ "where utm.deleted=false AND utm.active=true AND utm.tokens.id=:tokenId")
	List<TokenUserMappingProjection> findAllUsersByTokenId(String tokenId);

}
