
package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.lms.userservice.entity.Students;
import com.lms.userservice.projection.StudentsProjection;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.response.dto.AbsenteesResponseDto;
import com.lms.userservice.response.dto.BatchReceptionistRequestDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto;
import com.lms.userservice.response.dto.StudentDetailsMinResponseDto;

/**
 * <AUTHOR> C Achari
 * @since 11-Mar-2022
 *
 */
@Repository
public interface StudentsRepository extends JpaRepository<Students, String> {

	boolean existsById(String id);

	boolean existsByFirstName(String firstName);

	boolean existsByUserName(String userName);

	Students findByUserNameIgnoreCase(String userName);

	boolean existsBySectionIdAndDeleted(String sectionId, boolean deleted);

	@Query(value = "SELECT  std.id as id, std.first_name as firstName, std.last_name as lastName, "
			+ "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
			+ "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
			+ "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
			+ "std.section_id as sectionId, b.id as branchId, b.name as branchName, s.id as schoolId, "
			+ "s.name as schoolName, s.code as schoolCode, std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
			+ "std.address as address, u.user_name as userName, u.id as userId, "
			+ "to_char(to_timestamp(u.last_login_time / 1000), 'DD-MM-YYYY HH12:MI:SS AM') as lastLoginTime, std.active as active, "
			+ "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel,std.ir_status as irStatus "
			+ "FROM students std " + "INNER JOIN branches b ON b.id=std.branch_id "
			+ "INNER JOIN schools s ON s.id=std.school_id " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.active=true AND std.id=:id", nativeQuery = true)
	StudentsProjection findStudentById(String id);

	@Query(value = "SELECT std.id as id, std.first_name as firstName, std.last_name as lastName, "
			+ "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
			+ "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
			+ "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
			+ "std.section_id as sectionId, b.id as branchId, b.name as branchName, s.id as schoolId, "
			+ "s.name as schoolName, s.code as schoolCode, b.board_id as boardId, std.is_promoted as isPromoted, "
			+ "std.year_end_process as yearEndProcess, std.address as address, std.user_name as userName, u.id as userId, "
			+ "to_char(to_timestamp(u.last_login_time / 1000), 'DD-MM-YYYY HH12:MI:SS AM') as lastLoginTime, std.active as active, "
			+ "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel,std.ir_status as irStatus "
			+ "FROM students std " + "INNER JOIN branches b ON b.id=std.branch_id "
			+ "INNER JOIN schools s ON s.id=std.school_id " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.active=true AND std.user_name=:username", nativeQuery = true)
	StudentsProjection findStudentByUserName(String username);

	@Query(value = "SELECT  std.id as id, std.first_name as firstName, std.last_name as lastName, "
			+ "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
			+ "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
			+ "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
			+ "std.section_id as sectionId, b.id as branchId, b.name as branchName, s.id as schoolId, "
			+ "s.name as schoolName, s.code as schoolCode, std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
			+ "std.address as address, u.user_name as userName, u.id as userId, "
			+ "to_char(to_timestamp(u.last_login_time / 1000), 'DD-MM-YYYY HH12:MI:SS AM') as lastLoginTime, "
			+ "std.active as active, std.created_at as createdAt, "
			+ "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel,std.ir_status as irStatus "
			+ "FROM students std "
			+ "INNER JOIN branches b ON b.id=std.branch_id " + "INNER JOIN schools s ON s.id=std.school_id "
			+ "INNER JOIN users u ON u.user_name=std.user_name " + "WHERE std.deleted=false "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (std.section_id=(cast(:sectionId as text)))) "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (std.grade_id=(cast(:gradeId as text)))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (b.id=(cast(:branchId as text)))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (s.id=(cast(:schoolId as text)))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(std.active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (CONCAT(concat(LOWER(std.first_name), ' '), (LOWER(std.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(std.last_name), ' '), (LOWER(std.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(std.first_name)), (LOWER(std.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(std.last_name)), (LOWER(std.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (LOWER(std.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(std.mobile) LIKE CONCAT(cast(:search as text),'%')))", nativeQuery = true)
	Page<StudentsProjection> findAllStudentsByPagination(String sectionId, String gradeId, String branchId,
			String schoolId, String search, Boolean active, Pageable pageable);

	@Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s WHERE ((:schoolId IS NULL ) OR (s.schools.id =:schoolId)) "
			+ "AND s.deleted IS FALSE AND s.active IS TRUE")
	long getStudentCount(String schoolId);

	@Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
			+ "LEFT JOIN com.lms.userservice.entity.Branches b ON (b.id=s.branches.id AND b.deleted=false AND b.active=true) "
			+ "LEFT JOIN com.lms.userservice.entity.Schools sc ON (sc.id=s.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND b.boardId IN (:boardIds) "
			+ "AND ((:schoolId IS NULL) OR (s.schools.id=cast(:schoolId AS text))) ")
	long getStudentCountWithBoard(String schoolId, List<String> boardIds);

	@Query(value = "SELECT count(*) FROM students WHERE deleted=false AND active=true AND grade_id=:gradeId", nativeQuery = true)
	Long findCountOfStudentsByGradeId(String gradeId);

	@Query(value = "SELECT count(*) FROM students WHERE deleted=false AND active=true AND section_id=:sectionId", nativeQuery = true)
	Long findCountOfStudentBySectionId(String sectionId);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from students", nativeQuery = true)
	String findLastModifiedAt();

	@Modifying
	@Transactional
	@Query(value = "update students set deleted=true, active=false, modified_at=:modifiedAt, "
			+ "last_modified_by=:lastModifiedBy where deleted=false and id=:id", nativeQuery = true)
	int setDeleteStatus(String id, Long modifiedAt, String lastModifiedBy);

	@Query("SELECT new com.lms.userservice.response.dto.NameCommonResponseDto(id, firstName,lastName ) "
			+ "FROM #{#entityName} WHERE ( firstLanguageId =:languageId OR secondLanguageId =:languageId) AND deleted IS FALSE AND active IS TRUE")
	List<NameCommonResponseDto> getStudentsByLanguageId(String languageId);

	@Query("SELECT new com.lms.userservice.response.dto.NameCommonResponseDto(id, firstName,lastName ) "
			+ "FROM #{#entityName} WHERE studentCategoryId =:studentCategoryId AND deleted IS FALSE AND active IS TRUE")
	List<NameCommonResponseDto> getStudentsByStudentCategory(String studentCategoryId);

	@Query(value = "SELECT  std.id AS stdId, u.id as userId FROM students std "
			+ "INNER JOIN users u ON u.user_name=std.user_name " + "WHERE std.deleted=false AND std.active=true "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (std.section_id=(cast(:sectionId as text)))) "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (std.grade_id=(cast(:gradeId as text)))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (std.branch_id=(cast(:branchId as text)))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (std.school_id=(cast(:schoolId as text)))) "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (CONCAT(concat(LOWER(std.first_name), ' '), (LOWER(std.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(std.last_name), ' '), (LOWER(std.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(std.first_name)), (LOWER(std.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(std.last_name)), (LOWER(std.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (LOWER(std.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(std.mobile) LIKE CONCAT(cast(:search as text),'%')))", nativeQuery = true)
	List<Object[]> getUserIds(String sectionId, String gradeId, String branchId, String schoolId, String search);

	boolean existsByIdAndDeleted(String id, boolean b);

	@Query("SELECT new com.lms.userservice.response.dto.ShareDetailsResponseDto(userName, firstName, lastName, email, mobile) "
			+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
	ShareDetailsResponseDto findByUsernameForShareDetails(String userName);

	public boolean existsByUserNameAndDeleted(String userName, boolean deleted);

	@Query("SELECT new com.lms.userservice.request.dto.CreateUserEmailRequestDto(email, userName, firstName) "
			+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
	CreateUserEmailRequestDto findByUsernameForUpdatePassByAdmin(String userName);

	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndSectionIdIsNull(String schoolId, String branchId,
			String gradeId, boolean deleted);

	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndSectionId(String schoolId, String branchId,
			String gradeId, boolean deleted, String sectionId);

	@Query("SELECT count(id) FROM #{#entityName} WHERE deleted IS FALSE AND active=true "
			+ "AND schools.id=:schoolId AND branches.id=:branchId AND gradeId=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=(cast(:sectionId as text)))) ")
	long findCountByFilters(String schoolId, String branchId, String gradeId, String sectionId);

	@Query("SELECT count(id) > 0 FROM #{#entityName} WHERE deleted=false "
			+ "AND schools.id=:schoolId AND branches.id=:branchId AND gradeId=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=(cast(:sectionId as text)))) ")
	boolean checkGradeSectionHasMapping(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT s.id, s.first_name, s.last_name "
			+ "FROM public.students s INNER JOIN users u ON u.user_name = s.user_name where "
			+ "s.school_id =:schoolId and s.branch_id =:branchId and s.grade_id =:gradeId ", nativeQuery = true)
	public Page<StudentsProjection> findAllStudents(String schoolId, String branchId, String gradeId,
			Pageable pageable);

	@Query("SELECT count(id) FROM #{#entityName} WHERE deleted=false AND active=true "
			+ "AND schools.id=:schoolId AND branches.id=:branchId AND gradeId=:gradeId AND sectionId IN(:sectionIds) ")
	long countOfStudentbasedOnGradeAndSections(String schoolId, String branchId, String gradeId,
			List<String> sectionIds);

	@Query("SELECT count(id) FROM #{#entityName} WHERE deleted=false AND active=true "
			+ "AND schools.id=:schoolId AND branches.id=:branchId AND gradeId=:gradeId ")
	long countOfStudentbasedOnGrade(String schoolId, String branchId, String gradeId);

	@Query(value = "SELECT count(id) FROM students WHERE deleted IS FALSE AND active=true "
			+ "AND school_id=:schoolId AND branch_id=:branchId AND grade_id=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (section_id=(cast(:sectionId as text))))", nativeQuery = true)
	long findCountByPrincipalGradeAndSections(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT DISTINCT u.id FROM students std " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.active=true "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (std.section_id=(cast(:sectionId as text)))) "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (std.grade_id=(cast(:gradeId as text)))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (std.branch_id=(cast(:branchId as text)))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (std.school_id=(cast(:schoolId as text)))) ", nativeQuery = true)
	List<String> findUserIdsForStudent(String sectionId, String gradeId, String branchId, String schoolId);

//	@Query(value = "SELECT DISTINCT u.id FROM students std " + "INNER JOIN users u ON u.user_name=std.user_name "
//			+ "WHERE std.deleted=false AND std.active=true AND std.id IN(:studentsId) ", nativeQuery = true)
//	List<String> findUserIdsByStudentIds(List<String> studentsId);
	
	@Query(value = "SELECT DISTINCT u.id FROM students std " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.id IN(:studentsId) ", nativeQuery = true)
	List<String> findUserIdsByStudentIds(List<String> studentsId);

	@Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=s.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.UsersInstitutionMapping uim ON (uim.schools.id=s.schools.id AND uim.branches.id=s.branches.id AND uim.deleted=false AND uim.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Users us ON (us.id=uim.users.id AND us.deleted=false AND us.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Administration adm ON (us.userName=adm.userName AND us.deleted=false AND us.active=true) "
			+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND adm.userName=:userName "
			+ "AND ((:schoolId IS NULL ) OR (s.schools.id =:schoolId))")
	long findStudentCountForAdministrator(String schoolId, String userName);

	@Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=s.branches.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=s.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.UsersInstitutionMapping uim "
			+ "ON (uim.branches.id=b.id AND uim.schools.id=s.schools.id AND uim.deleted=false AND uim.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Users us ON (us.id=uim.users.id AND us.deleted=false AND us.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Administration adm ON (us.userName=adm.userName AND us.deleted=false AND us.active=true) "
			+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND adm.userName=:userName AND b.boardId IN (:boardIds) "
			+ "AND ((:schoolId IS NULL) OR (s.schools.id=cast(:schoolId AS text))) ")
	long findStudentCountWithBoardForAdministrator(String schoolId, List<String> boardIds, String userName);

	boolean existsBySchoolsIdAndBranchesIdAndDeletedAndActiveAndIdIn(String schoolId, String branchId, boolean deleted,
			boolean Active, List<String> id);

	@Modifying
	@Transactional
	@Query(value = "update students set active=:active, last_modified_by=:currentUser, modified_at=:modifiedAt "
			+ "WHERE deleted=false AND active=true AND branch_id=:branchId AND "
			+ "school_id=:schoolId AND id IN (:ids) ", nativeQuery = true)
	int deActiveStudentProfile(boolean active, String currentUser, Long modifiedAt, String branchId, String schoolId,
			List<String> ids);

	@Query(value = "SELECT COUNT(DISTINCT std.id) FROM students std " + "WHERE std.deleted=false AND std.active=true "
			+ "AND std.school_id=:schoolId AND std.branch_id=:branchId ", nativeQuery = true)
	long findStudentCountWithSchoolAndBranch(String schoolId, String branchId);

	@Query("SELECT count(id) FROM #{#entityName} WHERE deleted=false AND active=true "
			+ "AND schools.id=:schoolId AND branches.id=:branchId AND gradeId=:gradeId AND sectionId =:sectionId ")
	long countOfStudentbasedOnGradeAndSection(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT s.id AS id, s.first_name AS firstName, s.last_name AS lastName FROM students s "
			+ "WHERE s.deleted=false AND s.active=true "
			+ "AND s.school_id =:schoolId and s.branch_id =:branchId and s.grade_id =:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (s.section_id=(cast(:sectionId as text)))) ", nativeQuery = true)
	public List<StudentsProjection> findStudentDetailsByFilters(String schoolId, String branchId, String gradeId,
			String sectionId);

	@Query(value = "SELECT DISTINCT u.id FROM students std " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.active=true AND std.id=:studentId ", nativeQuery = true)
	String findUserIdByStudentId(String studentId);

	@Query("SELECT userName FROM #{#entityName} WHERE deleted=false AND active=true "
			+ "AND schools.id=:schoolId AND branches.id=:branchId ")
	List<String> findUserNameBySchoolAndBranch(String schoolId, String branchId);

	@Query(value = "SELECT COUNT(id) FROM students  " + " WHERE school_id = :schoolId " + "AND branch_id = :branchId "
			+ "AND grade_id = :gradeId " + "AND section_id = :sectionId", nativeQuery = true)
	Integer getCountOfStudentsSection(String schoolId, String branchId, String gradeId, String sectionId);

//	@Query(value="SELECT count(id) from students where grade_id=:gradeId and school_id=:schoolId "
//			+ "and active=true and deleted=false ",nativeQuery = true)
//	long getStudentCountByGradeId(String gradeId, String schoolId);

	@Query(value = "SELECT DISTINCT u.id FROM students std " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.active=true "
			+ "AND std.section_id IN(:sectionIds) AND std.grade_id IN(:gradeIds) "
			+ "AND std.branch_id IN(:branchIds) AND std.school_id IN(:schoolIds) ", nativeQuery = true)
	List<String> findUserIdsForDifferentSchoolsWithSection(List<String> sectionIds, List<String> gradeIds,
			List<String> branchIds, List<String> schoolIds);

	@Query(value = "SELECT DISTINCT u.id FROM students std " + "INNER JOIN users u ON u.user_name=std.user_name "
			+ "WHERE std.deleted=false AND std.active=true AND std.grade_id IN(:gradeIds) "
			+ "AND std.branch_id IN(:branchIds) AND std.school_id IN(:schoolIds) ", nativeQuery = true)
	List<String> findUserIdsForDifferentSchools(List<String> gradeIds, List<String> branchIds, List<String> schoolIds);

	@Query(value = "SELECT count(id) " + "from students " + "where grade_id=:gradeId and " + "school_id=:schoolId and "
			+ "active=true and deleted=false ", nativeQuery = true)
	long getStudentCountByGradeId(String gradeId, String schoolId);

	@Query(value = "SELECT count(DISTINCT id) FROM students WHERE deleted IS FALSE AND active=true "
			+ "AND school_id=:schoolId AND branch_id=:branchId AND grade_id=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (section_id=(cast(:sectionId as text))))", nativeQuery = true)
	long findCountByPrincipalGradeSections(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT COUNT(DISTINCT id) FROM students WHERE deleted=false AND active=true "
			+ "AND school_id=:schoolId AND branch_id=:branchId AND grade_id=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (section_id=(cast(:sectionId as text))))", nativeQuery = true)
	Integer findStudentsCountByGradesWithSection(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT DISTINCT st.id FROM students st "
			+ "INNER JOIN schools s ON(s.id=st.school_id AND s.deleted=false AND s.active=true) "
			+ "INNER JOIN branches b ON(b.id=st.branch_id AND b.deleted=false AND b.active=true) "
			+ "WHERE st.deleted=false AND st.active=true "
			+ "AND ((cast(:boardId as text) IS NULL) OR (b.board_id=(cast(:boardId as text)))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (st.school_id=(cast(:schoolId as text)))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (st.branch_id=(cast(:branchId as text)))) "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (st.grade_id=(cast(:gradeId as text)))) "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (st.section_id=(cast(:sectionId as text)))) ", nativeQuery = true)
	List<String> getStudentIds(String boardId, String schoolId, String branchId, String gradeId, String sectionId);

	@Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=s.branches.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=s.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND b.boardId=:boardId AND s.gradeId=:gradeId "
			+ "AND ((:branchId IS NULL) OR (s.branches.id=cast(:branchId AS text))) "
			+ "AND ((:schoolId IS NULL) OR (s.schools.id=cast(:schoolId AS text))) "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (s.sectionId=(cast(:sectionId as text)))) ")
	Integer findStudentCountForPrincipal(String boardId, String schoolId, String branchId, String gradeId,
			String sectionId);

	@Query("SELECT new com.lms.userservice.response.dto.NameCommonResponseDto(id, firstName,lastName ) "
			+ "FROM #{#entityName} WHERE (id IN(:Ids)) AND deleted IS FALSE AND active IS TRUE")
	List<NameCommonResponseDto> getStudentsByStudentIds(List<String> Ids);

	@Query(value = "SELECT st.id AS id, b.board_id AS boardId, st.grade_id AS gradeId, st.section_id AS sectionId, st.branch_id AS branchId, "
			+ " st.school_id AS schoolId FROM students st "
			+ "INNER JOIN branches b ON (b.id=st.branch_id AND b.deleted=false AND b.active=true) "
			+ "WHERE st.deleted=false AND st.active=true AND st.user_name=:userName ", nativeQuery = true)
	StudentsProjection getStudentDetailsByName(String userName);

	@Query("SELECT new com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto(st.id, b.boardId, s.id, s.name, b.id, b.name, st.gradeId, st.sectionId) "
			+ "FROM #{#entityName} st "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND  b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
			+ "WHERE st.deleted IS FALSE AND st.active IS TRUE AND st.userName=:userName ")
	StudentAssignedMinDetailResponseDto getStudentsByStudentIds(String userName);

	@Query(value = "SELECT count(id) " + "from students " + "where grade_id=:gradeId and " + "school_id=:schoolId and "
			+ "section_id=:sectionId and " + "active=true and deleted=false ", nativeQuery = true)
	long getStudentCountByGradeIdSection(String gradeId, String schoolId, String sectionId);

	@Query(value = "SELECT COUNT(DISTINCT id) FROM students WHERE deleted=false AND active=true "
			+ "AND school_id=:schoolId AND branch_id=:branchId AND grade_id=:gradeId "
			+ "AND section_id IN (:sectionIds)", nativeQuery = true)
	Integer getStudentsCountByGradeWithSectionList(String schoolId, String branchId, String gradeId,
			List<String> sectionIds);

//	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto(st.id, b.boardId, s.id, s.name, b.id, b.name, st.gradeId, st.sectionId) "
//			+ "FROM #{#entityName} st "
//			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND b.deleted=false AND b.active=true) "
//			+ "INNER JOIN com.lms.userservice.entity.BranchPlanMappings bpm ON (bpm.branches.id=b.id AND bpm.deleted=false AND bpm.active=true) "
//			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
//			+ "WHERE st.deleted=false AND st.active=true AND b.boardId=:boardId "
//			+ "AND bpm.planId IN(:planIds) AND st.gradeId IN(:gradeIds)")
//	List<StudentAssignedMinDetailResponseDto> findAllStudentsByTheFilters(String boardId, List<String> planIds,
//			List<String> gradeIds);	

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto(st.id, b.boardId, s.id, s.name, b.id, b.name, st.gradeId, st.sectionId) "
			+ "FROM #{#entityName} st "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
			+ "WHERE st.deleted=false AND st.active=true AND b.boardId=:boardId AND st.gradeId IN(:gradeIds) ")
	List<StudentAssignedMinDetailResponseDto> findAllStudentsByTheFilters(String boardId, List<String> gradeIds);

	@Query(value ="SELECT DISTINCT s.id As id,s.first_name AS firstName ,s.last_name AS lastName FROM students s WHERE s.school_id=:schoolId " +
			"AND s.branch_id=:branchId AND s.grade_id=:gradeId AND s.active=true AND s.deleted=false " +
			"AND ((cast(:sectionId as text) IS NULL) OR (s.section_id=(cast(:sectionId as text)))) ",nativeQuery = true)
	List<StudentsProjection> getStudentListInSection(String schoolId, String branchId, String gradeId, String sectionId);
	
	@Query(value = " SELECT s.id As id, s.first_name AS firstName, s.last_name As lastName  FROM students s "
			+ "WHERE s.deleted = false AND s.active = true AND s.school_id = :schoolId "
			+ "AND s.branch_id = :branchId AND s.section_id = :sectionId AND s.grade_id = :gradeId "
			+ "AND s.id IN (:studentIds)", nativeQuery = true)
	List<StudentsProjection> getFormalStudentDetailsByName(String schoolId, String branchId, String sectionId,
			String gradeId, List<String> studentIds);

	@Query("SELECT Count(DISTINCT st.id) FROM #{#entityName} st "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
			+ "WHERE st.deleted=false AND st.active=true AND b.boardId=:boardId AND st.gradeId=:gradeId ")
	long globalStudentsCount(String boardId, String gradeId);

	@Query(value ="SELECT DISTINCT st.id As id,st.first_name AS firstName ,st.last_name AS lastName FROM students st " +
			"INNER JOIN branches b ON (b.id=st.branch_id AND b.deleted=false AND b.active=true) " +
			"INNER JOIN schools s ON (s.id=st.school_id AND s.deleted=false AND s.active=true) " +
			"WHERE st.deleted=false AND st.active=true AND b.board_id=:boardId AND st.grade_id=:gradeId ",nativeQuery = true)
	List<StudentsProjection> getStudentListInGrade(String boardId, String gradeId);

	@Query("SELECT new com.lms.userservice.response.dto.StudentDetailsMinResponseDto(st.id, st.firstName, st.lastName, st.userName, st.mobile, st.active) "
			+ "FROM #{#entityName} st "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND  b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
			+ "WHERE st.deleted IS FALSE AND st.active IS TRUE AND st.id=:studentId ")
	StudentDetailsMinResponseDto findStudentDetails(String studentId);

	@Query("SELECT new com.lms.userservice.response.dto.BatchReceptionistRequestDto(st.firstName, st.userName, st.mobile) "
			+ "FROM #{#entityName} st "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
			+ "WHERE st.deleted IS FALSE AND st.active IS TRUE AND st.schools.id=:schoolId "
			+ "AND st.branches.id=:branchId AND st.gradeId=:gradeId AND b.boardId=:boardId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (st.sectionId=(cast(:sectionId as text)))) ")
	List<BatchReceptionistRequestDto> gettingAllStudentsDetails(String boardId, String schoolId, String branchId,
			String gradeId, String sectionId);

	@Query("SELECT new com.lms.userservice.response.dto.BatchReceptionistRequestDto(st.firstName, st.userName, st.mobile) "
			+ "FROM #{#entityName} st "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.id=st.branches.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Schools s ON (s.id=st.schools.id AND s.deleted=false AND s.active=true) "
			+ "WHERE st.deleted IS FALSE AND st.active IS TRUE AND st.schools.id=:schoolId AND st.id IN (:studentIds) "
			+ "AND st.branches.id=:branchId AND st.gradeId=:gradeId AND b.boardId=:boardId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (st.sectionId=(cast(:sectionId as text)))) ")
	List<BatchReceptionistRequestDto> gettingStudentsDetails(String boardId, String schoolId, String branchId,
			String gradeId, String sectionId, List<String> studentIds);
	
	@Query("SELECT branches.id FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
	String getBranchByUsername(String userName);
	
	@Query("SELECT new com.lms.userservice.response.dto.AbsenteesResponseDto(id, CONCAT(firstName, ' ', lastName), userName) "
			+ "FROM #{#entityName} "
			+ "WHERE deleted=false AND active=true AND schools.id=:schoolId "
			+ "AND branches.id=:branchId AND gradeId=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=(cast(:sectionId as text)))) ")
	List<AbsenteesResponseDto> absenteesDetailsWithoutIds(String schoolId, String branchId, String gradeId,
			String sectionId);
	
	@Query("SELECT new com.lms.userservice.response.dto.AbsenteesResponseDto(id, CONCAT(firstName, ' ', lastName), userName) "
			+ "FROM #{#entityName} "
			+ "WHERE deleted=false AND active=true AND schools.id=:schoolId AND branches.id=:branchId "
			+ "AND gradeId=:gradeId AND id NOT IN(:studentIds) "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=(cast(:sectionId as text)))) ")
	List<AbsenteesResponseDto> absenteesDetails(String schoolId, String branchId, String gradeId, String sectionId,
			List<String> studentIds);
	
	@Query(value = "SELECT s.id AS id, s.first_name AS firstName, s.last_name AS lastName FROM students s "
			+ "WHERE s.deleted=false AND s.active=true "
			+ "AND s.school_id =:schoolId and s.grade_id =:gradeId and s.branch_id =:branchId "
			+ "AND s.section_id=:sectionId  ", nativeQuery = true)
	List<StudentsProjection> findAvidStudentDetailsByFilters(String schoolId, String gradeId, String sectionId,String branchId);


	
    @Query(value = "SELECT std.id as id, std.first_name as firstName, std.last_name as lastName, "
            + "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
            + "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
            + "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
            + "std.section_id as sectionId, b.id as branchId, b.name as branchName, s.id as schoolId, "
            + "s.name as schoolName, s.code as schoolCode, std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
            + "std.address as address, u.user_name as userName, u.id as userId, "
            + "to_char(to_timestamp(u.last_login_time / 1000), 'DD-MM-YYYY HH12:MI:SS AM') as lastLoginTime, "
            + "std.active as active, std.created_at as createdAt, "
            + "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel, "
            + "std.ir_status as irStatus "
            + "FROM students std "
            + "INNER JOIN branches b ON b.id = std.branch_id "
            + "INNER JOIN schools s ON s.id = std.school_id "
            + "INNER JOIN users u ON u.user_name = std.user_name "
            + "WHERE std.deleted = false AND std.active=true "
            + "AND std.ir_status =:irStatus "
            + "AND std.section_id IN (:assignedSections) "
            + "AND std.grade_id IN (:assignedGrades) ", nativeQuery = true)           
    Page<StudentsProjection> findAllStudentBysectionsAndGradesAndIrstatus(@Param("irStatus") String irStatus,@Param("assignedGrades") List<String> assignedGrades,@Param("assignedSections") List<String> assignedSections,
            Pageable pageable);


    
    @Query(value = "SELECT std.id as id, std.first_name as firstName, std.last_name as lastName, "
            + "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
            + "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
            + "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
            + "std.section_id as sectionId, b.id as branchId, b.name as branchName, s.id as schoolId, "
            + "s.name as schoolName, s.code as schoolCode, std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
            + "std.address as address, u.user_name as userName, u.id as userId, "
            + "to_char(to_timestamp(u.last_login_time / 1000), 'DD-MM-YYYY HH12:MI:SS AM') as lastLoginTime, "
            + "std.active as active, std.created_at as createdAt, "
            + "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel,std.ir_status as irStatus "
            + "FROM students std "
            + "INNER JOIN branches b ON b.id = std.branch_id "
            + "INNER JOIN schools s ON s.id = std.school_id "
            + "INNER JOIN users u ON u.user_name = std.user_name "
            + "WHERE std.deleted = false AND std.active=true "
            + "AND std.section_id IN (:assignedSections) "
            + "AND std.grade_id IN (:assignedGrades)", nativeQuery = true)
    Page<StudentsProjection> findAllStudentBysectionsAndGrades(@Param("assignedGrades") List<String> sectionIds,@Param("assignedSections") List<String> gradeIds,
            Pageable pageable);


    @Query(value = "SELECT std.id as id, std.first_name as firstName, std.last_name as lastName, "
            + "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
            + "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
            + "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
            + "std.section_id as sectionId, b.id as branchId, b.name as branchName, s.id as schoolId, "
            + "s.name as schoolName, s.code as schoolCode, std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
            + "std.address as address, u.user_name as userName, u.id as userId, "
            + "to_char(to_timestamp(u.last_login_time / 1000), 'DD-MM-YYYY HH12:MI:SS AM') as lastLoginTime, "
            + "std.active as active, std.created_at as createdAt, "
            + "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel,std.ir_status as irStatus, "
            + "std.diagnostic_test as diagnosticTest "
            + "FROM students std "
            + "INNER JOIN branches b ON b.id = std.branch_id "
            + "INNER JOIN schools s ON s.id = std.school_id "
            + "INNER JOIN users u ON u.user_name = std.user_name "
            + "WHERE std.deleted = false AND std.active=true ", nativeQuery = true)
	Page<StudentsProjection> findAllStudents(Pageable pageable);
    
    
    @Modifying
    @Transactional
    @Query(value = "UPDATE students SET ir_status = 'ONBOARD' WHERE id = :id", nativeQuery = true)
    int updateIrStatus(@Param("id") String id);

    
    @Query(value = "SELECT std.id as id, std.first_name as firstName, std.last_name as lastName, "
            + "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
            + "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
            + "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
            + "std.section_id as sectionId,  "
            + "b.id as branchId, b.name as branchName, s.id as schoolId, "
            + "s.name as schoolName, "
            + "std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
            + "std.address as address, std.active as active, std.created_at as createdAt, "
            + "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel, "
            + "std.ir_status as irStatus, "
            + "std.diagnostic_test as diagnosticTest "
            + "FROM students std "
            + "INNER JOIN branches b ON b.id = std.branch_id "
            + "INNER JOIN schools s ON s.id = std.school_id "            
            + "WHERE std.deleted = false AND std.active=true "
            + "AND std.ir_status =:irStatus "
            + "AND std.section_id IN (:assignedSections) "
            + "AND std.grade_id IN (:assignedGrades) "
            + "AND ((cast(:schoolId as text) IS NULL) OR (s.id=(cast(:schoolId as text)))) "
            + "AND ((cast(:branchId as text) IS NULL) OR (b.id=(cast(:branchId as text)))) ", nativeQuery = true)           
    Page<StudentsProjection> findAllStudentsBysectionsAndGradesAndIrstatus(@Param("irStatus") String irStatus,@Param("assignedGrades") List<String> assignedGrades,@Param("assignedSections") List<String> assignedSections,
            String schoolId, String branchId, Pageable pageable);

    @Query(value = "SELECT std.id as id, std.first_name as firstName, std.last_name as lastName, "
            + "std.email as email, std.mobile as mobile, std.dob as dob, std.gender as gender, "
            + "std.document_url as documentUrl, std.first_language_id as firstLanguageId, std.second_language_id as secondLanguageId, "
            + "std.admission_date as admissionDate, std.student_category_id as studentCategoryId, std.grade_id as gradeId, "
            + "std.section_id as sectionId, "
            + "b.id as branchId, b.name as branchName, s.id as schoolId, "
            + "s.name as schoolName, "
            + "std.is_promoted as isPromoted, std.year_end_process as yearEndProcess, "
            + "std.address as address, std.active as active, std.created_at as createdAt, "
            + "std.learning_generation as learningGeneration, std.geographical_type as geographicalType, std.grade_level as gradeLevel,std.ir_status as irStatus, "
            + "std.diagnostic_test as diagnosticTest "
            + "FROM students std "
            + "INNER JOIN branches b ON b.id = std.branch_id "
            + "INNER JOIN schools s ON s.id = std.school_id "
            + "WHERE std.deleted = false AND std.active=true "
            + "AND std.section_id IN (:assignedSections) "
            + "AND std.grade_id IN (:assignedGrades) "
            + "AND ((cast(:schoolId as text) IS NULL) OR (s.id=(cast(:schoolId as text)))) "
            + "AND ((cast(:branchId as text) IS NULL) OR (b.id=(cast(:branchId as text)))) ", nativeQuery = true)        
    Page<StudentsProjection> findAllStudentsBysectionsAndGrades(@Param("assignedGrades") List<String> assignedGrades,@Param("assignedSections") List<String> assignedSections,
    		String schoolId, String branchId, Pageable pageable);
    
   
}
