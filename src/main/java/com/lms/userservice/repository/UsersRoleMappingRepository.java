package com.lms.userservice.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.UsersRoleMapping;

/**
 * <AUTHOR>
 * @since 0.0.1
 *
 */
@Repository
public interface UsersRoleMappingRepository extends JpaRepository<UsersRoleMapping, String> {

	boolean existsById(String id);

	Long countByUsersId(String userId);

	@Transactional
	@Modifying
	Long deleteByUsersId(String userId);
	
	@Modifying
	@Transactional
	@Query("update #{#entityName} set active=:active, deleted=:deleted, modifiedAt=:modifiedAt"
			+ " where users.id=:userId ")
	void updateUserMappingByUserId(@Param("active") boolean active, @Param("deleted") boolean deleted,
			@Param("modifiedAt") Long modifiedAt, @Param("userId") String userId);
	
	@Query(value = "SELECT urm.role_id FROM users_role_mapping urm WHERE urm.deleted=false AND urm.active=true AND urm.user_id=:userId", nativeQuery = true)
	List<String> findAllRolesByUserId(@Param("userId") String userId);
        
	@Query(value = "SELECT urm.user_id FROM users_role_mapping urm WHERE urm.deleted=false AND urm.active=true AND urm.role_id=:roleId", nativeQuery = true)
	List<String> findAllUserIdsByRoleId(@Param("roleId") String roleId);
	
	boolean existsByUsersId(String userId);
        
	@Query(value = "SELECT sum(count) FROM ("
			+ "SELECT count(*) FROM users_role_mapping urm WHERE urm.deleted=false AND urm.role_id=:roleId UNION ALL "
			+ "SELECT count(*) FROM tokens t WHERE t.deleted=false AND t.role_id=:roleId UNION ALL "
			+ "SELECT count(*) FROM teachers tc WHERE tc.deleted=false AND tc.role_id=:roleId "
			+ ") as count", nativeQuery = true)
	Long countRoleIdForDeletion(@Param("roleId") String roleId);
	
	@Modifying
	@Transactional
	@Query(value = "update users_role_mapping set active=:active, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "where deleted=false and role_id=:id", nativeQuery = true)
	void setActiveStatus(String id, boolean active, Long modifiedAt, String lastModifiedBy);

	@Query(value = "select active from users_role_mapping where role_id=:id", nativeQuery = true)
	boolean findActiveStatus(String id);

	@Query("SELECT urm FROM UsersRoleMapping urm WHERE urm.users.id = :userId")
    UsersRoleMapping findByUserId(@Param("userId") String userId);

}
