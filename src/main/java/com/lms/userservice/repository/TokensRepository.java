package com.lms.userservice.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.Tokens;
import com.lms.userservice.projection.TokenListProjection;

@Repository
public interface TokensRepository extends JpaRepository<Tokens, String> {

	boolean existsById(String id);

	boolean existsByToken(String token);

	Tokens findByToken(String token);

	@Query(value = "SELECT distinct t.id as id, t.token as token, t.role_id as roleId, u.email as email, "
			+ "CASE WHEN (t.multi_user=true) THEN 'Yes' ELSE 'No' END as multiUser, "
			+ "t.token_use_count as noOfUsers,  t.expiary_date as expiaryDate, "
			+ "TO_TIMESTAMP(t.created_at/1000) as dateCreated, u.id as usedUserId, "
			+ "s.id as schoolId, s.name as school, b.id as branchId, b.name as branch, t.created_at as createdAt "
			+ "FROM tokens t " + "LEFT JOIN user_token_mapping utp ON utp.token_id=t.id "
			+ "LEFT JOIN users u ON utp.user_id=u.id " + "INNER JOIN schools s ON s.id=t.school_id "
			+ "INNER JOIN branches b ON (b.id=t.branch_id AND b.school_id=s.id) " + "WHERE t.deleted=false "
			+ "AND ((cast(:roleId as text) IS NULL) OR (t.role_id=(cast(:roleId as text)))) "
			+ "AND ((cast(:userId as text) IS NULL) OR (u.id=(cast(:userId as text)))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (t.branch_id=(cast(:branchId as text)))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (t.school_id=(cast(:schoolId as text)))) "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (LOWER(u.user_name) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(u.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(u.phone_number) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(t.active AS text) = cast(:active AS text))) ", nativeQuery = true)
	Page<TokenListProjection> findTokenByPagination(String roleId, String userId, String branchId, String schoolId,
			String search, Pageable pageable, Boolean active);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from tokens", nativeQuery = true)
	String findLastModifiedAt();

}
