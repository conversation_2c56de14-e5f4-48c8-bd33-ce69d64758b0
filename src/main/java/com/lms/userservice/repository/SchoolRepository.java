package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.lms.userservice.entity.Schools;
import com.lms.userservice.model.InstitutionList;
import com.lms.userservice.projection.BranchesProjection;
import com.lms.userservice.projection.SchoolBranchesProjection;
import com.lms.userservice.projection.SchoolsProjection;
import com.lms.userservice.response.dto.InstitutionStudentCountResponseDto;
import com.lms.userservice.response.dto.SchoolResponseDto;
import com.lms.userservice.response.dto.SchoolsBranchsMinResponseDto;

/**
 * The School Repository interface
 * 
 * <AUTHOR>
 */

public interface SchoolRepository extends JpaRepository<Schools, String> {

	boolean existsById(String id);
	
	boolean existsByIdAndDeletedAndActive(String id, boolean deleted, boolean active);

	boolean existsByNameAndDeleted(String name, boolean deleted);
	
	boolean existsByNameAndDeletedAndIdNot(String name, boolean deleted, String id);

	boolean existsByCodeAndDeleted(String code, boolean deleted);

	boolean existsByCodeAndDeletedAndIdNot(String code, boolean deleted, String id);
	
	boolean existsByPocEmailAndDeleted(String pocEmail, boolean deleted);
	
	boolean existsByPocEmailAndDeletedAndIdNot(String pocEmail, boolean deleted, String id);

	@Query(value = "SELECT new com.lms.userservice.response.dto.SchoolResponseDto"
			+ "(s.id, s.name, s.code, s.cityId, s.pocEmail, s.phoneNumber,s.website, "
			+ "s.signatoryName, s.signatoryRole, s.logoUrl, s.active) " 
			+ "FROM #{#entityName} s WHERE s.id=:id and s.deleted=false")
	SchoolResponseDto findSchoolById(@Param("id") String id);
	
	//========= Below both methods for pagination, IN is not working with IS NULL checking so created another method with board is mandatory ====//
//	@Query(value = "select distinct s.id as id, s.name as name, s.code as code, s.city_id as cityId, s.poc_email as pocEmail, "
//			+ "s.phone_number as phoneNumber, s.website as website, s.signatory_name as signatoryName, "
//			+ "s.signatory_role as signatoryRole, s.logo_url as logoUrl, s.active as active, s.created_at as createdAt, "
//			+ "count(distinct b.id) AS numberOfBranches, "
//			+ "count(distinct t.id) filter(WHERE t.academic_staff_profile='COORDINATOR') AS numberOfCoordinators, "
//			+ "count(distinct t.id) filter(WHERE t.academic_staff_profile='TEACHER') AS numberOfTeachers, "
//			+ "count(distinct t.id) filter(WHERE t.academic_staff_profile='PRINCIPAL') AS numberOfPrincipals, "
//			+ "count(distinct st.id) AS numberOfStudents "
//			+ "FROM schools s "
//			+ "LEFT JOIN branches b ON (b.school_id=s.id AND b.deleted=false AND b.active=true) "
//			+ "LEFT JOIN teachers t ON (t.school_id=s.id AND t.deleted=false AND t.active=true) "
//			+ "LEFT JOIN students st ON (st.school_id=s.id AND st.deleted=false AND st.active=true) "
//			+ "WHERE s.deleted=false "
//			+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) "
//			+ "AND ((cast(:active as text) IS NULL) OR (cast(s.active AS text) = cast(:active AS text))) "
//			+ "AND ((cast(:search as text) IS NULL) "
//			+ "OR ((LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))) "
//			+ "OR ((LOWER(s.poc_email) LIKE CONCAT(cast(:search as text),'%'))) "
//			+ "OR ((LOWER(s.phone_number) LIKE CONCAT(cast(:search as text),'%')))) "
//			+ "GROUP BY (s.id, s.board_id, s.poc_email, s.phone_number, s.active)", nativeQuery = true)

	@Query(value = "select distinct s.id as id, s.name as name, s.code as code, s.city_id as cityId, s.poc_email as pocEmail, "
			+ "s.phone_number as phoneNumber, s.website as website, s.signatory_name as signatoryName, "
			+ "s.signatory_role as signatoryRole, s.logo_url as logoUrl, s.active as active, s.created_at as createdAt, "
			+ "'0' AS numberOfBranches, "
			+ "'0' AS numberOfCoordinators, "
			+ "'0' AS numberOfTeachers, "
			+ "'0' AS numberOfPrincipals, "
			+ "'0' AS numberOfStudents "
			+ "FROM schools s "
			+ "LEFT JOIN branches b ON (b.school_id=s.id AND b.deleted=false AND b.active=true) "
//			+ "LEFT JOIN teachers t ON (t.school_id=s.id AND t.deleted=false AND t.active=true) "
//			+ "LEFT JOIN students st ON (st.school_id=s.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false "
			+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(s.active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR ((LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "OR ((LOWER(s.poc_email) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "OR ((LOWER(s.phone_number) LIKE CONCAT(cast(:search as text),'%')))) "
			+ "GROUP BY (s.id, s.board_id, s.poc_email, s.phone_number, s.active)", nativeQuery = true)
	Page<SchoolsProjection> findAllSchoolsByPagination(String schoolId, String search, Pageable pageable,
			Boolean active);
	
//	@Query(value = "select distinct s.id as id, s.name as name, s.code as code, s.city_id as cityId, s.poc_email as pocEmail, "
//			+ "s.phone_number as phoneNumber, s.website as website, s.signatory_name as signatoryName, "
//			+ "s.signatory_role as signatoryRole, s.logo_url as logoUrl, s.active as active, s.created_at as createdAt, "
//			+ "count(distinct b.id) AS numberOfBranches, "
//			+ "count(distinct t.id) filter(WHERE t.academic_staff_profile='COORDINATOR') AS numberOfCoordinators, "
//			+ "count(distinct t.id) filter(WHERE t.academic_staff_profile='TEACHER') AS numberOfTeachers, "
//			+ "count(distinct t.id) filter(WHERE t.academic_staff_profile='PRINCIPAL') AS numberOfPrincipals, "
//			+ "count(distinct st.id) AS numberOfStudents "
//			+ "FROM schools s "
//			+ "LEFT JOIN branches b ON (b.school_id=s.id AND b.deleted=false AND b.active=true) "
//			+ "LEFT JOIN teachers t ON (t.school_id=s.id AND t.branch_id=b.id AND t.deleted=false AND t.active=true) "
//			+ "LEFT JOIN students st ON (st.school_id=s.id AND st.branch_id=b.id AND st.deleted=false AND st.active=true) "
//			+ "WHERE s.deleted=false AND b.board_id IN (:boardId) "
//			+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) "
//			+ "AND ((cast(:active as text) IS NULL) OR (cast(s.active AS text) = cast(:active AS text))) "
//			+ "AND ( (cast(:search as text) IS NULL) "
//			+ "OR ((LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))) "
//			+ "OR ((LOWER(s.poc_email) LIKE CONCAT(cast(:search as text),'%'))) "
//			+ "OR ((LOWER(s.phone_number) LIKE CONCAT(cast(:search as text),'%'))) ) "
//			+ "GROUP BY (s.id, b.board_id, s.poc_email, s.phone_number, s.active)", nativeQuery = true)

	@Query(value = "select distinct s.id as id, s.name as name, s.code as code, s.city_id as cityId, s.poc_email as pocEmail, "
			+ "s.phone_number as phoneNumber, s.website as website, s.signatory_name as signatoryName, "
			+ "s.signatory_role as signatoryRole, s.logo_url as logoUrl, s.active as active, s.created_at as createdAt, "
			+ "'0' AS numberOfBranches, "
			+ "'0' AS numberOfCoordinators, "
			+ "'0' AS numberOfTeachers, "
			+ "'0' AS numberOfPrincipals, "
			+ "'0' AS numberOfStudents "
			+ "FROM schools s "
			+ "LEFT JOIN branches b ON (b.school_id=s.id AND b.deleted=false AND b.active=true) "
//			+ "LEFT JOIN teachers t ON (t.school_id=s.id AND t.branch_id=b.id AND t.deleted=false AND t.active=true) "
//			+ "LEFT JOIN students st ON (st.school_id=s.id AND st.branch_id=b.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false AND b.board_id IN (:boardId) "
			+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(s.active AS text) = cast(:active AS text))) "
			+ "AND ( (cast(:search as text) IS NULL) "
			+ "OR ((LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "OR ((LOWER(s.poc_email) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "OR ((LOWER(s.phone_number) LIKE CONCAT(cast(:search as text),'%'))) ) "
			+ "GROUP BY (s.id, b.board_id, s.poc_email, s.phone_number, s.active)", nativeQuery = true)
	Page<SchoolsProjection> withBoardFindAllSchoolsByPagination(String schoolId, List<String> boardId, String search,
			Pageable pageable, Boolean active);
	//================== Pagination ended ====================//

	long countByDeleted(boolean deleted);

	//========== find all schools for the drop-down. 1st method use for SUPER_ADMIN, 2nd method is used for SCHOOL_ADMIN/MANAGEMENT //
	@Query(value = "SELECT s.id as id, s.name AS name, s.poc_email as pocEmail,  "
			+ "s.phone_number as phoneNumber, s.active as active, s.code as code FROM schools s "
			+ "WHERE s.deleted IS FALSE AND s.active IS TRUE "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (LOWER(s.code) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "ORDER BY s.created_at DESC limit 50", nativeQuery = true)
	List<SchoolsProjection> getSchools(String search);
	
	@Query(value = "SELECT DISTINCT s.id as id, s.name AS name, s.poc_email as pocEmail,  "
			+ "s.phone_number as phoneNumber, s.active as active, s.code as code "
			+ "FROM schools s "
			+ "INNER JOIN users_institution_mapping uim ON (uim.school_id=s.id AND uim.deleted=false AND uim.active=true) "
			+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND uim.user_id=:userId "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (LOWER(s.code) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))) "
			, nativeQuery = true)
	List<SchoolsProjection> getSchoolsForSchoolAdmin(String userId, String search);
	//========== find all schools for the drop-down Query END ================================= //

	@Query(value = "SELECT s.id as schoolId, s.name as schoolName, s.active as schoolActive, "
			+ "b.id as branchId, b.name as branchName, b.active as branchActive  FROM schools s INNER JOIN "
			+ "branches b ON (s.id = b.school_id AND b.deleted IS FALSE AND b.active IS TRUE ) "
			+ "WHERE s.deleted IS false AND s.active IS TRUE  "
			+ "AND ((cast(:search as text) IS NULL) OR (LOWER(s.name) LIKE CONCAT(cast(:search as text),'%') ) "
			+ "OR (LOWER(s.code) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(b.name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "ORDER BY s.created_at DESC limit 50 ", nativeQuery = true)
	List<SchoolBranchesProjection> getSchooWithlBranches(String search);

	@Query(value = "select count(*) from branches where school_id=:id and deleted=false AND active=true " 
			+ "UNION ALL "
			+ "select count(*) from students where school_id=:id and deleted=false AND active=true " 
			+ "UNION ALL "
			+ "select count(*) from teachers where school_id=:id and deleted=false AND active=true " 
			+ "UNION ALL "
			+ "select count(*) from tokens where school_id=:id and deleted=false AND active=true " 
			+ "UNION ALL "
			+ "select count(*) from grade_section_mapping where school_id=:id and deleted=false AND active=true "
			+ "UNION ALL "
			+ "select count(*) from users_institution_mapping where school_id=:id and deleted=false AND active=true", nativeQuery = true)
	List<Long> findAllMappingCountOfSchool(String id);

	@Query(value = "SELECT count(*) FROM schools WHERE deleted=false AND board_id=:boardId " 
			+ "UNION ALL "
			+ "SELECT count(*) FROM branches WHERE deleted=false AND active=true AND board_id=:boardId", nativeQuery = true)
	List<Long> findCountOfSchoolAndBranchByBoardId(String boardId);

	@Query(value = "SELECT count(*) FROM schools WHERE deleted=false AND active=true AND city_id=:cityId "
			+ "UNION ALL "
			+ "SELECT count(*) FROM branches WHERE deleted=false AND active=true AND city_id=:cityId", nativeQuery = true)
	List<Long> findCountOfSchoolAndBranchByCityId(String cityId);

// 	@Query(value = "select distinct s.id as id, s.name as name, s.code as code, s.city_id as cityId, s.poc_email as pocEmail, "
// 			+ "s.phone_number as phoneNumber, s.website as website, s.signatory_name as signatoryName, "
// 			+ "s.signatory_role as signatoryRole, s.logo_url as logoUrl, s.active as active, "
// 			+ "'0' AS numberOfBranches, "
// 			+ "'0' filter(WHERE t.academic_staff_profile='COORDINATOR') AS numberOfCoordinators, "
// 			+ "'0' filter(WHERE t.academic_staff_profile='TEACHER') AS numberOfTeachers, "
// 			+ "'0' filter(WHERE t.academic_staff_profile='PRINCIPAL') AS numberOfPrincipals, "
// 			+ "'0' AS numberOfStudents "
// 			+ "FROM schools s "
// 			+ "LEFT JOIN branches b ON (b.school_id=s.id AND b.active=true AND b.deleted=false) " 
// //			+ "LEFT JOIN teachers t ON (t.school_id=s.id AND t.active=true AND t.deleted=false) "
// //			+ "LEFT JOIN students st ON (st.school_id=s.id AND st.active=true AND st.deleted=false) "
// 			+ "WHERE s.deleted=false AND s.active=true AND s.id=:id "
// 			+ "GROUP BY (s.id, s.board_id, s.poc_email, s.phone_number, s.active)"
// 			, nativeQuery = true)
@Query(value = "select distinct s.id as id, s.name as name, s.code as code, s.city_id as cityId, s.poc_email as pocEmail, "
			+ "s.phone_number as phoneNumber, s.website as website, s.signatory_name as signatoryName, "
			+ "s.signatory_role as signatoryRole, s.logo_url as logoUrl, s.active as active, "
			+ "'0' AS numberOfBranches, "
			+ "'0'  AS numberOfCoordinators, "
			+ "'0'  AS numberOfTeachers, "
			+ "'0'  AS numberOfPrincipals, "
			+ "'0' AS numberOfStudents "
			+ "FROM schools s "
			+ "LEFT JOIN branches b ON (b.school_id=s.id AND b.active=true AND b.deleted=false) " 
//			+ "LEFT JOIN teachers t ON (t.school_id=s.id AND t.active=true AND t.deleted=false) "
//			+ "LEFT JOIN students st ON (st.school_id=s.id AND st.active=true AND st.deleted=false) "
			+ "WHERE s.deleted=false AND s.active=true AND s.id=:id "
			+ "GROUP BY (s.id, s.board_id, s.poc_email, s.phone_number, s.active)"
			, nativeQuery = true)
	SchoolsProjection findSchoolsById(String id);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from schools", nativeQuery = true)
	String findLastModifiedAt();

	Schools getByCode(String schoolCode);

    boolean existsByIdAndDeleted(String id, boolean b);
        
    @Query(value = "select count(*) from branches where school_id=:id and deleted=false " 
		+ "UNION ALL "
		+ "select count(*) from grade_section_mapping where school_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from students where school_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from teachers where school_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from users_institution_mapping where school_id=:id and deleted=false ", nativeQuery = true)
	List<Long> findUsages(String id);
    
    @Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
    	+ "WHERE s.deleted IS FALSE AND s.active IS TRUE "
    	+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) ")
	long getSchoolCount(String schoolId);
    
    @Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
    	+ "LEFT JOIN com.lms.userservice.entity.Branches b ON (s.id=b.schools.id AND b.deleted=false AND b.active=true) "
    	+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND b.boardId IN (:boardIds) "
    	+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) ")
	long getSchoolCountWithBoard(String schoolId, List<String> boardIds);
    
    @Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
    		+ "INNER JOIN com.lms.userservice.entity.UsersInstitutionMapping uim ON (uim.schools.id=s.id AND uim.deleted=false AND uim.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Users us ON (us.id=uim.users.id AND us.deleted=false AND us.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Administration adm ON (us.userName=adm.userName AND us.deleted=false AND us.active=true) "
        	+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND adm.userName=:userName "
        	+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) ")
    long findSchoolCountForAdministrator(String schoolId, String userName);
        
    @Query("SELECT COUNT(distinct s.id) FROM #{#entityName} s "
    		+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.schools.id=s.id AND b.deleted=false AND b.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.UsersInstitutionMapping uim "
    		+ "ON (uim.branches.id=b.id AND uim.schools.id=s.id AND uim.deleted=false AND uim.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Users us ON (us.id=uim.users.id AND us.deleted=false AND us.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Administration adm ON (us.userName=adm.userName AND us.deleted=false AND us.active=true) "
        	+ "WHERE s.deleted IS FALSE AND s.active IS TRUE AND adm.userName=:userName AND b.boardId IN (:boardIds) "
        	+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) ")
    long findSchoolCountWithBoardForAdministrator(String schoolId, List<String> boardIds, String userName);
    
    @Query("SELECT name FROM #{#entityName}  WHERE deleted=false AND active=true "
    		+ "AND ((:id IS NULL) OR (id=cast(:id AS text))) ")
	String findSchoolNameById(String id);
    
	@Query(value = "SELECT DISTINCT s.id AS schoolId, s.name AS name, s.code AS code, s.poc_email AS pocEmail, s.phone_number AS phoneNumber, s.website AS website, "
			+ "s.logo_url AS logoUrl, COUNT(DISTINCT b.id) AS branchCount, COUNT(DISTINCT gsm.grade_id) AS totalGrades, COUNT(DISTINCT st.id) AS numberOfStudents, s.city_id AS cityId "
			+ "FROM schools s "
			+ "INNER JOIN branches b ON (b.school_id=s.id AND b.deleted=false AND b.active=true) "
			+ "LEFT JOIN grade_section_mapping gsm ON (gsm.branch_id=b.id AND gsm.school_id=s.id AND gsm.deleted=false AND gsm.active=true) "
			+ "LEFT JOIN students st ON (st.branch_id=b.id AND st.school_id=s.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false AND s.active=true AND s.city_id=:cityId GROUP BY s.id", nativeQuery = true)
	List<BranchesProjection> getSchoolDetails(String cityId);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.SchoolsBranchsMinResponseDto(s.id, b.id) FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.schools.id=s.id AND b.deleted=false AND b.active=true) "
			+ "WHERE s.deleted=false AND s.active=true ")
	List<SchoolsBranchsMinResponseDto> findAllActiveSchools();

	@Query(value = "SELECT DISTINCT s.id AS id, s.name AS name, s.code AS code, s.created_at as createdAt FROM schools s "
			+ "WHERE s.deleted=false "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(s.active AS text) = cast(:active AS text))) "
			+ "AND s.id IN(:schoolId) AND ((cast(:search as text) IS NULL) "
			+ "OR ((LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))))", nativeQuery = true)
	Page<SchoolsProjection> findSchoolsForAdministration(List<String> schoolId, String search, Boolean active,
			Pageable pageable);
	
	@Query(value = "SELECT DISTINCT s.id AS id, s.name AS name, s.code AS code, s.created_at AS createdAt FROM schools s "
			+ "WHERE s.deleted=false "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(s.active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (s.id=cast(:schoolId AS text))) "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR ((LOWER(s.name) LIKE CONCAT(cast(:search as text),'%'))))", nativeQuery = true)
	Page<SchoolsProjection> findSchoolsForAdminUsers(String schoolId, String search, Boolean active, Pageable pageable);
	
	@Query(value = "SELECT DISTINCT s.id AS id, s.name AS name FROM schools s "
			+ "WHERE s.deleted=false AND s.active=true AND s.id IN(:schoolIds) ", nativeQuery = true)
	List<SchoolsProjection> findSchoolsForAdministration(List<String> schoolIds);
	
	@Query(value = "SELECT DISTINCT s.id AS id, s.name AS name FROM schools s "
			+ "WHERE s.deleted=false AND s.active=true ", nativeQuery = true)
	List<SchoolsProjection> findSchoolsForAdminUsers();
	
	@Query(value = "SELECT name as name, poc_email as pocEmail, signatory_name as signatoryName, "
			+ "to_char(to_timestamp(created_at/1000), 'DD-MM-YYYY | HH12:MI AM') as createdOn, "
			+ "to_char(to_timestamp(modified_at/1000), 'DD-MM-YYYY | HH12:MI AM') as modifiedOn, "
			+ "CASE WHEN last_modified_by IS NOT NULL THEN last_modified_by "
			+ "ELSE created_by END as createOrModifiedBy "
			+ "FROM schools WHERE deleted=false and id=:id", nativeQuery = true)
	SchoolsProjection findDetailsForSendMail(String id);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.InstitutionStudentCountResponseDto(s.id, s.name, b.id, b.name, count(DISTINCT st.id)) "
			+ "FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON(b.schools.id=s.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.BranchPlanMappings bpm ON(bpm.branches.id=b.id AND bpm.deleted=false AND bpm.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Students st ON(st.schools.id=s.id AND st.branches.id=b.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false AND s.active=true AND st.gradeId IN(:gradeIds) AND bpm.planId IN(:planIds) "
			+ "GROUP BY (s.id, s.name, b.id, b.name)")
	List<InstitutionStudentCountResponseDto> findInstitutionWithTotalStudents(List<String> gradeIds, List<String> planIds);
	
	@Query("SELECT DISTINCT new com.lms.userservice.model.InstitutionList(s.id, b.id, count(DISTINCT st.id)) "
			+ "FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.schools.id=s.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.BranchPlanMappings bpm ON (bpm.branches.id=b.id AND bpm.deleted=false AND bpm.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Students st ON (st.schools.id=s.id AND st.branches.id=b.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false AND s.active=true AND st.gradeId=:gradeId AND bpm.planId IN (:planIds) "
			+ "GROUP BY (s.id, s.name, b.id, b.name)")
	List<InstitutionList> findAllInstitutionbyGradeAndPlans(String gradeId, List<String> planIds);
	
	@Query("SELECT DISTINCT new com.lms.userservice.model.InstitutionList(s.id, b.id, count(DISTINCT st.id)) "
			+ "FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.schools.id=s.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Students st ON (st.schools.id=s.id AND st.branches.id=b.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false AND s.active=true AND st.gradeId=:gradeId AND b.boardId=:boardId "
			+ "GROUP BY (s.id, b.id)")
	List<InstitutionList> findAllInstitutionbyGradeAndBoardAndNotBranch(String gradeId, String boardId);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.InstitutionStudentCountResponseDto(s.id, s.name, b.id, b.name, count(DISTINCT st.id)) "
			+ "FROM #{#entityName} s "
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON (b.schools.id=s.id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.Students st ON (st.schools.id=s.id AND st.branches.id=b.id AND st.deleted=false AND st.active=true) "
			+ "WHERE s.deleted=false AND s.active=true AND st.gradeId=:gradeId AND b.boardId=:boardId "
			+ "GROUP BY (s.id, s.name, b.id, b.name)")
	List<InstitutionStudentCountResponseDto> findInstitutionWithFilters(String gradeId, String boardId);
    
}