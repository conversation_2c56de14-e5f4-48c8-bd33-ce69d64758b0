package com.lms.userservice.repository;

import java.util.List;

import com.lms.userservice.projection.StudentsProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.lms.userservice.entity.AssignTeacher;
import com.lms.userservice.model.GradesSubjectModel;
import com.lms.userservice.projection.TeacherAssignProjection;
import com.lms.userservice.response.dto.StudentFormativeMinResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentNameDetails;
import com.lms.userservice.response.dto.TeacherEditAccessResponseDto;

@Repository
public interface AssignTeacherRepository extends JpaRepository<AssignTeacher, String> {

	@Query("SELECT COUNT(id) > 0 FROM #{#entityName} WHERE teacherId=:teacherId "
			+ "AND (:sectionId IS NULL OR sectionId=:sectionId) "
			+ "AND (:subtopicId IS NULL OR subtopicId=:subtopicId) "
			+ "AND gradeId=:gradeId AND subjectId =:subjectId AND deleted IS FALSE")
	boolean existsByFilters(String teacherId, String sectionId, String subtopicId, String gradeId, String subjectId);

	@Query("SELECT  DISTINCT subjectId as subjectId, gradeId as gradeId, sectionId as sectionId "
			+ "FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE AND teacherId =:teacherId")
	List<TeacherAssignProjection> assignedSubjectGradeAndSectionByTeacherId(String teacherId);

	@Query("SELECT DISTINCT subjectId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (gradeId=(cast(:gradeId as text)))) "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=(cast(:sectionId as text))))")
	List<String> getAllSubjectIdsByFilters(String teacherId, String gradeId, String sectionId);

	@Query("SELECT DISTINCT subtopicId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId=:teacherId AND subjectId=:subjectId "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (gradeId=(cast(:gradeId as text)))) "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=(cast(:sectionId as text))))")
	List<String> getAllSubTopicIdsByFilters(String teacherId, String gradeId, String sectionId, String subjectId);

	@Query("SELECT DISTINCT new com.lms.userservice.model.GradesSubjectModel( "
			+ "gradeId, subjectId, subtopicId) FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId AND (:subjectId IS NULL OR subjectId =:subjectId) ")
	List<GradesSubjectModel> getGradeSubjectModelForTeacher(String teacherId, String subjectId);

	@Query("SELECT DISTINCT COUNT(s.id) FROM #{#entityName} a "
			+ "JOIN com.lms.userservice.entity.Students s ON (a.gradeId = s.gradeId "
			+ "AND a.sectionId = s.sectionId) WHERE a.deleted IS FALSE AND a.active IS TRUE "
			+ "AND a.teacherId=:teacherId")
	Integer getCountOfStudents(String teacherId);

	@Query(value = "SELECT COUNT(*) FROM assign_teacher WHERE deleted=false AND subject_id=:subjectId", nativeQuery = true)
	List<Long> findTheMappingForSubject(String subjectId);

	@Query(value = "SELECT COUNT(*) FROM assign_teacher WHERE deleted=false AND subtopic_id=:subtopicId", nativeQuery = true)
	List<Long> findTheMappingForSubTopic(String subtopicId);

	@Query(value = "SELECT COUNT(*) FROM assign_teacher WHERE deleted=false AND teacher_id=:teacherId", nativeQuery = true)
	List<Long> findTheMappingForTeacher(String teacherId);

	@Query("SELECT DISTINCT gradeId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE AND teacherId =:teacherId")
	List<String> getAllGradeIdsByTeacherId(String teacherId);

	@Query("SELECT DISTINCT sectionId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId AND gradeId=:gradeId")
	List<String> getAllSectionsByGradeAndTeacherId(String teacherId, String gradeId);

	@Query("SELECT COUNT(id) > 0 FROM #{#entityName} WHERE deleted=:deleted AND active=:active "
			+ "AND teacherId=:teacherId AND gradeId=:gradeId AND subjectId =:subjectId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=cast(:sectionId as text))) "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (subtopicId=cast(:subtopicId as text)))")
	boolean checkingTeacherAccessGiven(String teacherId, String gradeId, String sectionId, String subjectId,
			String subtopicId, boolean deleted, boolean active);

	@Query("SELECT DISTINCT subjectId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE AND teacherId =:teacherId")
	List<String> getSubjectIdsByTeacherId(String teacherId);

	@Query("SELECT DISTINCT gradeId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId AND subjectId=:subjectId")
	List<String> getGradeByTeacherAndSubject(String teacherId, String subjectId);

	@Query("SELECT DISTINCT sectionId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId AND subjectId=:subjectId AND gradeId=:gradeId ")
	List<String> getSectionByTeacherSubjectAndGrade(String teacherId, String subjectId, String gradeId);

	@Query("SELECT DISTINCT sectionId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId AND subjectId=:subjectId AND gradeId=:gradeId "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (subtopicId=cast(:subtopicId as text))) ")
	List<String> getSectionByTeacherSubjectGradeAndSubtopic(String teacherId, String subjectId, String gradeId,
			String subtopicId);

	@Query("SELECT DISTINCT ast.sectionId FROM #{#entityName} ast "
			+ "INNER JOIN com.lms.userservice.entity.Teachers t ON (t.id=ast.teacherId AND t.deleted IS FALSE AND t.active IS TRUE)"
			+ "WHERE ast.deleted IS FALSE AND ast.active IS TRUE AND t.id=:teacherId "
			+ "AND ast.subjectId=:subjectId AND ast.gradeId=:gradeId "
			+ "AND t.schools.id=:schoolId AND t.branches.id=:branchId "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (ast.subtopicId=cast(:subtopicId as text))) ")
	List<String> getSectionBySubjectAndGrade(String teacherId, String schoolId, String branchId, String gradeId,
			String subjectId, String subtopicId);

	boolean existsByTeacherIdAndDeleted(String teacherId, boolean deleted);

	@Query(value = "SELECT DISTINCT subject_id as subjectId, grade_id as gradeId, section_id as sectionId "
			+ "FROM assign_teacher WHERE deleted=false AND active=true AND teacher_id=:teacherId ", nativeQuery = true)
	List<TeacherAssignProjection> findAllAssignedItemsByTeacher(String teacherId);

	@Query(value = "SELECT DISTINCT ast.subject_id as subjectId, ast.subtopic_id as subTopicId "
			+ "FROM assign_teacher ast "
			+ "INNER JOIN teachers t on (t.id=ast.teacher_id AND t.deleted=false AND t.active=true) "
			+ "WHERE ast.deleted=false AND ast.active=true "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (t.school_id=cast(:schoolId as text))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (t.branch_id=cast(:branchId as text))) "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (ast.grade_id=cast(:gradeId as text))) "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (ast.section_id=cast(:sectionId as text))) ", nativeQuery = true)
	List<TeacherAssignProjection> findAllSubjectsAndSubtopics(String schoolId, String branchId, String gradeId,
			String sectionId);

	@Query(value = "SELECT DISTINCT subject_id as subjectId, grade_id as gradeId, section_id as sectionId, subtopic_id as subTopicId "
			+ "FROM assign_teacher WHERE deleted=false AND active=true AND teacher_id=:teacherId ", nativeQuery = true)
	List<TeacherAssignProjection> findAllAccessOfTeacher(String teacherId);

	@Query(value = "SELECT DISTINCT subject_id as subjectId, grade_id as gradeId, section_id as sectionId, subtopic_id as subTopicId "
			+ "FROM assign_teacher WHERE deleted=false AND active=true AND teacher_id=:teacherId "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (grade_id=cast(:gradeId as text))) ", nativeQuery = true)
	List<TeacherAssignProjection> findAllAccessOfTeacher(String teacherId, String gradeId);

	@Query(value = "SELECT COUNT(DISTINCT s.id) FROM students s where s.school_id=:schoolId AND s.grade_id=:gradeId AND s.section_id=:sectionId "
			+ "ANd branch_id=:branchId ", nativeQuery = true)
	Integer getStudentsCountByFilters(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT DISTINCT s.id, s.first_name AS firstName, s.last_name AS lastName " + "FROM students s "
			+ "WHERE s.school_id = :schoolId " + "  AND s.grade_id = :gradeId " + "  AND s.section_id = :sectionId "
			+ "  AND s.branch_id = :branchId ", nativeQuery = true)
	List<StudentsProjection> getStudentsListByFilters(String schoolId, String branchId, String gradeId,
			String sectionId);

	@Query("SELECT DISTINCT s.id FROM #{#entityName} ast "
			+ "INNER JOIN com.lms.userservice.entity.Students s ON (ast.gradeId = s.gradeId AND ast.sectionId = s.sectionId) "
			+ "WHERE ast.deleted IS FALSE AND ast.active IS TRUE " + "AND ast.subjectId=:subjectId "
			+ "AND ast.gradeId=:gradeId AND s.schools.id=:schoolId AND s.branches.id=:branchId "
			+ "AND (:sectionIdList IS NULL OR ast.sectionId IN (:sectionIdList)) "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (ast.subtopicId=cast(:subtopicId as text))) ")
	List<String> getAssignedStudentsCountByFilters(String schoolId, String branchId, String subjectId, String gradeId,
			List<String> sectionIdList, String subtopicId);

	@Query(value = "SELECT DISTINCT ast.section_id FROM assign_teacher ast "
			+ "INNER JOIN teachers t ON (t.id=ast.teacher_id AND t.deleted=false AND t.active=true) "
			+ "WHERE ast.deleted=false AND ast.active=true AND ast.teacher_id=:teacherId "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (t.school_id=cast(:schoolId as text))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (t.branch_id=cast(:branchId as text))) "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (ast.grade_id=cast(:gradeId as text))) "
			+ "AND ((cast(:subjectId as text) IS NULL) OR (ast.subject_id=cast(:subjectId as text))) "
			+ "AND ((cast(:subTopicId as text) IS NULL) OR (ast.subtopic_id=cast(:subTopicId as text))) ", nativeQuery = true)
	List<String> findSectionIdsForTeacher(String teacherId, String schoolId, String branchId, String gradeId,
			String subjectId, String subTopicId);

	@Query("SELECT DISTINCT ast.sectionId FROM #{#entityName} ast "
			+ "INNER JOIN com.lms.userservice.entity.Teachers t ON (t.id=ast.teacherId AND t.deleted IS FALSE AND t.active IS TRUE)"
			+ "WHERE ast.deleted IS FALSE AND ast.active IS TRUE "
			+ "AND ast.subjectId=:subjectId AND ast.gradeId=:gradeId "
			+ "AND t.schools.id=:schoolId AND t.branches.id=:branchId "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (ast.subtopicId=cast(:subtopicId as text))) ")
	List<String> getSectionBySubjectAndGrade(String schoolId, String branchId, String gradeId, String subjectId,
			String subtopicId);

	@Modifying
	@Transactional
	@Query(value = "update assign_teacher set deleted=true, active=false, modified_at=:modifiedAt, "
			+ "last_modified_by=:lastModifiedBy WHERE deleted=false AND teacher_id=:teacherId AND grade_id=:gradeId AND subject_id=:subjectId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (section_id=cast(:sectionId as text))) "
			+ "AND ((cast(:subTopicId as text) IS NULL) OR (subtopic_id=cast(:subTopicId as text))) ", nativeQuery = true)
	int deleteTeacherAccess(String teacherId, Long modifiedAt, String lastModifiedBy, String sectionId,
			String subTopicId, String gradeId, String subjectId);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.TeacherEditAccessResponseDto (teacherId, gradeId, sectionId , subjectId, subtopicId) FROM #{#entityName} "
			+ "WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId=:teacherId AND subjectId=:subjectId AND gradeId=:gradeId "
			+ "AND ((cast(:sectionId as text) IS NULL) OR (sectionId=cast(:sectionId as text))) "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (subtopicId=cast(:subtopicId as text))) ")
	List<TeacherEditAccessResponseDto> getCureentTeacherAccess(String teacherId, String subjectId, String gradeId,
			String sectionId, String subtopicId);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.StudentFormativeMinResponseDto (s.id, s.firstName, s.lastName) FROM #{#entityName} ast "
			+ "INNER JOIN com.lms.userservice.entity.Students s ON (ast.gradeId = s.gradeId AND ast.sectionId = s.sectionId) "
			+ "WHERE ast.deleted IS FALSE AND ast.active IS TRUE "
			+ "AND ast.teacherId=:teacherId AND ast.subjectId=:subjectId "
			+ "AND ast.gradeId=:gradeId AND s.schools.id=:schoolId AND s.branches.id=:branchId "
			+ "AND ast.sectionId=:sectionId  "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (ast.subtopicId=cast(:subtopicId as text)))")
	List<StudentFormativeMinResponseDto> getFormativeStudentsListByFilters(String teacherId, String schoolId,
			String branchId, String subjectId, String gradeId, String sectionId, String subtopicId);

	@Query(value = "select DISTINCT id, first_name AS firstName, last_name AS lastName from students where section_id=:sectionId AND grade_id=:gradeId "
			+ "AND school_id=:schoolId AND branch_id=:branchId AND active=true AND deleted=false", nativeQuery = true)
	List<StudentsProjection> getStudentsNameList(String schoolId, String branchId, String gradeId, String sectionId);

	@Query(value = "SELECT DISTINCT grade_id as gradeId, section_id as sectionId "
			+ "FROM assign_teacher WHERE deleted=false AND active=true AND teacher_id=:teacherId ", nativeQuery = true)
	List<TeacherAssignProjection> findAllAssignedGradesByTeacher(String teacherId);

	@Query(value = "SELECT Distinct id FROM teachers where deleted=false AND active=true AND school_id=:schoolId"
			+ " AND id in(SELECT Distinct teacher_id FROM assign_teacher where section_id=:sectionId"
			+ " AND grade_id=:gradeId AND deleted=false AND active=true)", nativeQuery = true)
	List<String> getTeacherIdsByGradeAndSection(String schoolId, String gradeId, String sectionId);

	@Query("SELECT  DISTINCT subjectId as subjectId, gradeId as gradeId, sectionId as sectionId "
			+ "FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE AND teacherId IN (:teacherIds)"
			+ " AND gradeId=:gradeId AND subjectId=:subjectId ")
	List<TeacherAssignProjection> assignedSubjectGradeAndSectionByTeacherIds(List<String> teacherIds, String gradeId,
			String subjectId);

	@Query("SELECT DISTINCT sectionId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ " AND teacherId =:teacherId AND gradeId=:gradeId AND subjectId=:subjectId ")
	List<String> assignTeacherSectionsList(String teacherId, String gradeId, String subjectId);
	
	@Query("SELECT DISTINCT sectionId FROM #{#entityName} WHERE deleted IS FALSE AND active IS TRUE "
			+ "AND teacherId =:teacherId")
	List<String> getAllSectionsByGradeAndTeacherId(String teacherId);
}
