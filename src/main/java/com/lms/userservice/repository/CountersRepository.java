package com.lms.userservice.repository;

import com.lms.userservice.entity.AssignTeacher;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


@Repository
public interface CountersRepository extends JpaRepository<AssignTeacher, String> {
        
    @Query(value = "select count(*) from subject_groups where branch_id=:id and deleted=false ", nativeQuery = true)
    List<Long> branchIds(String id);
}
