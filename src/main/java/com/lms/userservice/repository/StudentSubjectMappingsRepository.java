package com.lms.userservice.repository;

import com.lms.userservice.entity.StudentSubjectMapping;
import com.lms.userservice.response.dto.StudentSubjectGroupSubjectMappingsResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

@Repository
public interface StudentSubjectMappingsRepository extends JpaRepository<StudentSubjectMapping, String> {

    @Query("SELECT new com.lms.userservice.response.dto.StudentSubjectGroupSubjectMappingsResponseDto(s.id, s.subjectGroupSubjectMappingsId) "
            + " FROM #{#entityName} s WHERE s.deleted=false and s.student.id=:studentId and s.academicYearsId = :academicYearId ")
    List<StudentSubjectGroupSubjectMappingsResponseDto> findByStudentIdAndAcademicYearId(@Param("studentId") String studentId, @Param("academicYearId") String academicYearId);

    @Query("SELECT new com.lms.userservice.response.dto.StudentSubjectMappingDto(s.id, s.subjectGroupSubjectMappingsId) "
            + " FROM #{#entityName} s WHERE s.deleted=false and s.student.id=:studentId and s.academicYearsId = :academicYearId ")
    List<StudentSubjectMappingDto> findMinByStudentIdAndAcademicYearId(@Param("studentId") String studentId, @Param("academicYearId") String academicYearId);

    @Modifying
    @Query(value = "update student_subject_mappings_mapping set deleted=true, active=false, "
            + "last_modified_by=:currentUser, modified_at=:modifiedAt "
            + "WHERE deleted=false AND student_id =:studentId and id not in (:ids) and academic_years_id = :academicYearId ", nativeQuery = true)
    void deleteDataByStudentIdAndIdsNotInAndAcademicYearsId(String currentUser, Long modifiedAt, String studentId, List<String> ids, String academicYearId);

    @Query(value = "SELECT academic_years_id FROM student_subject_mapping ORDER BY created_by DESC LIMIT 1", nativeQuery = true)
    public String getCurrentAcademicYear();

}
