package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.lms.userservice.entity.Teachers;
import com.lms.userservice.model.UserMinDetails;
import com.lms.userservice.projection.DashboardProjection;
import com.lms.userservice.projection.PrincipalCountProjection;
import com.lms.userservice.projection.TeacherCountProjection;
import com.lms.userservice.projection.TeachersProjection;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.response.dto.AllTypeUserMinResponseDto;
import com.lms.userservice.response.dto.GradeSectionSubjectsResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.response.dto.TeacherDetailsForFeignResponseDto;
import com.lms.userservice.response.dto.TeacherNameResponse;

@Repository
public interface TeacherRepository extends JpaRepository<Teachers, String> {

	boolean existsByIdAndDeleted(String teacherId, boolean deleted);

	boolean existsByEmailAndDeleted(String email, boolean deleted);

	boolean existsByMobileAndDeleted(String mobile, boolean deleted);

	boolean existsByEmailAndDeletedAndIdNot(String email, boolean deleted, String id);

	boolean existsByMobileAndDeletedAndIdNot(String mobile, boolean deleted, String id);
	
	boolean existsByIdAndDeletedAndActive(String id, boolean deleted, boolean active);
	
	boolean existsByIdAndBranchesIdAndSchoolsIdAndDeletedAndActive(String id, String branchId, String schoolId,
			boolean deleted, boolean active);
	
	boolean existsByUserName(String userName);
	
	Teachers findByUserNameIgnoreCase(String userName);

	@Query(value = "SELECT t.id as id, " 
			+ "CONCAT(t.first_name, ' ', t.last_name) as name, "
			+ "u.id as userId, u.user_name as userName, t.email as email, "
			+ "t.mobile as mobile, t.coordinator_type_id as coordinatorTypeId, "
			+ "t.academic_staff_profile as academicStaffProfile, "
			+ "urm.role_id as roleId, s.id as schoolId, s.name as school, "
			+ "b.id as branchId, b.name as branch, t.active as active, t.created_at as createdAt, "
			+ "TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime FROM teachers t "
			+ "INNER JOIN users u ON u.user_name=t.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id=u.id " 
			+ "INNER JOIN schools s ON s.id=t.school_id "
			+ "INNER JOIN branches b ON b.id=t.branch_id WHERE t.deleted = false "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (CONCAT(concat(LOWER(t.first_name), ' '), (LOWER(t.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.last_name), ' '), (LOWER(t.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.first_name)), (LOWER(t.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.last_name)), (LOWER(t.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (LOWER(t.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(t.mobile) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:schoolId AS text) IS NULL) OR (s.id=cast(:schoolId AS text))) "
			+ "AND ((cast(:branchId AS text) IS NULL) OR (b.id=cast(:branchId AS text))) "
			+ "AND ((cast(:profile AS text) IS NULL) OR (t.academic_staff_profile=cast(:profile AS text))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(t.active AS text) = cast(:active AS text))) ", nativeQuery = true)
	Page<TeachersProjection> getPageResponseByFilters(String search, String schoolId, String branchId, String profile,
			Boolean active, Pageable page);

	@Query(value = "SELECT COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') as teacherCount,  "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='PRINCIPAL') as principalCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM teachers t WHERE ((:schoolId IS NULL) OR (t.school_id=cast(:schoolId AS text))) "
			+ "AND t.deleted IS FALSE AND t.active IS TRUE", nativeQuery = true)
	TeacherCountProjection getTeacherCounts(String schoolId);
	
	@Query(value = "SELECT COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') as teacherCount,  "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='PRINCIPAL') as principalCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM teachers t "
			+ "LEFT JOIN schools s ON (s.id=t.school_id AND s.deleted=false AND s.active=true) "
			+ "LEFT JOIN branches b ON (b.id=t.branch_id AND b.deleted=false AND b.active=true) "
			+ "WHERE t.deleted IS FALSE AND t.active IS TRUE AND b.board_id IN (:boardIds) "
			+ "AND ((:schoolId IS NULL) OR (s.id=cast(:schoolId AS text))) ", nativeQuery = true)
	TeacherCountProjection getTeacherCountsWithBoard(String schoolId, List<String> boardIds);

	@Query(value = "SELECT t.id as id, t.first_name as firstName, t.last_name as lastName, "
			+ "t.email as email, t.mobile as mobile, t.dob as dob, t.gender as gender, "
			+ "t.join_date as joinDate, t.previous_work_exp as previousWorkExp, t.address as address, "
			+ "t.designation as designation, s.id as schoolId, s.name as school, s.code as schoolCode, "
			+ "b.id as branchId, b.name as branch, urm.role_id as roleId, "
			+ "t.academic_staff_profile as academicStaffProfile, t.coordinator_type_id as coordinatorTypeId, "
			+ "u.id as userId, u.user_name as userName, "
			+ "TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime, t.active as active " + "FROM teachers t "
			+ "INNER JOIN users u ON u.user_name=t.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id=u.id " + "INNER JOIN schools s ON s.id=t.school_id "
			+ "INNER JOIN branches b ON b.id=t.branch_id "
			+ "WHERE t.deleted = false AND t.active=true AND t.id=:id", nativeQuery = true)
	TeachersProjection getTeachersById(String id);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from teachers", nativeQuery = true)
	String findLastModifiedAt();

	@Modifying
	@Transactional
	@Query(value = "update teachers set deleted=true, active=false, modified_at=:modifiedAt, "
			+ "last_modified_by=:lastModifiedBy where deleted=false and id=:id", nativeQuery = true)
	int setDeleteStatus(String id, Long modifiedAt, String lastModifiedBy);

	@Query("SELECT new com.lms.userservice.response.dto.NameCommonResponseDto(id, firstName,lastName ) "
			+ "FROM #{#entityName} WHERE coordinatorTypeId =:coordinatorTypeId AND deleted IS FALSE AND active IS TRUE")
	List<NameCommonResponseDto> getAllTeacherMappingsByCoordinatorType(String coordinatorTypeId);

	@Query("SELECT new com.lms.userservice.response.dto.NameCommonResponseDto(id, firstName,lastName ) "
			+ "FROM #{#entityName} WHERE roleId=:roleId AND deleted IS FALSE AND active IS TRUE")
	List<NameCommonResponseDto> getAllTeacherMappingsByRole(String roleId);

	@Query(value = "SELECT new com.lms.userservice.response.dto.TeacherNameResponse(id,CONCAT(first_name, ' ', last_name)) " +
			" FROM #{#entityName} WHERE  id IN (:teacherIds)")
	List<TeacherNameResponse> getTeacherName(List<String> teacherIds);

	@Query(value = "SELECT t.id as id, t.first_name as firstName, t.last_name as lastName, "
			+ "t.email as email, t.mobile as mobile, t.dob as dob, t.gender as gender, "
			+ "t.join_date as joinDate, t.previous_work_exp as previousWorkExp, t.address as address, "
			+ "t.designation as designation, s.id as schoolId, s.name as school, s.code as schoolCode, "
			+ "b.id as branchId, b.name as branch, b.board_id as boardId, urm.role_id as roleId, "
			+ "t.academic_staff_profile as academicStaffProfile, t.coordinator_type_id as coordinatorTypeId, "
			+ "u.id as userId, u.user_name as userName, "
			+ "TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime, t.active as active " 
			+ "FROM teachers t "
			+ "INNER JOIN users u ON u.user_name=t.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id=u.id " 
			+ "INNER JOIN schools s ON s.id=t.school_id "
			+ "INNER JOIN branches b ON b.id=t.branch_id "
			+ "WHERE t.deleted = false AND t.user_name=:username", nativeQuery = true)
	TeachersProjection getTeachersByUserName(String username);
	
	@Query(value = "SELECT COUNT(*) FROM teachers WHERE deleted=false AND coordinator_type_id=:coordinatorId", nativeQuery = true)
	List<Long> findTheMappingorCoordinatorType(String coordinatorId);
	
	@Query("SELECT new com.lms.userservice.response.dto.ShareDetailsResponseDto(userName, firstName, lastName, email, mobile) "
    		+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
    ShareDetailsResponseDto findByUsernameForShareDetails(String userName);
	
	public boolean existsByUserNameAndDeleted(String userName, boolean deleted);
    
    @Query("SELECT new com.lms.userservice.request.dto.CreateUserEmailRequestDto(email, userName, firstName) "
    		+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
    CreateUserEmailRequestDto findByUsernameForUpdatePassByAdmin(String userName);
    
    @Query(value = "SELECT DISTINCT ast.section_id FROM teachers t "
			+ "LEFT JOIN assign_teacher ast ON (ast.teacher_id = t.id AND ast.deleted = false AND ast.active = true ) "
			+ "INNER JOIN schools s ON(s.id=t.school_id AND s.deleted = false AND s.active = true) "
			+ "INNER JOIN branches b ON(b.id=t.branch_id AND b.school_id=s.id AND b.deleted = false AND b.active = true) "			 
			+ "WHERE t.deleted = false AND t.active = true "
			+ "AND t.id=:teacherId AND s.id =:schoolId AND b.id =:branchId "
			+ "AND ((:gradeId IS NULL) OR (ast.grade_id=cast(:gradeId AS text))) "
			+ "AND ((:subjectId IS NULL) OR (ast.subject_id=cast(:subjectId AS text))) "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (ast.subtopic_id=cast(:subtopicId as text))) ", nativeQuery = true)
	List<String> findSectionIdListByFilters(String teacherId, String schoolId, String branchId, String gradeId, String subjectId, String subtopicId);

	@Query(value = "SELECT COUNT(DISTINCT s.id) FROM students s "
			+ "INNER JOIN assign_teacher ast ON (ast.grade_id=s.grade_id AND ast.deleted = false AND ast.active = true) "
			+ "WHERE s.deleted = false AND s.active = true AND s.school_id =:schoolId AND s.branch_id =:branchId "
			+ "AND ((:gradeId IS NULL) OR (s.grade_id=cast(:gradeId AS text))) "
			+ "AND ((:subjectId IS NULL) OR (ast.subject_id=cast(:subjectId AS text))) "
			+ "AND ((cast(:subtopicId as text) IS NULL) OR (ast.subtopic_id=cast(:subtopicId as text))) ", nativeQuery = true)
	Long getStudentCount(String schoolId, String branchId, String gradeId, String subjectId, String subtopicId);
	
	@Query(value = "SELECT COUNT(DISTINCT id) FROM students "
			+ "WHERE deleted = false AND active = true AND section_id IN (:sectionIds) "
			+ "AND school_id =:schoolId AND branch_id =:branchId "
			+ "AND ((:gradeId IS NULL) OR (grade_id=cast(:gradeId AS text))) ", nativeQuery = true)
	Long getStudentCountWithSectionList(String schoolId, String branchId, String gradeId, List<String> sectionIds);	
	
	@Query(value = "SELECT COUNT(DISTINCT gsm.grade_id) AS gradeCount, COUNT(DISTINCT s.id) AS studentCount, "
			+ "COUNT(DISTINCT t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') AS teacherCount, "
			+ "COUNT(DISTINCT t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') AS coordinatorCount "
			+ "FROM teachers t "
			+ "LEFT JOIN grade_section_mapping gsm ON (gsm.branch_id=t.branch_id AND gsm.school_id=t.school_id AND gsm.deleted=false AND gsm.active=true) "
			+ "LEFT JOIN students s ON (s.branch_id=t.branch_id AND s.school_id=t.school_id AND s.deleted=false AND s.active=true) "
			+ "WHERE t.deleted=false AND t.active=true "
			+ "AND t.id=:teacherId AND t.branch_id=:branchId AND t.school_id=:schoolId ", nativeQuery = true)
	DashboardProjection getGradeStudentTeacherCoordinatorCount(String teacherId, String schoolId, String branchId);
	
	@Query(value = "SELECT DISTINCT t.id as id, " 
			+ "CONCAT(t.first_name, ' ', t.last_name) as name, "
			+ "u.id as userId, u.user_name as userName, t.email as email, "
			+ "t.mobile as mobile, t.coordinator_type_id as coordinatorTypeId, "
			+ "t.academic_staff_profile as academicStaffProfile, "
			+ "urm.role_id as roleId, s.id as schoolId, s.name as school, "
			+ "b.id as branchId, b.name as branch, t.active as active, t.created_at as createdAt, "
			+ "TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime FROM teachers t "
			+ "INNER JOIN users u ON u.user_name=t.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id=u.id " 
			+ "INNER JOIN schools s ON s.id=t.school_id "
			+ "INNER JOIN branches b ON b.id=t.branch_id "
			+ "LEFT JOIN assign_teacher ast ON (t.id=ast.teacher_id AND ast.deleted=false AND ast.active=true) "
			+ "LEFT JOIN grade_section_mapping gsm ON (t.school_id=gsm.school_id AND t.branch_id=gsm.branch_id AND gsm.deleted=false AND gsm.active=true) "
			+ "WHERE t.deleted = false AND s.id=:schoolId AND b.id=:branchId AND (gsm.grade_id IN(:gradeIds) OR ast.grade_id IN(:gradeIds)) " 
			+ "AND t.id!=:teacherId AND ((cast(:search as text) IS NULL) "
			+ "OR (CONCAT(concat(LOWER(t.first_name), ' '), (LOWER(t.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.last_name), ' '), (LOWER(t.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.first_name)), (LOWER(t.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.last_name)), (LOWER(t.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (LOWER(t.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(t.mobile) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(t.active AS text) = cast(:active AS text))) "
			, nativeQuery = true)
	Page<TeachersProjection> findTeachersByGradesForPrincipalORCoordinator(String teacherId, String search,
			String schoolId, String branchId, List<String> gradeIds, Boolean active, Pageable page);
	
	@Query(value = "SELECT DISTINCT t.id as id, " 
			+ "CONCAT(t.first_name, ' ', t.last_name) as name, "
			+ "u.id as userId, u.user_name as userName, t.email as email, "
			+ "t.mobile as mobile, t.coordinator_type_id as coordinatorTypeId, "
			+ "t.academic_staff_profile as academicStaffProfile, "
			+ "urm.role_id as roleId, s.id as schoolId, s.name as school, "
			+ "b.id as branchId, b.name as branch, t.active as active, t.created_at as createdAt, "
			+ "TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime FROM teachers t "
			+ "INNER JOIN users u ON u.user_name=t.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id=u.id " 
			+ "INNER JOIN schools s ON s.id=t.school_id "
			+ "INNER JOIN branches b ON b.id=t.branch_id "
			+ "WHERE t.deleted = false AND s.id=:schoolId AND b.id=:branchId AND t.id!=:teacherId "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (CONCAT(concat(LOWER(t.first_name), ' '), (LOWER(t.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.last_name), ' '), (LOWER(t.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.first_name)), (LOWER(t.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(t.last_name)), (LOWER(t.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (LOWER(t.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(t.mobile) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(t.active AS text) = cast(:active AS text))) "
			, nativeQuery = true)
	Page<TeachersProjection> findTeachersWithOutGradesForPrincipal(String teacherId, String search,
			String schoolId, String branchId, Boolean active, Pageable page);
	
	@Query(value = "SELECT DISTINCT t.id as id,  t.coordinator_type_id as coordinatorTypeId,"
			+ "t.academic_staff_profile as academicStaffProfile from teachers t "
			+ "LEFT JOIN assign_teacher ast ON (t.id=ast.teacher_id AND ast.deleted=false AND ast.active=true) "
			+ "LEFT JOIN grade_section_mapping gsm ON (t.school_id=gsm.school_id AND t.branch_id=gsm.branch_id AND "
			+ "gsm.deleted=false AND gsm.active=true) WHERE t.deleted = false AND t.school_id=:schoolId "
			+ "AND t.branch_id=:branchId AND (gsm.grade_id IN(:gradeIds) " + "OR ast.grade_id IN(:gradeIds)) "
			+ "AND t.id=:teacherId ", nativeQuery = true)
	TeachersProjection findTeachersByGradesForCoordinator(String teacherId, String schoolId, String branchId,
			List<String> gradeIds);
	
	@Query(value = "SELECT COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') as teacherCount,  "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='PRINCIPAL') as principalCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM teachers t "
			+ "INNER JOIN branches b ON (b.id=t.branch_id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN schools sc ON (sc.id=t.school_id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN users_institution_mapping uim ON (uim.branch_id=b.id AND uim.school_id=sc.id AND uim.deleted=false AND uim.active=true) "
			+ "INNER JOIN users us ON (us.id=uim.user_id AND us.deleted=false AND us.active=true) "
			+ "INNER JOIN administration adm ON (us.user_name=adm.user_name AND us.deleted=false AND us.active=true) "
			+ "WHERE t.deleted IS FALSE AND t.active IS TRUE AND adm.user_name=:userName "
			+ "AND ((:schoolId IS NULL) OR (t.school_id=cast(:schoolId AS text)))", nativeQuery = true)
	TeacherCountProjection findTeacherCountsForAdministrator(String schoolId, String userName);
	
	@Query(value = "SELECT COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') as teacherCount,  "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='PRINCIPAL') as principalCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM teachers t "
			+ "INNER JOIN branches b ON (b.id=t.branch_id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN schools sc ON (sc.id=t.school_id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN users_institution_mapping uim ON (uim.branch_id=b.id AND uim.school_id=sc.id AND uim.deleted=false AND uim.active=true) "
			+ "INNER JOIN users us ON (us.id=uim.user_id AND us.deleted=false AND us.active=true) "
			+ "INNER JOIN administration adm ON (us.user_name=adm.user_name AND us.deleted=false AND us.active=true) "
			+ "WHERE t.deleted IS FALSE AND t.active IS TRUE AND adm.user_name=:userName AND b.board_id IN (:boardIds) "
			+ "AND ((:schoolId IS NULL) OR (sc.id=cast(:schoolId AS text))) ", nativeQuery = true)
	TeacherCountProjection findTeacherCountsWithBoardForAdministrator(String schoolId, List<String> boardIds, String userName);
	
	@Query(value = "SELECT COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') as teacherCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='PRINCIPAL') as principalCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM teachers t "
			+ "LEFT JOIN schools s ON (s.id=t.school_id AND s.deleted=false AND s.active=true) "
			+ "LEFT JOIN branches b ON (b.id=t.branch_id AND b.deleted=false AND b.active=true) "
			+ "WHERE t.deleted IS FALSE AND t.active IS TRUE AND s.id=:schoolId "
			+ "AND b.id=:branchId ", nativeQuery = true)
	TeacherCountProjection findAcademicStaffProfileCount(String schoolId, String branchId);
	
	@Query("SELECT userName FROM #{#entityName} WHERE deleted=false AND active=true "
			+ "AND schools.id=:schoolId AND branches.id=:branchId "
			+ "AND academicStaffProfile='TEACHER' ")
    List<String> findUserNameBySchoolAndBranch(String schoolId, String branchId);
	
	@Query(value = "SELECT Count(id) > 0 FROM teachers WHERE user_name=:userName ", nativeQuery = true)
	boolean checkingTeacherExits(String userName);
	
	@Query(value = "SELECT COUNT(DISTINCT t.id) FILTER(WHERE t.academic_staff_profile='TEACHER') as teacherCount, "
			+ "COUNT(distinct t.id) FILTER(WHERE t.academic_staff_profile='COORDINATOR') as coordinatorCount "
			+ "FROM teachers t "
			+ "LEFT JOIN schools s ON (s.id=t.school_id AND s.deleted=false AND s.active=true) "
			+ "LEFT JOIN branches b ON (b.id=t.branch_id AND b.deleted=false AND b.active=true) "
			+ "WHERE t.deleted=false AND t.active=true "
			+ "AND s.id=:schoolId AND b.id=:branchId AND b.board_id=:boardId ", nativeQuery = true)
	PrincipalCountProjection getAcademicStaffCount(String schoolId, String branchId, String boardId);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.GradeSectionSubjectsResponseDto(ast.gradeId, ast.sectionId, ast.subjectId, ast.subtopicId) "
    		+ "FROM #{#entityName} t "
    		+ "INNER JOIN AssignTeacher ast ON(ast.teacherId=t.id AND ast.deleted=false AND ast.active=true) "
    		+ "WHERE t.deleted=false AND t.active=true AND t.id=:id AND t.schools.id=:schoolId AND t.branches.id=:branchId "
    		+ "AND ((:gradeId IS NULL) OR (ast.gradeId=cast(:gradeId AS text))) "
    		+ "AND ((:sectionId IS NULL) OR (ast.sectionId=cast(:sectionId AS text)))")
	List<GradeSectionSubjectsResponseDto> getGradeSectionSubjectsDetails(String id, String schoolId, String branchId,
			String gradeId, String sectionId);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.AllTypeUserMinResponseDto(t.id, CONCAT(t.firstName, ' ', t.lastName)) "
    		+ "FROM #{#entityName} t "
    		+ "INNER JOIN AssignTeacher ast ON(ast.teacherId=t.id AND ast.deleted=false AND ast.active=true) "
    		+ "WHERE t.deleted=false AND t.active=true AND t.schools.id=:schoolId AND t.branches.id=:branchId "
    		+ "AND ((cast(:gradeId as text) IS NULL) OR (ast.gradeId=cast(:gradeId AS text))) ")
	List<AllTypeUserMinResponseDto> findStaffsMinDetailsForPrincipal(String schoolId, String branchId, String gradeId);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.AllTypeUserMinResponseDto(t.id, CONCAT(t.firstName, ' ', t.lastName)) "
    		+ "FROM #{#entityName} t "
    		+ "INNER JOIN AssignTeacher ast ON(ast.teacherId=t.id AND ast.deleted=false AND ast.active=true) "
    		+ "WHERE t.deleted=false AND t.active=true AND t.schools.id=:schoolId AND t.branches.id=:branchId "
    		+ "AND ast.gradeId IN(:gradeIds) ")
	List<AllTypeUserMinResponseDto> findStaffsMinDetailsWithGradeIds(String schoolId, String branchId, List<String> gradeIds);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.TeacherDetailsForFeignResponseDto(id, userName, CONCAT(first_name, ' ', last_name)) "
			+ "FROM #{#entityName} WHERE id IN (:teacherIds)")
	List<TeacherDetailsForFeignResponseDto> getAllTeachersForFeign(List<String> teacherIds);
	
	@Query(value = "SELECT new com.lms.userservice.response.dto.TeacherDetailsForFeignResponseDto(id, userName, CONCAT(first_name, ' ', last_name), schools.id, schools.name, branches.id, branches.name) "
			+ "FROM #{#entityName} WHERE  id=:id")
	TeacherDetailsForFeignResponseDto findTeachersMinDetails(String id);
	
	@Query("SELECT branches.id FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
	String getBranchByUsername(String userName);
	
	@Query(value = "SELECT new com.lms.userservice.model.UserMinDetails(id, CONCAT(first_name, ' ', last_name), userName) "
			+ "FROM #{#entityName} WHERE deleted=false AND active=true AND id=:id")
	UserMinDetails getUserMinDetails(String id);
	
	@Query(value = "SELECT new com.lms.userservice.model.UserMinDetails(id, CONCAT(first_name, ' ', last_name), userName) "
			+ " FROM #{#entityName} WHERE deleted=false AND active=true AND id IN(:ids)")
	List<UserMinDetails> getAllUsersMinDetails(List<String> ids);

	@Query(value =  "select update_section_ids(:oldSectionId, :newSectionId)", nativeQuery = true)
void executeUpdateSectionIdsFunction( String oldSectionId, String newSectionId);

}
