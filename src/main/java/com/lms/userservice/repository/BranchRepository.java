package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.projection.BranchesProjection;
import com.lms.userservice.response.dto.BranchResponseDto;
import com.lms.userservice.response.dto.BranchSchoolNamesResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;

@Repository
public interface BranchRepository extends JpaRepository<Branches, String> {

	boolean existsById(String id);
	
	boolean existsByIdAndDeleted(String id, boolean b);
	 
	boolean existsByIdAndDeletedAndActive(String id, boolean deleted, boolean active);

	boolean existsByNameAndSchoolsIdAndDeleted(String name, String schoolId, boolean deleted);

	boolean existsByNameAndSchoolsIdAndDeletedAndIdNot(String name, String schoolId, boolean deleted, String id);

	boolean existsBySchoolsIdAndDeletedAndIdIn(String schoolId, boolean deleted, List<String> id);

	boolean existsBySchoolsIdAndDeletedAndId(String schoolId, boolean deleted, String id);

	boolean existsBySchoolsIdAndDeleted(String schoolId, boolean deleted);

	Branches findBySchoolsAndDeleted(Schools schools, boolean deleted);
	
	boolean existsByIdAndSchoolsIdAndDeletedAndActive(String id, String schoolId, boolean deleted, boolean active);
	
	boolean existsByPocEmailAndSchoolsIdAndDeleted(String pocEmail, String schoolId, boolean deleted);

	boolean existsByPocEmailAndSchoolsIdAndDeletedAndIdNot(String pocEmail, String schoolId, boolean deleted, String id);

	@Query("SELECT new com.lms.userservice.response.dto.BranchResponseDto(b.id, b.name, b.cityId,"
			+ " b.locality, b.pocEmail, b.phoneNumber, b.boardId,"
			+ " b.logoUrl, b.schools.id, b.schools.name, b.active)" 
			+ " FROM #{#entityName} b"
			+ " INNER JOIN com.lms.userservice.entity.Schools s ON s.id=b.schools.id"
			+ " WHERE b.id=:id and b.deleted=false")
	BranchResponseDto findBranchesById(String id);

	//Pagination query starts: both below queries used to bring the branches 1st one is for Super_Admin 2nd method is for School_Admin/Management//
	@Query(value = "SELECT DISTINCT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ "b.poc_email as pocEmail, b.phone_number as phoneNumber, b.board_id AS boardId, "
			+ "b.logo_url as logoUrl, b.school_id AS schoolId, s.name AS school, b.active as active, b.created_at AS createdAt, "
			+ "'0' AS numberOfTeachers, "
			+ "'0' AS numberOfCoordinators, "
			+ "'0' AS numberOfPrincipals, "
			+ "'0' AS numberOfStudents "
			+ "FROM branches b "
			// + "LEFT JOIN teachers t ON (t.branch_id=b.id AND t.deleted=false AND t.active=true) "
			// + "LEFT JOIN students st ON (st.branch_id=b.id AND st.deleted=false AND st.active=true) "
			+ "LEFT JOIN schools s ON (s.id=b.school_id AND s.deleted=false) "
			+ "WHERE b.deleted=false "
			+ "AND ((cast(:cityId AS text) IS NULL) OR (b.city_id=cast(:cityId AS text))) "
			+ "AND ((cast(:locality AS text) IS NULL) OR (b.locality=cast(:locality AS text))) "
			+ "AND ((cast(:boardId AS text) IS NULL) OR (b.board_id=cast(:boardId AS text))) "
			+ "AND ((cast(:search as text) IS NULL) OR ((LOWER(b.name) LIKE CONCAT(cast(:search as text),'%')))) "
			+ "AND ((cast(:branchId AS text) IS NULL) OR (b.id=cast(:branchId AS text))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(b.active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:schoolId AS text) IS NULL) OR (s.id=cast(:schoolId AS text))) "
			+ "GROUP BY (b.id, s.name)", nativeQuery = true)
	Page<BranchesProjection> findAllBranchesByPagination(String cityId, String locality, String boardId, String search,
			String branchId, String schoolId, Pageable pageable, Boolean active);
	
	@Query(value = "SELECT DISTINCT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ "b.poc_email as pocEmail, b.phone_number as phoneNumber, b.board_id AS boardId, "
			+ "b.logo_url as logoUrl, b.school_id AS schoolId, s.name AS school, b.active as active, b.created_at AS createdAt, "
			+ "'0' AS numberOfTeachers, "
			+ "'0' AS numberOfCoordinators, "
			+ "'0' AS numberOfPrincipals, "
			+ "'0' AS numberOfStudents "
			+ "FROM branches b "
			+ "LEFT JOIN schools s ON (s.id=b.school_id AND s.deleted=false AND s.active=true) "
			+ "INNER JOIN users_institution_mapping uim ON (uim.branch_id=b.id AND uim.school_id=b.school_id AND uim.deleted=false AND uim.active=true) "
			// + "LEFT JOIN teachers t ON (t.branch_id=b.id AND t.deleted=false AND t.active=true) "
			// + "LEFT JOIN students st ON (st.branch_id=b.id AND st.deleted=false AND st.active=true) "
			+ "WHERE b.deleted=false AND uim.user_id=:userId "
			+ "AND ((cast(:cityId AS text) IS NULL) OR (b.city_id=cast(:cityId AS text))) "
			+ "AND ((cast(:locality AS text) IS NULL) OR (b.locality=cast(:locality AS text))) "
			+ "AND ((cast(:boardId AS text) IS NULL) OR (b.board_id=cast(:boardId AS text))) "
			+ "AND ((cast(:search as text) IS NULL) OR ((LOWER(b.name) LIKE CONCAT(cast(:search as text),'%')))) "
			+ "AND ((cast(:branchId AS text) IS NULL) OR (b.id=cast(:branchId AS text))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(b.active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:schoolId AS text) IS NULL) OR (s.id=cast(:schoolId AS text))) "
			+ "GROUP BY (b.id, s.name)", nativeQuery = true)
	Page<BranchesProjection> forSchoolAdminFindAllBranchesByPagination(String cityId, String locality, String boardId,
			String search, String branchId, String schoolId, String userId, Pageable pageable, Boolean active);
	//*************** Pagination Ends ***********************//

	@Query("SELECT COUNT(distinct b.id) FROM #{#entityName} b WHERE b.deleted IS FALSE AND active IS TRUE AND "
			+ "((:schoolId IS NULL ) OR (b.schools.id =:schoolId)) ")
	long getBranchCount(String schoolId);
	
	@Query("SELECT COUNT(distinct b.id) FROM #{#entityName} b "
			+ "WHERE b.deleted IS FALSE AND b.active IS TRUE AND b.boardId IN (:boardIds) "
			+ "AND ((:schoolId IS NULL) OR (b.schools.id=cast(:schoolId AS text))) ")
	long getBranchCountWithBoard(String schoolId, List<String> boardIds);

	//************* Get all the branches: 1st method for SUPER_ADMIN and 2nd method for SCHOOL_ADMIN/MANAGEMENT ************//
	@Query(value = "SELECT b.id as id, b.name as name,b.active as active "
			+ "FROM branches b WHERE b.deleted IS FALSE AND b.active IS TRUE AND  ((cast(:search as text) IS NULL) OR "
			+ "(LOWER(b.name) LIKE CONCAT(cast(:search as text),'%'))) AND ((cast(:schoolId as text) IS NULL) "
			+ "OR b.school_id = cast(:schoolId AS text)) ORDER BY created_at DESC LIMIT 50 ", nativeQuery = true)
	List<BranchesProjection> getBranches(String search, String schoolId);
	
	@Query(value = "SELECT b.id as id, b.name as name,b.active as active "
			+ "FROM branches b "
			+ "INNER JOIN users_institution_mapping uim ON (uim.branch_id=b.id AND uim.school_id=b.school_id AND uim.deleted=false AND uim.active=true) "
			+ "WHERE b.deleted IS FALSE AND b.active IS TRUE AND uim.user_id=:userId "
			+ "AND  ((cast(:search as text) IS NULL) OR (LOWER(b.name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:schoolId as text) IS NULL) OR b.school_id = cast(:schoolId AS text)) "
			, nativeQuery = true)
	List<BranchesProjection> getBranchesForSchoolAdmin(String search, String schoolId, String userId);
	//************* Get all the branches: END ************//

	@Query("SELECT DISTINCT bp.planId FROM #{#entityName} b JOIN com.lms.userservice.entity.BranchPlanMappings bp  "
			+ "ON b.id = bp.branches.id WHERE b.id =:branchId AND b.schools.id =:schoolId AND bp.deleted IS FALSE "
			+ "AND bp.active IS TRUE ")
	List<String> getAllPlanIds(String branchId, String schoolId);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.BranchesMinDataResponseDto( "
			+ "b.id, b.name, b.active) FROM  #{#entityName} b JOIN  com.lms.userservice.entity.BranchPlanMappings bpm "
			+ "ON (b.id = bpm.branches.id) WHERE b.deleted IS FALSE AND b.active IS TRUE AND bpm.planId =:planId ")
	List<BranchesMinDataResponseDto> getAllBranchesByPlanId(String planId);

	@Query(value = "select count(*) from students where branch_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from teachers where branch_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from tokens where branch_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from branch_plan_mappings where branch_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from grade_section_mapping where branch_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from users_institution_mapping where branch_id=:id and deleted=false", nativeQuery = true)
	List<Long> findAllMappingCountOfBranch(String id);

	@Query(value = "SELECT count(*) FROM branches b " 
			+ "inner join branch_plan_mappings bpm on bpm.branch_id=b.id "
			+ "WHERE b.deleted=false AND b.active=true AND bpm.plan_id=:planId", nativeQuery = true)
	Long findCountOfBranchByPlanId(String planId);

	@Query(value = "SELECT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ "b.poc_email as pocEmail, b.phone_number as phoneNumber, b.board_id AS boardId, "
			+ "b.logo_url as logoUrl, b.school_id AS schoolId, s.name AS school, b.active as active, "
			+ "'0' AS numberOfTeachers, "
			+ "'0' AS numberOfCoordinators, "
			+ "'0' AS numberOfPrincipals, "
			+ "'0' AS numberOfStudents " 
			+ "FROM branches b " 
			// + "LEFT JOIN teachers t ON t.branch_id=b.id "
			// + "LEFT JOIN students st ON st.branch_id=b.id " 
			+ "LEFT JOIN schools s ON s.id=b.school_id "
			+ "WHERE b.deleted=false AND b.active=true AND b.id=:id " 
			+ "GROUP BY (b.id, s.name)", nativeQuery = true)
	BranchesProjection findBranchById(String id);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from branches", nativeQuery = true)
	String findLastModifiedAt();

	Branches getByName(String branchName);

	@Query(value = "SELECT DISTINCT board_id FROM branches WHERE deleted=false AND school_id=:schoolId ", nativeQuery = true)
	List<String> findAllBoardFromBranchBySchooldId(String schoolId);

	@Query(value = "SELECT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ "b.poc_email as pocEmail, b.phone_number as phoneNumber, b.board_id AS boardId, "
			+ "b.logo_url as logoUrl, b.school_id AS schoolId, s.name AS school, b.active as active "
			+ "FROM branches b " 
			+ "LEFT JOIN schools s ON s.id=b.school_id "
			+ "WHERE b.deleted=false AND b.id IN (:ids) ", nativeQuery = true)
	List<BranchesProjection> findAllBranchesByIds(List<String> ids);

        @Query("SELECT maxQuizReleases FROM #{#entityName} "
		+ " WHERE id =:branchId AND deleted IS FALSE AND active IS TRUE ")
	Integer getMaxQuizRelease(String branchId);       

    @Query(value = "select count(*) from grade_section_mapping where branch_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from students where branch_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from teachers where branch_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from tokens where branch_id=:id and deleted=false "
		+ "UNION ALL "
		+ "select count(*) from users_institution_mapping where branch_id=:id and deleted=false ", nativeQuery = true)
	List<Long> findUsages(String id);
    
    @Query("SELECT COUNT(distinct b.id) FROM #{#entityName} b "
    		+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.UsersInstitutionMapping uim "
    			+ "ON (uim.branches.id=b.id AND uim.schools.id=sc.id AND uim.deleted=false AND uim.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Users us ON (us.id=uim.users.id AND us.deleted=false AND us.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Administration adm ON (us.userName=adm.userName AND us.deleted=false AND us.active=true) "
    		+ "WHERE b.deleted IS FALSE AND b.active IS TRUE AND adm.userName=:userName "
			+ "AND ((:schoolId IS NULL ) OR (b.schools.id =:schoolId)) ")
	long findBranchCountForAdministrator(String schoolId, String userName);
	
    @Query("SELECT COUNT(distinct b.id) FROM #{#entityName} b "
    		+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.UsersInstitutionMapping uim "
    			+ "ON (uim.branches.id=b.id AND uim.schools.id=sc.id AND uim.deleted=false AND uim.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Users us ON (us.id=uim.users.id AND us.deleted=false AND us.active=true) "
    		+ "INNER JOIN com.lms.userservice.entity.Administration adm ON (us.userName=adm.userName AND us.deleted=false AND us.active=true) "
    		+ "WHERE b.deleted IS FALSE AND b.active IS TRUE AND adm.userName=:userName AND b.boardId IN (:boardIds) "
			+ "AND ((:schoolId IS NULL) OR (b.schools.id=cast(:schoolId AS text))) ")
	long findBranchCountWithBoardForAdministrator(String schoolId, List<String> boardIds, String userName);
    
    Branches getByNameAndSchoolsAndDeleted(String branchName, Schools schools, boolean deleted);
    
    @Query(value = "SELECT b.id AS id, b.name AS name, b.active as active "
			+ "FROM branches b "
			+ "WHERE b.deleted=false AND b.active=true AND b.id=:id ", nativeQuery = true)
	BranchesProjection findBrancheById(String id);
    
    @Query("SELECT name FROM #{#entityName}  WHERE deleted=false AND active=true "
    		+ "AND ((cast(:id AS text) IS NULL) OR (id=cast(:id AS text)))  ")
	String findBranchNameById(String id);
    
	@Query(value = "SELECT DISTINCT s.id AS schoolId, s.name AS name, s.code AS code, s.poc_email AS pocEmail, s.phone_number AS phoneNumber, s.website AS website, "
			+ "s.logo_url AS logoUrl, COUNT(DISTINCT b.id) AS branchCount,"
			// + " COUNT(DISTINCT gsm.grade_id) AS totalGrades,"
			// + " COUNT(DISTINCT st.id) AS numberOfStudents, s.city_id AS cityId "
			+ " '0' AS totalGrades,"
			+ " '0' AS numberOfStudents, s.city_id AS cityId "
			+ "FROM branches b "
			+ "INNER JOIN schools s ON (s.id=b.school_id AND s.deleted=false AND s.active=true) "
			// + "LEFT JOIN grade_section_mapping gsm ON (gsm.branch_id=b.id AND gsm.school_id=s.id AND gsm.deleted=false AND gsm.active=true) "
			// + "LEFT JOIN students st ON (st.branch_id=b.id AND st.school_id=s.id AND st.deleted=false AND st.active=true) "
			+ "WHERE b.deleted=false AND b.active=true AND b.board_id=:boardId GROUP BY s.id", nativeQuery = true)
	List<BranchesProjection> getAllSchoolDetails(String boardId);
	
	@Query(value = "SELECT id as id, name as name, city_id as cityId, "
			+ "locality as locality, board_id as boardId, plan_id as planId, school_id as schoolId, school as school,  "
			+ "s_count as numberOfStudents, g_count as totalGrades FROM "
			+ "(SELECT DISTINCT b.id, b.name, b.city_id, "
			+ "b.locality, b.board_id, bp.plan_id, sc.id as school_id, sc.name as school, COUNT(DISTINCT s.id) as s_count, "
			+ "COUNT(DISTINCT gsm.grade_id) as g_count FROM branches b "
			+ "INNER JOIN branch_plan_mappings bp ON(bp.branch_id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "INNER JOIN schools sc ON (sc.id=b.school_id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN students s ON(s.branch_id=b.id AND s.deleted=false AND s.active=true) "
			+ "LEFT JOIN grade_section_mapping gsm ON(gsm.branch_id=b.id and gsm.deleted=false and gsm.active=true) "
			+ "WHERE b.deleted=false AND b.active=true "
			+ "GROUP BY (b.id, bp.plan_id, sc.id) ) as branchlist ORDER BY s_count DESC LIMIT 5"
			, nativeQuery = true)
	List<BranchesProjection> findTopFiveBranchesByStudentCount();
	
	@Query(value = "SELECT DISTINCT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ " b.board_id AS boardId, bp.plan_id as planId,"
			// + " COUNT(DISTINCT st.id) AS numberOfStudents "
				+ " '0' AS numberOfStudents "
			+ "FROM branches b "
			+ "LEFT JOIN schools s ON (s.id=b.school_id AND s.deleted=false AND s.active=true) "
			// + "LEFT JOIN students st ON (st.branch_id=b.id AND st.deleted=false AND st.active=true) "
			+ "INNER JOIN branch_plan_mappings bp ON(bp.branch_id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE b.deleted=false AND active=true AND s.id=:schoolId "
			+ "GROUP BY (b.id, b.name)", nativeQuery = true)
	List<BranchesProjection> findBranchesForSuperAdmin(String schoolId);
	
	@Query(value = "SELECT DISTINCT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ " b.board_id AS boardId, bp.plan_id as planId,"
			+ " '0' AS numberOfStudents "
			+ "FROM branches b "
			+ "LEFT JOIN schools s ON (s.id=b.school_id AND s.deleted=false AND s.active=true) "
			+ "LEFT JOIN students st ON (st.branch_id=b.id AND st.deleted=false AND st.active=true) "
			+ "LEFT JOIN branch_plan_mappings bp ON(bp.branch_id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE b.deleted=false AND active=true AND s.id=:schoolId AND b.id IN(:branchIds) "
			+ "GROUP BY (b.id, s.name)", nativeQuery = true)
	List<BranchesProjection> findBranchesForAdministration(String schoolId, List<String> branchIds);
	
	@Query(value = "SELECT DISTINCT b.id AS id, b.name AS name, b.city_id AS cityId, b.locality AS locality, "
			+ " b.board_id AS boardId, bp.plan_id as planId,"
			// + " count(distinct st.id) AS numberOfStudents, s.id as schoolId,"
			+ " '0' AS numberOfStudents, s.id as schoolId,"
			+ " s.name AS school," 
			// + "count(distinct gsm.grade_id) AS totalGrades "
			+ " '0' AS totalGrades "
			+ "FROM branches b "
			+ "INNER JOIN schools s ON (s.id=b.school_id AND s.deleted=false AND s.active=true) "
			// + "LEFT JOIN grade_section_mapping gsm ON (gsm.branch_id=b.id AND gsm.school_id=s.id AND gsm.deleted=false AND gsm.active=true) "
			// + "LEFT JOIN students st ON (st.branch_id=b.id AND st.school_id=s.id AND st.deleted=false AND st.active=true) "
			+ "LEFT JOIN branch_plan_mappings bp ON(bp.branch_id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE b.deleted=false AND b.active=true AND b.id IN(:branchIds) "
			+ "GROUP BY (b.id,bp.plan_id,s.id) ORDER BY numberOfStudents DESC", nativeQuery = true)
	List<BranchesProjection> findAllForSchoolOverview(List<String> branchIds);
	
	@Query("SELECT DISTINCT b.boardId FROM #{#entityName} b WHERE b.deleted=false AND b.active=true "
			+ "AND b.id =:branchId AND b.schools.id =:schoolId ")
	String getBoardWithBranchAndSchool(String branchId, String schoolId);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.BranchSchoolNamesResponseDto(b.name, b.schools.name) FROM #{#entityName} b "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "WHERE b.deleted=false AND b.active=true "
			+ "AND b.boardId=:boardId AND b.id=:branchId AND b.schools.id=:schoolId ")
	BranchSchoolNamesResponseDto getBranchAndSchoolName(String boardId, String branchId, String schoolId);
	
	@Query("SELECT DISTINCT bp.planId FROM #{#entityName} b "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.BranchPlanMappings bp ON(bp.branches.id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE b.deleted=false AND b.active=true "
			+ "AND b.boardId=:boardId AND b.id=:branchId AND b.schools.id=:schoolId ")
	String getByPlanId(String boardId, String branchId, String schoolId);
	
	@Query(value="SELECT DISTINCT b.id FROM branches b "
			+ "INNER JOIN schools s ON(s.id=b.school_id AND s.deleted=false AND s.active=true) "
			+ "WHERE b.deleted=false AND b.active=true AND b.school_id IN(:schoolIds)", nativeQuery = true)
	List<String> findAllIdsBySchoolIds(List<String> schoolIds);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.SchoolBranchResponseDto(sc.id, sc.name, b.id, b.name) "
			+ "FROM #{#entityName} b "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "INNER JOIN com.lms.userservice.entity.BranchPlanMappings bp ON(bp.branches.id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE b.deleted=false AND b.active=true AND bp.planId IN(:planIds) ")
	List<SchoolBranchResponseDto> findForAssignAssessments(List<String> planIds);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.SchoolBranchResponseDto(sc.id, sc.name, b.id, b.name) "
			+ "FROM #{#entityName} b "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "WHERE b.deleted=false AND b.active=true AND sc.id IN(:schoolIds) AND b.id IN(:branchIds) ")
	List<SchoolBranchResponseDto> findInstituteDetailsForAssessmentPaper(List<String> schoolIds, List<String> branchIds);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.SchoolBranchResponseDto(sc.id, sc.name, b.id, b.name, b.logoUrl, b.cityId) "
			+ "FROM #{#entityName} b "
			+ "INNER JOIN com.lms.userservice.entity.Schools sc ON (sc.id=b.schools.id AND sc.deleted=false AND sc.active=true) "
			+ "WHERE b.deleted=false AND b.active=true AND sc.id=:schoolId AND b.id=:branchId ")
	SchoolBranchResponseDto findShoolBranchMinDetails(String schoolId, String branchId);

	@Query(value="select * from branches where id=:branchId",nativeQuery=true)
	Branches getByIds(String branchId);
	
	@Query(value="select id from branches where deleted=false and active=true and test_branch=true ",nativeQuery=true)
	List<String> getTestBranchList();
	
}
