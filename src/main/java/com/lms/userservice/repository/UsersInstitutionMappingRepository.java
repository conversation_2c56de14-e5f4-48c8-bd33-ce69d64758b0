package com.lms.userservice.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.UsersInstitutionMapping;
import com.lms.userservice.projection.SchoolsBranchesProjection;
import com.lms.userservice.response.dto.SchoolsBranchsMinResponseDto;

@Repository
public interface UsersInstitutionMappingRepository extends JpaRepository<UsersInstitutionMapping, String> {
	
	boolean existsById(String id);
	
	Long countByUsersId(String userId);

	@Modifying
	@Transactional
	Long deleteByUsersId(String userId);
	
	boolean existsByUsersIdAndBranchesIdAndSchoolsId(String usersId, String branchesId, String schoolsId);
	
	Long countByUsersIdAndSchoolsId(String userId, String schoolsId);
	
	@Query(value = "select uim.school_id as schoolId, "
			+ "    ( "
			+ "      select array_to_json(array_agg(row_to_json(d))) "
			+ "      from ( "
			+ "        select branch_id as branchId "
			+ "        from users_institution_mapping "
			+ "        where school_id = uim.school_id "
			+ "        group by branch_id "
			+ "      ) d "
			+ "    ) as branches "
			+ "  from users_institution_mapping uim "
			+ "  where user_id =:userId"
			+ "  group by school_id", nativeQuery = true)
	List<SchoolsBranchesProjection> findAllSchoolsAndBranchesByUserId(@Param("userId") String userId);
	
	@Query(value = "select distinct school_id from users_institution_mapping "
			+ "where user_id=:userId", nativeQuery = true)
	List<String> findAllSchoolIdByUserId(@Param("userId") String userId);
	
	@Query(value = "select distinct branch_id from users_institution_mapping "
			+ "where user_id=:userId and school_id=:schoolId", nativeQuery = true)
	List<String> findAllBranchIdByUserIdAndSchoolId(@Param("userId") String userId, @Param("schoolId") String schoolId);
	
	@Query(value = "SELECT COUNT(id) > 0 from users_institution_mapping WHERE user_id=:userId ", nativeQuery = true)
	boolean checkingUserIdExists(String userId);
	
	@Query("SELECT new com.lms.userservice.response.dto.SchoolsBranchsMinResponseDto(uim.schools.id, uim.branches.id) FROM #{#entityName} uim "
			+ "WHERE uim.deleted=false AND uim.active=true AND uim.users.id=:userId ")
	List<SchoolsBranchsMinResponseDto> findSchoolBranchsForAdmin(String userId);
}
