package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.BranchCommunication;
import com.lms.userservice.projection.BranchCommunicationProjection;

@Repository
public interface BranchCommunicationRepository extends JpaRepository<BranchCommunication, String> {

	@Query("select count(id) > 0 from #{#entityName} where branches.id=:branchId ")
	boolean findActiveStatus(String branchId);

	@Query(value = "(SELECT id AS id, communication_action AS communication, sms AS sms, email AS email, active AS active "
			+ "FROM branch_communication WHERE deleted=false AND id=:id) ", nativeQuery = true)
	BranchCommunicationProjection getCommunicationDetails(String id);

	List<BranchCommunication> findBybranchesId(String branchId);

	BranchCommunication getBybranchesId(String branchId);
	
	boolean existsByBranchesIdAndDeleted(String branchId, boolean deleted);
	
	@Query(value = "SELECT sms "
			+ "FROM branch_communication WHERE deleted=false AND branch_id=:branchId AND communication_action=:smsAction ", nativeQuery = true)
	boolean checkSmsAllowed(String branchId, String smsAction);
	
	@Query(value = "SELECT email "
			+ "FROM branch_communication WHERE deleted=false AND branch_id=:branchId AND communication_action=:smsAction ", nativeQuery = true)
	boolean checkEmailAllowed(String branchId, String smsAction);
	
	@Query(value = "SELECT bc.sms FROM branch_communication bc "
			+ "INNER JOIN branches b ON (b.id=bc.branch_id AND b.deleted=false AND b.active=true) "
			+ "WHERE bc.deleted=false AND bc.active=true AND bc.communication_action=:smsAction "
			+ "AND b.id=:branchId ", nativeQuery = true)
	boolean checkSMSAllowedForBranchByUsername(String branchId, String smsAction);

}
