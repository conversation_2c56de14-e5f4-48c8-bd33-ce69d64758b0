package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.lms.userservice.entity.Administration;
import com.lms.userservice.projection.AdministrationProjection;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;

@Repository
public interface AdministrationRepository extends JpaRepository<Administration, String> {

	boolean existsById(String id);

	@Query(value = " SELECT distinct a.id as id, a.first_name as firstName , a.last_name as lastName, "
			+ "a.email as email, a.mobile as mobile, a.gender as gender, "
			+ "uim_2.schoolCount as numberOfSchools, uim_2.branchCount as numberOfBranches, "
			+ "CASE WHEN uim_2.schoolCount = 1 then s.name END as schoolName, u.id as userId, "
			+ "u.user_name as userName, a.active as active, a.created_at as createdAt "
			+ "FROM administration a "
			+ "INNER JOIN users u ON u.user_name = a.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id = u.id "
			+ "INNER JOIN users_institution_mapping uim ON uim.user_id = u.id "
			+ "INNER JOIN (SELECT user_id, count(distinct school_id) as schoolCount, "
			+ "count(distinct branch_id) as branchCount "
			+ "FROM users_institution_mapping "
			+ "GROUP BY user_id) uim_2 ON uim_2.user_id = u.id "
			+ "INNER JOIN schools s ON s.id = uim.school_id "
			+ "INNER JOIN branches b ON b.id = uim.branch_id "
			+ "WHERE a.deleted=false "
			+ "AND ((cast(:schoolId as text) IS NULL) OR (s.id=cast(:schoolId as text))) "
			+ "AND ((cast(:branchId as text) IS NULL) OR (b.id=cast(:branchId as text))) "
			+ "AND ((cast(:roleId as text) IS NULL) OR (urm.role_id=cast(:roleId as text))) "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (LOWER(a.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(a.mobile) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(a.first_name), ' '), (LOWER(a.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(a.last_name), ' '), (LOWER(a.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(a.first_name)), (LOWER(a.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(a.last_name)), (LOWER(a.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) ) "
			+ "AND ((cast(:adminType as text) IS NULL) OR (a.administration_type=cast(:adminType as text))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(a.active AS text) = cast(:active AS text))) ", nativeQuery = true)
	Page<AdministrationProjection> findAllAdministrations(String schoolId, String branchId, String roleId,
			String search, String adminType, Boolean active, Pageable pageable);

	@Query(value = " SELECT a.id as id, a.first_name as firstName, a.last_name as lastName, "
			+ "a.email as email, a.mobile as mobile, a.gender as gender, u.id as userId, u.user_name as userName " 
			+ "FROM administration a "
			+ "INNER JOIN users u ON u.user_name = a.user_name "
			+ "WHERE a.deleted=false AND a.id=:id ", nativeQuery = true)
	AdministrationProjection findAdministrationById(@Param("id") String id);

	@Query(value = "SELECT distinct a.id as id, a.first_name as firstName , a.last_name as lastName, "
			+ "a.email as email, a.mobile as mobile, a.gender as gender, "
			+ "uim_2.schoolCount as numberOfSchools, uim_2.branchCount as numberOfBranches, "
			+ "CASE WHEN uim_2.schoolCount = 1 then s.name END as schoolName, u.id as userId, "
			+ "a.active as active, u.user_name as userName FROM administration a "
			+ "INNER JOIN users u ON u.user_name = a.user_name "
			+ "INNER JOIN users_role_mapping urm ON urm.user_id = u.id "
			+ "INNER JOIN users_institution_mapping uim ON uim.user_id = u.id "
			+ "INNER JOIN (SELECT user_id, count(distinct school_id) as schoolCount, "
			+ "count(distinct branch_id) as branchCount " 
			+ "FROM users_institution_mapping "
			+ "GROUP BY user_id) "
			+ "uim_2 ON uim_2.user_id = u.id " 
			+ "INNER JOIN schools s ON s.id = uim.school_id "
			+ "INNER JOIN branches b ON b.id = uim.branch_id "
			+ "WHERE a.deleted=false AND a.active=true "
			+ "AND ((cast(:idOrUserName as text) IS NULL) OR (a.id=cast(:idOrUserName as text)) "
			+ "OR (a.user_name=cast(:idOrUserName as text))) ", nativeQuery = true)
	AdministrationProjection findAdministrationByIdOrUsername(String idOrUserName);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from administration where administration_type=:adminType", nativeQuery = true)
	String findLastModifiedAt(String adminType);

	@Modifying
	@Transactional
	@Query(value = "update administration set deleted=true, active=false, modified_at=:modifiedAt, "
			+ "last_modified_by=:lastModifiedBy where deleted=false and id=:id", nativeQuery = true)
	int setDeleteStatus(String id, Long modifiedAt, String lastModifiedBy);

	@Query("SELECT new com.lms.userservice.response.dto.NameCommonResponseDto( "
			+ "id, firstName, lastName) FROM #{#entityName} WHERE deleted IS FALSE "
			+ "AND active=true AND roleId=:roleId")
	List<NameCommonResponseDto> getAllAdministrationDetailsByRoleId(String roleId);

	@Query(value = "SELECT a.id as id, a.first_name as firstName, a.last_name as lastName, "
			+ "a.email as email, a.mobile as mobile, a.gender as gender, u.id as userId, u.user_name as userName " 
			+ "FROM administration a "
			+ "INNER JOIN users u ON u.user_name = a.user_name "
			+ "WHERE a.deleted=false AND a.user_name=:username", nativeQuery = true)
	AdministrationProjection getAdminByUserName(String username);

    public boolean existsByIdAndDeleted(String id, boolean b);
    
    @Query("SELECT new com.lms.userservice.response.dto.ShareDetailsResponseDto(userName, firstName, lastName, email, mobile) "
    		+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
    ShareDetailsResponseDto findByUsernameForShareDetails(String userName);
    
    public boolean existsByUserNameAndDeleted(String userName, boolean deleted);
        
    @Query("SELECT new com.lms.userservice.request.dto.CreateUserEmailRequestDto(email, userName, firstName) "
    		+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
    CreateUserEmailRequestDto findByUsernameForUpdatePassByAdmin(String userName);
    
    @Query("SELECT COUNT(id) FROM #{#entityName} WHERE deleted=:deleted AND (id=:idOrUsername OR userName=:idOrUsername)")
    int exitAdministrationByIdOrUsername(String idOrUsername, boolean deleted);
}
