package com.lms.userservice.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.lms.userservice.entity.Users;
import com.lms.userservice.projection.UsersProjection;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;
import com.lms.userservice.response.dto.ShareDetailsResponseDto;
import com.lms.userservice.response.dto.UserDetailsResponseDto;
import com.lms.userservice.response.dto.UserMinResponseDto;
import com.lms.userservice.response.dto.UsersFeignDto;
import com.lms.userservice.response.dto.UsersResponseDto;

@Repository
public interface UsersRepository extends JpaRepository<Users, String> {

	Users findByUserName(String userName);

	Users findByUserNameIgnoreCase(String userName);

	boolean existsById(String id);

	boolean existsByUserName(String userName);

	boolean existsByEmail(String email);
	
	boolean existsByEmailAndDeleted(String email, boolean deleted);

	boolean existsByPhoneNumber(String phoneNumber);

	boolean existsByPhoneNumberAndOtp(String phoneNumber, String otp);

	Optional<Users> findByEmail(String email);

	Optional<Users> findByPhoneNumber(String phoneNumber);

	@Query(value = "select * from users where deleted=false AND active=true AND phone_number=:phoneNumber "
			+ "AND user_name=:userName", nativeQuery = true)
	Users findUserByPhoneNumber(String phoneNumber, String userName);

	@Query(value = "select * from users where deleted=false and phone_number=:phoneNumber and otp=:otp", nativeQuery = true)
	Users findUserByPhoneNumberAndOtp(String phoneNumber, String otp);

	@Query("SELECT new com.lms.userservice.response.dto.UsersResponseDto(u.id, u.userName, u.email, u.phoneNumber) "
			+ "FROM #{#entityName} u WHERE u.deleted=false AND u.active=true AND u.id=:id")
	UsersResponseDto findUsersById(@Param("id") String id);

	@Query(value = "SELECT distinct u.id as id, u.first_name as firstName, u.last_name as lastName, "
			+ "u.user_name as userName, u.email as email, u.phone_number as phoneNumber, "
			+ "TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime, u.active as active, u.created_at as createdAt "
			+ "FROM users u "
			+ "LEFT JOIN users_role_mapping urm ON u.id=urm.user_id "
			+ "WHERE u.deleted=false and admin_user=:adminUser "
			+ "AND ((cast(:search as text) IS NULL) "
			+ "OR (CONCAT(concat(LOWER(u.first_name), ' '), (LOWER(u.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(u.last_name), ' '), (LOWER(u.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(u.first_name)), (LOWER(u.last_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (CONCAT(concat(LOWER(u.last_name)), (LOWER(u.first_name))) LIKE CONCAT('%',cast(:search as text),'%')) "
			+ "OR (LOWER(u.email) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(u.phone_number) LIKE CONCAT(cast(:search as text),'%')) "
			+ "OR (LOWER(u.user_name) LIKE CONCAT(cast(:search as text),'%'))) "
			+ "AND ((cast(:active as text) IS NULL) OR (cast(u.active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:roleId as text) IS NULL) OR (urm.role_id=cast(:roleId as text)))", nativeQuery = true)
	Page<UsersProjection> findAllUserBySearchAndFilter(boolean adminUser, String search, String roleId,
			Pageable pageable, Boolean active);

	@Query("SELECT new com.lms.userservice.response.dto.UsersFeignDto(u.id, u.userName, u.email, u.phoneNumber, u.password, u.firstName, u.lastName) "
			+ "FROM #{#entityName} u WHERE u.deleted=false AND u.userName=:userName")
	UsersFeignDto findUsersByUserNameForFeign(@Param("userName") String userName);

	@Query("SELECT new com.lms.userservice.response.dto.UsersResponseDto(u.id, u.userName, u.email, u.phoneNumber) "
			+ "FROM #{#entityName} u WHERE u.deleted=false AND u.active=true AND u.userName=:userName")
	UsersResponseDto findUsersByUserName(@Param("userName") String userName);

	@Query("SELECT new com.lms.userservice.response.dto.UsersResponseDto(u.id, u.userName, u.email, u.phoneNumber) "
			+ "FROM #{#entityName} u " + "WHERE u.deleted=false AND u.userName=:userName "
			+ "AND ((:emailOrMobile IS NULL) OR (u.email=:emailOrMobile) OR (u.phoneNumber=:emailOrMobile))")
	UsersResponseDto findByUserNameOrEmailOrMobile(@Param("userName") String userName,
			@Param("emailOrMobile") String emailOrMobile);

	@Query(value = "select count(*) from users_institution_mapping where user_id=:id and deleted=false AND active=true "
			+ "UNION ALL "
			+ "select count(*) from users_role_mapping where user_id=:id and deleted=false AND active=true "
			+ "UNION ALL "
			+ "select count(*) from user_token_mapping where user_id=:id and deleted=false AND active=true ", nativeQuery = true)
	List<Long> findAllMappingCountOfUser(String id);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from users", nativeQuery = true)
	String findLastModifiedAt();

	@Query("SELECT new com.lms.userservice.response.dto.AdminUsersResponseDto(u.id, u.firstName, u.lastName, "
			+ "u.userName, u.email, u.phoneNumber, u.active) "
			+ "FROM #{#entityName} u WHERE u.deleted=false AND u.active=true AND u.id=:id")
	AdminUsersResponseDto findAdminUsersById(@Param("id") String id);
	
	long countByFirstNameAndLastName(String firstName, String lastName);

	@Modifying
	@Transactional
	@Query(value = "update users set active=:active, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "where deleted=false and id=:id", nativeQuery = true)
	int setActiveStatus(String id, boolean active, Long modifiedAt, String lastModifiedBy);

	@Query("select active from #{#entityName} where id=:id")
	boolean findActiveStatus(String id);

	@Modifying
	@Transactional
	@Query(value = "update users set deleted=true, active=false, password=null, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy, "
			+ "email=:email, phone_number=:phoneNumber where deleted=false and id=:id", nativeQuery = true)
	int setDeleteStatus(String id, Long modifiedAt, String lastModifiedBy, String email, String phoneNumber);

	@Modifying
	@Transactional
	@Query(value = "update users set password=:password, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "where deleted=false and id=:id", nativeQuery = true)
	int setPasswordById(String id, Long modifiedAt, String lastModifiedBy, String password);

	@Query(value = "select distinct user_name from users", nativeQuery = true)
	List<String> findAllUsernameForUpdate();

	@Query("SELECT new com.lms.userservice.response.dto.UserMinResponseDto(u.id, "
			+ "u.userName, u.email) FROM #{#entityName} u JOIN com.lms.userservice.entity.UsersRoleMapping urm "
			+ "ON (u.id = urm.users.id) WHERE urm.deleted IS FALSE AND urm.active IS TRUE AND u.deleted IS FALSE AND u.active IS TRUE AND urm.roleId =:roleId ")
	List<UserMinResponseDto> getAllUsersMappedToRole(String roleId);

	@Query(value = "SELECT distinct u.id as id, u.first_name as firstName, u.last_name as lastName,"
			+ " u.user_name as userName, u.email as email, u.phone_number as phoneNumber, "
			+ " TO_TIMESTAMP(u.last_login_time/1000) as lastLoginTime, u.active as active " 
			+ " FROM users u "
			+ " WHERE u.deleted=false and u.user_name=:username", nativeQuery = true)
	UsersProjection getUserByUserName(String username);

	boolean existsByUserNameAndDeletedAndActive(String userName, boolean deleted, boolean active);

	boolean existsByUserNameIgnoreCaseAndDeletedAndActive(String userName, boolean deleted, boolean active);

	boolean existsByIdAndDeleted(String id, boolean b);

	@Query(value = "select count(*) from users_institution_mapping where user_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from users_role_mapping where user_id=:id and deleted=false " 
			+ "UNION ALL "
			+ "select count(*) from user_token_mapping where user_id=:id and deleted=false ", nativeQuery = true)
	List<Long> findUsages(String id);
	
	@Query("SELECT new com.lms.userservice.response.dto.ShareDetailsResponseDto(userName, firstName, lastName, email, phoneNumber) "
    		+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
    ShareDetailsResponseDto findByUsernameForShareDetails(String userName);
	
	public boolean existsByUserNameAndDeleted(String userName, boolean deleted);
    
    @Query("SELECT new com.lms.userservice.request.dto.CreateUserEmailRequestDto(email, userName, firstName) "
    		+ "FROM #{#entityName} WHERE deleted IS FALSE AND active=true AND userName=:userName")
    CreateUserEmailRequestDto findByUsernameForUpdatePassByAdmin(String userName);
    
    public boolean existsByUserNameAndPhoneNumberAndDeleted(String userName, String phoneNumber, boolean deleted);
    
	@Query(value = "SELECT distinct u.user_name FROM users u WHERE u.active=true and u.deleted=false "
			+ "AND (phone_number=:extractor OR email=:extractor) ", nativeQuery = true)
	List<String> getAllExtractingUserName(String extractor);
	
	@Query("SELECT COUNT(distinct u.id) FROM #{#entityName} u WHERE u.deleted=false AND u.active=true "
			+ "AND u.userName LIKE CONCAT(cast(:username as text),'%') ")
	Long countByStudentUsername(String username);
	
	@Query("SELECT new com.lms.userservice.response.dto.UserDetailsResponseDto(u.id, "
			+ "u.userName, u.firstName, u.lastName, u.email, u.phoneNumber, u.active) FROM #{#entityName} u "
			+ "INNER JOIN com.lms.userservice.entity.UsersRoleMapping urm ON (u.id = urm.users.id "
			+ "AND urm.deleted=false AND urm.active=true) "
			+ "WHERE urm.roleId =:roleId ")
	List<UserDetailsResponseDto> getAllUsersRole(String roleId);
	
	@Query(value = "SELECT DISTINCT u.id FROM users u "
			+ "WHERE u.deleted=false AND u.active=true AND u.user_name=:userName ", nativeQuery = true)
	String findUserIdByUserName(String userName);

	@Query(value = "SELECT DISTINCT u.admin_user FROM users u "
			+ "WHERE u.deleted=false AND u.active=true AND u.user_name=:userName ", nativeQuery = true)
	boolean checkingAdminUserExists(String userName);
	
	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.UserMinResponseDto(u.userName, concat(u.firstName, ' ', u.lastName)) "
			+ "FROM #{#entityName} u WHERE u.deleted=false AND u.active=true AND u.userName IN(:userNames)")
	List<UserMinResponseDto> findAllUsersByUserNames(List<String> userNames);
	
	@Query(value = "SELECT Count(id) > 0 FROM users WHERE user_name=:userName ", nativeQuery = true)
	boolean checkingUsersExits(String userName);
	
	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') "
			+ "from users where user_name=:username", nativeQuery = true)
	String findLastModifiedAtByUsername(String username);

	@Query(value = "SELECT distinct u.user_name FROM users u "
			+ "JOIN users_role_mapping urm ON u.id=urm.user_id "
			+ "WHERE u.active=true and u.deleted=false and urm.active=true and urm.deleted=false "
			+ "and urm.role_id=:roleId AND (phone_number=:extractor OR email=:extractor)", nativeQuery = true)
	List<String> getAllExtractingUserNameWithRoleId(String extractor, String roleId);

}
