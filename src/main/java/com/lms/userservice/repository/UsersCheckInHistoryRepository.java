package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.lms.userservice.entity.UsersCheckInHistory;
import com.lms.userservice.projection.UsersWebMobCountProjection;

public interface UsersCheckInHistoryRepository extends JpaRepository<UsersCheckInHistory, String> {

//	@Query(value = "SELECT " +
//		    "   SUM(CASE WHEN EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
//		    "            AND NOT EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
//		    "           THEN 1 ELSE 0 END) AS onlyWebUsersCount, " +
//		    "   SUM(CASE WHEN EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
//		    "            AND NOT EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
//		    "           THEN 1 ELSE 0 END) AS onlyMobUsersCount, " +
//		    "   SUM(CASE WHEN EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
//		    "            AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
//		    "            AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) > " +
//		    "                (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
//		    "           THEN 1 ELSE 0 END) AS bothUsersWebAndMobWebFavored, " +
//		    "   SUM(CASE WHEN EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
//		    "            AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
//		    "            AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) >= " +
//		    "                (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
//		    "           THEN 1 ELSE 0 END) AS bothUsersWebAndMobMobFavored " +
//		    "FROM users_check_in_history u " +
//		    "WHERE (EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
//		    "           OR EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true)) " +
//		    "   AND u.user_name IN :userNames", nativeQuery = true)
//		UsersWebMobCountProjection getUsersCounts(List<String> userNames);

	@Query(value = "SELECT COUNT(*) AS entryCount FROM users_check_in_history u "
			+ "WHERE EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) "
			+ "AND NOT EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) "
			+ "AND u.user_name IN :userNames ", nativeQuery = true)
	long countOnlyWebUsers(List<String> userNames);

	@Query(value = "SELECT COUNT(*) AS entryCount FROM users_check_in_history u "
			+ "WHERE EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) "
			+ "AND NOT EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) "
			+ "AND u.user_name IN :userNames ", nativeQuery = true)
	long countOnlyMobUsers(List<String> userNames);

	@Query(value = "SELECT COUNT(distinct user_name) AS entryCount FROM users_check_in_history u "
			+ "WHERE EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) "
			+ "AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) "
			+ "AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) > "
			+ "(SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) "
			+ "AND u.user_name IN :userNames ", nativeQuery = true)
	long countBothWebMobWebFavored(List<String> userNames);

	@Query(value = "SELECT COUNT(distinct user_name) AS entryCount FROM users_check_in_history u "
			+ "WHERE EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) "
			+ "AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) "
			+ "AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) >= "
			+ "(SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) "
			+ "AND u.user_name IN :userNames ", nativeQuery = true)
	long countBothWebMobMobFavored(List<String> userNames);

	@Query("SELECT COUNT(DISTINCT userName) FROM #{#entityName} "
			+ " WHERE deleted=false AND active=true AND userName IN(:userNames) ")
	long totalUserCount(List<String> userNames);
	
	@Query(value = "SELECT " +
            "   b.id AS branchId, " +
            "   b.name AS branch, " +
            "   s.id AS schoolId, " +
            "   s.name AS school, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = true AND u.mob_in = false THEN u.user_name END) AS onlyWebUsersCount, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = false AND u.mob_in = true THEN u.user_name END) AS onlyMobUsersCount, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = true AND u.mob_in = false " +
            "                       AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
            "                       AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) > " +
            "                           (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
            "                  THEN u.user_name END) AS bothUsersWebAndMobWebFavored, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = true AND u.mob_in = false " +
            "                       AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
            "                       AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) >= " +
            "                           (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
            "                  THEN u.user_name END) AS bothUsersWebAndMobMobFavored, " +
            "   COUNT(DISTINCT u.user_name) AS totalUsersCount " +
            "FROM users_check_in_history u " +
            "INNER JOIN students st ON u.user_name = st.username " +
            "INNER JOIN branches b ON st.branch_id = b.id " +
            "INNER JOIN schools s ON b.school_id = s.id " +
            "WHERE (u.web_in = true AND u.mob_in = false) OR (u.web_in = false AND u.mob_in = true) " +
            "   AND st.branch_id IN :branchIds " +
            "GROUP BY b.id, b.name, s.id, s.name", nativeQuery = true)
    List<UsersWebMobCountProjection> findAllCountForAdministration(List<String> branchIds);
	
	@Query(value = "SELECT " +
            "   b.id AS branchId, " +
            "   b.name AS branch, " +
            "   s.id AS schoolId, " +
            "   s.name AS school, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = true AND u.mob_in = false THEN u.user_name END) AS onlyWebUsersCount, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = false AND u.mob_in = true THEN u.user_name END) AS onlyMobUsersCount, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = true AND u.mob_in = false " +
            "                       AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
            "                       AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) > " +
            "                           (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
            "                  THEN u.user_name END) AS bothUsersWebAndMobWebFavored, " +
            "   COUNT(DISTINCT CASE WHEN u.web_in = true AND u.mob_in = false " +
            "                       AND EXISTS (SELECT 1 FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) " +
            "                       AND (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = false AND mob_in = true) >= " +
            "                           (SELECT COUNT(*) FROM users_check_in_history WHERE user_name = u.user_name AND web_in = true AND mob_in = false) " +
            "                  THEN u.user_name END) AS bothUsersWebAndMobMobFavored, " +
            "   COUNT(DISTINCT u.user_name) AS totalUsersCount " +
            "FROM users_check_in_history u " +
            "INNER JOIN students st ON u.user_name = st.username " +
            "INNER JOIN branches b ON st.branch_id = b.id " +
            "INNER JOIN schools s ON b.school_id = s.id " +
            "WHERE (u.web_in = true AND u.mob_in = false) OR (u.web_in = false AND u.mob_in = true) " +
            "GROUP BY b.id, b.name, s.id, s.name", nativeQuery = true)
    List<UsersWebMobCountProjection> findAllCountForSuperAdmin();
	
}
