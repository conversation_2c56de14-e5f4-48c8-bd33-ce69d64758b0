package com.lms.userservice.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.GradeSectionMapping;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.projection.TeacherAssignProjection;
import com.lms.userservice.response.dto.GradeSectionMinResponseDto;

public interface GradeSectionMappingRepository extends JpaRepository<GradeSectionMapping, String> {

	boolean existsById(String id);

	boolean existsByGradeIdAndDeleted(String gradeId, boolean deleted);
	
	//boolean existsByGradeIdAndSectionIdAndDeletedAndIdNot(String gradeId, String sectionId, boolean deleted, String id);
	
	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndIdNot(String schoolId, String branchId, String gradeId,
			boolean deleted, String id);

	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeletedAndIdNot(String schoolId, String branchId,
			String gradeId, String sectionId, boolean deleted, String id);
	
	
	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndDeleted(String schoolId, String branchId, String gradeId,
			boolean deleted);

	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeleted(String schoolId, String branchId,
			String gradeId, String sectionId, boolean deleted);
	
	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndSectionIdAndDeletedAndActive(String schoolId, String branchId,
			String gradeId, String sectionId, boolean deleted, boolean active);
	
	boolean existsBySchoolsIdAndBranchesIdAndGradeIdAndDeletedAndActiveAndSectionIdIsNull(String schoolId, String branchId, String gradeId,
			boolean deleted, boolean active);

	List<GradeSectionMapping> findBySchoolsIdAndBranchesIdAndDeleted(String schoolId, String branchId, boolean deleted);

	@Query(value = "SELECT count(*)>0  FROM grade_section_mapping WHERE deleted=:deleted "
			+ " AND id=:id", nativeQuery = true)
	boolean existsByIdAndDeleted(String id, boolean deleted);

	@Query(value = "SELECT count(*)>0  FROM grade_section_mapping WHERE deleted=:deleted AND active=:active "
			+ " AND id=:id", nativeQuery = true)
	boolean existsByIdAndActiveAndDeleted(String id, boolean active, boolean deleted);

	@Query(value = "SELECT count(*) FROM grade_section_mapping WHERE deleted=false AND "
			+ "school_id=:schoolId AND branch_id=:branchId AND "
			+ "grade_id=:gradeId AND section_id IS NULL", nativeQuery = true)
	Long countBySchoolBrachGrade(String schoolId, String branchId, String gradeId);

	@Query(value = "SELECT DISTINCT grade_id FROM grade_section_mapping WHERE deleted=false AND id IN (:ids) ", nativeQuery = true)
	List<String> findAllGradeByIds(List<String> ids);

	@Query(value = "SELECT DISTINCT section_id FROM grade_section_mapping WHERE deleted=false AND id IN (:ids) ", nativeQuery = true)
	List<String> findAllSectionByIds(List<String> ids);

	@Query(value = "SELECT * FROM grade_section_mapping WHERE deleted=false AND id IN (:ids)", nativeQuery = true)
	List<GradeSectionMapping> findMapingByIdIn(List<String> ids);

	List<GradeSectionMapping> findAllByIdIn(List<String> ids);

	@Query(value = "SELECT DISTINCT grade_id FROM grade_section_mapping "
			+ "WHERE deleted=false AND school_id=:schoolId AND branch_id=:branchId ", nativeQuery = true)
	List<String> findAllGradesBySchoolBranch(String schoolId, String branchId);

	@Query(value = "SELECT DISTINCT grade_id FROM grade_section_mapping "
			+ "WHERE deleted=false AND active=true AND school_id=:schoolId AND branch_id=:branchId ", nativeQuery = true)
	List<String> findAllActiveGradesBySchoolBranch(String schoolId, String branchId);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.GradeSectionMinResponseDto(gsm.id, gsm.sectionId, gsm.active) "
			+ "FROM #{#entityName} gsm WHERE gsm.deleted=false AND gsm.active=true AND gsm.gradeId=:gId "
			+ "AND gsm.branches.id=:bId AND gsm.schools.id=:scId ")
	List<GradeSectionMinResponseDto> findAllSectionByGradeBranchSchool(String gId, String bId, String scId);

	@Query("SELECT DISTINCT gsm.sectionId FROM #{#entityName} gsm "
			+ "WHERE gsm.deleted=false AND gsm.active=true "
			+ "AND ((cast(:gradeId as text) IS NULL) OR (gsm.gradeId=(cast(:gradeId as text)))) "
			+ "AND gsm.branches.id=:branchId AND gsm.schools.id=:schoolId")
	List<String> findAllSectionByGradeIdBranchAndSchool(String gradeId, String branchId, String schoolId);

	@Query(value = "SELECT * FROM grade_section_mapping WHERE deleted=false AND section_id=:sId "
			+ "AND grade_id=:gId AND branch_id=:bId AND school_id=:scId", nativeQuery = true)
	GradeSectionMapping findGradeSectionMapping(String sId, String gId, String bId, String scId);

	@Query(value = "SELECT DISTINCT section_id FROM grade_section_mapping WHERE deleted=false "
			+ "AND grade_id=:gId AND branch_id=:bId AND school_id=:scId", nativeQuery = true)
	List<String> findSections(String gId, String bId, String scId);

	@Query(value = "SELECT * FROM grade_section_mapping WHERE deleted=false AND section_id=:sId "
			+ "AND grade_id=:gId AND branch_id=:bId AND school_id=:scId", nativeQuery = true)
	List<GradeSectionMapping> findAllSections(String sId, String gId, String bId, String scId);

	@Query(value = "select count(*) from grade_section_mapping gsm where deleted=false AND active=true and grade_id=:gradeId", nativeQuery = true)
	Long findCountOfSectionsByGradeId(String gradeId);

	@Query(value = "select count(*) from grade_section_mapping gsm where deleted=false AND active=true and section_id=:sectionId", nativeQuery = true)
	Long findCountOfSectionsBySectionId(String sectionId);

	@Query(value = "select to_char(to_timestamp(MAX(modified_at) / 1000), 'DD-MM-YYYY | HH12:MI AM') from grade_section_mapping", nativeQuery = true)
	String findLastModifiedAt();

	@Modifying
	@Transactional
	@Query(value = "UPDATE grade_section_mapping SET active=:active, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "WHERE deleted=false AND section_id=:sectionId AND grade_id=:gradeId AND branch_id=:branchId AND school_id=:schoolId", nativeQuery = true)
	void updateActiveBySectionIdAndGradeIdAndBrachIdAndSchoolId(String sectionId, String gradeId, String branchId,
			String schoolId, boolean active, Long modifiedAt, String lastModifiedBy);

	@Modifying
	@Transactional
	@Query(value = "UPDATE grade_section_mapping SET active=:active, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "WHERE deleted=false AND id=:id ", nativeQuery = true)
	void toggleActiveById(String id, boolean active, Long modifiedAt, String lastModifiedBy);

	@Modifying
	@Transactional
	@Query(value = "UPDATE grade_section_mapping SET active=false, deleted=true, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "WHERE deleted=false AND section_id=:sectionId AND grade_id=:gradeId AND branch_id=:branchId AND school_id=:schoolId", nativeQuery = true)
	void deleteBySectionIdAndGradeIdAndBrachIdAndSchoolId(String sectionId, String gradeId, String branchId,
			String schoolId, Long modifiedAt, String lastModifiedBy);

	@Modifying
	@Transactional
	@Query(value = "UPDATE grade_section_mapping SET active=false, deleted=true, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "WHERE deleted=false AND id=:id ", nativeQuery = true)
	void deleteById(String id, Long modifiedAt, String lastModifiedBy);

	@Query(value = "SELECT DISTINCT active FROM grade_section_mapping WHERE deleted=false AND grade_id=:gradeId "
			+ "AND branch_id=:branchId AND school_id=:schoolId", nativeQuery = true)
	List<Boolean> findActiveByGradeIdBrachIdAndSchoolId(String gradeId, String branchId, String schoolId);

	@Query(value = "SELECT DISTINCT deleted FROM grade_section_mapping WHERE deleted=true AND grade_id=:gradeId "
			+ "AND branch_id=:branchId AND school_id=:schoolId", nativeQuery = true)
	List<Boolean> findDeletedByGradeIdBrachIdAndSchoolId(String gradeId, String branchId, String schoolId);
	
	@Query(value = "SELECT COUNT(*) FROM students WHERE deleted=false AND grade_id=:gradeId "
			+ "UNION ALL "
			+ "SELECT COUNT(*) FROM grade_section_mapping WHERE deleted=false AND grade_id=:gradeId "
			+ "UNION ALL "
			+ "SELECT COUNT(*) FROM assign_teacher WHERE deleted=false AND grade_id=:gradeId ", nativeQuery = true)
	List<Long> findMappingForGrade(String gradeId);
	
	//confirmation-api checking for the academicYearId
	@Query(value = "SELECT COUNT(*) FROM grade_section_mapping WHERE deleted=false AND academic_year_id=:academicYearId ", nativeQuery = true)
	List<Long> findTheGradeSectionMappingAcademicYearId(String academicYearId);
	
	@Modifying
	@Transactional
	@Query(value = "UPDATE grade_section_mapping SET section_data=:sectionData, section_id=:sectionId, "
			+ "grade_id=:gradeId, modified_at=:modifiedAt, last_modified_by=:lastModifiedBy "
			+ "WHERE deleted=false AND active=true AND id=:id", nativeQuery = true)
	int updateGradeSectionMapping(String sectionData, String sectionId, String gradeId, Long modifiedAt, String lastModifiedBy, String id);
	
	@Query(value = "SELECT COUNT(DISTINCT grade_id) FROM grade_section_mapping "
			+ "WHERE deleted=false AND active=true AND school_id=:schoolId AND branch_id=:branchId ", nativeQuery = true)
	long countGradeBySchoolAndBranch(String schoolId, String branchId);
	
	@Query(value = "SELECT DISTINCT grade_id as gradeId, section_id as sectionId FROM grade_section_mapping "
			+ "WHERE deleted=false AND active=true AND school_id=:schoolId AND branch_id=:branchId ", nativeQuery = true)
	List<TeacherAssignProjection> findAllActiveGradesBySchoolBranchIds(String schoolId, String branchId);
	
	@Query(value = "SELECT count(section_id) > 0 FROM grade_section_mapping WHERE deleted=false AND active=true "
			 + "AND school_id=:schoolId AND branch_id=:branchId AND grade_id=:gradeId "
			 //+ "group by(section_id) "
			 , nativeQuery = true)
	boolean existsBySectionByFilter(String schoolId, String branchId, String gradeId);
	
	@Query(value = "SELECT DISTINCT gsm.section_id FROM grade_section_mapping gsm "
			+ "where gsm.school_id=:schoolId AND gsm.branch_id=:branchId "
			+ "AND gsm.grade_id=:gradeId ", nativeQuery = true)
	List<String> findSectionByIds(String schoolId, String branchId, String gradeId);
	
	@Query("SELECT COUNT(gsm) FROM #{#entityName} gsm WHERE gsm.branches.id = :branchId AND gsm.deleted=false AND gsm.active = true")
	    long countByBranchesAndActiveTrue(String branchId);
	 
	@Query(value = "SELECT (SELECT COUNT(*) FROM students WHERE deleted=false AND active=true AND section_id=:sectionId "
		    + "AND grade_id=:gradeId AND school_id=:schoolId AND branch_id=:branchId ) + "		    
		    + "(SELECT COUNT(*) FROM assign_teacher at INNER JOIN teachers t ON(at.teacher_id = t.id) "
		    + "WHERE at.deleted=false AND at.active=true AND at.section_id=:sectionId AND at.grade_id=:gradeId "
		    + "AND t.school_id=:schoolId AND t.branch_id=:branchId) AS total_count ", nativeQuery = true)
	long findMappingForSection(String sectionId, String gradeId, String schoolId, String branchId);
	
	@Query(value = "SELECT DISTINCT grade_id FROM grade_section_mapping "
			+ "WHERE deleted=false AND active=true AND school_id=:schoolId AND branch_id=:branchId "
			+ "AND ((cast(:academicYearId as text) IS NULL) OR (academic_year_id=(cast(:academicYearId as text)))) ", nativeQuery = true)
	List<String> getAllActiveGrages(String schoolId, String branchId, String academicYearId);
	
	@Query(value = "SELECT DISTINCT section_id FROM grade_section_mapping "
			+ "WHERE deleted=false AND active=true AND grade_id IN(:gradeIds) "
			+ "AND ((cast(:academicYearId as text) IS NULL) OR (academic_year_id=(cast(:academicYearId as text)))) ", nativeQuery = true)
	List<String> getAllActiveSectionIds(List<String> gradeIds, String academicYearId);
	
	@Query(value = "SELECT DISTINCT gsm.grade_id as gradeId, gsm.section_id as sectionId FROM grade_section_mapping gsm "
			+ "INNER JOIN schools s ON (s.id=gsm.school_id AND s.deleted=false AND s.active=true) "
			+ "INNER JOIN branches b ON (b.id=gsm.branch_id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN branch_plan_mappings bp ON(bp.branch_id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE gsm.deleted=false AND gsm.active=true AND b.board_id=:boardId AND gsm.academic_year_id=:academicYearId "
			+ "AND gsm.school_id=:schoolId AND gsm.branch_id=:branchId "
			+ "AND ((:gradeId IS NULL) OR (gsm.grade_id=cast(:gradeId AS text))) ", nativeQuery = true)
	List<TeacherAssignProjection> findAllActiveGradesSections(String boardId, String schoolId, String branchId,
			String gradeId, String academicYearId);
	
	@Query(value = "SELECT DISTINCT gsm.grade_id FROM grade_section_mapping gsm "
			+ "INNER JOIN schools s ON (s.id=gsm.school_id AND s.deleted=false AND s.active=true) "
			+ "INNER JOIN branches b ON (b.id=gsm.branch_id AND b.deleted=false AND b.active=true) "
			+ "INNER JOIN branch_plan_mappings bp ON(bp.branch_id=b.id AND bp.deleted=false AND bp.active=true) "
			+ "WHERE gsm.deleted=false AND gsm.active=true AND b.board_id=:boardId AND gsm.academic_year_id=:academicYearId "
			+ "AND gsm.school_id=:schoolId AND gsm.branch_id=:branchId "
			+ "AND ((:gradeId IS NULL) OR (gsm.grade_id=cast(:gradeId AS text))) ", nativeQuery = true)
	List<String> findAllGradesSections(String boardId, String schoolId, String branchId, String gradeId,
			String academicYearId);
	
	boolean existsBySchoolsAndBranchesAndGradeIdAndSectionIdAndDeletedAndActive(Schools school, Branches branch,
			String gradeId, String sectionId, boolean deleted, boolean active);

	@Query("SELECT DISTINCT  gsm.sectionId "
			+ "FROM #{#entityName} gsm WHERE gsm.deleted=false AND gsm.active=true AND gsm.gradeId=:gradeId ")
	List<String> findSectionByGradeId(String gradeId);

	@Query(value =  "select update_section_ids(:oldSectionId, :newSectionId, :schoolId, :branchId, :gradeId, :section)", nativeQuery = true)
void executeUpdateSectionIdsFunction( String oldSectionId, String newSectionId,String schoolId,String branchId,String gradeId,String section);

}
