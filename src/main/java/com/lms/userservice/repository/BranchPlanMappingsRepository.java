package com.lms.userservice.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.userservice.entity.BranchPlanMappings;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.response.dto.BranchPlanResponseDto;

@Repository
public interface BranchPlanMappingsRepository extends JpaRepository<BranchPlanMappings, String> {

	BranchPlanMappings findByBranchesAndDeletedAndActive(Branches branch, boolean deleted, boolean active);

	List<BranchPlanMappings> findAllByBranchesAndDeleted(Branches branch, boolean deleted);

	@Modifying
	@Transactional
	void deleteByBranches(Branches branches);

	@Modifying
	@Transactional
	@Query("UPDATE #{#entityName} SET modifiedAt =:currentTime, lastModifiedBy =:currentUser, "
			+ "deleted = true,active = false WHERE planId=:planId ")
	void softDeleteByPlanId(String currentUser, Long currentTime, String planId);

	@Modifying
	@Transactional
	@Query("UPDATE #{#entityName} SET modifiedAt =:currentTime, lastModifiedBy =:currentUser, "
			+ "active =:active WHERE planId=:planId ")
	void updateActiveByPlanId(String currentUser, Long currentTime, String planId, boolean active);

	List<BranchPlanMappings> findByBranches(Branches branches);

	@Query("SELECT new com.lms.userservice.response.dto.BranchPlanResponseDto(bpm.id, bpm.planId, bpm.planName, bpm.active) "
			+ "FROM #{#entityName} bpm " + "INNER JOIN com.lms.userservice.entity.Branches b ON b.id=bpm.branches.id "
			+ "WHERE bpm.deleted=false AND bpm.active=true AND bpm.branches.id=:branchId")
	List<BranchPlanResponseDto> findPlansByBranchId(String branchId);

	boolean existsByPlanIdAndBranchesId(String planId, String branchId);

	@Query("SELECT DISTINCT new com.lms.userservice.response.dto.BranchPlanResponseDto(bpm.id, bpm.planId, bpm.planName, bpm.active) "
			+ "FROM #{#entityName} bpm " 
			+ "INNER JOIN com.lms.userservice.entity.Branches b ON b.id=bpm.branches.id "
			+ "WHERE bpm.deleted=false AND bpm.active=true AND bpm.branches.id=:branchId")
	BranchPlanResponseDto findPlanByBranchId(String branchId);

	@Query(value = "SELECT DISTINCT bpm.plan_id FROM branch_plan_mappings bpm "
			+ "WHERE bpm.deleted=false AND bpm.active=true AND bpm.branch_id=:branchId", nativeQuery = true)
	String findPlanIdByBranchId(String branchId);
}
