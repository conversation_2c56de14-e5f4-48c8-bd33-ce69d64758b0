package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.SchoolRequestDto;
import com.lms.userservice.response.dto.CitiesSchoolCountDetailsResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SchoolBranchDetailsResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;
import com.lms.userservice.response.dto.SchoolBranchesResponseDto;
import com.lms.userservice.response.dto.SchoolMinResponseDto;
import com.lms.userservice.response.dto.SchoolResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;
import com.lms.userservice.service.SchoolService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

/**
 * Processes an {@code SchoolController} request.
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/v1/api/user/schools")
public class SchoolController {

	private static final Logger log = LoggerFactory.getLogger(SchoolController.class);
	@Autowired
	private SchoolService schoolService;

	@PostMapping()
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to create a School", value = "Screen No. 13")
	public LMSResponse<SchoolResponseDto> createSchool(@Valid @RequestBody SchoolRequestDto request) {
		SchoolResponseDto response = schoolService.createSchool(request);
		return ResponseHelper.createResponse(new LMSResponse<SchoolResponseDto>(), response,
				Translator.toLocale("school.create.success", null), Translator.toLocale("school.create.failed", null));
	}

	@GetMapping("/{id}")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to Get a School by School ID", value = "Screen No. 15,16")
	public LMSResponse<SchoolResponseDto> getSchoolById(@PathVariable("id") String id) {
		SchoolResponseDto response = schoolService.getSchoolById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SchoolResponseDto>(), response,
				Translator.toLocale("school.get.by.id.success", null),
				Translator.toLocale("school.get.by.id.failed", null));
	}

	@GetMapping()
	@Lazy
	@SuppressWarnings("all")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/c8903710-6eb9-4b13-8b54-51a50feca973", value = "Pagination. Screen No. 10,12,13,17,41,42,46,48,51,53")
	public LMSResponse<Page<SchoolResponseDto>> getPageSchools(
			@RequestParam(value = "pageNumber", defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "order", required = false, defaultValue = "true") boolean order,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "boardId", required = false) List<String> boardId,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<SchoolResponseDto> response = schoolService.getPageSchools(pageNumber, pageSize, sortBy,
				order, search, schoolId, boardId, active);
		log.debug("Schools size in con: " +response.getData().size());
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<SchoolResponseDto>>(), response,
				Translator.toLocale("school.get.all.success", null),
				Translator.toLocale("school.get.all.failed", null));
	}

	@PutMapping("/{id}")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to update a School using School ID", value = "Screen No. 10,17")
	public LMSResponse<SchoolResponseDto> updateSchool(@PathVariable("id") String id,
			@Valid @RequestBody SchoolRequestDto request) {
		SchoolResponseDto response = schoolService.updateSchool(id, request);
		return ResponseHelper.createResponse(new LMSResponse<SchoolResponseDto>(), response,
				Translator.toLocale("school.update.success", null), Translator.toLocale("school.update.failed", null));
	}

	@DeleteMapping("/{id}")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to mark a School as deleted using School ID", value = "*Delete a School")
	public LMSResponse<Boolean> deleteSchoolById(@PathVariable("id") String id) {
		Boolean response = schoolService.deleteSchoolById(id);
		return ResponseHelper.createResponse(new LMSResponse<SchoolResponseDto>(), response,
				Translator.toLocale("school.delete.success", null), Translator.toLocale("school.delete.failed", null));
	}

	@GetMapping("/counts")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/c8903710-6eb9-4b13-8b54-51a50feca973. "
			+ "Count of <strong>Schools, teacher, principals, coordinators and students</strong> ", value = "Count from registered school's upper ribbon. Screen No. 12 ")
	public LMSResponse<UsersCountResponseDto> getAllCounts(
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "boardId", required = false) List<String> boardIds,
			@RequestParam(value = "administrator", required = false) boolean administrator,
			@RequestParam(value = "userName", required = false) String userName) {
		UsersCountResponseDto response = schoolService.getAllCounts(schoolId, boardIds, administrator, userName);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SchoolResponseDto>(), response,
				Translator.toLocale("counts.successful", null), Translator.toLocale("counts.failed", null));
	}

	@GetMapping("/all")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Add school admin dropdown", value = "Screen No. 46 ")
	public LMSResponse<SchoolMinResponseDto> getAllSchools(
			@RequestParam(value = "userId", required = false) String userId,
			@RequestParam(value = "search", required = false) String search) {
		List<SchoolMinResponseDto> response = schoolService.getAllSchools(userId, search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SchoolMinResponseDto>>(), response,
				Translator.toLocale("school.get.all.success", null),
				Translator.toLocale("school.get.all.failed", null));
	}

	@GetMapping("/all/withBranches")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Add Management Id school and branch name dropdown", value = "Screen No. 51 ")
	public LMSResponse<SchoolResponseDto> getAllSchoolsWithBranches(
			@RequestParam(value = "search", required = false) String search) {

		List<SchoolBranchesResponseDto> response = schoolService.getAllSchoolsWithBranches(search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SchoolResponseDto>>(), response,
				Translator.toLocale("school.get.all.success", null),
				Translator.toLocale("school.get.all.failed", null));
	}

	@GetMapping("/{id}/active")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to activate/deactive School using School ID", value = "Screen No. 10,12,13,17")
	public LMSResponse<Boolean> updateActive(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = schoolService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/check-mapping")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/c8903710-6eb9-4b13-8b54-51a50feca973", value = "Check the mapping exist before delete or toggling Active, Screen No. 12 ")
	public LMSResponse<String> checkTheMappingExistBeforeDeleteOrTogglingActive(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		String response = schoolService.checkTheMappingExistBeforeDeleteOrTogglingActive(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("request.handled.success", null),
				Translator.toLocale("something.went.wrong", null));
	}

	@GetMapping("/{cityId}/city-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> checkCityMappedData(@PathVariable("cityId") String cityId) {
		Boolean response = schoolService.getCountOfSchoolAndBranchByCityId(cityId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("school.branch.mapped.with.city.success", null),
				Translator.toLocale("school.branch.mapped.with.city.failed", null));
	}

	@GetMapping("/{boardId}/board-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> checkBoardMappedData(@PathVariable("boardId") String boardId) {
		Boolean response = schoolService.getCountOfSchoolAndBranchByBoardId(boardId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("school.branch.mapped.with.board.success", null),
				Translator.toLocale("school.branch.mapped.with.board.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = schoolService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the school", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> checkTheMappingForConcept(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = schoolService.confirmationApiForSchool(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@GetMapping("/school-branch")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This is Fiegn call to get schoolName and BranchName. This is hidden from FE.", value = "This is fiegn call ", hidden = true)
	public LMSResponse<SchoolBranchResponseDto> getSchoolAndBranchById(
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId) {
		SchoolBranchResponseDto response = schoolService.getSchoolAndBranchById(schoolId, branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SchoolBranchResponseDto>(), response,
				Translator.toLocale("school.branch.get.by.id.success", null),
				Translator.toLocale("school.branch.get.by.id.failed", null));
	}

	@GetMapping("/cities-school/details")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API is used for master dash board for getting school details", value = "Feign call for Master service", hidden = true)
	public LMSResponse<List<CitiesSchoolCountDetailsResponseDto>> getCitiesSchoolDetails(
			@RequestParam(value = "cityIds", required = true) List<String> cityIds) {
		List<CitiesSchoolCountDetailsResponseDto> response = schoolService.getCitiesSchoolDetails(cityIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<CitiesSchoolCountDetailsResponseDto>>(),
				response, Translator.toLocale("school.details.success", null),
				Translator.toLocale("school.details.failed", null));
	}

	@GetMapping("/over-view/schools-branches")
	@Lazy
	@SuppressWarnings("all")
	@ApiOperation(notes = "This API is used for management/super-admin, school-admin dashboard for getting school's branches details", value = "Management/Super-Admin/School-Admin Dashboard")
	public LMSResponse<PaginatedResponse<SchoolBranchDetailsResponseDto>> getAllSchoolDetailsWithUserName(
			@RequestParam(value = "pageNumber", defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "order", required = false, defaultValue = "true") boolean order,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<SchoolBranchDetailsResponseDto> response = schoolService
				.getAllSchoolDetailsWithUserName(pageNumber, pageSize, sortBy, order, search, schoolId, active);
		return ResponseHelper.responseForGetOrFeign(
				new LMSResponse<PaginatedResponse<SchoolBranchDetailsResponseDto>>(), response,
				Translator.toLocale("school.branch.overview.success", null),
				Translator.toLocale("school.branch.overview.failed", null));
	}

	@GetMapping("/assign/assessment")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "School/branch for assign assessment. This is hidden from FE.", value = "School/Branch for assign assessment", hidden = true)
	public LMSResponse<List<SchoolBranchResponseDto>> schoolAndBranchForAssignAssessment(
			@RequestParam("planIds") List<String> planIds) {
		List<SchoolBranchResponseDto> response = schoolService.schoolAndBranchForAssignAssessment(planIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SchoolBranchResponseDto>>(), response,
				Translator.toLocale("institutions.for.assign.assessments.success", null),
				Translator.toLocale("institutions.for.assign.assessments.failed", null));
	}

	@GetMapping("/details-for/assessment-paper")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "School/branch details for assessment-paper if assigned. This is hidden from FE.", value = "School/Branch for assessment-paper", hidden = true)
	public LMSResponse<List<SchoolBranchResponseDto>> assignedInstitutesDetails(
			@RequestParam("schoolIds") List<String> schoolIds, @RequestParam("branchIds") List<String> branchIds) {
		List<SchoolBranchResponseDto> response = schoolService.assignedInstitutesDetails(schoolIds, branchIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SchoolBranchResponseDto>>(), response,
				Translator.toLocale("institutions.for.assign.assessments.success", null),
				Translator.toLocale("institutions.for.assign.assessments.failed", null));
	}

}