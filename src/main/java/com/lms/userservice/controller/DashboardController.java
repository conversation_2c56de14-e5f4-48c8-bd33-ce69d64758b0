package com.lms.userservice.controller;

import java.util.List;

import com.lms.userservice.assignedTeacher.ChapterTrackingWithQuizResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.assignedTeacher.AssignedTeacherResponseDto;
import com.lms.userservice.assignedTeacher.ChapterTrackingResponseDto;
import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.response.dto.BlueprintLevelResponse;
import com.lms.userservice.response.dto.ChaptersVSQuizzesRelasesResponseDto;
import com.lms.userservice.response.dto.DashboardResponseDto;
import com.lms.userservice.response.dto.DashboardUnitQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.GradeAccessInfoStudentsDetailsResponseDto;
import com.lms.userservice.response.dto.GradeSubjectScoreResponseDto;
import com.lms.userservice.response.dto.PrincipalGradeDetailedPerformanceResponseDto;
import com.lms.userservice.response.dto.PrincipalGradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.StudentLevelResponseDto;
import com.lms.userservice.response.dto.SyllabusGradeAccessResponseDto;
import com.lms.userservice.response.dto.TeacherFormativeAssessmentResponseDto;
import com.lms.userservice.response.dto.TeacherGradeWiseQuizPerformanceResponseDto;
import com.lms.userservice.response.dto.TotalVsComplatedResponseDto;
import com.lms.userservice.service.DashboardService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/v1/api/user/dashboard")
public class DashboardController {

	@Autowired
	private DashboardService dashboardService;

	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for count of student,section,grade,teacher and coordinator."
			+ "<br> Use teacherId, schoolId, branchId, gradeId and subjectId is mandatory for TEACHER and COORDINATOR. "
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/76467f2f-7bd3-419e-bc7b-773c9f540a1a/ "
			+ "<br> https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/a82cdac6-34d1-4a71-8922-eaa3020773ee?fullscreen"
			+ "<br> For PRINCIPAL teacherId, schoolId and  branchId is mandatory. "
			+ "<br> https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/5a8bf4df-573e-4d6a-a674-0355478342e6 ", value = "This api is used for teacher/coordinator/principal dashboard", hidden = true)
	@GetMapping("/count-for-dashboard")
	public LMSResponse<DashboardResponseDto> countToDashboard(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = false) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId) {
		DashboardResponseDto response = dashboardService.countToDashboard(teacherId, schoolId, branchId, gradeId,
				subjectId, subtopicId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<DashboardResponseDto>(), response,
				Translator.toLocale("teacher.get.by.id.success", null), "teacher.get.by.id.failed");
	}

	@GetMapping("/count-for-chapter-quizzes")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for count of chapters and quizzes."
			+ "<br> https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/a82cdac6-34d1-4a71-8922-eaa3020773ee/ "
			+ "<br> Use teacherId, boardId, schoolId, branchId, gradeId and subjectId is mandatory for COORDINATOR ", value = "This api is used for coordinator dashboard", hidden = true)
	public LMSResponse<List<TotalVsComplatedResponseDto>> countForChapterQuizzes(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId) {
		List<TotalVsComplatedResponseDto> response = dashboardService.countForChapterQuizzes(teacherId, boardId,
				schoolId, branchId, gradeId, subjectId, subTopicId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<TotalVsComplatedResponseDto>>(), response,
				Translator.toLocale("fetch.teacher.chapter.quizzes.count.success", null),
				"fetch.teacher.chapter.quizzes.count.failed");
	}

	@GetMapping("/syllbus-percentage")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for percentage of total syllabus and completed syllabus."
			+ "<br> https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/5471e5bf-548a-431a-b631-b1a111d584ed/ "
			+ "<br> Use boardId, schoolId, branchId, gradeId and subjectId is mandatory for PRINCIPAL ", value = "This api is used for principal dashboard", hidden = true)
	public LMSResponse<List<SyllabusGradeAccessResponseDto>> percentageForSyllabus(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId) {
		List<SyllabusGradeAccessResponseDto> response = dashboardService.percentageForSyllabus(boardId, schoolId,
				branchId, gradeId, subjectId, subTopicId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SyllabusGradeAccessResponseDto>>(), response,
				Translator.toLocale("fetch.teacher.chapter.percentage.success", null),
				"fetch.teacher.chapter.percentage.failed");
	}

	@GetMapping("/student-level")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for student level."
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/76467f2f-7bd3-419e-bc7b-773c9f540a1a/ "
			+ "<br> https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/a82cdac6-34d1-4a71-8922-eaa3020773ee/ "
			+ "<br> Use teacherId, schoolId, branchId, gradeId and subjectId is mandatory ", value = "This api is used for teacher and coordinator dashboard", hidden = true)
	public LMSResponse<List<StudentLevelResponseDto>> getScoreRangeAndPercentageOfStudents(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId) {
		List<StudentLevelResponseDto> response = dashboardService.getScoreRangeAndPercentageOfStudents(teacherId,
				boardId, schoolId, branchId, gradeId, subjectId, subTopicId, academicYearId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentLevelResponseDto>>(), response,
				Translator.toLocale("fetch.teacher.student.level.success", null), "fetch.teacher.student.level.failed");
	}

	@GetMapping("/chapters-completed-vs-quizzes-releases")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for Chapters completed VS Quizzes Relaases."
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/76467f2f-7bd3-419e-bc7b-773c9f540a1a/ "
			+ "<br> Use boardId, schoolId, branchId, gradeId and subjectId is mandatory for TEACHER, COORDINATOR, PRINCIPAL ", value = "This api is used for Teacher dashboard", hidden = true)
	public LMSResponse<ChaptersVSQuizzesRelasesResponseDto> countForTotalChaptersReleasedQuizzes(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId) {
		ChaptersVSQuizzesRelasesResponseDto response = dashboardService.countForTotalChaptersReleasedQuizzes(teacherId,
				boardId, schoolId, branchId, gradeId, subjectId, subTopicId, academicYearId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ChaptersVSQuizzesRelasesResponseDto>(), response,
				Translator.toLocale("chapter.completed.quiz.release.success", null),
				"chapter.completed.quiz.release.failed");
	}

	@GetMapping("/unit-quiz-performance")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard unit quiz performance."
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/628de104-8a13-4210-b5a4-4e49760f2114 ", value = "Teacher dashboard unit quiz performance", hidden = true)
	public LMSResponse<DashboardUnitQuizPerformanceResponseDto> getUnitQuizPerformance(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "chapterId", required = true) String chapterId) {
		DashboardUnitQuizPerformanceResponseDto response = dashboardService.getUnitQuizPerformance(teacherId, schoolId,
				branchId, boardId, gradeId, subjectId, subTopicId, chapterId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<DashboardUnitQuizPerformanceResponseDto>(),
				response, Translator.toLocale("student.unit.quiz.performance.success", null),
				"student.unit.quiz.performance.failed");
	}

	@GetMapping("/formative-assessment")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard foarmative assessment."
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/76467f2f-7bd3-419e-bc7b-773c9f540a1a/ ", value = "Formative Assessment", hidden = true)
	public LMSResponse<List<TeacherFormativeAssessmentResponseDto>> getTeacherFormativeAssessment(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId,
			@RequestParam(value = "academicYearId", required = false) String academicYearId) {
		List<TeacherFormativeAssessmentResponseDto> response = dashboardService.getTeacherFormativeAssessment(teacherId,
				boardId, schoolId, branchId, gradeId, subjectId, subTopicId, academicYearId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<TeacherFormativeAssessmentResponseDto>>(),
				response, Translator.toLocale("teacher.formative.assessment.success", null),
				"teacher.formative.assessment.failed");
	}

	@GetMapping("/grade-wise-quiz-performance")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard Grade Wise Quiz Performance."
			+ "<br> https://www.figma.com/proto/dRhPC3oDEsG86yTqhyasEZ/Azvasa?page-id=212%3A510&type=design&node-id=364-698&viewport=822%2C375%2C0.61&t=D7BKKNsz0GJLlzSm-1&scaling=scale-down&starting-point-node-id=364%3A698", value = "Grade wise Quiz Performance")
	public LMSResponse<TeacherGradeWiseQuizPerformanceResponseDto> getTeacherGradeWiseQuizPerformance(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "gradeId", required = false) String gradeId) {
		TeacherGradeWiseQuizPerformanceResponseDto response = dashboardService
				.getTeacherGradeWiseQuizPerformance(teacherId, gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<TeacherGradeWiseQuizPerformanceResponseDto>(),
				response, Translator.toLocale("teacher.grade.wise.quiz.performance.success", null),
				"teacher.grade.wise.quiz.performance.failed");
	}

	@GetMapping("/grades-students/details")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher, co-ordinator, principal dashboard for getting grades and students details.", value = "Dashboard for Teacher, Co-ordinator, Principal")
	public LMSResponse<GradeAccessInfoStudentsDetailsResponseDto> getGradeAndStudentDetails(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "gradeId", required = false) String gradeId) {
		GradeAccessInfoStudentsDetailsResponseDto response = dashboardService.getGradeAndStudentDetails(teacherId,
				gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradeAccessInfoStudentsDetailsResponseDto>(),
				response, Translator.toLocale("fetch.principal.grades.students.counted.success", null),
				"fetch.principal.grades.students.counted.failed");
	}

	@GetMapping("/grade-wise-quiz-performance/principal")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for principal, co-ordinator dashboard for getting grade wise quiz performance.", value = "Dashboard for Principal, Co-ordinator")
	public LMSResponse<PrincipalGradeWiseQuizPerformanceResponseDto> getGradeWiseQuizPerformanceForPrincipal(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "gradeId", required = false) String gradeId) {
		PrincipalGradeWiseQuizPerformanceResponseDto response = dashboardService
				.getGradeWiseQuizPerformanceForPrincipal(teacherId, gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PrincipalGradeWiseQuizPerformanceResponseDto>(),
				response, Translator.toLocale("fetch.principal.grade.wise.performance.success", null),
				"fetch.principal.grade.wise.performance.failed");
	}

	@GetMapping("/grade-subject-wise-performance/principal")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for principal, co-ordinator dashboard for getting grade detailed or subject wise performance.", value = "Dashboard for Principal, Co-ordinator")
	public LMSResponse<PrincipalGradeDetailedPerformanceResponseDto> getGradeDetailedPerformanceForPrincipal(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId) {
		PrincipalGradeDetailedPerformanceResponseDto response = dashboardService
				.getGradeDetailedPerformanceForPrincipal(teacherId, gradeId, sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<PrincipalGradeDetailedPerformanceResponseDto>(),
				response, Translator.toLocale("fetch.principal.grade.detailed.performance.success", null),
				"fetch.principal.grade.detailed.performance.failed");
	}

	@GetMapping("/chapter-wise-quiz-performance")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard Chapter Wise Quiz Performance."
			+ "<br> https://www.figma.com/proto/dRhPC3oDEsG86yTqhyasEZ/Azvasa?page-id=212%3A510&type=design&node-id=364-698&viewport=822%2C375%2C0.61&t=D7BKKNsz0GJLlzSm-1&scaling=scale-down&starting-point-node-id=364%3A698", value = "Chapter wise Quiz Performance")
	public LMSResponse<GradeSubjectScoreResponseDto> getChapterWiseQuizPerformance(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId) {
		GradeSubjectScoreResponseDto response = dashboardService.getChapterWiseQuizPerformance(teacherId, gradeId,
				sectionId, subjectId, subTopicId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradeSubjectScoreResponseDto>(), response,
				Translator.toLocale("teacher.chapter.wise.quiz.performance.success", null),
				"teacher.chapter.wise.quiz.performance.failed");
	}

	@GetMapping("/global-student/count")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for principal dashboard subject Wise Quiz Performance.", value = "subject wise Quiz Performance", hidden = true)
	public LMSResponse<Long> getGlobalStudentCount(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId) {
		Long response = dashboardService.getGlobalStudentCount(boardId, gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Long>(), response,
				Translator.toLocale("teacher.chapter.wise.quiz.performance.success", null),
				"teacher.chapter.wise.quiz.performance.failed");
	}
	
	
	/**
	 * New Dashboard for teacher login showing purpose.
	 * @param teacherId
	 * @param gradeId
	 * @param sectionId
	 * @param subjectId
	 * @param subTopicId
	 * @return
	 */
	@GetMapping("/chapter-wise-quiz-performance-dashboard")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard Chapter Wise Quiz Performance."
			+ "<br> https://www.figma.com/proto/dRhPC3oDEsG86yTqhyasEZ/Azvasa?page-id=212%3A510&type=design&node-id=364-698&viewport=822%2C375%2C0.61&t=D7BKKNsz0GJLlzSm-1&scaling=scale-down&starting-point-node-id=364%3A698", value = "Chapter wise Quiz Performance")
	public LMSResponse<GradeSubjectScoreResponseDto> getChapterWiseQuizPerformanceDashboard(
			@RequestParam(value = "teacherId", required = false) String teacherId,
			@RequestParam(value = "gradeId", required = true) String gradeId,			
			@RequestParam(value = "subjectId", required = true) String subjectId) {
		GradeSubjectScoreResponseDto response = dashboardService.getChapterWiseQuizPerformanceDashboard(teacherId, gradeId,subjectId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradeSubjectScoreResponseDto>(), response,
				Translator.toLocale("teacher.chapter.wise.quiz.performance.success", null),
				"teacher.chapter.wise.quiz.performance.failed");
	}
	@GetMapping("/blueprint-level-details")
	@Lazy
	@SuppressWarnings("unchecked")
	public LMSResponse<List<BlueprintLevelResponse>> getBlueprintLevelDetailsBybranchId(
			@RequestParam(value = "branchId", required = true) String branchId) {
		List<BlueprintLevelResponse> response = dashboardService.getBlueprintLevelDetailsBybranchId(branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BlueprintLevelResponse>>(), response,
				Translator.toLocale("fetch.blueprint.level.details.success", null),
				"fetch.blueprint.level.details.failed");
	}
	
	
	@GetMapping("/chapter-wise-quiz-performance-dashboard-admin")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard Chapter Wise Quiz Performance."
			+ "<br> https://www.figma.com/proto/dRhPC3oDEsG86yTqhyasEZ/Azvasa?page-id=212%3A510&type=design&node-id=364-698&viewport=822%2C375%2C0.61&t=D7BKKNsz0GJLlzSm-1&scaling=scale-down&starting-point-node-id=364%3A698", value = "Chapter wise Quiz Performance")
	public LMSResponse<List<ChapterTrackingWithQuizResponseDto>> getChapterWiseQuizPerformanceDashboardAdmin(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,			
			@RequestParam(value = "subjectId", required = true) String subjectId) {
		List<ChapterTrackingWithQuizResponseDto> response = dashboardService.getChapterWiseQuizPerformanceDashboardAdmin(boardId,schoolId,branchId,gradeId,subjectId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<ChapterTrackingResponseDto>>(), response,
				Translator.toLocale("teacher.chapter.wise.quiz.performance.success", null),
				"teacher.chapter.wise.quiz.performance.failed");
	}
	
	
	
	@GetMapping("/chapter-wise-quiz-performance/without-teacherId")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard Chapter Wise Quiz Performance."
			+ "<br> https://www.figma.com/proto/dRhPC3oDEsG86yTqhyasEZ/Azvasa?page-id=212%3A510&type=design&node-id=364-698&viewport=822%2C375%2C0.61&t=D7BKKNsz0GJLlzSm-1&scaling=scale-down&starting-point-node-id=364%3A698", value = "Chapter wise Quiz Performance")
	public LMSResponse<List<GradeSubjectScoreResponseDto>> getChapterWiseQuizPerformance(			
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId) {
		List<GradeSubjectScoreResponseDto> response = dashboardService.getChapterWiseQuizPerformance(gradeId,
				sectionId, subjectId, subTopicId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradeSubjectScoreResponseDto>(), response,
				Translator.toLocale("teacher.chapter.wise.quiz.performance.success", null),
				"teacher.chapter.wise.quiz.performance.failed");
	}
	
	@GetMapping("/grade-wise-quiz-performance/without-teacherId")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is used for teacher dashboard Grade Wise Quiz Performance."
			+ "<br> https://www.figma.com/proto/dRhPC3oDEsG86yTqhyasEZ/Azvasa?page-id=212%3A510&type=design&node-id=364-698&viewport=822%2C375%2C0.61&t=D7BKKNsz0GJLlzSm-1&scaling=scale-down&starting-point-node-id=364%3A698", value = "Grade wise Quiz Performance")
	public LMSResponse<List<TeacherGradeWiseQuizPerformanceResponseDto>> getGradeWiseQuizPerformance(		
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId){
		List<TeacherGradeWiseQuizPerformanceResponseDto> response = dashboardService
				.getGradeWiseQuizPerformance(gradeId,subjectId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<TeacherGradeWiseQuizPerformanceResponseDto>>(),
				response, Translator.toLocale("teacher.grade.wise.quiz.performance.success", null),
				"teacher.grade.wise.quiz.performance.failed");
	}
}
