package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.TokensRequestDto;
import com.lms.userservice.response.dto.SchoolResponseDto;
import com.lms.userservice.response.dto.StudentsResponseDto;
import com.lms.userservice.response.dto.TokenDetailsResponseDto;
import com.lms.userservice.response.dto.TokenListResponseDto;
import com.lms.userservice.response.dto.TokensResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;
import com.lms.userservice.service.TokensService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/user/tokens")
public class TokensController {

	@Autowired
	private TokensService tokensService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to create a Token", value = "Screen No. 34")
	public LMSResponse<List<TokensResponseDto>> createTokens(@Valid @RequestBody TokensRequestDto request) {
		List<TokensResponseDto> response = tokensService.createTokens(request);
		return ResponseHelper.createResponse(new LMSResponse<List<TokensResponseDto>>(), response,
				Translator.toLocale("token.create.success", null), Translator.toLocale("token.create.failed", null));
	}

	@PutMapping("/self-registration/{token}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update a Token by Token ID", value = "*Update a Token")
	public LMSResponse<TokensResponseDto> updateTokenDuringRegistration(@PathVariable("token") String token,
			@RequestParam String userId) {
		TokensResponseDto response = tokensService.updateTokenDuringRegistration(token, userId);
		return ResponseHelper.createResponse(new LMSResponse<TokensResponseDto>(), response,
				Translator.toLocale("token.update.with.user.success", null),
				Translator.toLocale("token.update.with.user.failed", null));
	}

	@GetMapping("/{token}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Tokens by Token", value = "*Get Token Details by Token")
	public LMSResponse<TokensResponseDto> getByToken(@PathVariable String token) {
		TokensResponseDto response = tokensService.getTokensByToken(token);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<TokensResponseDto>(), response,
				Translator.toLocale("get.token.success", null), Translator.toLocale("get.token.failed", null));
	}

	@Lazy
	@GetMapping()
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to get all Tokens list", value = "Screen No. 34,35")
	public LMSResponse<Page<TokenListResponseDto>> getTokenByFilterSearchOrsorted(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "roleId", required = false) String roleId,
			@RequestParam(value = "userId", required = false) String userId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<TokenListResponseDto> response = tokensService.getTokenByFilterSearchOrsorted(pageNumber,
				pageSize, sortOrder, sortBy, roleId, userId, branchId, schoolId, search, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<TokenListResponseDto>>(), response,
				Translator.toLocale("get.all.token.success", null), Translator.toLocale("get.all.token.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to mark Token as deleted by Token ID", value = "*Delete a Token")
	public LMSResponse<Boolean> deleteStudent(@PathVariable("id") String id) {
		Boolean response = tokensService.deleteTokenById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("delete.token.successfully", null),
				Translator.toLocale("delete.token.failed", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/counts")
	@Lazy
	@ApiOperation(notes = "Used to retrieve token use counts", value = "Screen No. 35 ")
	public LMSResponse<UsersCountResponseDto> getAllCounts(
			@RequestParam(value = "tokenId", required = false) String tokenId,
			@RequestParam(value = "roleId", required = false) String roleId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "userId", required = false) String userId) {
		UsersCountResponseDto response = tokensService.getAllCounts(tokenId, roleId, schoolId, branchId, userId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SchoolResponseDto>(), response,
				Translator.toLocale("counts.successful", null), Translator.toLocale("counts.failed", null));
	}

	@GetMapping("/{id}/active")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to activate/deactivate token by Token ID", value = "Update active status of a Token")
	public LMSResponse<Boolean> updateActiveField(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = tokensService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/used-userId")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/11c29de4-347c-49f5-8e9f-22d508207436", value = "Userid dropdown Screen no: 34, 35")
	public LMSResponse<List<TokenDetailsResponseDto>> getUsedUserId(
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "tokenId", required = false) String tokenId) {
		List<TokenDetailsResponseDto> response = tokensService.getUsedUserId(search, tokenId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<TokenDetailsResponseDto>>(), response,
				Translator.toLocale("used.user.id.listed.success", null),
				Translator.toLocale("used.user.id.listed.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = tokensService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}
}
