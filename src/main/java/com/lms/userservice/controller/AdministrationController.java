package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AdministrationRequestDto;
import com.lms.userservice.response.dto.AdministrationResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.service.AdministrationService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/user/administrations")
public class AdministrationController {

	@Autowired
	private AdministrationService administrationService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used for adding a School Admin (for single School and single Branch) and Management ID (for single School and multiple Branches)", value = "Create then send email. Screen Nos. 46,51")
	public LMSResponse<List<AdministrationResponseDto>> createAdministrationAndSendEmail(
			@RequestBody @Valid AdministrationRequestDto request) {
		AdministrationResponseDto response = administrationService.createAdministrationAndSendEmail(request);
		return ResponseHelper.createResponse(new LMSResponse<List<AdministrationResponseDto>>(), response,
				Translator.toLocale("admin.create.success", null), Translator.toLocale("admin.create.failed", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping()
	@Lazy
	@ApiOperation(notes = "Used to get all School Admin (for single School and single Branch) and Management ID list (for single School and multiple Branches) and Pagination", value = "Pagination API. Screen Nos. 45,46,48,50,51,53")
	public LMSResponse<Page<AdministrationResponseDto>> getAdministrationByPagniation(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "roleId", required = false) String roleId,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "adminType", required = false, defaultValue = "SCHOOL_ADMIN") String adminType,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<AdministrationResponseDto> response = administrationService.getAdministrationByPagniation(
				pageNumber, pageSize, sortOrder, sortBy, schoolId, branchId, roleId, search, adminType, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<AdministrationResponseDto>>(), response,
				Translator.toLocale("admin.get.all.success", null), Translator.toLocale("admin.get.all.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get a School Admin (for single School and single Branch) or "
			+ "a Management ID (for single School and multiple Branches) using <strong>id</strong> or <strong>userName</strong>", value = "Get by either id or userName. Screen Nos. 49,54")
	public LMSResponse<AdministrationResponseDto> getAdministrationById(@PathVariable String id) {
		AdministrationResponseDto response = administrationService.getAdministrationById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<AdministrationResponseDto>(), response,
				Translator.toLocale("admin.get.by.id.success", null),
				Translator.toLocale("admin.get.by.id.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update a School Admin (for single School and single Branch) or a Management ID "
			+ "<br> (for single School and multiple Branches) by Administration ID ", value = "Update API. Screen No. 48,53")
	public LMSResponse<List<AdministrationResponseDto>> createUpdateAdministrationByIdAndSendEmail(@PathVariable String id,
			@RequestBody @Valid AdministrationRequestDto request) {
		AdministrationResponseDto response = administrationService.updateAdministrationById(id, request);
		return ResponseHelper.createResponse(new LMSResponse<List<AdministrationResponseDto>>(), response,
				Translator.toLocale("admin.update.success", null), Translator.toLocale("admin.update.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to delete a School Admin (for single School and single Branch) or a Management ID "
			+ "<br> (for single School and multiple Branches) by Administration ID", value = "Delete API. Screen Nos. 45,46,50")
	public LMSResponse<Boolean> deleteAdministration(@PathVariable("id") String id) {
		Boolean response = administrationService.deleteAdministration(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<AdministrationResponseDto>(), response,
				Translator.toLocale("admin.delete.success", null), Translator.toLocale("admin.delete.failed", null));
	}

	@GetMapping("/{id}/active")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to activate/deactivate a School Admin (for single School and single Branch) or a Management ID (for single School and multiple Branches) by Administration ID ", value = "Toggle active button. Screen No. 45,46,48,50,51,53")
	public LMSResponse<Boolean> updateActive(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = administrationService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Last updated time of SCHOOL_ADMIN or MANAGEMENT", value = "Last modified date and time")
	public LMSResponse<String> getLastModifiedAt(
			@RequestParam(value = "adminType", defaultValue = "SCHOOL_ADMIN") String adminType) {
		String response = administrationService.getLastModifiedAt(adminType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/{roleId}/role-mappings")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Feign call", value = "hiding from public view", hidden = true)
	public LMSResponse<List<NameCommonResponseDto>> getAllAdministrationDetailsByRoleMapping(
			@PathVariable("roleId") String roleId) {
		List<NameCommonResponseDto> response = administrationService.getAllAdministrationDetailsByRoleMapping(roleId);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("request.handled.success", null),
				Translator.toLocale("something.went.wrong", null));

	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the concept", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> checkTheMappingForConcept(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = administrationService.confirmationAPI(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}
}
