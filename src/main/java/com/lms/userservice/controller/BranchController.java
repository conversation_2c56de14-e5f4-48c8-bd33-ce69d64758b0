package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.BranchCommunicationRequestDto;
import com.lms.userservice.request.dto.BranchRequestDto;
import com.lms.userservice.response.dto.BranchCommunicationResponseDto;
import com.lms.userservice.response.dto.BranchMinResponseDto;
import com.lms.userservice.response.dto.BranchResponseDto;
import com.lms.userservice.response.dto.BranchesMinDataResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.SchoolBranchResponseDto;
import com.lms.userservice.response.dto.SchoolCountDetailsResponseDto;
import com.lms.userservice.response.dto.UsersCountResponseDto;
import com.lms.userservice.service.BranchService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/v1/api/user/branches")
public class BranchController {

	@Autowired
	private BranchService branchService;

	@GetMapping()
	@SuppressWarnings("all")
	@Lazy
	@ApiOperation(notes = "Used to get Branches List pagination", value = "Pagination for branch: Screen No. 15,16,18-36,41,42,46,48,51,53")
	public LMSResponse<Page<BranchResponseDto>> getAllSchoolBranches(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "cityId", required = false) String cityId,
			@RequestParam(value = "locality", required = false) String locality,
			@RequestParam(value = "boardId", required = false) String boardId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "userId", required = false) String userId,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<BranchResponseDto> response = branchService.getAllBranchesByPage(pageNumber, pageSize, sortBy,
				sortOrder, search, cityId, locality, boardId, branchId, schoolId, userId, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<BranchResponseDto>>(), response,
				Translator.toLocale("branch.get.all.success", null),
				Translator.toLocale("branch.get.all.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Branches by Branch ID", value = "Screen No. 16")
	public LMSResponse<BranchResponseDto> getSchoolBranchesById(@PathVariable("id") String id) {
		BranchResponseDto response = branchService.getSchoolBranchesById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<BranchResponseDto>(), response,
				Translator.toLocale("branch.get.by.id.success", null),
				Translator.toLocale("branch.get.by.id.failed", null));
	}

	@GetMapping("/all-by-ids")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<BranchResponseDto>> getAllBranchesByIds(@RequestParam("ids") List<String> ids) {
		List<BranchResponseDto> response = branchService.getAllBranchesByIds(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BranchResponseDto>>(), response,
				Translator.toLocale("branch.get.all.success", null),
				Translator.toLocale("branch.get.all.failed", null));
	}

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to add a Branch", value = "Screen No. 15")
	public LMSResponse<BranchResponseDto> addSchoolBranch(@RequestBody @Valid BranchRequestDto branchRequest) {
		BranchResponseDto response = branchService.addSchoolBranch(branchRequest);
		return ResponseHelper.createResponse(new LMSResponse<BranchResponseDto>(), response,
				Translator.toLocale("branch.create.success", null), Translator.toLocale("branch.create.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update a Branch", value = "Update branch (*Screen No. 16)")
	public LMSResponse<BranchResponseDto> updateSchoolBranch(@PathVariable("id") String id,
			@RequestBody @Valid BranchRequestDto branchRequest) {
		BranchResponseDto response = branchService.updateSchoolBranch(id, branchRequest);
		return ResponseHelper.createResponse(new LMSResponse<BranchResponseDto>(), response,
				Translator.toLocale("branch.update.success", null), Translator.toLocale("branch.update.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to mark Branch as deleted using Branch ID", value = "Delete Branch (*Screen No. 16)")
	public LMSResponse<Boolean> removeSchoolBranch(@PathVariable("id") String id) {
		Boolean response = branchService.removeSchoolBranch(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<BranchResponseDto>(), response,
				Translator.toLocale("branch.delete.success", null), Translator.toLocale("branch.delete.failed", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/all")
	@Lazy
	@ApiOperation(notes = "Add school admin dropdown", value = "Screen No. 46 ")
	public LMSResponse<BranchesMinDataResponseDto> getAllBranches(
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "userId", required = false) String userId) {
		List<BranchesMinDataResponseDto> response = branchService.getAllBranches(search, schoolId, userId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BranchesMinDataResponseDto>>(), response,
				Translator.toLocale("branch.get.all.success", null),
				Translator.toLocale("branch.get.all.failed", null));
	}

	@GetMapping("/{id}/active")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to activate/deactivate branch", value = "Screen No. 16")
	public LMSResponse<Boolean> updateActive(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = branchService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/max-quiz-release/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get maximum limit of a quiz's re-release at this branch.", value = "")
	public LMSResponse<Integer> getMaxQuizRelease(@PathVariable("id") String id) {
		Integer response = branchService.getMaxQuizRelease(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Integer>(), response,
				Translator.toLocale("branch.get.max-quiz-release.success", null),
				Translator.toLocale("branch.get.max-quiz-release.success", null));
	}

	@GetMapping("/{planId}/mappings")
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<List<BranchesMinDataResponseDto>> getAllBranchMappingsForPlan(
			@PathVariable("planId") String planId) {
		List<BranchesMinDataResponseDto> response = branchService.getAllBranchMappingsForPlan(planId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BranchesMinDataResponseDto>>(), response,
				Translator.toLocale("get.all.branches.mapped.with.plans.success", null),
				Translator.toLocale("get.all.branches.mapped.with.plans.failed", null));
	}

	@DeleteMapping("/{planId}/mappings")
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<Boolean> deleteAllBranchMappingsForPlan(@PathVariable("planId") String planId) {
		Boolean response = branchService.deleteAllBranchMappingsForPlan(planId);
		return ResponseHelper.createResponse(new LMSResponse<Boolean>(), response,
				Translator.toLocale("delete.all.branches.mapped.with.plans.success", null),
				Translator.toLocale("delete.all.branches.mapped.with.plans.failed", null));
	}

	@GetMapping("/{planId}/active/mappings")
	@SuppressWarnings("unchecked")
	@Lazy
	public LMSResponse<Boolean> updateActiveFieldOfAllBranchMappingsForPlan(@PathVariable("planId") String planId,
			@RequestParam(value = "active") boolean active) {
		Boolean response = branchService.updateActiveFieldOfAllBranchMappingsForPlan(planId, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.all.branches.mapped.with.plans.success", null),
				Translator.toLocale("update.all.branches.mapped.with.plans.failed", null));
	}

	@GetMapping("/check-mapping")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/be6e94f9-b609-4b40-9235-9551b0a0f1a0", value = "Check the mapping exist before delete or toggling Active, Screen No. 16")
	public LMSResponse<String> checkTheMappingExistBeforeDeleteOrTogglingActive(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		String response = branchService.checkTheMappingExistBeforeDeleteOrTogglingActive(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("request.handled.success", null),
				Translator.toLocale("something.went.wrong", null));
	}

	@GetMapping("/{planId}/plan-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> checkPlanHasAnyMapping(@PathVariable("planId") String planId) {
		boolean response = branchService.checkPlanHasAnyMapping(planId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = branchService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the branch", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> confirmationAPI(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = branchService.confirmationAPI(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}
	
	@GetMapping("/count-by/persona")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API is used for Coordinator, Principal, Teacher, Student Count's", value = "Count's API")
	public LMSResponse<UsersCountResponseDto> countByPersona(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId) {
		UsersCountResponseDto response = branchService.countByPersona(schoolId, branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("user.count.success", null),
				Translator.toLocale("user.count.failed", null));
	}
	
	@GetMapping("/{id}/get-by-id")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to get Branch by Branch ID", value = "Feign call for User service", hidden = true)
	public LMSResponse<BranchesMinDataResponseDto> getBranchesById(@PathVariable("id") String id) {
		BranchesMinDataResponseDto response = branchService.getBranchesById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<BranchesMinDataResponseDto>(), response,
				Translator.toLocale("branch.get.by.id.success", null),
				Translator.toLocale("branch.get.by.id.failed", null));
	}
	
	@GetMapping("/feign-school/details")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API is used for master dash board for getting school details", value = "Feign call for Master service", hidden = true)
	public LMSResponse<List<SchoolCountDetailsResponseDto>> getSchoolsDetails(
			@RequestParam(value = "boardIds", required = true) List<String> boardIds) {
		List<SchoolCountDetailsResponseDto> response = branchService.getSchoolsDetails(boardIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SchoolCountDetailsResponseDto>>(), response,
				Translator.toLocale("school.details.success", null),
				Translator.toLocale("school.details.failed", null));
	}
	
	@GetMapping("/top-branches/no-of-students")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API is used for get the Top 5 branches based on the no of students.", value = "Dashboard: Top 5 Branches")
	public LMSResponse<List<BranchMinResponseDto>> getTopBranchesBasedOnStudent() {
		List<BranchMinResponseDto> response = branchService.getTopBranchesBasedOnStudent();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BranchMinResponseDto>>(), response,
				Translator.toLocale("branch.top.5.retrieve.success", null),
				Translator.toLocale("branch.top.5.retrieve.failed", null));
	}
	
	@GetMapping("/plan")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API is used for get the plan by board, branch and school for student. This is feign call , hidding from FE.", value = "This is feign call")
	public LMSResponse<String> getPlanByBranchSchool(@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "schoolId", required = true) String schoolId) {
		String response = branchService.getPlanByBranchSchool(boardId, branchId, schoolId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("branch.get.all.plans.success", null),
				Translator.toLocale("branch.get.all.plans.failed", null));
	}
	
	@PostMapping("/sms/email-branch-communication")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to add a Branch Communication", value = "SMS or EMAIL Communication", hidden = true)
	public LMSResponse<List<BranchCommunicationResponseDto>> addBranchCommunication(
			@RequestBody @Valid BranchCommunicationRequestDto branchRequest) {
		List<BranchCommunicationResponseDto> response = branchService.addBranchCommunication(branchRequest);
		return ResponseHelper.createResponse(new LMSResponse<List<BranchCommunicationResponseDto>>(), response,
				Translator.toLocale("branch.communication.create.success", null),
				Translator.toLocale("branch.communication.create.failed", null));
	}
	
	@PutMapping("/update-branch-communication")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update a Branch Communication", value = "Update Branch SMS or EMAIL Communication", hidden = true)
	public LMSResponse<List<BranchCommunicationResponseDto>> editBranchCommunication(
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestBody @Valid BranchCommunicationRequestDto branchRequest) {
		List<BranchCommunicationResponseDto> response = branchService.editBranchCommunication(branchId, branchRequest);
		return ResponseHelper.createResponse(new LMSResponse<List<BranchCommunicationResponseDto>>(), response,
				Translator.toLocale("branch.update.success", null), Translator.toLocale("branch.update.failed", null));
	}
	
	@GetMapping("/school-branch/subjective-papers")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This is Fiegn call to get school and Branch details.", value = "Feign subjective-Papers", hidden = true)
	public LMSResponse<SchoolBranchResponseDto> schoolBranchMinDetails(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId) {
		SchoolBranchResponseDto response = branchService.schoolBranchMinDetails(schoolId, branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SchoolBranchResponseDto>(), response,
				Translator.toLocale("school.branches.details.success", null),
				Translator.toLocale("school.branches.details.failed", null));
	}
	
	@GetMapping("/getTestBranches")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This is Fiegn call to get test Branch details.", value = "Get test Branch details", hidden = false)
	public LMSResponse<List<String>> getTestBranchList() {
		List<String> response = branchService.getTestBranchList();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("fetch.test.branches.details.success", null),
				Translator.toLocale("fetch.test.branches.details.failed", null));
	}

}