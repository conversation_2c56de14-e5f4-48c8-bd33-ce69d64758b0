package com.lms.userservice.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Users;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.request.dto.AuthRequestDto;
import com.lms.userservice.request.dto.ValidateTokenRequestDto;
import com.lms.userservice.response.dto.AuthResponseDto;
import com.lms.userservice.response.dto.UsersResponseDto;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

/**
 * Processes an {@link AuthController} request.
 * <p>
 * 1. {@code generateToken} to login & authenticate.
 * <p>
 * 2. {@code refreshtoken} to refresh the authentication token thus extend the
 * login time.
 * <p>
 * 3. {@code tokenValidation} to validate the token, check the token is valid &
 * expire time is exceed etc
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api/user/authenticate")
public class AuthController {

	@Autowired
	private UserService userService;

	/**
	 * Processes an {@link generate JWT Token} request.
	 * 
	 * @RequestBody AuthRequestDto
	 * @return Generated JWT Token
	 * 
	 */
	@SuppressWarnings("unchecked")
	@PostMapping()
	@Lazy
	@ApiOperation(notes = "Used to generate a Token", value = "*Token generation")
	public LMSResponse<AuthResponseDto> generateToken(@Valid @RequestBody AuthRequestDto authRequest,
			HttpServletRequest request) {
		AuthResponseDto authResponse = userService.authentication(authRequest);
		return ResponseHelper.createResponse(new LMSResponse<AuthResponseDto>(), authResponse,
				Translator.toLocale("user.authenticated.scucessfully", null),
				Translator.toLocale("user.authentication.failed", null));

	}

	/**
	 * Processes an {@link generate JWT Refresh Token} request.
	 * 
	 * @RequestBody AuthRequestDto
	 * @return Generated JWT Refresh Token
	 * 
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/extendtoken")
	@Lazy
	@ApiOperation(notes = "Used to extend an expired token with valid max_idle_timeout", value = "*Extend a Token")
	public LMSResponse<AuthResponseDto> refreshtoken(HttpServletRequest request) {
		AuthResponseDto authResponse = userService.authenticationByRefreshToken(request);
		return ResponseHelper.createResponse(new LMSResponse<AuthResponseDto>(), authResponse,
				Translator.toLocale("user.authenticated.scucessfully", null),
				Translator.toLocale("user.authentication.failed", null));
	}

	/**
	 * Processes an {@link Validate JWT Token} request.
	 * 
	 * @RequestBody AuthRequestDto
	 * @return Generated JWT Token
	 * 
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/validatetoken")
	@Lazy
	@ApiOperation(notes = "Used to validate a Token", value = "*Token validation")
	public LMSResponse<UsersResponseDto> tokenValidation(@Valid @RequestBody ValidateTokenRequestDto request) {
		UsersResponseDto response = userService.tokenValidation(request);
		return ResponseHelper.createResponse(new LMSResponse<Users>(), response,
				Translator.toLocale("user.authenticated.scucessfully", null),
				Translator.toLocale("user.authentication.failed", null));
	}

}
