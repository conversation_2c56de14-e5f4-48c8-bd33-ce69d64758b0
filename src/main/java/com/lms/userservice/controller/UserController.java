package com.lms.userservice.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

import com.lms.userservice.entity.Teachers;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.repository.StudentsRepository;
import com.lms.userservice.repository.TeacherRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.response.dto.*;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.EncryptionAndDecryption;
import lombok.Data;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.ChangePasswordRequestDto;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.EmailRequestDto;
import com.lms.userservice.request.dto.ForgetPasswordNumberOtpRequestDto;
import com.lms.userservice.request.dto.ForgetPasswordOtpRequestDto;
import com.lms.userservice.request.dto.ResetPasswordWithoutTokenRequestDto;
import com.lms.userservice.request.dto.ShareDetailsRequestDto;
import com.lms.userservice.request.dto.UpdateMobileNumberRequestDto;
import com.lms.userservice.request.dto.UsersRequestDto;
import com.lms.userservice.request.dto.UsersResetPasswordDto;
import com.lms.userservice.request.dto.VerifyOtpRequestDto;
import com.lms.userservice.service.UserService;
import com.lms.userservice.util.Constants;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

/**
 * Processes an {@link UserController} request.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api/user/users")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);
    @Autowired
    private UserService userService;


    @Autowired
    TeacherRepository teacherRepository;


    @Autowired
    StudentsRepository studentRepository;

    @Autowired
    MastersFeignClient mastersFeignClient;



    @Lazy
    @PostMapping()
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used to create a User", value = "*Create a User")
    public LMSResponse<UsersResponseDto> createUser(@Valid @RequestBody UsersRequestDto request) {
        UsersResponseDto response = userService.createuser(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.created.successfully", null),
                Translator.toLocale("user.create.failed", null));
    }

    @Lazy
    @PutMapping("/{id}")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used to update a User by User ID", value = "*Update a User")
    public LMSResponse<UsersResponseDto> updateUser(@PathVariable("id") String id,
                                                    @Valid @RequestBody UsersRequestDto request) {
        UsersResponseDto response = userService.updateUser(id, request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.updated.successfully", null),
                Translator.toLocale("user.update.failed", null));
    }

    @Lazy
    @GetMapping("/{id}")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used to get a User by User's id", value = "*Get User details by User's id")
    public LMSResponse<UsersResponseDto> getUserById(@PathVariable("id") String id) {
        UsersResponseDto response = userService.getuserById(id);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.get.by.id.success", null),
                Translator.toLocale("user.get.by.id.failed", null));
    }

    /**
     * Feign call for the other services
     *
     * @param username
     * @return
     */
    @Lazy
    @GetMapping("/username/{username}")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used get User by userName", value = "Get User by Username")
    public LMSResponse<UsersFeignDto> getUsersByUsernameForFeign(@PathVariable("username") String username) {
        UsersFeignDto response = userService.getUsersByUsernameForFeign(username);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<UsersFeignDto>(), response,
                Translator.toLocale("user.get.by.id.success", null),
                Translator.toLocale("user.get.by.id.failed", null));
    }
    
    @Lazy
    @GetMapping("/userDetails/{username}")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used get User by userName", value = "Get User by Username")
    public LMSResponse<UsersResponseDto> getUserDetailsForFeign(@PathVariable("username") String username) {
    	UsersResponseDto response = userService.getUserDetailsForFeign(username);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.get.by.id.success", null),
                Translator.toLocale("user.get.by.id.failed", null));
    }

    @Lazy
    @GetMapping()
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "List Users by Pagination", value = "List Users by Pagination")
    public LMSResponse<Page<UsersResponseDto>> getUsersByPagniation(
            @RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
            @RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
            @RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
            @RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
            @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "roleId", required = false) String roleId,
            @RequestParam(value = "active", required = false) Boolean active) {
        PaginatedResponse<UsersResponseDto> response = userService.getUsersByPagniation(pageNumber, pageSize, sortOrder,
                sortBy, search, roleId, active);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<UsersResponseDto>>(), response,
                Translator.toLocale("user.get.all.success", null), Translator.toLocale("user.get.all.failed", null));
    }

    /**
     * Processes an {resetPassword} request. This API required authorisation token.
     * Call this API after the login.
     *
     * @param id
     * @return Password Reset Success or Failure
     * @RequestBody UsersResetPasswordDto
     */
    @Lazy
    @PutMapping("/resetpassword/{id}")
    @SuppressWarnings({"unchecked", "all"})
    @ApiOperation(notes = "Used to reset a Password", value = "Reset password after the login. Screen No. 5")
    public LMSResponse<UsersResponseDto> resetPassword(@PathVariable("id") String id,
                                                       @Valid @RequestBody UsersResetPasswordDto request) {
        UsersResponseDto response = userService.resetPassword(id, request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("password.reset.scucessfully", null),
                Translator.toLocale("password.reset.failed", null));
    }

    @Lazy
    @GetMapping("/check-mapping")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/c8903710-6eb9-4b13-8b54-51a50feca973", value = "Check the mapping exist before delete or toggling Active, Screen No. 9")
    public LMSResponse<String> checkTheMappingExistBeforeDeleteOrTogglingActive(@RequestParam(value = "id") String id,
                                                                                @RequestParam(value = "operationType") String operationType) {
        String response = userService.checkTheMappingExistBeforeDeleteOrTogglingActive(id, operationType);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("request.handled.success", null),
                Translator.toLocale("something.went.wrong", null));
    }

    /**
     * Processes forgot password by email.
     * <p>
     * This API no need of authorisation token. Used for resetting a forgotten
     * password through email
     *
     * @param email Email Service will send reset Password Link to given email
     * @return Email Send Success or Failure
     */
    @Lazy
    @GetMapping("/forgot-password")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/105ee274-17bc-4425-8908-208c5a625ae0", value = "Forgot Password(email), before the login. No need of token. Screen No. 2")
    public LMSResponse<EmailRequestDto> createForgotPasswordAndSendEmail(
            @RequestParam("email") @NotBlank(message = Constants.MANDATORY_FIELD) String email,
            @RequestParam("lmsEnv") @NotBlank(message = Constants.MANDATORY_FIELD) String lmsEnv,
            @RequestParam(value = "userName", required = true) String userName, HttpServletRequest request) {
        EmailRequestDto response = userService.forgotPassword(email, lmsEnv, userName);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<EmailRequestDto>(), response,
                Translator.toLocale("email.sent.successfully", null), Translator.toLocale("email.sent.failed", null));
    }

    /**
     * API no need of authorisation token. Call this API followed by the API
     * /forgot-password. Take the password and encrypted userId from the request
     *
     * @param request
     * @return
     */
    @Lazy
    @PutMapping("/reset-password")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/6e2c55f6-9a53-46e3-b4b7-100f675523af", value = "Setup New Password, followd by the Forgot Password(email). No need of token. Screen No. 5")
    public LMSResponse<UsersResponseDto> withoutTokenResetPassword(
            @Valid @RequestBody ResetPasswordWithoutTokenRequestDto request) {
        UsersResponseDto response = userService.withoutTokenResetPassword(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("password.reset.scucessfully", null),
                Translator.toLocale("password.reset.failed", null));
    }

    /**
     * By MOBILE API no need of authorisation token. Call this API if the forgot
     * password functionality is execute by the mobile number.
     *
     * @param phoneNumber
     * @param request
     * @return
     */
    @Lazy
    @GetMapping("/forgotpasswordMobile")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/c4aa3969-52f1-4463-8a6c-91f7149c014c", value = "Forgot Password(mobile), before login. Screen No. 3")
    public LMSResponse<String> forgotPasswordMobile(@RequestParam("phoneNumber") String phoneNumber,
                                                    @RequestParam(value = "userName", required = true) String userName, HttpServletRequest request) {
        String response = userService.forgotPasswordMobile(phoneNumber, userName);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.send.succes", null), Translator.toLocale("user.otp.send.failed", null));
    }

    /**
     * By MOBILE This API no need of the authorisation token. Call this API followed
     * by the API /forgotpasswordMobile Take the phoneNumnber and received OTP
     *
     * @param request
     * @return
     */
    @Lazy
    @PutMapping("/verify-otp")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/a2cc7222-817b-4b8a-b923-a17e1aef770d", value = "Enter your OTP. Screen No: 4")
    public LMSResponse<UsersResponseDto> verifyOtp(@Valid @RequestBody VerifyOtpRequestDto request) {
        UsersResponseDto response = userService.verifyOtp(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.otp.verified.success", null),
                Translator.toLocale("user.otp.verified.failed", null));
    }

    /**
     * By MOBILE This API no need of the authorisation. Call this API followed by
     * /verify-otp Take the userId (from response of above API) and the password
     *
     * @param request
     * @return
     */
    @Lazy
    @PutMapping("/reset-password-mobile")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/6e2c55f6-9a53-46e3-b4b7-100f675523af", value = "Setup New Password :Screen No. 5")
    public LMSResponse<UsersResponseDto> mobileResetPasswordWithoutToken(
            @Valid @RequestBody ResetPasswordWithoutTokenRequestDto request) {
        UsersResponseDto response = userService.mobileResetPasswordWithoutToken(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("password.reset.scucessfully", null),
                Translator.toLocale("password.reset.failed", null));
    }

    @GetMapping("/last-modified-at")
    @SuppressWarnings("unchecked")
    public LMSResponse<String> getLastModifiedAt() {
        String response = userService.getLastModifiedAt();
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("last.modified.time.fetch.success", null),
                Translator.toLocale("last.modified.time.fetch.failed", null));
    }

    @GetMapping("/role-mapping/{roleId}")
    @SuppressWarnings("unchecked")
    public LMSResponse<List<UserMinResponseDto>> getAllUserResponsesForRole(@PathVariable("roleId") String roleId) {
        List<UserMinResponseDto> response = userService.getAllUsersMappedToRole(roleId);
        return ResponseHelper.createResponse(new LMSResponse<UserMinResponseDto>(), response,
                Translator.toLocale("request.handled.success", null),
                Translator.toLocale("something.went.wrong", null));

    }

    /**
     * @param username
     * @param roles
     * @return
     */
    @GetMapping("/profile")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used get User by UserName and Role", value = "Get User by UserName and Role")
    public LMSResponse<ProfileResponseDto> getUsersByUsername(
            @RequestParam(value = "username", required = true) String username,
            @RequestParam(value = "roles", required = true) List<String> roles) {
        ProfileResponseDto response = userService.getUsersByUserName(username, roles);
        return ResponseHelper.createResponse(new LMSResponse<ProfileResponseDto>(), response,
                Translator.toLocale("user.get.by.username.and.role.success", null),
                Translator.toLocale("user.get.by.username.and.role.failed", null));
    }

    @Lazy
    @GetMapping("/count-role-id")
    @SuppressWarnings("unchecked")
    @ApiOperation(value = "Feign call to find the mapping of role", hidden = true)
    public LMSResponse<Long> countRoleIdForDeletion(@RequestParam("roleId") String roleId) {
        Long response = userService.countRoleIdForDeletion(roleId);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<Long>(), response,
                Translator.toLocale("role.id.count.success", null), Translator.toLocale("role.id.count.failed", null));
    }

    @GetMapping("/ids-by-role-id")
    @SuppressWarnings("unchecked")
    @ApiOperation(value = "", hidden = true)
    public LMSResponse<List<String>> getUserIdsByRoleId(@RequestParam("roleId") String roleId) {
        List<String> response = userService.getUserIdsByRoleId(roleId);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
                Translator.toLocale("user.ids.fetch.success", null),
                Translator.toLocale("user.ids.fetch.failed", null));
    }

    @Lazy
    @PostMapping("/share-details")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "To share the user-details, only mail integrated", value = "Share user details")
    public LMSResponse<ShareDetailsResponseDto> createShareDetailsAndSendEmail(
            @Valid @RequestBody ShareDetailsRequestDto request) {
        ShareDetailsResponseDto response = userService.shareUserDetails(request);
        return ResponseHelper.createResponse(new LMSResponse<ShareDetailsResponseDto>(), response,
                Translator.toLocale("user.details.share.success", null),
                Translator.toLocale("user.details.share.failed", null));
    }

    @Lazy
    @PutMapping("/update-password")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Update password for all screens <strong> Admin Users, Academic Staff, School Admin, Add Management and Students </strong>", value = "Update password, Column action: lock icon.")
    public LMSResponse<CreateUserEmailRequestDto> createPasswordAndSendEmail(
            @Valid @RequestBody ChangePasswordRequestDto request) {
        CreateUserEmailRequestDto response = userService.changePasswordBySuperAdmin(request);
        return ResponseHelper.createResponse(new LMSResponse<CreateUserEmailRequestDto>(), response,
                Translator.toLocale("password.updated.successfully", null),
                Translator.toLocale("password.update.failed", null));
    }

    @PutMapping("/change-mobile-number/generate-otp")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used to generate the otp to update the principal, teacher, student, and cordinator's mobile number.. "
            + "<br> https://xd.adobe.com/view/ef2e5185-36a9-480b-b2df-ef26df3e4d77-ba7d/screen/fe98e513-9d3c-4628-afd5-8e5ec0d5603c/ "
            + "https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/f6708398-b5a9-4b5b-847f-81672b15fb7c/ "
            + "https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/6460d3fc-0b4a-4af8-a301-ec3fe85e8be7 "
            + "https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/9d16b587-ea89-4042-a3ec-be02613876d1 ", value = "Generate the otp to update mobile number..")
    public LMSResponse<String> generateOtp(@Valid @RequestBody UpdateMobileNumberRequestDto request) {
        String response = userService.generateOtp(request);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.send.succes", null), Translator.toLocale("user.otp.send.failed", null));
    }

    @Lazy
    @PutMapping("/change-mobile-number/verify-otp")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used to verify the otp and Update the principal, teacher, student, and cordinator's mobile number.. "
            + "<br> https://xd.adobe.com/view/ef2e5185-36a9-480b-b2df-ef26df3e4d77-ba7d/screen/ed9264d2-41f5-42c9-aba6-583020a21ecf/ "
            + "https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/41baa682-3e9f-44cf-a3d0-95b6fc9e7981/ "
            + "https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/43fd85c4-9baa-4618-815f-5b0cce787e0d "
            + "https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/a0f10efa-019b-46d4-94a0-8308ff288f1e/ ", value = "Verify otp and Update mobile number.. ")
    public LMSResponse<UsersResponseDto> verifyOtpToChangeMobileNumber(
            @Valid @RequestBody UpdateMobileNumberRequestDto request) {
        UsersResponseDto response = userService.verifyOtpToChangeMobileNumber(request);
        return ResponseHelper.createResponse(new LMSResponse<UsersResponseDto>(), response,
                Translator.toLocale("user.mobileNumber.update.success", null),
                Translator.toLocale("user.mobileNumber.update.failed", null));
    }

    @GetMapping("/extracting-user-name")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Extract the user_name which is created based on either phone_number or email. "
            + "<br>The param <strong>extractor</strong> can hold either phone number or email", value = "Extracting username for change password")
    public LMSResponse<List<String>> getUserNameExtractingList(
            @RequestParam(value = "extractor", required = true) String extractor) {
        List<String> response = userService.getUserNameExtractingList(extractor);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
                Translator.toLocale("user.details.fetch.success", null),
                Translator.toLocale("user.details.fetch.failed", null));
    }

    @GetMapping("/user-details/by-role")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "This is a feign call to get the users details ralated to role. ", value = "This is a feign call", hidden = true)
    public LMSResponse<List<UsersRoleResponseDto>> getAllAdminUsersRole(
            @RequestParam(value = "roleId", required = false) List<String> roleIds) {
        List<UsersRoleResponseDto> response = userService.getAllAdminUsersRole(roleIds);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UsersRoleResponseDto>>(), response,
                Translator.toLocale("user.details.fetch.success", null),
                Translator.toLocale("user.details.fetch.failed", null));
    }

    @Lazy
    @GetMapping("/users-count")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Used get the total count of users", value = "Total count Web, Mob and Both WebAndMob user")
    public LMSResponse<List<UsersUseWebMobCountResponseDto>> getUserWebMobCount() {
        List<UsersUseWebMobCountResponseDto> response = userService.getUserWebMobCount();
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UsersUseWebMobCountResponseDto>>(), response,
                Translator.toLocale("user.check.in.history.total.count.success", null),
                Translator.toLocale("user.check.in.history.total.count.failed", null));
    }

    @Lazy
    @GetMapping("/name-by-username")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Get the name of users by username, feign to content-service.", value = "Feign hiding from FE", hidden = true)
    public LMSResponse<List<UserMinResponseDto>> getAllUsersByUserNames(
            @RequestParam("userNames") List<String> userNames) {
        List<UserMinResponseDto> response = userService.getAllUsersByUserNames(userNames);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UserMinResponseDto>>(), response,
                Translator.toLocale("user.name.success", null),
                Translator.toLocale("user.name.failed", null));
    }

    @Lazy
    @PutMapping("/forgot-password/generate-otp")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "User forget-password send otp.", value = "Generate OTP for forget password")
    public LMSResponse<String> userSendOtp(
    		 @Valid @RequestBody ForgetPasswordNumberOtpRequestDto request) {
        String response = userService.userSendOtp(request);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.send.succes", null),
                Translator.toLocale("user.otp.send.failed", null));
    }
    
    @Lazy
    @PutMapping("/forgot-password/verify-otp")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "User forget-password verify otp ", value = "Verify OTP for forget password")
    public LMSResponse<String> userVerifyOtp(
    		@Valid @RequestBody ForgetPasswordOtpRequestDto request) {
        String response = userService.userVerifyOtp(request);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
                Translator.toLocale("user.otp.verified.success", null),
                Translator.toLocale("user.otp.verified.failed", null));
    }
    
    @GetMapping("/extracting-user-name-with-user-type")
    @SuppressWarnings("unchecked")
    @ApiOperation(notes = "Extract the user_name which is created based on either phone_number or email and user type. "
            + "<br>The param <strong>extractor</strong> can hold either phone number or email", value = "Extracting username for change password")
    public LMSResponse<List<String>> getUserNameExtractingListWithUserType(
            @RequestParam(value = "extractor", required = true) String extractor,
            @RequestParam(value = "userType", required = true) String userType) {
        List<String> response = userService.getUserNameExtractingListWithUserType(extractor,userType);
        return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
                Translator.toLocale("user.details.fetch.success", null),
                Translator.toLocale("user.details.fetch.failed", null));
    }
}
