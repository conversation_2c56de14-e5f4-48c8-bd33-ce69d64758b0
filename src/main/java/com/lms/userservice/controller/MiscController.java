package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.request.dto.QuizReleaseCheckingRequestDto;
import com.lms.userservice.response.dto.EnumsResponseDto;
import com.lms.userservice.response.dto.SchoolBranchTeacherNameResponseDto;
import com.lms.userservice.service.MiscService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/user/misc")
public class MiscController {

	@Autowired
	private MiscService miscService;

	@Lazy
	@GetMapping("/coordinator-type")
	@SuppressWarnings({ "unchecked", "all" })
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/258ffc1c-bf60-47f7-9ddf-0e48e3af0560", value = "Coordinator type, radio buttons. Screen no: 24")
	public LMSResponse<List<EnumsResponseDto>> listOfCoordinatorType() {
		List<EnumsResponseDto> response = miscService.listOfCoordinatorType();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("enum.listing.success", null), Translator.toLocale("enum.listing.failed", null));
	}

	@Lazy
	@GetMapping("/gender")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Wherever gender is going to use", value = "list of gender")
	public LMSResponse<List<EnumsResponseDto>> listOfGender() {
		List<EnumsResponseDto> response = miscService.listOfGender();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("enum.listing.success", null), Translator.toLocale("enum.listing.failed", null));
	}

	@Lazy
	@GetMapping("/operation-type")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Wherever the confirmartion API use", value = "list of delete/toggle type operation")
	public LMSResponse<List<EnumsResponseDto>> listOfOperationType() {
		List<EnumsResponseDto> response = miscService.listOfOperationType();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("enum.listing.success", null), Translator.toLocale("enum.listing.failed", null));
	}

	@Lazy
	@GetMapping("/app-env")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Application Environment", value = "List of server environment type.")
	public LMSResponse<List<EnumsResponseDto>> listOfApplicationEnvironment() {
		List<EnumsResponseDto> response = miscService.listOfApplicationEnvironment();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("enum.listing.success", null), Translator.toLocale("enum.listing.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/checking/grade_section_mapping/{academicYearId}")
	@ApiOperation(notes = "Find the mapping of academicy year id. This API will be act as a feign call.", value = "Hiding from Front-End", hidden = true)
	public LMSResponse<Boolean> checkTheGradeSectionMappingForAcademicYearId(
			@PathVariable("academicYearId") String academicYearId) {
		boolean response = miscService.checkTheGradeSectionMappingForAcademicYearId(academicYearId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/checking/relationship/for/teach-status")
	@ApiOperation(notes = "This is a feign call from teacher-service, which used to call before the teach-status APIs", value = "Hiding from Front-End", hidden = true)
	public LMSResponse<Boolean> fiegnCallFromTeacherServiceForTeachStatus(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subTopicId", required = false) String subTopicId) {
		boolean response = miscService.fiegnCallFromTeacherServiceForTeachStatus(schoolId, branchId, teacherId, gradeId,
				sectionId, subjectId, subTopicId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response,
				Translator.toLocale("checking.for.teach.status.success", null),
				Translator.toLocale("checking.for.teach.status.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/names/for/teach-status")
	@ApiOperation(notes = "This is a feign call from teacher-service, which used to get names of PK Id in teach-status", value = "Hiding from Front-End", hidden = true)
	public LMSResponse<SchoolBranchTeacherNameResponseDto> getSchoolBranchAndTeachersName(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "startedById", required = true) String startedById,
			@RequestParam(value = "endedById", required = false) String endedById) {
		SchoolBranchTeacherNameResponseDto response = miscService.getSchoolBranchAndTeachersName(schoolId, branchId,
				startedById, endedById);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<SchoolBranchTeacherNameResponseDto>(), response,
				Translator.toLocale("names.extracted.successfully", null),
				Translator.toLocale("names.extract.failed", null));
	}

	@Lazy
	@GetMapping("/school-menus")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Menu name for Academic staffs and students", value = "List of menu name for Academic staffs/students")
	public LMSResponse<List<EnumsResponseDto>> listOfSchoolMenus() {
		List<EnumsResponseDto> response = miscService.listOfSchoolMenus();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<EnumsResponseDto>>(), response,
				Translator.toLocale("enum.listing.success", null), Translator.toLocale("enum.listing.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@PutMapping("/checking/relationship/for/quiz-release")
	@ApiOperation(notes = "This is a feign call from content-service, which used to call before the quiz release or re-release.", value = "Hiding from Front-End", hidden = true)
	public LMSResponse<Boolean> checkingAccessProvideBeforeQuizRelease(@Valid @RequestBody QuizReleaseCheckingRequestDto request) {
		boolean response = miscService.checkingAccessProvideBeforeQuizRelease(request);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response,
				Translator.toLocale("checking.for.quiz.release.success", null),
				Translator.toLocale("checking.for.quiz.release.failed", null));
	}
}
