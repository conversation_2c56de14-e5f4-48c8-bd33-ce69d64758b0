package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.request.dto.GradeSectionMapRequestDto;
import com.lms.userservice.request.dto.GradeSectionPutRequestDto;
import com.lms.userservice.request.dto.ToggleGradeSectionRequestDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.GradeSectionGetResponseDto;
import com.lms.userservice.response.dto.GradeSectionMapResponseDto;
import com.lms.userservice.response.dto.GradeSectionMappingResponseDto;
import com.lms.userservice.response.dto.SectionDataResponseDto;
import com.lms.userservice.service.GradeSectionMappingService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/user/grade-section")
public class GradeSectionMappingController {

	@Autowired
	private GradeSectionMappingService gradeSectionMappingService;

	@GetMapping("/section-data")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Radio buttons value. Screen No. 21")
	public LMSResponse<List<SectionDataResponseDto>> getAllSectionData() {
		List<SectionDataResponseDto> response = gradeSectionMappingService.getAllSectionData();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SectionDataResponseDto>>(), response,
				Translator.toLocale("section.data.get.all.success", null),
				Translator.toLocale("section.data.get.all.failed", null));
	}

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Create Grade Section Mapping. Screen No. 21")
	public LMSResponse<List<GradeSectionMapResponseDto>> createGradeSectionMapping(
			@Valid @RequestBody GradeSectionMapRequestDto request) {
		List<GradeSectionMapResponseDto> response = gradeSectionMappingService.createGradeSectionMapping(request);
		return ResponseHelper.createResponse(new LMSResponse<List<GradeSectionMapResponseDto>>(), response,
				Translator.toLocale("gs.mapping.create.success", null),
				Translator.toLocale("gs.mapping.create.failed", null));
	}

	@GetMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Get all grade and section mapping. Screen No. 21")
	public LMSResponse<List<GradeSectionGetResponseDto>> getAllMappingBySchoolAndBranch(
			@RequestParam("schoolId") String schoolId, @RequestParam("branchId") String branchId) {
		List<GradeSectionGetResponseDto> response = gradeSectionMappingService.getAllMappingBySchoolAndBranch(schoolId,
				branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradeSectionGetResponseDto>>(), response,
				Translator.toLocale("gs.mapping.create.success", null),
				Translator.toLocale("gs.mapping.create.failed", null));
	}

	@PutMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Update the grade section mapping. Screen No. 21")
	public LMSResponse<GradeSectionMapResponseDto> updateGradeSectionsMapping(
			@Valid @RequestBody GradeSectionPutRequestDto request) {
		GradeSectionMapResponseDto response = gradeSectionMappingService.updateGradeSectionsMapping(request);
		return ResponseHelper.createResponse(new LMSResponse<GradeSectionMapResponseDto>(), response,
				Translator.toLocale("gs.mapping.update.success", null),
				Translator.toLocale("gs.mapping.update.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Delete the grade section mapping. Screen No. 21")
	@DeleteMapping()
	public LMSResponse<Boolean> deleteActiveFieldByGradeIdBranchIdAndSchoolId(
			@Valid @RequestBody ToggleGradeSectionRequestDto request) {
		boolean response = gradeSectionMappingService.deleteActiveFieldByGradeIdBranchIdAndSchoolId(request);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("gs.mapping.delete.success", null),
				Translator.toLocale("gs.mapping.delete.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Activate/Deactivate Grade-section mapping. Screen No. 21")
	@PutMapping("/toggle/active")
	public LMSResponse<Boolean> updateActiveFieldByGradeIdBranchIdAndSchoolId(
			@Valid @RequestBody ToggleGradeSectionRequestDto request) {
		boolean response = gradeSectionMappingService.updateActiveFieldByGradeIdBranchIdAndSchoolId(request);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/{gradeId}/grade-mappings")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation api to before delete the grade_section mapping", value = "Confirmation API before delete, with grade")
	public LMSResponse<Boolean> checkGradeMappedData(@PathVariable("gradeId") String gradeId) {
		Boolean response = gradeSectionMappingService.getCountOfGradeSectionsByGradeId(gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response,
				Translator.toLocale("gs.mapped.with.grade.success", null),
				Translator.toLocale("gs.mapped.with.grade.failed", null));
	}

	@GetMapping("/{sectionId}/section-mappings")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation api to before delete the grade_section mapping, with section", value = "Confirmation API before delete, with section")
	public LMSResponse<String> checkSectionMappedData(@PathVariable("sectionId") String sectionId) {
		Boolean response = gradeSectionMappingService.getCountOfGradeSectionsBySectionId(sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	@GetMapping("/grades")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/72355049-c0f8-424f-9507-ace41d3133c4", value = "Grades mapped with branch and school. Screen No: 30")
	public LMSResponse<List<GradesResponseDto>> getAllGradesBySchoolAndBranch(@RequestParam("branchId") String branchId,
			@RequestParam("schoolId") String schoolId) {
		List<GradesResponseDto> response = gradeSectionMappingService.getAllGradesBySchoolAndBranch(branchId, schoolId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradesResponseDto>>(), response,
				Translator.toLocale("gs.mapped.with.grade.success", null),
				Translator.toLocale("gs.mapped.with.grade.failed", null));
	}

	@GetMapping("/sections")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/72355049-c0f8-424f-9507-ace41d3133c4 "
			+ "<br> <strong>search</strong> also implementing here to finds out the section exist or not. <strong>gradeId</strong> is oprional here.", 
			value = "Sections mapped with grade, branch and school. Screen No: 30")
	public LMSResponse<List<SectionsResponseDto>> getAllSectionByGradesSchoolAndBranch(
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "schoolId", required = true) String schoolId) {
		List<SectionsResponseDto> response = gradeSectionMappingService.getAllSectionByGradesSchoolAndBranch(search,
				gradeId, branchId, schoolId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<SectionsResponseDto>>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = gradeSectionMappingService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Get all grade and section mapping(New API). Screen No. 21")
	public LMSResponse<List<GradeSectionMappingResponseDto>> getMappingsBySchoolIdAndBranchId(
			@RequestParam("schoolId") String schoolId, @RequestParam("branchId") String branchId) {
		List<GradeSectionMappingResponseDto> response = gradeSectionMappingService
				.getMappingsBySchoolIdAndBranchId(schoolId, branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<GradeSectionMappingResponseDto>>(), response,
				Translator.toLocale("gs.mapping.create.success", null),
				Translator.toLocale("gs.mapping.create.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Delete the grade section mapping (New API). Screen No. 21")
	@DeleteMapping("/{id}")
	public LMSResponse<Boolean> deleteById(@PathVariable("id") String id) {
		boolean response = gradeSectionMappingService.deleteById(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("gs.mapping.delete.success", null),
				Translator.toLocale("gs.mapping.delete.failed", null));
	}

	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/5fdf8ad9-1ef3-40c6-997d-8cb4d7fc87bb", value = "Activate/Deactivate Grade-section mapping. Screen No. 21")
	@GetMapping("/toggle-active/{id}")
	public LMSResponse<Boolean> toggleActiveStatusById(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		boolean response = gradeSectionMappingService.toggleActiveStatusById(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/confirm/{mappingId}")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation api to before delete or toggle active the grade_section mapping", value = "Confirmation API before delete or toggle active, with section")
	public LMSResponse<String> checkMappedData(@PathVariable("mappingId") String mappingId) {
		Boolean response = gradeSectionMappingService.existsByIdAndDeleted(mappingId, false);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the concept", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> confirmationApi(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = gradeSectionMappingService.confirmationApi(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@Lazy
	@GetMapping("/checking-mapping-for-grade/{gradeId}")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Check mapping for GradesController (confirmation-api)", value = "Fiegn call hiding from Front-End", hidden = true)
	public LMSResponse<Boolean> checkTheGradeHasMapping(@PathVariable("gradeId") String gradeId) {
		boolean response = gradeSectionMappingService.checkTheGradeHasMapping(gradeId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}
	
	@GetMapping("/grade-related/sections")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Find all sections for Principal dashboard Syllabus completion details", value = "Sections mapped with grade, branch and school")
	public LMSResponse<List<String>> getAllSectionsForPrincipal(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = false) String gradeId) {
		List<String> response = gradeSectionMappingService.getAllSectionsForPrincipal(schoolId, branchId, gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("gs.mapped.with.section.success", null),
				Translator.toLocale("gs.mapped.with.section.failed", null));
	}

	
		@GetMapping("/getMappedDataForGradeId/{gradeId}")
		@SuppressWarnings("unchecked")
		@ApiOperation(notes = "Confirmation api to before delete the grade_section mapping", value = "Confirmation API before delete, with grade")
		public LMSResponse<List<String>> getMappedDataForGradeId(@PathVariable("gradeId") String gradeId) {
			List<String> response = gradeSectionMappingService.getSectionByGradeId(gradeId);
			return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
					Translator.toLocale("gs.mapped.with.grade.success", null),
					Translator.toLocale("gs.mapped.with.grade.failed", null));
		}
}
