package com.lms.userservice.controller;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AdminUsersRequestDto;
import com.lms.userservice.request.dto.ToggleActiveStatusRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.StudentsResponseDto;
import com.lms.userservice.service.AdminUsersService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

/**
 * API end points for create, update, get by Id, get by pagination, toggle
 * active status, delete and update password
 * 
 * <AUTHOR> C Achari
 *
 */
@RestController
@RequestMapping("/v1/api/user/admin-users")
public class AdminUsersController {

	@Autowired
	private AdminUsersService adminUsersService;

	@Lazy
	@PostMapping()
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/1e2b14a7-1d99-4606-9f48-fb04d9bb7e3c/", value = "Create Admin-users, Screen No. 10 ")
	public LMSResponse<AdminUsersResponseDto> createAdminUserAndSendEmail(
			@Valid @RequestBody AdminUsersRequestDto request) {
		AdminUsersResponseDto response = adminUsersService.createAdminUserAndSendEmail(request);
		return ResponseHelper.createResponse(new LMSResponse<AdminUsersResponseDto>(), response,
				Translator.toLocale("user.created.successfully", null),
				Translator.toLocale("user.create.failed", null));
	}

	/**
	 * Don't change the method name, via name email is sending.
	 * 
	 * @param request
	 * @return
	 */
	@Lazy
	@PutMapping()
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/1e2b14a7-1d99-4606-9f48-fb04d9bb7e3c/", value = "Update Admin-users, Screen No. 10 ")
	public LMSResponse<AdminUsersResponseDto> createUpdateAdminUserAndSendEmail(
			@Valid @RequestBody AdminUsersRequestDto request) {
		AdminUsersResponseDto response = adminUsersService.updateAdminUser(request);
		return ResponseHelper.createResponse(new LMSResponse<AdminUsersResponseDto>(), response,
				Translator.toLocale("user.updated.successfully", null),
				Translator.toLocale("user.update.failed", null));
	}

	@Lazy
	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get admin-user by id", value = "Get Admin-users by id")
	public LMSResponse<AdminUsersResponseDto> getAdminUserById(@PathVariable("id") String id) {
		AdminUsersResponseDto response = adminUsersService.getAdminUserById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<AdminUsersResponseDto>(), response,
				Translator.toLocale("user.get.by.id.success", null),
				Translator.toLocale("user.get.by.id.failed", null));
	}

	@Lazy
	@GetMapping("/all")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/c8903710-6eb9-4b13-8b54-51a50feca973", value = "All admin-users. Screen no: 9, 10")
	public LMSResponse<Page<AdminUsersResponseDto>> getAllAdminUsersByPagniation(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "roleId", required = false) String roleId,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<AdminUsersResponseDto> response = adminUsersService.getAllAdminUsersByPagniation(pageNumber,
				pageSize, sortOrder, sortBy, search, roleId, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<AdminUsersResponseDto>>(), response,
				Translator.toLocale("user.get.all.success", null), Translator.toLocale("user.get.all.failed", null));
	}

	@PutMapping("/toggle-active")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/c8903710-6eb9-4b13-8b54-51a50feca973", value = "Toggle active status of admin-users. Screen no: 9, 10")
	public LMSResponse<Boolean> toggleActiveStatus(@Valid @RequestBody ToggleActiveStatusRequestDto request) {
		Boolean response = adminUsersService.toggleActiveStatus(request);
		return ResponseHelper.createResponseForFlags(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "https://xd.adobe.com/view/89c88a6c-df2c-49eb-a4db-fd947f84a4a5-dc9b/screen/c8903710-6eb9-4b13-8b54-51a50feca973", value = "Delete admin-users. Screen no: 9, 10")
	public LMSResponse<Boolean> deleteStudent(@PathVariable("id") String id) {
		Boolean response = adminUsersService.deleteAdminUser(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("user.delete.success", null), Translator.toLocale("user.delete.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the concept", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> checkTheMappingForConcept(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = adminUsersService.checkTheMappingForConcept(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@PutMapping("/toggle-active-status")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "This is a feign call, only for back-end", value = "Toggle active status of admin-users", hidden = true)
	public LMSResponse<Boolean> toggleActiveStatusForMaster(@RequestParam("id") String id,
			@RequestParam("active") boolean active) {
		Boolean response = adminUsersService.toggleActiveStatusForMaster(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}
}
