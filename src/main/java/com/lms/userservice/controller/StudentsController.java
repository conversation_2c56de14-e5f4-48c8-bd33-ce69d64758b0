package com.lms.userservice.controller;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.userservice.component.Translator;
import com.lms.userservice.entity.Students;
import com.lms.userservice.feign.master.SubjectsResponseDto;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.request.dto.AbsenteesRequestDto;
import com.lms.userservice.request.dto.ChangeProfileRequestDto;
import com.lms.userservice.request.dto.DeActivateProfileRequestDto;
import com.lms.userservice.request.dto.StudentRequestIRDTO;
import com.lms.userservice.request.dto.StudentSubjectMappingRequestDto;
import com.lms.userservice.request.dto.StudentsRequestDto;
import com.lms.userservice.response.dto.AbsenteesResponseDto;
import com.lms.userservice.response.dto.BatchReceptionistRequestDto;
import com.lms.userservice.response.dto.ChangeProfileResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.FormalStudentWiseGetName;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.StudentAssignedMinDetailResponseDto;
import com.lms.userservice.response.dto.StudentDetailsMinResponseDto;
import com.lms.userservice.response.dto.StudentDetailsResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingsMinResponseDto;
import com.lms.userservice.response.dto.StudentSubjectMappingsResponseDto;
import com.lms.userservice.response.dto.StudentsMinResponseDto;
import com.lms.userservice.response.dto.StudentsResponseDto;
import com.lms.userservice.service.StudentsService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/user/students")
public class StudentsController {

	@Autowired
	private StudentsService studentsService;

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to add a Student", value = "Screen No. 30,41")
	public LMSResponse<StudentsResponseDto> createStudentAndSendEmail(@Valid @RequestBody StudentsRequestDto request) {
		StudentsResponseDto response = studentsService.createStudent(request);
//		return ResponseHelper.createResponse(new LMSResponse<StudentsResponseDto>(), response,
//				Translator.toLocale("student.created.successfully", null),
//				Translator.toLocale("student.create.failed", null));
		
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentsResponseDto>>(),
				response, Translator.toLocale("student.created.successfully", null),
				Translator.toLocale("student.create.failed", null));
	}

	@SuppressWarnings("unchecked")
	@PostMapping("/self-registration")
	@Lazy
	@ApiOperation(notes = "Used to self register Student", value = "Student self registration")
	public LMSResponse<StudentsResponseDto> selfRegistrationByStudent(@Valid @RequestBody StudentsRequestDto request) {
		StudentsResponseDto response = studentsService.selfRegistrationByStudent(request);
		return ResponseHelper.createResponse(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("student.self.registered.successfully", null),
				Translator.toLocale("student.self.registration.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update a Student by Student ID", value = "Screen No. 30")
	public LMSResponse<StudentsResponseDto> createUpdateStudentAndSendEmail(@PathVariable("id") String id,
			@Valid @RequestBody StudentsRequestDto request) {
		StudentsResponseDto response = studentsService.updateStudent(id, request);
		return ResponseHelper.createResponse(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("student.details.updated.successfully", null),
				Translator.toLocale("student.details.update.failed", null));
	}
	
	

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Student by Student ID", value = "Screen No. 30")
	public LMSResponse<StudentsResponseDto> getStudentById(@PathVariable("id") String id) {
		StudentsResponseDto response = studentsService.getStudentById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@GetMapping("/username/{username}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Student by User Name", value = "Chat Screen")
	public LMSResponse<StudentsResponseDto> getStudentByUserName(@PathVariable("username") String username) {
		StudentsResponseDto response = studentsService.getStudentByUserName(username);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to delete a Student using Student ID", value = "Screen No. 31")
	public LMSResponse<Boolean> deleteStudent(@PathVariable("id") String id) {
		Boolean response = studentsService.deleteStudent(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("student.deleted.successfully", null),
				Translator.toLocale("student.delete.failed", null));
	}

	@Lazy
	@GetMapping
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to get all Student list", value = "Screen Nos. 30,31,33")
	public LMSResponse<Page<StudentsResponseDto>> getAllStudentsByPagination(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<StudentsResponseDto> response = studentsService.getAllStudentsByPagination(pageNumber,
				pageSize, sortOrder, sortBy, sectionId, gradeId, branchId, schoolId, search, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<StudentsResponseDto>>(), response,
				Translator.toLocale("students.detail.filtered.successfully", null),
				Translator.toLocale("failed.to.filter.students.detail", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/{id}/active")
	@Lazy
	@ApiOperation(notes = "Used to activate/deactive student using Teacher ID", value = "Screen No. 30,31,33")
	public LMSResponse<Boolean> updateActive(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = studentsService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/check-mapping")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/504d9a3d-4375-4b51-89dc-1b26924233ab", value = "Check the mapping exist before delete or toggling Active, Screen No. 31")
	public LMSResponse<String> checkTheMappingExistBeforeDeleteOrTogglingActive(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		String response = studentsService.checkTheMappingExistBeforeDeleteOrTogglingActive(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("request.handled.success", null),
				Translator.toLocale("something.went.wrong", null));
	}

	@GetMapping("/{gradeId}/grade-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> checkGradeMappedData(@PathVariable("gradeId") String gradeId) {
		Boolean response = studentsService.getCountOfStudentsByGradeId(gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("student.mapped.with.grade.success", null),
				Translator.toLocale("student.mapped.with.grade.failed", null));
	}

	@GetMapping("/{sectionId}/section-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> checkSectionMappedData(@PathVariable("sectionId") String sectionId) {
		Boolean response = studentsService.getCountOfStudentsBySectionId(sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("student.mapped.with.section.success", null),
				Translator.toLocale("student.mapped.with.section.failed", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = studentsService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@GetMapping("/{languageId}/language-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<NameCommonResponseDto>> getAllStudentsByLanguage(
			@PathVariable("languageId") String languageId) {
		List<NameCommonResponseDto> response = studentsService.getAllStudentsByLanguage(languageId);
		return ResponseHelper.createResponse(new LMSResponse<NameCommonResponseDto>(), response,
				Translator.toLocale("students.mappings.by.language.fetch.success", null),
				Translator.toLocale("students.mappings.by.language.fetch.failed", null));
	}

	@GetMapping("/{studentCategoryId}/student-category-mappings")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<NameCommonResponseDto>> getStudentsByStudentCategory(
			@PathVariable("studentCategoryId") String studentCategoryId) {
		List<NameCommonResponseDto> response = studentsService.getStudentsByStudentCategory(studentCategoryId);
		return ResponseHelper.createResponse(new LMSResponse<NameCommonResponseDto>(), response,
				Translator.toLocale("students.mappings.by.student.category.fetch.success", null),
				Translator.toLocale("students.mappings.by.student.category.fetch.failed", null));
	}

	@PostMapping("/student-subjects-mapping")
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentSubjectMappingsResponseDto> mapSubjectsToStudent(
			@Valid @RequestBody StudentSubjectMappingRequestDto request) {
		StudentSubjectMappingsResponseDto response = studentsService.mapSubjectsToStudent(request);
		return ResponseHelper.createResponse(new LMSResponse<StudentSubjectMappingsResponseDto>(), response,
				Translator.toLocale("student.subject.groups.subject.mapping.success", null),
				Translator.toLocale("student.subject.groups.subject.mapping.failed", null));
	}

	@GetMapping("/subject-mapping/{studentId}/{academicYearId}")
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentSubjectMappingsResponseDto> getStudentWithSubjects(
			@RequestParam("studentId") String studentId, @RequestParam("academicYearId") String academicYearId) {
		StudentSubjectMappingsResponseDto response = studentsService.getStudentWithSubjects(studentId, academicYearId);
		return ResponseHelper.createResponse(new LMSResponse<StudentSubjectMappingsResponseDto>(), response,
				Translator.toLocale("student.subject.groups.subject.mapping.fetch.success", null),
				Translator.toLocale("student.subject.groups.subject.mapping.fetch.failed", null));
	}

	@GetMapping("/subject-mapping/sub-topic/{studentId}")
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentSubjectMappingsMinResponseDto> getStudentWithSubjects(
			@RequestParam("studentId") String studentId) {
		StudentSubjectMappingsMinResponseDto response = studentsService.getStudentWithSubjectsAndSubTopics(studentId);
		return ResponseHelper.createResponse(new LMSResponse<StudentSubjectMappingsMinResponseDto>(), response,
				Translator.toLocale("student.subject.groups.subject.mapping.fetch.success", null),
				Translator.toLocale("student.subject.groups.subject.mapping.fetch.failed", null));
	}

	@GetMapping("/all-subjects/{studentId}")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Subject assigned to a student, use it where ever student subject needed", value = "Subject list of student")
	public LMSResponse<List<SubjectsResponseDto>> getAllSubjectsByStudentId(
			@PathVariable("studentId") String studentId) {
		List<SubjectsResponseDto> response = studentsService.getAllSubjectsByStudentId(studentId);
		return ResponseHelper.createResponse(new LMSResponse<List<SubjectsResponseDto>>(), response,
				Translator.toLocale("student.subject.groups.subject.mapping.fetch.success", null),
				Translator.toLocale("student.subject.groups.subject.mapping.fetch.failed", null));
	}

	@GetMapping("/get-user-ids")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get user ids of students <student id, user id>", value = "")
	public LMSResponse<Map<String, String>> getUserIds(
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "search", required = false) String search) {
		Map<String, String> response = studentsService.getUserIds(sectionId, gradeId, branchId, schoolId, search);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Map<String, String>>(), response,
				Translator.toLocale("students.user.ids.fetch.success", null),
				Translator.toLocale("students.user.ids.fetch.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the concept", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> checkTheMappingForConcept(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = studentsService.checkTheMappingForConcept(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@PutMapping("/change-profile")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/504d9a3d-4375-4b51-89dc-1b26924233ab", value = "Change profile of students, Screen No.33")
	public LMSResponse<ChangeProfileResponseDto> changeStudentProfile(
			@Valid @RequestBody ChangeProfileRequestDto request) {
		ChangeProfileResponseDto response = studentsService.changeStudentProfile(request);
		return ResponseHelper.createResponse(new LMSResponse<ChangeProfileResponseDto>(), response,
				Translator.toLocale("student.profile.details.updated.successfully", null),
				Translator.toLocale("student.profile.details.updated.failed", null));
	}

	@GetMapping("/rerelase-list")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Finding the students examination details  and pagination from the given Ids .", value = "Examination details | Re-relase Students List , Screen No. 40 ")
	public LMSResponse<Page<StudentsResponseDto>> getStudentsExaminationStatus(
			@RequestParam("pageNumber") @Min(0) int pageNumber, @RequestParam("pageSize") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "quizId", required = true) String quizId,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "sectionId", required = false) String sectionId
			//@RequestParam(value = "active", required = false) Boolean active
			) {
		PaginatedResponse<StudentsResponseDto> response = studentsService.getStudentsExaminationStatus(pageNumber,
				pageSize, schoolId, branchId, gradeId, quizId, search, sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<StudentsResponseDto>>(), response,
				Translator.toLocale("students.detail.filtered.successfully", null),
				Translator.toLocale("failed.to.filter.students.detail", null));
	}

	@GetMapping("/user-ids/release-quiz")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get user ids of students for quiz release", value = "This is a feign call hidding from FE", hidden = true)
	public LMSResponse<List<String>> getAllUserIdForStudent(
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "schoolId", required = false) String schoolId) {
		List<String> response = studentsService.getAllUserIdForStudent(sectionId, gradeId, branchId, schoolId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("student.user.id.listing.success", null),
				Translator.toLocale("student.user.id.listing.failed", null));
	}

	@GetMapping("/user-ids/re-release-quiz")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get user ids of students for quiz re-release", value = "This is a feign call hidding from FE", hidden = true)
	public LMSResponse<List<String>> getAllUserIdByStudentIds(
			@RequestParam(value = "studentsId", required = false) List<String> studentsId) {
		List<String> response = studentsService.getAllUserIdByStudentIds(studentsId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("student.user.id.listing.success", null),
				Translator.toLocale("student.user.id.listing.failed", null));
	}

	@GetMapping("/count")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get students count to student-service", value = "This is a feign call hidding from FE", hidden = true)
	public LMSResponse<Long> countOfStudentsByFilters(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId) {
		long response = studentsService.countOfStudentsByFilters(schoolId, branchId, gradeId, sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Long>(), response,
				Translator.toLocale("student.count.success", null), Translator.toLocale("student.count.failed", null));
	}

	@PutMapping("/de-activate-profile")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/ddf0c3ff-c3e5-4ed5-a209-55d98944e1ef", value = "De-Activate profile of students, Screen No.33")
	public LMSResponse<Boolean> deActivateStudentProfile(@RequestBody DeActivateProfileRequestDto request) {
		Boolean response = studentsService.deActivateStudentProfile(request);
		return ResponseHelper.createResponse(new LMSResponse<Boolean>(), response,
				Translator.toLocale("student.profile.updated.successfully", null),
				Translator.toLocale("student.profile.updated.failed", null));
	}

	@GetMapping("/student-details")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get students details", value = "Student details")
	public LMSResponse<List<StudentsMinResponseDto>> getStudentsDetail(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId) {
		List<StudentsMinResponseDto> response = studentsService.getStudentsDetail(schoolId, branchId, gradeId,
				sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentsMinResponseDto>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@GetMapping("/user-id/re-release-quiz")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get user id of student for quiz re-release", value = "This is a feign call hidding from FE", hidden = true)
	public LMSResponse<String> getUserIdByStudentId(
			@RequestParam(value = "studentId", required = false) String studentId) {
		String response = studentsService.getUserIdByStudentId(studentId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("student.user.id.listing.success", null),
				Translator.toLocale("student.user.id.listing.failed", null));
	}

	@GetMapping("/student-count-sectionid")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get the student count in section", value = "This is a feign call hidding from FE", hidden = true)
	public LMSResponse<Integer> getStudentSectionCount(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId) {
		Integer response = studentsService.getStudentSectionCount(schoolId, branchId, gradeId, sectionId);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("fetch.student.section.count.success", null),
				Translator.toLocale("fetch.student.section.count.failed", null));

	}

	@GetMapping("/get-students-count-by-gradeid")
	@ApiOperation(notes = "This api is used for get the student count using grade id ", value = "This is feign call for content Service ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<Long> getStudentCountByGrade(@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "sectionId", required = true) String sectionId) {
		Long response = studentsService.getStudentCountByGrade(gradeId, schoolId, sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Long>(), response,
				Translator.toLocale("student.count.by.grade.id.success", null),
				Translator.toLocale("student.count.by.grade.id.failed", null));
	}

	@Lazy
	@GetMapping("/userId-by-schools")
	@ApiOperation(notes = "Get the user-id of students from different school, this is a feign for content-service", value = "Feign hiding from FE", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> getUserIdOfStudentsFromDifferentSchools(
			@RequestParam(value = "sectionIds", required = false) List<String> sectionIds,
			@RequestParam(value = "gradeIds", required = true) List<String> gradeIds,
			@RequestParam(value = "branchIds", required = true) List<String> branchIds,
			@RequestParam(value = "schoolIds", required = true) List<String> schoolIds) {
		List<String> response = studentsService.getUserIdOfStudentsFromDifferentSchools(sectionIds, gradeIds, branchIds,
				schoolIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<String>>(), response,
				Translator.toLocale("student.userid.from.different.schools.success", null),
				Translator.toLocale("student.userid.from.different.schools.failed", null));
	}

	@Lazy
	@GetMapping("/user-name-details")
	@ApiOperation(notes = "This api is for getting student details , this is a feign for student-service", value = "Feign hiding from FE", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentDetailsResponseDto> getStudentDetailsByName(
			@RequestParam(value = "userName", required = true) String userName) {
		StudentDetailsResponseDto response = studentsService.getStudentDetailsByName(userName);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentDetailsResponseDto>(), response,
				Translator.toLocale("student.user.name.details.success", null),
				Translator.toLocale("student.user.name.details.failed", null));
	}

	@GetMapping("/student-details/dashboard")
	@ApiOperation(notes = "Get the students deatils by username, this is a feign for content-service", value = "Feign hiding from FE", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentAssignedMinDetailResponseDto> getStudentAssignedDetails(
			@RequestParam(value = "username", required = true) String username) {
		StudentAssignedMinDetailResponseDto response = studentsService.getStudentAssignedDetails(username);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentAssignedMinDetailResponseDto>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));

	}

	@SuppressWarnings("unchecked")
	@GetMapping("/get-student-name-details")
	public LMSResponse<List<FormalStudentWiseGetName>> getFormalWiseStudentName(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "studentIds", required = true) List<String> studentIds) {

		List<FormalStudentWiseGetName> response = studentsService.getFormalWiseStudentName(teacherId, gradeId, schoolId,
				branchId, sectionId, studentIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<FormalStudentWiseGetName>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@GetMapping("/get-student-list-section")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is for getting student details, this is a feign for teacher-service", value = "Feign hiding from FE", hidden = true)
	public LMSResponse<List<StudentMinResponseDto>> getStudentListInSection(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId) {
		List<StudentMinResponseDto> response = studentsService.getStudentListInSection(schoolId, branchId, gradeId,
				sectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentMinResponseDto>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@GetMapping("/get-student-list-grade")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api is for getting student details, this is a feign for teacher-service", value = "Feign hiding from FE", hidden = true)
	public LMSResponse<List<StudentMinResponseDto>> getStudentListInGrade(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "gradeId", required = true) String gradeId) {
		List<StudentMinResponseDto> response = studentsService.getStudentListInGrade(boardId, gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentMinResponseDto>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@Lazy
	@GetMapping("/user-details")
	@ApiOperation(notes = "This api is for getting student details , this is a feign for student-service", value = "Feign hiding from FE", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<StudentDetailsMinResponseDto> findStudentDetails(
			@RequestParam(value = "studentId", required = true) String studentId) {
		StudentDetailsMinResponseDto response = studentsService.findStudentDetails(studentId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentDetailsMinResponseDto>(), response,
				Translator.toLocale("student.user.name.details.success", null),
				Translator.toLocale("student.user.name.details.failed", null));
	}

	@GetMapping("/sms-student's-details")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Getting students details, this is a feign for content-service", value = "Feign, hiding from FE", hidden = true)
	public LMSResponse<List<BatchReceptionistRequestDto>> findingStudentsDetails(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "studentIds", required = false) List<String> studentIds) {
		List<BatchReceptionistRequestDto> response = studentsService.findingStudentsDetails(boardId, schoolId, branchId,
				gradeId, sectionId, studentIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BatchReceptionistRequestDto>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

	@PutMapping("/exam-absentees")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Feign call from Teacher-Service to get the absentees details", value = "Feign call, hiding from FE", hidden = true)
	public LMSResponse<List<AbsenteesResponseDto>> getAllAbsenteesDetails(
			@RequestBody @Valid AbsenteesRequestDto request) {
		List<AbsenteesResponseDto> response = studentsService.getAllAbsenteesDetails(request);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<AbsenteesResponseDto>>(), response,
				Translator.toLocale("student.absentees.details.success", null),
				Translator.toLocale("student.absentees.details.failed", null));
	}
	
	
	@GetMapping("/avid-student-details")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Get students details", value = "Student details")
	public LMSResponse<List<StudentsMinResponseDto>> getAvidStudentsDetails(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestParam(value = "branchId", required = false) String branchId) {
		List<StudentsMinResponseDto> response = studentsService.getAvidStudentsDetails(schoolId,  gradeId,
				sectionId,branchId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<StudentsMinResponseDto>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}
	
	@PutMapping("/profile/ir/{studentId}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update a Student by Student ID", value = "Screen No. 30")
	public LMSResponse<Boolean> getStudentProfileIRDetails(
			@PathVariable("studentId") String studentId,
			@RequestBody @Valid StudentRequestIRDTO request) {
		Boolean response = studentsService.getStudentProfileIRDetails(studentId,request);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response,
				Translator.toLocale("student.details.updated.successfully", null),
				Translator.toLocale("student.details.update.failed", null));
	}
	
	
	@Lazy
	@GetMapping("/ir/teacher-section-grade-based")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Teacher id assigned section and grade based student details get API.", value = "Screen Nos. 30,31,33")
	public LMSResponse<Page<StudentsResponseDto>> getAllStudentSetionAndGradeBased(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "100") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,			
			@RequestParam(value = "teacherId", required = true) String teacherId,
	        @RequestParam(value = "irStatus", required = false) String irStatus,
	        @RequestParam(value = "schoolId", required = false) String schoolId,
	        @RequestParam(value = "branchId", required = false) String branchId) {
		PaginatedResponse<StudentsResponseDto> response = studentsService.getAllStudentSetionAndGradeBased(pageNumber,pageSize, teacherId,irStatus,schoolId,branchId,sortOrder,sortBy);
	    return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<StudentsResponseDto>>(), response,
	            Translator.toLocale("students.detail.filtered.successfully", null),
	            Translator.toLocale("failed.to.filter.students.detail", null));
	}

	
	@PutMapping("/ir-status-update")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "update IR status coloumn in student entity", value = "Screen No. 30,41")
	public LMSResponse<Boolean> updateStudentEntityIrStatus(@RequestParam("studentId") String studentId) {
		Boolean response = studentsService.updateStudentEntityIrStatus(studentId);		
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(),
				response, Translator.toLocale("student.details.updated.successfully", null),
				Translator.toLocale("student.details.update.failed", null));
	}
	
	@PostMapping("/sms-student's-details")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Getting students details, this is a feign for content-service", value = "Feign, hiding from FE", hidden = true)
	public LMSResponse<List<BatchReceptionistRequestDto>> findingStudentsDetailsV2(
			@RequestParam(value = "boardId", required = true) String boardId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestBody List<String> studentIds) {
		List<BatchReceptionistRequestDto> response = studentsService.findingStudentsDetails(boardId, schoolId, branchId,
				gradeId, sectionId, studentIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<BatchReceptionistRequestDto>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}
	
	@SuppressWarnings("unchecked")
	@PostMapping("/get-student-name-details")
	public LMSResponse<List<FormalStudentWiseGetName>> getFormalWiseStudentNameV2(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "sectionId", required = true) String sectionId,
			@RequestBody List<String> studentIds) {

		List<FormalStudentWiseGetName> response = studentsService.getFormalWiseStudentName(teacherId, gradeId, schoolId,
				branchId, sectionId, studentIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<FormalStudentWiseGetName>>(), response,
				Translator.toLocale("student.details.fetched.successfully", null),
				Translator.toLocale("student.details.fetch.failed", null));
	}

}