package com.lms.userservice.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.JsonNode;
import com.lms.userservice.component.Translator;
import com.lms.userservice.dto.EmailUserDto;
import com.lms.userservice.entity.Users;
import com.lms.userservice.feign.master.GradesSectionFeignResponseDto;
import com.lms.userservice.feign.notification.NotificationFeignClient;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.model.PaginatedResponse;
import com.lms.userservice.model.UserMinDetails;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.request.dto.CreateUserEmailRequestDto;
import com.lms.userservice.request.dto.TeacherAssignRequest;
import com.lms.userservice.request.dto.TeacherRequestDto;
import com.lms.userservice.request.dto.TeachersSelfRegRequestDto;
import com.lms.userservice.response.dto.AcademicStaffResponseDto;
import com.lms.userservice.response.dto.AllTypeUserMinResponseDto;
import com.lms.userservice.response.dto.ConfirmationApiResponseDto;
import com.lms.userservice.response.dto.EmailResponseDto;
import com.lms.userservice.response.dto.GradeAccessInfoResponseDto;
import com.lms.userservice.response.dto.GradeSectionResponseDto;
import com.lms.userservice.response.dto.NameCommonResponseDto;
import com.lms.userservice.response.dto.StudentFormativeMinResponseDto;
import com.lms.userservice.response.dto.StudentMinResponseDto;
import com.lms.userservice.response.dto.StudentNameDetails;
import com.lms.userservice.response.dto.StudentsResponseDto;
import com.lms.userservice.response.dto.SubjectsMinResponseDto;
import com.lms.userservice.response.dto.TeacherAccessResponseDto;
import com.lms.userservice.response.dto.TeacherAssignResponse;
import com.lms.userservice.response.dto.TeacherAssignmentResponse;
import com.lms.userservice.response.dto.TeacherDetailsForFeignResponseDto;
import com.lms.userservice.response.dto.TeacherNameResponse;
import com.lms.userservice.response.dto.TeacherResponseDto;
import com.lms.userservice.response.dto.TeacherSubjectMappingFeignResponse;
import com.lms.userservice.service.TeacherService;
import com.lms.userservice.util.CommonUtilities;
import com.lms.userservice.util.Constants;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

@Slf4j
@RestController
@RequestMapping("/v1/api/user/teachers")
public class TeacherController {

	@Autowired
	private TeacherService teacherService;

	@Autowired
	UsersRepository usersRepository;

	@Autowired
	private NotificationFeignClient notificationFeignClient;


	@Lazy
	@GetMapping()
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to get Academic Staff List pagination", value = "Screen No. 24-28")
	public LMSResponse<Page<AcademicStaffResponseDto>> getTeachersByPage(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "schoolId", required = false) String schoolId,
			@RequestParam(value = "branchId", required = false) String branchId,
			@RequestParam(value = "profile", required = false) String profile,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<AcademicStaffResponseDto> response = teacherService.getTeachersByPage(pageNumber, pageSize,
				sortOrder, sortBy, search, schoolId, branchId, profile, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<AcademicStaffResponseDto>>(), response,
				Translator.toLocale("teacher.get.all.success", null),
				Translator.toLocale("teacher.get.list.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Academic Staff by ID", value = "Screen No. 9")
	public LMSResponse<TeacherResponseDto> getTeachersById(@PathVariable("id") String id) {
		TeacherResponseDto teacherResponse = teacherService.getTeacherById(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<TeacherResponseDto>(), teacherResponse,
				Translator.toLocale("teacher.get.by.id.success", null),
				Translator.toLocale("teacher.get.by.id.failed", null));
	}

	@PostMapping()
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to add an Academic Staff", value = "Screen No. 24-26,42")
	public LMSResponse<TeacherResponseDto> createTeachersAndSendEmail(
			@RequestBody @Valid TeacherRequestDto teacherRequest) {
		TeacherResponseDto response = teacherService.addTeachers(teacherRequest);
		return ResponseHelper.createResponse(new LMSResponse<TeacherResponseDto>(), response,
				Translator.toLocale("teacher.create.success", null),
				Translator.toLocale("teacher.create.failed", null));
	}

	@PostMapping("/self-register")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to self register an Academic Staff", value = "Academic Staff self registration")
	public LMSResponse<TeacherResponseDto> selfRegisterTeacher(@RequestBody @Valid TeachersSelfRegRequestDto request) {
		TeacherResponseDto response = teacherService.teacherSelfRegistration(request);
		return ResponseHelper.createResponse(new LMSResponse<TeacherResponseDto>(), response,
				Translator.toLocale("teacher.self.registration.success", null),
				Translator.toLocale("teacher.self.registration.failed", null));
	}

	@PutMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to update an Academic Staff", value = "Screen No. 28")
	public LMSResponse<TeacherResponseDto> createUpdateTeacherAndSendEmail(@PathVariable("id") String id,
			@RequestBody @Valid TeacherRequestDto teacherRequest) {
		TeacherResponseDto response = teacherService.updateTeacher(id, teacherRequest);
		return ResponseHelper.createResponse(new LMSResponse<TeacherResponseDto>(), response,
				Translator.toLocale("teacher.update.success", null),
				Translator.toLocale("teacher.update.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to mark an Academic Staff as deleted using Teacher ID", value = "Screen No. 27")
	public LMSResponse<Boolean> removeTeacher(@PathVariable("id") String id) {
		Boolean response = teacherService.removeTeacher(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<TeacherResponseDto>(), response,
				Translator.toLocale("teacher.delete.success", null),
				Translator.toLocale("teacher.delete.failed", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/{id}/active")
	@Lazy
	@ApiOperation(notes = "Used to activate/deactive teacher using Teacher ID", value = "Screen No. 27")
	public LMSResponse<Boolean> updateActive(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = teacherService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@GetMapping("/check-mapping")
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "https://xd.adobe.com/view/46475c11-a043-4105-9490-cfcdc2a4039c-1314/screen/1ed59a9b-e308-4420-b567-8aa921005442", value = "Check the mapping exist before delete or toggling Active, Screen No. 27")
	public LMSResponse<String> checkTheMappingExistBeforeDeleteOrTogglingActive(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		String response = teacherService.checkTheMappingExistBeforeDeleteOrTogglingActive(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("request.handled.success", null),
				Translator.toLocale("something.went.wrong", null));
	}

	@GetMapping("/last-modified-at")
	@SuppressWarnings("unchecked")
	public LMSResponse<String> getLastModifiedAt() {
		String response = teacherService.getLastModifiedAt();
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<String>(), response,
				Translator.toLocale("last.modified.time.fetch.success", null),
				Translator.toLocale("last.modified.time.fetch.failed", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/coordinatorType-mapping/{coordinatorTypeId}")
	public LMSResponse<List<NameCommonResponseDto>> getAllTeacherMappingsForCoordinatorType(
			@PathVariable("coordinatorTypeId") String coordinatorTypeId) {
		List<NameCommonResponseDto> response = teacherService
				.getAllTeacherMappingsForCoordinatorType(coordinatorTypeId);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("teacher.coordinator.type.mapping.fetch.success", null),
				Translator.toLocale("teacher.coordinator.type.mapping.fetch.failed", null));
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/role-mapping/{roleId}")
	public LMSResponse<List<NameCommonResponseDto>> getAllTeacherMappingsForRole(
			@PathVariable("roleId") String roleId) {
		List<NameCommonResponseDto> response = teacherService.getAllTeacherMappingsForRole(roleId);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("request.handled.success", null),
				Translator.toLocale("something.went.wrong", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@PostMapping("/{teacherId}/map-subjects")
	@ApiOperation(notes = "Assign grade, subject, subtopic and section."
			+ "<br> <strong>School Admin</strong> https://xd.adobe.com/view/a61ae1aa-5d5b-4eb2-baac-c136b394af1c-f349/screen/b7c8a140-cde9-4a9a-b920-fe04932e7e97/ "
			+ "<br> <strong>Principal</strong> https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/ef720388-9d9a-4845-a88a-2afccf4a088b/ "
			+ "<br> <strong>Coordinator</strong> https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/8e59d023-abfe-4e75-b92d-34f5b01deee0 "
			+ "<br>", value = "Assign grade, subject, subtopic and section")
	public LMSResponse<List<TeacherAssignResponse>> assignSubjectToTeacher(@PathVariable("teacherId") String teacherId,
			@RequestBody TeacherAssignRequest request) {
		List<TeacherAssignResponse> response = teacherService.assignSubjectToTeacher(teacherId, request);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("teacher.assigned.subjects.success", null),
				Translator.toLocale("teacher.assigned.subjects.failed", null));
	}

	@SuppressWarnings("unchecked")
	// @GetMapping("/{teacherId}/mapped-subjects")
	@GetMapping("/grade-access-information/{teacherId}")
	@ApiOperation(notes = "Assigned subject, grade and section with student count."
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/2881197b-11e5-4ec5-9778-ddd1c9637bcd/", value = "Grade Access Information")
	public LMSResponse<List<GradeAccessInfoResponseDto>> gradeAccessInformation(
			@PathVariable("teacherId") String teacherId) {
		List<GradeAccessInfoResponseDto> response = teacherService.gradeAccessInformation(teacherId);
		return ResponseHelper.createResponse(new LMSResponse<GradeAccessInfoResponseDto>(), response,
				Translator.toLocale("fetch.teacher.assigned.subjects.success", null),
				Translator.toLocale("fetch.teacher.assigned.subjects.failed", null));
	}

	@SuppressWarnings("unchecked")
	@PutMapping("/map-subjects/{id}/toggle-active")
	public LMSResponse<Boolean> toggleActiveMappedSubjects(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = teacherService.toggleActiveMappedSubjects(id, active);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("toggle.active.teacher.assigned.subjects.success", null),
				Translator.toLocale("toggle.active.teacher.assigned.subjects.failed", null));

	}

	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API can be used to list the subjects mapped to a teacher for a grade and section", value = "Assigned Subject list with subtopic")
	@GetMapping("/subjects")
	public LMSResponse<List<SubjectsMinResponseDto>> listAllSubjectsByTeacher(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "gradeId", required = false) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "menuName", required = false) String menuName) {
		List<SubjectsMinResponseDto> response = teacherService.listAllSubjectsByTeacher(teacherId, gradeId, sectionId,
				menuName);
		return ResponseHelper.createResponse(new LMSResponse<List<SubjectsMinResponseDto>>(), response,
				Translator.toLocale("fetch.teacher.assigned.subjects.success", null),
				Translator.toLocale("fetch.teacher.assigned.subjects.failed", null));
	}

	@GetMapping("/{teacherId}/student-count")
	@Lazy
	@SuppressWarnings("unchecked")
	public LMSResponse<Integer> getCountOfStudents(@PathVariable("teacherId") String teacherId,
			@RequestParam(value = "subjectId", required = false) String subjectId) {
		Integer response = teacherService.getCountOfStudents(teacherId, subjectId);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response,
				Translator.toLocale("fetch.teacher.student.count.success", null),
				Translator.toLocale("fetch.teacher.student.count.failed", null));

	}

	@GetMapping("/{teacherId}/mapping")
	@Lazy
	@SuppressWarnings("unchecked")
	public LMSResponse<TeacherSubjectMappingFeignResponse> getGradeSubjectMappingMinResponse(
			@PathVariable("teacherId") String teacherId,
			@RequestParam(value = "subjectId", required = false) String subjectId) {
		TeacherSubjectMappingFeignResponse response = teacherService.getTeacherSubjectMappingMinResponse(teacherId,
				subjectId);
		return ResponseHelper.createResponse(new LMSResponse<TeacherSubjectMappingFeignResponse>(), response,
				Translator.toLocale("fetch.teacher.assigned.subjects.success", null),
				Translator.toLocale("fetch.teacher.assigned.subjects.failed", null));

	}

	@ApiIgnore
	@ApiOperation(value = "This API is used by feign calls")
	@GetMapping("/feign/names-by-ids")
	@Lazy
	@SuppressWarnings("unchecked")
	public LMSResponse<List<TeacherNameResponse>> getTeacherNameResponseByIds(
			@RequestParam(value = "teacherIds") List<String> teacherIds) {
		List<TeacherNameResponse> response = teacherService.getTeacherNameResponseByIds(teacherIds);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<TeacherSubjectMappingFeignResponse>(), response,
				Translator.toLocale("fetch.teacher.names.success", null),
				Translator.toLocale("fetch.teacher.names.failed", null));

	}

	@ApiIgnore
	@ApiOperation(value = "This API is used by feign calls")
	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/{id}/exists")
	public LMSResponse<Boolean> checkIfTeacherExistsById(@PathVariable("id") String teacherId) {
		Boolean response = teacherService.checkIfTeacherExistsById(teacherId);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("teacher.get.by.id.success", null),
				Translator.toLocale("teacher.get.by.id.failed", null));
	}

	@GetMapping("/confirmation-api")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Confirmation API before toggle or delete the concept", value = "Confirmation API before toggle/delete")
	public LMSResponse<ConfirmationApiResponseDto> checkTheMappingForConcept(@RequestParam(value = "id") String id,
			@RequestParam(value = "operationType") String operationType) {
		ConfirmationApiResponseDto response = teacherService.checkTheMappingForConcept(id, operationType);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<ConfirmationApiResponseDto>(), response,
				Translator.toLocale("confirmation.api.success", null),
				Translator.toLocale("confirmation.api.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/checking/coordinator-mapping/{coordinatorTypeId}")
	@ApiOperation(value = "This API is used by feign calls. Hidding from Front-end", hidden = true)
	public LMSResponse<Boolean> checkTheMappingForCoordinatoryType(
			@PathVariable("coordinatorTypeId") String coordinatorTypeId) {
		boolean response = teacherService.checkTheMappingForCoordinatoryType(coordinatorTypeId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/checking/subject-mapping/{subjectId}")
	@ApiOperation(value = "This API is used by feign calls. Hidding from Front-end", hidden = true)
	public LMSResponse<Boolean> checkTheMappingForSubject(@PathVariable("subjectId") String subjectId) {
		boolean response = teacherService.checkTheMappingForSubject(subjectId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@GetMapping("/checking/sub-topic-mapping/{subTopicId}")
	@ApiOperation(value = "This API is used by feign calls. Hidding from Front-end", hidden = true)
	public LMSResponse<Boolean> checkTheMappingForSubTopic(@PathVariable("subTopicId") String subTopicId) {
		boolean response = teacherService.checkTheMappingForSubTopic(subTopicId);
		String message = response ? Translator.toLocale("mapping.found", null)
				: Translator.toLocale("mapping.not.found", null);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response, message,
				Translator.toLocale("mapping.api.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API can be used to list the assigned grades and section for an Academic-staff."
			+ "<br> Academic-staffs could be <strong>TEACHER/COORDINATOR/PRINCIPAL</strong>", value = "Assigned Grades list with sections")
	@GetMapping("/assigned-grades")
	public LMSResponse<List<GradesSectionFeignResponseDto>> gradesFromTeacherAccessEntity(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "menuName", required = false) String menuName) {
		List<GradesSectionFeignResponseDto> response = teacherService.gradesFromTeacherAccessEntity(teacherId,
				menuName);
		return ResponseHelper.createResponse(new LMSResponse<List<GradesSectionFeignResponseDto>>(), response,
				Translator.toLocale("teacher.assigned.grade.fetch.success", null),
				Translator.toLocale("teacher.assigned.grade.fetch.failed", null));
	}
	
	
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This API can be used to list the assigned grades and section for an Academic-staff."
			+ "<br> Academic-staffs could be <strong>TEACHER/COORDINATOR/PRINCIPAL</strong>", value = "Assigned Grades list with sections")
	@GetMapping("/assign-grades")
	public LMSResponse<JsonNode> getAssignGrades(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "menuName", required = false) String menuName) {
		JsonNode response = teacherService.getAssignGrades(teacherId,
				menuName);
		return ResponseHelper.createResponse(new LMSResponse<JsonNode>(), response,
				Translator.toLocale("teacher.assigned.grade.fetch.success", null),
				Translator.toLocale("teacher.assigned.grade.fetch.failed", null));
	}

	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "This api can be used for getting section or grade for a perticular teacher.", value = "This is feign call so hiding from swagger documentation", hidden = true)
	@GetMapping("/grade-section/for-quizRelease")
	public LMSResponse<GradeSectionResponseDto> getGradeOrSectionToTeacherForQuizRelease(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId,
			@RequestParam(value = "teacherId", required = true) String teacherId) {
		GradeSectionResponseDto response = teacherService.getGradeOrSectionToTeacherForQuizRelease(schoolId, branchId,
				gradeId, subjectId, subtopicId, teacherId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<GradeSectionResponseDto>(), response,
				Translator.toLocale("teacher.assigned.section.or.grade.listing.success", null),
				"teacher.assigned.section.or.grade.listing.failed");
	}

	@GetMapping("/access-for-coordinator")
	@ApiOperation(notes = "This api can be used for getting coordinator and principal for a perticular teacher. "
			+ "https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/30c792b0-6766-4d44-8538-249cabaeda35/", value = "This is Teacher Access For Coordinator And Principal ")
	@SuppressWarnings("unchecked")
	public LMSResponse<Page<AcademicStaffResponseDto>> currentTeacherAccessForCoordinatorORPrincipal(
			@RequestParam("pageNumber") @Min(0) int pageNumber, @RequestParam("pageSize") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<AcademicStaffResponseDto> response = teacherService
				.currentTeacherAccessForCoordinatorORPrincipal(pageNumber, pageSize, schoolId, branchId, teacherId,
						search, active);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Page<TeacherResponseDto>>(), response,
				Translator.toLocale("teacher.get.all.success", null),
				Translator.toLocale("teacher.get.list.failed", null));
	}

	@GetMapping("/school-strength/{teacherId}")
	@ApiOperation(notes = "Assigned subject, grade and section with student count for Principal and coordinator"
			+ "<br> https://xd.adobe.com/view/16cbaf8a-46d0-45aa-889a-3c33e6775baa-4398/screen/2881197b-11e5-4ec5-9778-ddd1c9637bcd/", value = "This is Student Count For Principal And Coordinator")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<GradeAccessInfoResponseDto>> schoolStrength(@PathVariable("teacherId") String teacherId) {
		List<GradeAccessInfoResponseDto> response = teacherService.schoolStrength(teacherId);
		return ResponseHelper.createResponse(new LMSResponse<GradeAccessInfoResponseDto>(), response,
				Translator.toLocale("fetch.principal.students.counted.success", null),
				Translator.toLocale("fetch.principal.students.counted.failed", null));
	}

	@GetMapping("/list/assigned-access")
	@ApiOperation(notes = "List down the assigned access for teacher"
			+ "<br> https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/ef720388-9d9a-4845-a88a-2afccf4a088b/ "
			+ "<br> https://xd.adobe.com/view/9a00808b-99b3-4869-97b9-f631e3f3081c-cf53/screen/1193b8ed-429c-4b6a-b85e-6770609a5683/ <br>"
			+ "", value = "Assigned access of academic staff")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<TeacherAccessResponseDto>> getAllAssignedAccessOfTeacher(
			@RequestParam("teacherId") String teacherId) {
		List<TeacherAccessResponseDto> response = teacherService.getAllAssignedAccessOfTeacher(teacherId);
		return ResponseHelper.createResponse(new LMSResponse<TeacherAccessResponseDto>(), response,
				Translator.toLocale("teacher.list.access.success", null),
				Translator.toLocale("teacher.list.access.failed", null));
	}

	@GetMapping("/student-count")
	@ApiOperation(notes = "This api is used for get the count of student for grade or section.This is feign call, Hidding from Front-end ", value = "This is feign call for Teacher Service ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<Integer> getStudentsCount(@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId) {
		Integer response = teacherService.getStudentsCount(teacherId, schoolId, branchId, subjectId, gradeId, sectionId,
				subtopicId);
		return ResponseHelper.createResponse(new LMSResponse<Integer>(), response,
				Translator.toLocale("fetch.teacher.student.count.success", null),
				Translator.toLocale("fetch.teacher.student.count.failed", null));
	}

	@GetMapping("/student-details")
	@ApiOperation(notes = "This api is used for get the student details for grade or section.This is feign call, Hidding from Front-end ", value = "This is feign call for Teacher Service ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<StudentMinResponseDto>> getStudentDetails(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId) {
		List<StudentMinResponseDto> response = teacherService.getStudentDetails(teacherId, schoolId, branchId,
				subjectId, gradeId, sectionId, subtopicId);
		return ResponseHelper.createResponse(new LMSResponse<StudentMinResponseDto>(), response,
				Translator.toLocale("fetch.teacher.student.details.success", null),
				Translator.toLocale("fetch.teacher.student.details.failed", null));
	}

	@PutMapping("/edit-teacher-access/subjects")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Edit Teacher's Assigned grade, subject, subtopic and section."
			+ "<br> https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/ef720388-9d9a-4845-a88a-2afccf4a088b "
			+ "<br> https://xd.adobe.com/view/c8dbee87-9f00-48b4-b135-2b14214a60dc-9d88/screen/93edfd7a-b71c-42fc-af24-597663a0591f "
			+ "<br>", value = "Principal dashboard Screen NO 17,18 Edit Assigned grade, subject, subtopic and section")
	public LMSResponse<List<TeacherAssignResponse>> updateTeacherAccess(@RequestParam("teacherId") String teacherId,
			@RequestBody TeacherAssignRequest request) {
		List<TeacherAssignResponse> response = teacherService.updateTeacherAccess(teacherId, request);
		return ResponseHelper.createResponse(new LMSResponse<List<TeacherAssignResponse>>(), response,
				Translator.toLocale("edit.teacher.access.subjects.success", null),
				Translator.toLocale("edit.teacher.access.subjects.failed", null));
	}

	@GetMapping("/formative-student-details")
	@ApiOperation(notes = "This api is used for get the formative report student details for section.This is feign call", value = "This is feign call for Teacher Service ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<StudentFormativeMinResponseDto>> getFormativeStudentDetailsFilterBySection(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId) {
		List<StudentFormativeMinResponseDto> response = teacherService
				.getFormativeStudentDetailsFilterBySection(schoolId, branchId, gradeId, sectionId);
		return ResponseHelper.createResponse(new LMSResponse<List<StudentFormativeMinResponseDto>>(), response,
				Translator.toLocale("fetch.teacher.formative.student.details.success", null),
				Translator.toLocale("fetch.teacher.formative.student.details.failed", null));
	}

	@GetMapping("/student-name-details")
	@ApiOperation(notes = "This api is used for get the student name details  ", value = "This is feign call for Teacher Service ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<StudentNameDetails>> getStudentNameDetails(
			@RequestParam(value = "teacherId", required = true) String teacherId,
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId,
			@RequestParam(value = "subjectId", required = true) String subjectId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = false) String sectionId,
			@RequestParam(value = "subtopicId", required = false) String subtopicId) {
		List<StudentNameDetails> response = teacherService.getStudentNameDetails(teacherId, schoolId, branchId,
				subjectId, gradeId, sectionId, subtopicId);
		return ResponseHelper.createResponse(new LMSResponse<List<StudentNameDetails>>(), response,
				Translator.toLocale("student.name.details.success", null),
				Translator.toLocale("student.name.details.failed", null));
	}

	@GetMapping("/for-dashboard")
	@ApiOperation(notes = "Teacher's list for Principal's or Coordinator's dashboard.", value = "Teacher list: Principal's or Coordinator's Dashboard")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<AllTypeUserMinResponseDto>> getAllTeachersForPrincipalOrCoodinator(
			@RequestParam(value = "gradeId", required = false) String gradeId) {
		List<AllTypeUserMinResponseDto> response = teacherService.getAllTeachersForPrincipalOrCoodinator(gradeId);
		return ResponseHelper.createResponse(new LMSResponse<List<AllTypeUserMinResponseDto>>(), response,
				Translator.toLocale("teachers.listing.for.dashboard.success", null),
				Translator.toLocale("teachers.listing.for.dashboard.failed", null));
	}

	@GetMapping("/teacher-feign")
	@ApiOperation(notes = "Feign call for teacher service", value = "This is feign call for Teacher Service ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<TeacherDetailsForFeignResponseDto> getTeacherDetailsForFeign(
			@RequestParam(value = "teacherId", required = true) String teacherId) {
		TeacherDetailsForFeignResponseDto response = teacherService.getTeacherDetailsForFeign(teacherId);
		return ResponseHelper.createResponse(new LMSResponse<TeacherDetailsForFeignResponseDto>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}

	@GetMapping("/teacher-feign-all")
	@ApiOperation(notes = "Feign call for teacher service", value = "This is feign call for Teacher Service to get teachers by teacher ids ", hidden = true)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<TeacherDetailsForFeignResponseDto>> getAllTeacherDetailsForFeign(
			@RequestParam(value = "teacherId", required = true) List<String> teacherIds) {
		List<TeacherDetailsForFeignResponseDto> response = teacherService.getAllTeacherDetailsForFeign(teacherIds);
		return ResponseHelper.createResponse(new LMSResponse<List<TeacherDetailsForFeignResponseDto>>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}

	@Lazy
	@GetMapping("/min-details/subjective-paper")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Min details of teacher to subjective paper", value = "Feign to content-service", hidden = true)
	public LMSResponse<UserMinDetails> getUserMiniDetails(
			@RequestParam(value = "id", required = true) @NotBlank(message = Constants.MANDATORY_FIELD) String id) {
		UserMinDetails response = teacherService.getUserMiniDetails(id);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<UserMinDetails>(), response,
				Translator.toLocale("user.get.by.id.success", null),
				Translator.toLocale("user.get.by.id.failed", null));
	}

	@Lazy
	@GetMapping("/min-all-details/subjective-paper")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Min details of teacher to subjective paper, for the pagination", value = "Feign to content-service", hidden = true)
	public LMSResponse<List<UserMinDetails>> getAllUserMiniDetails(
			@RequestParam(value = "ids", required = true) @NotEmpty(message = Constants.MANDATORY_FIELD) List<String> ids) {
		List<UserMinDetails> response = teacherService.getAllUserMiniDetails(ids);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<UserMinDetails>>(), response,
				Translator.toLocale("user.get.all.success", null), Translator.toLocale("user.get.all.failed", null));
	}

	@Lazy
	@SuppressWarnings("all")
	@GetMapping("/getTeacherIds")
	public LMSResponse<List<String>> getteacherIdsListDetails(
			@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "gradeId", required = true) String gradeId,
			@RequestParam(value = "sectionId", required = true) String sectionId) {
		List<String> response = teacherService.getteacherIdsListDetails(schoolId, gradeId, sectionId);
		return ResponseHelper.createResponse(new LMSResponse<List<String>>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}

	@PutMapping("/update-sectionId/{oldSectionId}/{newSectionId}")
	@Lazy
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> updateSectionIds(@PathVariable String oldSectionId, @PathVariable String newSectionId) {
		Boolean response = teacherService.updateSectionIds(oldSectionId, newSectionId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<Boolean>(), response,
				Translator.toLocale("user.get.all.success", null), Translator.toLocale("user.get.all.failed", null));
	}
	
	/*@GetMapping("/getTeachersAndAssignedSections")
	@ApiOperation(notes = "Feign call for master service used purpose", value = "This is feign call for master Service to get teachers by teacher id and sectionId details ", hidden = false)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> getTeachersAndAssignedSections(){
		List<String> response = teacherService.getTeachersAndAssignedSections();
		return ResponseHelper.createResponse(new LMSResponse<List<String>>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}*/

	@Lazy
	@PostMapping("/send-register-email")
	public LMSResponse<?> sendRegisterationEmail(@RequestBody EmailUserDto emailUserDto) {
		try {
			log.debug("requestMap: " + emailUserDto);


			String password = CommonUtilities.generatePassayPassword();

			String userName = (emailUserDto.getUserName());
			Users user = usersRepository.findByUserName(userName);

			user.setPassword(CommonUtilities.passwordEncryptor(password));
			user = usersRepository.save(user);

			CreateUserEmailRequestDto requestDto = new CreateUserEmailRequestDto();
			requestDto.setUserId(user.getId());
			requestDto.setUserName(user.getUserName());
			requestDto.setPassword(password);

			requestDto.setFirstName(user.getFirstName());
			requestDto.setToEmail(user.getEmail());

			String resetPasswordUrl = "https://azvasa.online/#/setPass";
			requestDto.setForgotPasswordEmailLink(resetPasswordUrl + "?userId=" + user.getId());
			requestDto.setBaseFEUrl(resetPasswordUrl.substring(0, resetPasswordUrl.indexOf("#")));

			requestDto.setLmsEnv("DEV");
			requestDto.setTypeOfEmailSend("CREATE");
			requestDto.setRoleName(emailUserDto.getUserType());




			log.info("Sending email started...");

			LMSResponse<EmailResponseDto> lmsResponse =  notificationFeignClient.userCreated(requestDto);
			return  ResponseHelper.createResponse(new LMSResponse<List<String>>(), lmsResponse,"success", "faiil");
		} catch (Exception e) {
		e.printStackTrace();
			return  ResponseHelper.createResponse(new LMSResponse<List<String>>(), e.getMessage(),"success", "faiil");
		}

	}
	
	/*@GetMapping("/getTeachersAndAssignedSections")
	@ApiOperation(notes = "Feign call for master service used purpose", value = "This is feign call for master Service to get teachers by teacher id and sectionId details ", hidden = false)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<String>> getTeachersAndAssignedSections(@RequestParam("gradeId") String gradeId,@RequestParam("subjectId") String subjectId){
		List<String> response = teacherService.getTeachersAndAssignedSections(gradeId,subjectId);
		return ResponseHelper.createResponse(new LMSResponse<List<String>>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}*/

	
	@GetMapping("/getTeachersAndAssignedSections")
	@ApiOperation(notes = "Feign call for master service used purpose", value = "This is feign call for master Service to get teachers by teacher id and sectionId details ", hidden = false)
	@SuppressWarnings("unchecked")
	public LMSResponse<List<TeacherAssignmentResponse>> getTeachersAndAssignedSections(@RequestParam("gradeId") String gradeId,@RequestParam("subjectId") String subjectId){
		List<TeacherAssignmentResponse> response = teacherService.getTeachersAndAssignedSections(gradeId,subjectId);
		return ResponseHelper.createResponse(new LMSResponse<List<TeacherAssignmentResponse>>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}
	
	@GetMapping("/getTeacher/username/{username}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Teacher by User Name", value = "Chat Screen")
	public LMSResponse<TeacherResponseDto> getTeacherByUserName(@PathVariable("username") String username) {
		TeacherResponseDto response = teacherService.getTeacherByUserName(username);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<StudentsResponseDto>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}
	
	@GetMapping("/getAssignedTeacherList")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get Teacher list by User Name", value = "Chat Screen")
	public LMSResponse<List<TeacherResponseDto>> getAssignedTeacherList(@RequestParam(value = "gradeId", required = true) String gradeId) {
		List<TeacherResponseDto> response = teacherService.getAssignedTeacherList(gradeId);
		return ResponseHelper.responseForGetOrFeign(new LMSResponse<List<TeacherResponseDto>>(), response,
				Translator.toLocale("teacher.details.success", null),
				Translator.toLocale("teacher.details.failed", null));
	}

}
