package com.lms.userservice.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.lms.userservice.component.Translator;
import com.lms.userservice.enums.FileTypes;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.response.dto.BulkUploadResponseDto;
import com.lms.userservice.service.FileUploadService;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

/**
 * Controller only used to upload the csv/excel file and read its content
 * 
 * <AUTHOR> C Achari
 * @since 0.0.1
 */
@RestController
@RequestMapping(value = "/v1/api/user/file-upload")
public class FileUploadController {

	@Autowired
	private FileUploadService fileUploadService;

	@PostMapping()
	@SuppressWarnings("all")
	@Lazy
	@ApiOperation(notes = "Used to upload a CSV File", value = "Upload student or teacher via CSV")
	public LMSResponse<BulkUploadResponseDto> uploadFile(@RequestParam(name = "file") MultipartFile file,
			@RequestParam(name = "fileTypes") FileTypes fileTypes) {
		BulkUploadResponseDto response = fileUploadService.uploadFile(file, fileTypes);
		return ResponseHelper.createResponse(new LMSResponse<BulkUploadResponseDto>(), response,
				Translator.toLocale("csv.upload.success", null), Translator.toLocale("csv.upload.failed", null));
	}
}
