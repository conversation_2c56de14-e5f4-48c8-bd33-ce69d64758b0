package com.lms.userservice.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.tomcat.jni.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.userservice.component.Translator;
import com.lms.userservice.dto.readingpassport.response.dto.Grade;
import com.lms.userservice.entity.Teachers;
import com.lms.userservice.entity.Users;
import com.lms.userservice.enums.ErrorCodes;
import com.lms.userservice.exception.USException;
import com.lms.userservice.feign.master.GradesResponseDto;
import com.lms.userservice.feign.master.MastersFeignClient;
import com.lms.userservice.feign.master.SectionsResponseDto;
import com.lms.userservice.model.LMSResponse;
import com.lms.userservice.repository.BranchPlanMappingsRepository;
import com.lms.userservice.repository.UsersRepository;
import com.lms.userservice.request.dto.Access;
import com.lms.userservice.request.dto.Grades;
import com.lms.userservice.request.dto.ReadingPassportRequest;
import com.lms.userservice.request.dto.ReadingPassportResponse;
import com.lms.userservice.request.dto.ReadingPassportResponseDto;
import com.lms.userservice.request.dto.Sections;
import com.lms.userservice.response.dto.TeacherResponseDto;
import com.lms.userservice.response.dto.UserRolesResponseDto;
import com.lms.userservice.service.GradeSectionMappingService;
import com.lms.userservice.service.ReadingPassportService;
import com.lms.userservice.service.TeacherService;
import com.lms.userservice.util.JwtUtil;
import com.lms.userservice.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequestMapping(value = "/v1/api/user/readingPassportAccess")
public class ReadingPassportController {
	
	@Autowired
	ReadingPassportService readingPassportService;
	
	@Autowired
	private JwtUtil jwtUtil;
	
	@Autowired
	TeacherService teacherService;
	
	@Autowired
	GradeSectionMappingService gradeSectionMappingService;
	
	@Autowired
	private BranchPlanMappingsRepository branchPlanRepo;

	@Autowired
	private MastersFeignClient mastersFeignClient;

	@Autowired
	private UsersRepository usersRepository;

	@PostMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Create or Update Reading Passport", value = "Create or Update Reading Passport")
	public LMSResponse<ReadingPassportResponse> createOrUpdateReadingPassport(@PathVariable("id") String id,
			@RequestBody @Valid ReadingPassportRequest dto) {
		ReadingPassportResponse response = readingPassportService.createOrUpdateReadingPassport(id,dto);
		return ResponseHelper.createResponse(new LMSResponse<ReadingPassportResponse>(), response,
				Translator.toLocale("readingpassport.create.success", null),
				Translator.toLocale("readingpassport.create.failed", null));
	}
	
	@GetMapping("/getReadingPassport/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Retrieve Reading Passport", value = "Retrieve Reading Passport")
	public LMSResponse<ReadingPassportResponse> getReadingPassport(@PathVariable("id") String id) {
		ReadingPassportResponse response = readingPassportService.getReadingPassport(id);
		return ResponseHelper.createResponse(new LMSResponse<ReadingPassportResponse>(), response,
				Translator.toLocale("readingpassport.retrieved.success", null),
				Translator.toLocale("readingpassport.retrieved.failed", null));
	}

	@GetMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Retrieve Reading Passport", value = "Retrieve Reading Passport")
	public LMSResponse<ReadingPassportResponseDto> getReadingPassportById(@PathVariable("id") String id) {
		ReadingPassportResponse response = readingPassportService.getReadingPassport(id);
		ReadingPassportResponseDto dto = new ReadingPassportResponseDto();
		dto.setId(response.getId());
		dto.setUserId(response.getUserId());
		dto.setUserType(response.getUserType());
		dto.setLibrarian(response.isLibrarian());
		if(response.isLibrarian()) {
			String userName = jwtUtil.currentLoginUser();
			TeacherResponseDto teacher =null;
			try{
				 teacher = teacherService.getTeacherByUserName(userName);
			}catch(Exception e) {
				// log.error(ExceptionUtils.getStackTrace(e));
				System.out.println("Admin Auth token found");
				Users userdata =usersRepository.getById(response.getUserId()); 
				 teacher = teacherService.getTeacherByUserName(userdata.getUserName());
			}
			
			List<GradesResponseDto> grades = gradeSectionMappingService.getAllGradesBySchoolAndBranch(teacher.getBranch(), teacher.getSchool());
			Access access=new Access();
		
			access.setGrades(new ArrayList<>());
			for (GradesResponseDto grade : grades) {
				List<SectionsResponseDto> sections = gradeSectionMappingService.getAllSectionByGradesSchoolAndBranch("",
						grade.getId(), teacher.getBranch(), teacher.getSchool());
				
				Grades grade1 = new Grades();
				grade1.setGradeId(grade.getId());
				grade1.setGrade(grade.getGrade());
				grade1.setSections(new ArrayList<>());
				
				for (SectionsResponseDto sectionsResponseDto : sections) {
					Sections s = new Sections();
					s.setSectionId(sectionsResponseDto.getId());
					s.setSection(sectionsResponseDto.getSection());					
					grade1.getSections().add(s);
					
				}
				access.getGrades().add(grade1);
				
			}
			
			dto.setAccess(access);
		}
		else {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			String accessstr=response.getAccess();
			accessstr = accessstr.replace("\\", "");
			accessstr=accessstr.substring(1,accessstr.length()-1);
	        log.info("Access--->"+accessstr);
			Access access = objectMapper.readValue(accessstr, Access.class);
			
			// Retrieve the plan master grades @pankaj to handle plan template
			String planId = branchPlanRepo.findPlanIdByBranchId(response.getBranchId());
			LMSResponse<List<String>> planMasterResponse = mastersFeignClient.assignedGradesFromPlan(planId);

			// Check if planMasterResponse is available and has data
			if (planMasterResponse != null && !CollectionUtils.isEmpty(planMasterResponse.getData())) {
				// Filter the grades list based on planMasterResponse
				List<Grades> filteredGrades = access.getGrades().stream()
						.filter(grade -> planMasterResponse.getData().contains(grade.getGradeId()))
						.collect(Collectors.toList());
				access.setGrades(filteredGrades);
				log.info("Filtered grades: " + filteredGrades);
			}
			dto.setAccess(access);
		} catch (Exception e) {

			e.printStackTrace();
		}
		}

		return ResponseHelper.createResponse(new LMSResponse<ReadingPassportResponseDto>(), dto,
				Translator.toLocale("readingpassport.retrieved.success", null),
				Translator.toLocale("readingpassport.retrieved.failed", null));
	}
	
	@GetMapping("getAllAccessBySchoolIdAndBranchId")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Retrieve Reading Passport", value = "Retrieve Reading Passport")
	public LMSResponse<List<ReadingPassportResponse>> getAllAccessBySchoolIdAndBranchId(@RequestParam(value = "schoolId", required = true) String schoolId,
			@RequestParam(value = "branchId", required = true) String branchId) {
		List<ReadingPassportResponse> response = readingPassportService.getAllAccessBySchoolIdAndBranchId(schoolId,branchId);
		return ResponseHelper.createResponse(new LMSResponse<List<ReadingPassportResponse>>(), response,
				Translator.toLocale("readingpassport.retrieved.success", null),
				Translator.toLocale("readingpassport.retrieved.failed", null));
	}

}
