package com.lms.userservice.mapper;

import com.lms.userservice.entity.Schools;
import com.lms.userservice.projection.SchoolBranchesProjection;
import com.lms.userservice.response.dto.SchoolBranchesResponseDto;
import com.lms.userservice.response.dto.SchoolResponseDto;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T11:43:22+0530",
    comments = "version: 1.4.2.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250722-2055, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class SchoolMapperImpl implements SchoolMapper {

    @Override
    public SchoolResponseDto mapSchoolModelToResponseDto(Schools schools) {
        if ( schools == null ) {
            return null;
        }

        SchoolResponseDto schoolResponseDto = new SchoolResponseDto();

        schoolResponseDto.setActive( schools.isActive() );
        if ( schools.getCityId() != null ) {
            schoolResponseDto.setCityId( schools.getCityId() );
        }
        if ( schools.getCode() != null ) {
            schoolResponseDto.setCode( schools.getCode() );
        }
        if ( schools.getId() != null ) {
            schoolResponseDto.setId( schools.getId() );
        }
        if ( schools.getLogoUrl() != null ) {
            schoolResponseDto.setLogoUrl( schools.getLogoUrl() );
        }
        if ( schools.getName() != null ) {
            schoolResponseDto.setName( schools.getName() );
        }
        if ( schools.getPhoneNumber() != null ) {
            schoolResponseDto.setPhoneNumber( schools.getPhoneNumber() );
        }
        if ( schools.getPocEmail() != null ) {
            schoolResponseDto.setPocEmail( schools.getPocEmail() );
        }
        if ( schools.getSignatoryName() != null ) {
            schoolResponseDto.setSignatoryName( schools.getSignatoryName() );
        }
        if ( schools.getSignatoryRole() != null ) {
            schoolResponseDto.setSignatoryRole( schools.getSignatoryRole() );
        }
        if ( schools.getWebsite() != null ) {
            schoolResponseDto.setWebsite( schools.getWebsite() );
        }

        return schoolResponseDto;
    }

    @Override
    public SchoolBranchesResponseDto mapSchoolBranchesProjectionToDto(SchoolBranchesProjection response) {
        if ( response == null ) {
            return null;
        }

        SchoolBranchesResponseDto schoolBranchesResponseDto = new SchoolBranchesResponseDto();

        if ( response.getSchoolActive() != null ) {
            schoolBranchesResponseDto.setActive( response.getSchoolActive() );
        }
        if ( response.getSchoolId() != null ) {
            schoolBranchesResponseDto.setSchoolId( response.getSchoolId() );
        }
        if ( response.getSchoolName() != null ) {
            schoolBranchesResponseDto.setSchoolName( response.getSchoolName() );
        }

        return schoolBranchesResponseDto;
    }
}
