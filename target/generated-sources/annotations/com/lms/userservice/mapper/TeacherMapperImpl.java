package com.lms.userservice.mapper;

import com.lms.userservice.entity.AssignTeacher;
import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Teachers;
import com.lms.userservice.enums.AcademicStaffProfile;
import com.lms.userservice.enums.Gender;
import com.lms.userservice.projection.TeacherAssignProjection;
import com.lms.userservice.request.dto.TeacherAssignRequest;
import com.lms.userservice.request.dto.TeacherRequestDto;
import com.lms.userservice.request.dto.TeachersSelfRegRequestDto;
import com.lms.userservice.response.dto.TeacherAssignResponse;
import com.lms.userservice.response.dto.TeacherResponseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T11:43:22+0530",
    comments = "version: 1.4.2.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250722-2055, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TeacherMapperImpl implements TeacherMapper {

    @Override
    public TeacherResponseDto mapTeacherEntityToResponseDto(Teachers teacher) {
        if ( teacher == null ) {
            return null;
        }

        TeacherResponseDto teacherResponseDto = new TeacherResponseDto();

        String id = teacherSchoolsId( teacher );
        if ( id != null ) {
            teacherResponseDto.setSchool( id );
        }
        String name = teacherSchoolsName( teacher );
        if ( name != null ) {
            teacherResponseDto.setSchoolName( name );
        }
        String code = teacherSchoolsCode( teacher );
        if ( code != null ) {
            teacherResponseDto.setSchoolCode( code );
        }
        String id1 = teacherBranchesId( teacher );
        if ( id1 != null ) {
            teacherResponseDto.setBranch( id1 );
        }
        String name1 = teacherBranchesName( teacher );
        if ( name1 != null ) {
            teacherResponseDto.setBranchName( name1 );
        }
        if ( teacher.getAcademicStaffProfile() != null ) {
            teacherResponseDto.setAcademicStaffProfile( teacher.getAcademicStaffProfile().name() );
        }
        teacherResponseDto.setActive( teacher.isActive() );
        if ( teacher.getAddress() != null ) {
            teacherResponseDto.setAddress( teacher.getAddress() );
        }
        if ( teacher.getCoordinatorTypeId() != null ) {
            teacherResponseDto.setCoordinatorTypeId( teacher.getCoordinatorTypeId() );
        }
        if ( teacher.getDesignation() != null ) {
            teacherResponseDto.setDesignation( teacher.getDesignation() );
        }
        if ( teacher.getDob() != null ) {
            teacherResponseDto.setDob( teacher.getDob() );
        }
        if ( teacher.getDocumentUrl() != null ) {
            teacherResponseDto.setDocumentUrl( teacher.getDocumentUrl() );
        }
        if ( teacher.getEmail() != null ) {
            teacherResponseDto.setEmail( teacher.getEmail() );
        }
        if ( teacher.getFirstName() != null ) {
            teacherResponseDto.setFirstName( teacher.getFirstName() );
        }
        if ( teacher.getGender() != null ) {
            teacherResponseDto.setGender( teacher.getGender().name() );
        }
        if ( teacher.getId() != null ) {
            teacherResponseDto.setId( teacher.getId() );
        }
        if ( teacher.getJoinDate() != null ) {
            teacherResponseDto.setJoinDate( teacher.getJoinDate() );
        }
        if ( teacher.getLastName() != null ) {
            teacherResponseDto.setLastName( teacher.getLastName() );
        }
        if ( teacher.getMobile() != null ) {
            teacherResponseDto.setMobile( teacher.getMobile() );
        }
        if ( teacher.getPreviousWorkExp() != null ) {
            teacherResponseDto.setPreviousWorkExp( teacher.getPreviousWorkExp() );
        }
        if ( teacher.getRoleId() != null ) {
            teacherResponseDto.setRoleId( teacher.getRoleId() );
        }
        if ( teacher.getUserName() != null ) {
            teacherResponseDto.setUserName( teacher.getUserName() );
        }

        return teacherResponseDto;
    }

    @Override
    public List<TeacherResponseDto> mapTeacherEntityToResponseDto(List<Teachers> teachers) {
        if ( teachers == null ) {
            return null;
        }

        List<TeacherResponseDto> list = new ArrayList<TeacherResponseDto>( teachers.size() );
        for ( Teachers teachers1 : teachers ) {
            list.add( mapTeacherEntityToResponseDto( teachers1 ) );
        }

        return list;
    }

    @Override
    public Teachers toEntityFromRequest(TeacherRequestDto request) {
        if ( request == null ) {
            return null;
        }

        Teachers teachers = new Teachers();

        if ( request.getFirstName() != null ) {
            teachers.setFirstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            teachers.setLastName( request.getLastName() );
        }
        if ( request.getEmail() != null ) {
            teachers.setEmail( request.getEmail() );
        }
        if ( request.getMobile() != null ) {
            teachers.setMobile( request.getMobile() );
        }
        if ( request.getGender() != null ) {
            teachers.setGender( Enum.valueOf( Gender.class, request.getGender() ) );
        }
        if ( request.getDob() != null ) {
            teachers.setDob( request.getDob() );
        }
        if ( request.getJoinDate() != null ) {
            teachers.setJoinDate( request.getJoinDate() );
        }
        if ( request.getPreviousWorkExp() != null ) {
            teachers.setPreviousWorkExp( request.getPreviousWorkExp() );
        }
        if ( request.getDesignation() != null ) {
            teachers.setDesignation( request.getDesignation() );
        }
        if ( request.getDocumentUrl() != null ) {
            teachers.setDocumentUrl( request.getDocumentUrl() );
        }
        if ( request.getAcademicStaffProfile() != null ) {
            teachers.setAcademicStaffProfile( Enum.valueOf( AcademicStaffProfile.class, request.getAcademicStaffProfile() ) );
        }
        if ( request.getCoordinatorTypeId() != null ) {
            teachers.setCoordinatorTypeId( request.getCoordinatorTypeId() );
        }
        if ( request.getAddress() != null ) {
            teachers.setAddress( request.getAddress() );
        }

        teachers.setSchools( MappingHelper.getSchoolById(request.getSchool()) );
        teachers.setBranches( MappingHelper.getBranchById(request.getBranch()) );

        return teachers;
    }

    @Override
    public Teachers forUpdateToEntityFromRequest(TeacherRequestDto request, String id) {
        if ( request == null && id == null ) {
            return null;
        }

        Teachers teachers = new Teachers();

        if ( request != null ) {
            if ( request.getFirstName() != null ) {
                teachers.setFirstName( request.getFirstName() );
            }
            if ( request.getLastName() != null ) {
                teachers.setLastName( request.getLastName() );
            }
            if ( request.getEmail() != null ) {
                teachers.setEmail( request.getEmail() );
            }
            if ( request.getMobile() != null ) {
                teachers.setMobile( request.getMobile() );
            }
            if ( request.getGender() != null ) {
                teachers.setGender( Enum.valueOf( Gender.class, request.getGender() ) );
            }
            if ( request.getDob() != null ) {
                teachers.setDob( request.getDob() );
            }
            if ( request.getJoinDate() != null ) {
                teachers.setJoinDate( request.getJoinDate() );
            }
            if ( request.getPreviousWorkExp() != null ) {
                teachers.setPreviousWorkExp( request.getPreviousWorkExp() );
            }
            if ( request.getDesignation() != null ) {
                teachers.setDesignation( request.getDesignation() );
            }
            if ( request.getDocumentUrl() != null ) {
                teachers.setDocumentUrl( request.getDocumentUrl() );
            }
            if ( request.getAcademicStaffProfile() != null ) {
                teachers.setAcademicStaffProfile( Enum.valueOf( AcademicStaffProfile.class, request.getAcademicStaffProfile() ) );
            }
            if ( request.getCoordinatorTypeId() != null ) {
                teachers.setCoordinatorTypeId( request.getCoordinatorTypeId() );
            }
            if ( request.getAddress() != null ) {
                teachers.setAddress( request.getAddress() );
            }
        }
        if ( id != null ) {
            teachers.setId( id );
        }
        teachers.setSchools( MappingHelper.getSchoolById(request.getSchool()) );
        teachers.setBranches( MappingHelper.getBranchById(request.getBranch()) );

        return teachers;
    }

    @Override
    public Teachers selfRegistrationToEntityFromRequest(TeachersSelfRegRequestDto request) {
        if ( request == null ) {
            return null;
        }

        Teachers teachers = new Teachers();

        if ( request.getAcademicStaffProfile() != null ) {
            teachers.setAcademicStaffProfile( Enum.valueOf( AcademicStaffProfile.class, request.getAcademicStaffProfile() ) );
        }
        if ( request.getAddress() != null ) {
            teachers.setAddress( request.getAddress() );
        }
        if ( request.getCoordinatorTypeId() != null ) {
            teachers.setCoordinatorTypeId( request.getCoordinatorTypeId() );
        }
        if ( request.getDesignation() != null ) {
            teachers.setDesignation( request.getDesignation() );
        }
        if ( request.getDob() != null ) {
            teachers.setDob( request.getDob() );
        }
        if ( request.getDocumentUrl() != null ) {
            teachers.setDocumentUrl( request.getDocumentUrl() );
        }
        if ( request.getEmail() != null ) {
            teachers.setEmail( request.getEmail() );
        }
        if ( request.getFirstName() != null ) {
            teachers.setFirstName( request.getFirstName() );
        }
        if ( request.getGender() != null ) {
            teachers.setGender( Enum.valueOf( Gender.class, request.getGender() ) );
        }
        if ( request.getJoinDate() != null ) {
            teachers.setJoinDate( request.getJoinDate() );
        }
        if ( request.getLastName() != null ) {
            teachers.setLastName( request.getLastName() );
        }
        if ( request.getMobile() != null ) {
            teachers.setMobile( request.getMobile() );
        }
        if ( request.getPreviousWorkExp() != null ) {
            teachers.setPreviousWorkExp( request.getPreviousWorkExp() );
        }

        teachers.setSchools( MappingHelper.getSchoolById(request.getSchool()) );
        teachers.setBranches( MappingHelper.getBranchById(request.getBranch()) );

        return teachers;
    }

    @Override
    public AssignTeacher mapAssignTeacherRequestToEntity(TeacherAssignRequest request, AssignTeacher assignTeacher) {
        if ( request == null ) {
            return null;
        }

        if ( request.getGradeId() != null ) {
            assignTeacher.setGradeId( request.getGradeId() );
        }
        else {
            assignTeacher.setGradeId( null );
        }
        if ( request.getSubjectId() != null ) {
            assignTeacher.setSubjectId( request.getSubjectId() );
        }
        else {
            assignTeacher.setSubjectId( null );
        }

        return assignTeacher;
    }

    @Override
    public TeacherAssignResponse mapAssignTeacherEntityToResponse(AssignTeacher assignTeacher) {
        if ( assignTeacher == null ) {
            return null;
        }

        TeacherAssignResponse teacherAssignResponse = new TeacherAssignResponse();

        teacherAssignResponse.setActive( assignTeacher.isActive() );
        if ( assignTeacher.getGradeId() != null ) {
            teacherAssignResponse.setGradeId( assignTeacher.getGradeId() );
        }
        if ( assignTeacher.getId() != null ) {
            teacherAssignResponse.setId( assignTeacher.getId() );
        }
        if ( assignTeacher.getSectionId() != null ) {
            teacherAssignResponse.setSectionId( assignTeacher.getSectionId() );
        }
        if ( assignTeacher.getSubjectId() != null ) {
            teacherAssignResponse.setSubjectId( assignTeacher.getSubjectId() );
        }
        if ( assignTeacher.getSubtopicId() != null ) {
            teacherAssignResponse.setSubtopicId( assignTeacher.getSubtopicId() );
        }

        return teacherAssignResponse;
    }

    @Override
    public List<TeacherAssignResponse> mapAssignTeacherEntityToResponse(List<AssignTeacher> assignTeacher) {
        if ( assignTeacher == null ) {
            return null;
        }

        List<TeacherAssignResponse> list = new ArrayList<TeacherAssignResponse>( assignTeacher.size() );
        for ( AssignTeacher assignTeacher1 : assignTeacher ) {
            list.add( mapAssignTeacherEntityToResponse( assignTeacher1 ) );
        }

        return list;
    }

    @Override
    public List<TeacherAssignResponse> mapTeacherAssignProjectionToResponse(List<TeacherAssignProjection> projectionList) {
        if ( projectionList == null ) {
            return null;
        }

        List<TeacherAssignResponse> list = new ArrayList<TeacherAssignResponse>( projectionList.size() );
        for ( TeacherAssignProjection teacherAssignProjection : projectionList ) {
            list.add( mapTeacherAssignProjectionToResponse( teacherAssignProjection ) );
        }

        return list;
    }

    @Override
    public TeacherAssignResponse mapTeacherAssignProjectionToResponse(TeacherAssignProjection projection) {
        if ( projection == null ) {
            return null;
        }

        TeacherAssignResponse teacherAssignResponse = new TeacherAssignResponse();

        if ( projection.getGradeId() != null ) {
            teacherAssignResponse.setGradeId( projection.getGradeId() );
        }
        if ( projection.getSectionId() != null ) {
            teacherAssignResponse.setSectionId( projection.getSectionId() );
        }
        if ( projection.getSubjectId() != null ) {
            teacherAssignResponse.setSubjectId( projection.getSubjectId() );
        }

        return teacherAssignResponse;
    }

    private String teacherSchoolsId(Teachers teachers) {
        if ( teachers == null ) {
            return null;
        }
        Schools schools = teachers.getSchools();
        if ( schools == null ) {
            return null;
        }
        String id = schools.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String teacherSchoolsName(Teachers teachers) {
        if ( teachers == null ) {
            return null;
        }
        Schools schools = teachers.getSchools();
        if ( schools == null ) {
            return null;
        }
        String name = schools.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String teacherSchoolsCode(Teachers teachers) {
        if ( teachers == null ) {
            return null;
        }
        Schools schools = teachers.getSchools();
        if ( schools == null ) {
            return null;
        }
        String code = schools.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String teacherBranchesId(Teachers teachers) {
        if ( teachers == null ) {
            return null;
        }
        Branches branches = teachers.getBranches();
        if ( branches == null ) {
            return null;
        }
        String id = branches.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String teacherBranchesName(Teachers teachers) {
        if ( teachers == null ) {
            return null;
        }
        Branches branches = teachers.getBranches();
        if ( branches == null ) {
            return null;
        }
        String name = branches.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }
}
