package com.lms.userservice.mapper;

import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.request.dto.SchoolRequestDto;
import com.lms.userservice.response.dto.BranchResponseDto;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T11:43:22+0530",
    comments = "version: 1.4.2.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250722-2055, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class BranchMapperImpl implements BranchMapper {

    @Override
    public BranchResponseDto mapBranchModelToResponseDto(Branches branches) {
        if ( branches == null ) {
            return null;
        }

        BranchResponseDto branchResponseDto = new BranchResponseDto();

        branchResponseDto.setActive( branches.isActive() );
        if ( branches.getBoardId() != null ) {
            branchResponseDto.setBoardId( branches.getBoardId() );
        }
        if ( branches.getBranchCode() != null ) {
            branchResponseDto.setBranchCode( branches.getBranchCode() );
        }
        if ( branches.getCityId() != null ) {
            branchResponseDto.setCityId( branches.getCityId() );
        }
        if ( branches.getId() != null ) {
            branchResponseDto.setId( branches.getId() );
        }
        if ( branches.getLocality() != null ) {
            branchResponseDto.setLocality( branches.getLocality() );
        }
        if ( branches.getLogoUrl() != null ) {
            branchResponseDto.setLogoUrl( branches.getLogoUrl() );
        }
        if ( branches.getName() != null ) {
            branchResponseDto.setName( branches.getName() );
        }
        if ( branches.getPhoneNumber() != null ) {
            branchResponseDto.setPhoneNumber( branches.getPhoneNumber() );
        }
        if ( branches.getPocEmail() != null ) {
            branchResponseDto.setPocEmail( branches.getPocEmail() );
        }
        if ( branches.getTestBranch() != null ) {
            branchResponseDto.setTestBranch( branches.getTestBranch() );
        }

        return branchResponseDto;
    }

    @Override
    public Branches mapFromSchoolDtoToBranches(SchoolRequestDto source) {
        if ( source == null ) {
            return null;
        }

        Branches branches = new Branches();

        if ( source.getBoardId() != null ) {
            branches.setBoardId( source.getBoardId() );
        }
        if ( source.getCityId() != null ) {
            branches.setCityId( source.getCityId() );
        }
        if ( source.getLocality() != null ) {
            branches.setLocality( source.getLocality() );
        }
        if ( source.getLogoUrl() != null ) {
            branches.setLogoUrl( source.getLogoUrl() );
        }
        if ( source.getName() != null ) {
            branches.setName( source.getName() );
        }
        if ( source.getPhoneNumber() != null ) {
            branches.setPhoneNumber( source.getPhoneNumber() );
        }
        if ( source.getPocEmail() != null ) {
            branches.setPocEmail( source.getPocEmail() );
        }

        return branches;
    }

    @Override
    public Branches mapFromSchoolsToBranches(Schools schools, Branches branches) {
        if ( schools == null ) {
            return null;
        }

        branches.setActive( schools.isActive() );
        if ( schools.getCreatedAt() != null ) {
            branches.setCreatedAt( schools.getCreatedAt() );
        }
        else {
            branches.setCreatedAt( null );
        }
        if ( schools.getCreatedBy() != null ) {
            branches.setCreatedBy( schools.getCreatedBy() );
        }
        else {
            branches.setCreatedBy( null );
        }
        branches.setDeleted( schools.isDeleted() );
        if ( schools.getLastModifiedBy() != null ) {
            branches.setLastModifiedBy( schools.getLastModifiedBy() );
        }
        else {
            branches.setLastModifiedBy( null );
        }
        if ( schools.getModifiedAt() != null ) {
            branches.setModifiedAt( schools.getModifiedAt() );
        }
        else {
            branches.setModifiedAt( null );
        }
        if ( schools.getBoardId() != null ) {
            branches.setBoardId( schools.getBoardId() );
        }
        else {
            branches.setBoardId( null );
        }
        if ( schools.getCityId() != null ) {
            branches.setCityId( schools.getCityId() );
        }
        else {
            branches.setCityId( null );
        }
        if ( schools.getLocality() != null ) {
            branches.setLocality( schools.getLocality() );
        }
        else {
            branches.setLocality( null );
        }
        if ( schools.getLogoUrl() != null ) {
            branches.setLogoUrl( schools.getLogoUrl() );
        }
        else {
            branches.setLogoUrl( null );
        }
        if ( schools.getName() != null ) {
            branches.setName( schools.getName() );
        }
        else {
            branches.setName( null );
        }
        if ( schools.getPhoneNumber() != null ) {
            branches.setPhoneNumber( schools.getPhoneNumber() );
        }
        else {
            branches.setPhoneNumber( null );
        }
        if ( schools.getPocEmail() != null ) {
            branches.setPocEmail( schools.getPocEmail() );
        }
        else {
            branches.setPocEmail( null );
        }

        return branches;
    }
}
