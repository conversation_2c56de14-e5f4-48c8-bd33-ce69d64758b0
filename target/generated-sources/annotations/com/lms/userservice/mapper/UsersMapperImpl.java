package com.lms.userservice.mapper;

import com.lms.userservice.entity.Users;
import com.lms.userservice.request.dto.AdminUsersRequestDto;
import com.lms.userservice.response.dto.AdminUsersResponseDto;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T11:43:22+0530",
    comments = "version: 1.4.2.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250722-2055, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class UsersMapperImpl implements UsersMapper {

    @Override
    public Users fromAdminUserRequestToEntity(AdminUsersRequestDto request) {
        if ( request == null ) {
            return null;
        }

        Users users = new Users();

        if ( request.getId() != null ) {
            users.setId( request.getId() );
        }
        if ( request.getEmail() != null ) {
            users.setEmail( request.getEmail() );
        }
        if ( request.getFirstName() != null ) {
            users.setFirstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            users.setLastName( request.getLastName() );
        }
        if ( request.getPhoneNumber() != null ) {
            users.setPhoneNumber( request.getPhoneNumber() );
        }

        return users;
    }

    @Override
    public AdminUsersResponseDto fromEntityToAdminUsersResponse(Users users) {
        if ( users == null ) {
            return null;
        }

        AdminUsersResponseDto adminUsersResponseDto = new AdminUsersResponseDto();

        adminUsersResponseDto.setActive( users.isActive() );
        if ( users.getEmail() != null ) {
            adminUsersResponseDto.setEmail( users.getEmail() );
        }
        if ( users.getFirstName() != null ) {
            adminUsersResponseDto.setFirstName( users.getFirstName() );
        }
        if ( users.getId() != null ) {
            adminUsersResponseDto.setId( users.getId() );
        }
        if ( users.getLastName() != null ) {
            adminUsersResponseDto.setLastName( users.getLastName() );
        }
        if ( users.getPassword() != null ) {
            adminUsersResponseDto.setPassword( users.getPassword() );
        }
        if ( users.getPhoneNumber() != null ) {
            adminUsersResponseDto.setPhoneNumber( users.getPhoneNumber() );
        }
        if ( users.getUserName() != null ) {
            adminUsersResponseDto.setUserName( users.getUserName() );
        }

        return adminUsersResponseDto;
    }
}
