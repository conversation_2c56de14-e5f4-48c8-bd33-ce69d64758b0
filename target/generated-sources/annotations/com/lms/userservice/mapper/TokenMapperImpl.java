package com.lms.userservice.mapper;

import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Tokens;
import com.lms.userservice.entity.Users;
import com.lms.userservice.entity.UsersTokenMapping;
import com.lms.userservice.projection.TokenProjection;
import com.lms.userservice.projection.TokenUserMappingProjection;
import com.lms.userservice.response.dto.TokenDetailsResponseDto;
import com.lms.userservice.response.dto.TokensResponseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T11:43:22+0530",
    comments = "version: 1.4.2.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250722-2055, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TokenMapperImpl implements TokenMapper {

    @Override
    public TokensResponseDto mapTokenEntityToResponse(Tokens token) {
        if ( token == null ) {
            return null;
        }

        TokensResponseDto tokensResponseDto = new TokensResponseDto();

        String id = tokenBranchesId( token );
        if ( id != null ) {
            tokensResponseDto.setBranchId( id );
        }
        String name = tokenBranchesName( token );
        if ( name != null ) {
            tokensResponseDto.setBranchName( name );
        }
        String id1 = tokenSchoolsId( token );
        if ( id1 != null ) {
            tokensResponseDto.setSchoolId( id1 );
        }
        String name1 = tokenSchoolsName( token );
        if ( name1 != null ) {
            tokensResponseDto.setSchoolName( name1 );
        }
        String code = tokenSchoolsCode( token );
        if ( code != null ) {
            tokensResponseDto.setSchoolCode( code );
        }
        List<TokenDetailsResponseDto> list = mapTokenUserMappingToResponseDto( token.getUserTokenMappings() );
        if ( list != null ) {
            tokensResponseDto.setTokenDetails( list );
        }
        tokensResponseDto.setActive( token.isActive() );
        if ( token.getExpiaryDate() != null ) {
            tokensResponseDto.setExpiaryDate( token.getExpiaryDate() );
        }
        if ( token.getId() != null ) {
            tokensResponseDto.setId( token.getId() );
        }
        tokensResponseDto.setMultiUser( token.isMultiUser() );
        if ( token.getNumberOfTokens() != null ) {
            tokensResponseDto.setNumberOfTokens( token.getNumberOfTokens() );
        }
        if ( token.getNumberOfUsersPerToken() != null ) {
            tokensResponseDto.setNumberOfUsersPerToken( token.getNumberOfUsersPerToken() );
        }
        if ( token.getRoleId() != null ) {
            tokensResponseDto.setRoleId( token.getRoleId() );
        }
        if ( token.getToken() != null ) {
            tokensResponseDto.setToken( token.getToken() );
        }
        if ( token.getTokenUseCount() != null ) {
            tokensResponseDto.setTokenUseCount( token.getTokenUseCount() );
        }

        return tokensResponseDto;
    }

    @Override
    public List<TokensResponseDto> mapTokenEntityToResponse(List<Tokens> tokens) {
        if ( tokens == null ) {
            return null;
        }

        List<TokensResponseDto> list = new ArrayList<TokensResponseDto>( tokens.size() );
        for ( Tokens tokens1 : tokens ) {
            list.add( mapTokenEntityToResponse( tokens1 ) );
        }

        return list;
    }

    @Override
    public TokenDetailsResponseDto mapTokenUserMappingToResponseDto(UsersTokenMapping userTokenMapping) {
        if ( userTokenMapping == null ) {
            return null;
        }

        TokenDetailsResponseDto tokenDetailsResponseDto = new TokenDetailsResponseDto();

        String id = userTokenMappingUsersId( userTokenMapping );
        if ( id != null ) {
            tokenDetailsResponseDto.setUserId( id );
        }
        String email = userTokenMappingUsersEmail( userTokenMapping );
        if ( email != null ) {
            tokenDetailsResponseDto.setEmail( email );
        }
        String phoneNumber = userTokenMappingUsersPhoneNumber( userTokenMapping );
        if ( phoneNumber != null ) {
            tokenDetailsResponseDto.setPhone( phoneNumber );
        }

        return tokenDetailsResponseDto;
    }

    @Override
    public List<TokenDetailsResponseDto> mapTokenUserMappingToResponseDto(List<UsersTokenMapping> userTokenMappings) {
        if ( userTokenMappings == null ) {
            return null;
        }

        List<TokenDetailsResponseDto> list = new ArrayList<TokenDetailsResponseDto>( userTokenMappings.size() );
        for ( UsersTokenMapping usersTokenMapping : userTokenMappings ) {
            list.add( mapTokenUserMappingToResponseDto( usersTokenMapping ) );
        }

        return list;
    }

    @Override
    public List<TokensResponseDto> mapTokenProjectionToResponse(List<TokenProjection> tokenProjection) {
        if ( tokenProjection == null ) {
            return null;
        }

        List<TokensResponseDto> list = new ArrayList<TokensResponseDto>( tokenProjection.size() );
        for ( TokenProjection tokenProjection1 : tokenProjection ) {
            list.add( tokenProjectionToTokensResponseDto( tokenProjection1 ) );
        }

        return list;
    }

    @Override
    public List<TokenDetailsResponseDto> mapTokenUserMappingToTokenDetailsDto(List<TokenUserMappingProjection> tokenUserMappingProjections) {
        if ( tokenUserMappingProjections == null ) {
            return null;
        }

        List<TokenDetailsResponseDto> list = new ArrayList<TokenDetailsResponseDto>( tokenUserMappingProjections.size() );
        for ( TokenUserMappingProjection tokenUserMappingProjection : tokenUserMappingProjections ) {
            list.add( tokenUserMappingProjectionToTokenDetailsResponseDto( tokenUserMappingProjection ) );
        }

        return list;
    }

    private String tokenBranchesId(Tokens tokens) {
        if ( tokens == null ) {
            return null;
        }
        Branches branches = tokens.getBranches();
        if ( branches == null ) {
            return null;
        }
        String id = branches.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String tokenBranchesName(Tokens tokens) {
        if ( tokens == null ) {
            return null;
        }
        Branches branches = tokens.getBranches();
        if ( branches == null ) {
            return null;
        }
        String name = branches.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String tokenSchoolsId(Tokens tokens) {
        if ( tokens == null ) {
            return null;
        }
        Schools schools = tokens.getSchools();
        if ( schools == null ) {
            return null;
        }
        String id = schools.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String tokenSchoolsName(Tokens tokens) {
        if ( tokens == null ) {
            return null;
        }
        Schools schools = tokens.getSchools();
        if ( schools == null ) {
            return null;
        }
        String name = schools.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String tokenSchoolsCode(Tokens tokens) {
        if ( tokens == null ) {
            return null;
        }
        Schools schools = tokens.getSchools();
        if ( schools == null ) {
            return null;
        }
        String code = schools.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String userTokenMappingUsersId(UsersTokenMapping usersTokenMapping) {
        if ( usersTokenMapping == null ) {
            return null;
        }
        Users users = usersTokenMapping.getUsers();
        if ( users == null ) {
            return null;
        }
        String id = users.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String userTokenMappingUsersEmail(UsersTokenMapping usersTokenMapping) {
        if ( usersTokenMapping == null ) {
            return null;
        }
        Users users = usersTokenMapping.getUsers();
        if ( users == null ) {
            return null;
        }
        String email = users.getEmail();
        if ( email == null ) {
            return null;
        }
        return email;
    }

    private String userTokenMappingUsersPhoneNumber(UsersTokenMapping usersTokenMapping) {
        if ( usersTokenMapping == null ) {
            return null;
        }
        Users users = usersTokenMapping.getUsers();
        if ( users == null ) {
            return null;
        }
        String phoneNumber = users.getPhoneNumber();
        if ( phoneNumber == null ) {
            return null;
        }
        return phoneNumber;
    }

    protected TokensResponseDto tokenProjectionToTokensResponseDto(TokenProjection tokenProjection) {
        if ( tokenProjection == null ) {
            return null;
        }

        TokensResponseDto tokensResponseDto = new TokensResponseDto();

        if ( tokenProjection.getExpiaryDate() != null ) {
            tokensResponseDto.setExpiaryDate( tokenProjection.getExpiaryDate() );
        }
        if ( tokenProjection.getId() != null ) {
            tokensResponseDto.setId( tokenProjection.getId() );
        }
        if ( tokenProjection.getMultiUser() != null ) {
            tokensResponseDto.setMultiUser( tokenProjection.getMultiUser() );
        }
        if ( tokenProjection.getNumberOfUsersPerToken() != null ) {
            tokensResponseDto.setNumberOfUsersPerToken( tokenProjection.getNumberOfUsersPerToken() );
        }
        if ( tokenProjection.getRoleId() != null ) {
            tokensResponseDto.setRoleId( tokenProjection.getRoleId() );
        }
        if ( tokenProjection.getToken() != null ) {
            tokensResponseDto.setToken( tokenProjection.getToken() );
        }

        return tokensResponseDto;
    }

    protected TokenDetailsResponseDto tokenUserMappingProjectionToTokenDetailsResponseDto(TokenUserMappingProjection tokenUserMappingProjection) {
        if ( tokenUserMappingProjection == null ) {
            return null;
        }

        TokenDetailsResponseDto tokenDetailsResponseDto = new TokenDetailsResponseDto();

        if ( tokenUserMappingProjection.getEmail() != null ) {
            tokenDetailsResponseDto.setEmail( tokenUserMappingProjection.getEmail() );
        }
        if ( tokenUserMappingProjection.getPhone() != null ) {
            tokenDetailsResponseDto.setPhone( tokenUserMappingProjection.getPhone() );
        }
        if ( tokenUserMappingProjection.getUserId() != null ) {
            tokenDetailsResponseDto.setUserId( tokenUserMappingProjection.getUserId() );
        }
        if ( tokenUserMappingProjection.getUserName() != null ) {
            tokenDetailsResponseDto.setUserName( tokenUserMappingProjection.getUserName() );
        }

        return tokenDetailsResponseDto;
    }
}
