package com.lms.userservice.mapper;

import com.lms.userservice.entity.Branches;
import com.lms.userservice.entity.Schools;
import com.lms.userservice.entity.Students;
import com.lms.userservice.projection.StudentsProjection;
import com.lms.userservice.response.dto.StudentsResponseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T11:43:22+0530",
    comments = "version: 1.4.2.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250722-2055, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class StudentMapperImpl implements StudentMapper {

    @Override
    public StudentsResponseDto mapStudentToResponseDto(Students student) {
        if ( student == null ) {
            return null;
        }

        StudentsResponseDto studentsResponseDto = new StudentsResponseDto();

        String id = studentSchoolsId( student );
        if ( id != null ) {
            studentsResponseDto.setSchoolId( id );
        }
        String name = studentSchoolsName( student );
        if ( name != null ) {
            studentsResponseDto.setSchoolName( name );
        }
        String code = studentSchoolsCode( student );
        if ( code != null ) {
            studentsResponseDto.setSchoolCode( code );
        }
        String id1 = studentBranchesId( student );
        if ( id1 != null ) {
            studentsResponseDto.setBranchId( id1 );
        }
        String name1 = studentBranchesName( student );
        if ( name1 != null ) {
            studentsResponseDto.setBranchName( name1 );
        }
        studentsResponseDto.setActive( student.isActive() );
        if ( student.getAddress() != null ) {
            studentsResponseDto.setAddress( student.getAddress() );
        }
        if ( student.getAdmissionDate() != null ) {
            studentsResponseDto.setAdmissionDate( student.getAdmissionDate() );
        }
        if ( student.getDiagnosticTest() != null ) {
            studentsResponseDto.setDiagnosticTest( student.getDiagnosticTest() );
        }
        if ( student.getDob() != null ) {
            studentsResponseDto.setDob( student.getDob() );
        }
        if ( student.getDocumentUrl() != null ) {
            studentsResponseDto.setDocumentUrl( student.getDocumentUrl() );
        }
        if ( student.getEmail() != null ) {
            studentsResponseDto.setEmail( student.getEmail() );
        }
        if ( student.getFirstLanguageId() != null ) {
            studentsResponseDto.setFirstLanguageId( student.getFirstLanguageId() );
        }
        if ( student.getFirstName() != null ) {
            studentsResponseDto.setFirstName( student.getFirstName() );
        }
        if ( student.getGender() != null ) {
            studentsResponseDto.setGender( student.getGender().name() );
        }
        if ( student.getGeographicalType() != null ) {
            studentsResponseDto.setGeographicalType( student.getGeographicalType() );
        }
        if ( student.getGradeId() != null ) {
            studentsResponseDto.setGradeId( student.getGradeId() );
        }
        if ( student.getGradeLevel() != null ) {
            studentsResponseDto.setGradeLevel( student.getGradeLevel() );
        }
        if ( student.getId() != null ) {
            studentsResponseDto.setId( student.getId() );
        }
        if ( student.getIrStatus() != null ) {
            studentsResponseDto.setIrStatus( student.getIrStatus() );
        }
        if ( student.getIsPromoted() != null ) {
            studentsResponseDto.setIsPromoted( student.getIsPromoted() );
        }
        if ( student.getLastName() != null ) {
            studentsResponseDto.setLastName( student.getLastName() );
        }
        if ( student.getLearningGeneration() != null ) {
            studentsResponseDto.setLearningGeneration( student.getLearningGeneration() );
        }
        if ( student.getMobile() != null ) {
            studentsResponseDto.setMobile( student.getMobile() );
        }
        if ( student.getSecondLanguageId() != null ) {
            studentsResponseDto.setSecondLanguageId( student.getSecondLanguageId() );
        }
        if ( student.getSectionId() != null ) {
            studentsResponseDto.setSectionId( student.getSectionId() );
        }
        if ( student.getStudentCategoryId() != null ) {
            studentsResponseDto.setStudentCategoryId( student.getStudentCategoryId() );
        }
        if ( student.getUserName() != null ) {
            studentsResponseDto.setUserName( student.getUserName() );
        }
        if ( student.getYearEndProcess() != null ) {
            studentsResponseDto.setYearEndProcess( student.getYearEndProcess() );
        }

        return studentsResponseDto;
    }

    @Override
    public List<StudentsResponseDto> mapStudentToResponseDto(List<Students> students) {
        if ( students == null ) {
            return null;
        }

        List<StudentsResponseDto> list = new ArrayList<StudentsResponseDto>( students.size() );
        for ( Students students1 : students ) {
            list.add( mapStudentToResponseDto( students1 ) );
        }

        return list;
    }

    @Override
    public StudentsResponseDto mapStudentProjectionToResponseDto(StudentsProjection studentProjection) {
        if ( studentProjection == null ) {
            return null;
        }

        StudentsResponseDto studentsResponseDto = new StudentsResponseDto();

        studentsResponseDto.setActive( studentProjection.isActive() );
        if ( studentProjection.getAddress() != null ) {
            studentsResponseDto.setAddress( studentProjection.getAddress() );
        }
        if ( studentProjection.getAdmissionDate() != null ) {
            studentsResponseDto.setAdmissionDate( studentProjection.getAdmissionDate() );
        }
        if ( studentProjection.getBoardId() != null ) {
            studentsResponseDto.setBoardId( studentProjection.getBoardId() );
        }
        if ( studentProjection.getBranchId() != null ) {
            studentsResponseDto.setBranchId( studentProjection.getBranchId() );
        }
        if ( studentProjection.getBranchName() != null ) {
            studentsResponseDto.setBranchName( studentProjection.getBranchName() );
        }
        if ( studentProjection.getDiagnosticTest() != null ) {
            studentsResponseDto.setDiagnosticTest( studentProjection.getDiagnosticTest() );
        }
        if ( studentProjection.getDob() != null ) {
            studentsResponseDto.setDob( studentProjection.getDob() );
        }
        if ( studentProjection.getDocumentUrl() != null ) {
            studentsResponseDto.setDocumentUrl( studentProjection.getDocumentUrl() );
        }
        if ( studentProjection.getEmail() != null ) {
            studentsResponseDto.setEmail( studentProjection.getEmail() );
        }
        if ( studentProjection.getFirstLanguageId() != null ) {
            studentsResponseDto.setFirstLanguageId( studentProjection.getFirstLanguageId() );
        }
        if ( studentProjection.getFirstName() != null ) {
            studentsResponseDto.setFirstName( studentProjection.getFirstName() );
        }
        if ( studentProjection.getGender() != null ) {
            studentsResponseDto.setGender( studentProjection.getGender() );
        }
        if ( studentProjection.getGeographicalType() != null ) {
            studentsResponseDto.setGeographicalType( studentProjection.getGeographicalType() );
        }
        if ( studentProjection.getGradeId() != null ) {
            studentsResponseDto.setGradeId( studentProjection.getGradeId() );
        }
        if ( studentProjection.getGradeLevel() != null ) {
            studentsResponseDto.setGradeLevel( studentProjection.getGradeLevel() );
        }
        if ( studentProjection.getId() != null ) {
            studentsResponseDto.setId( studentProjection.getId() );
        }
        if ( studentProjection.getIrStatus() != null ) {
            studentsResponseDto.setIrStatus( studentProjection.getIrStatus() );
        }
        if ( studentProjection.getIsPromoted() != null ) {
            studentsResponseDto.setIsPromoted( studentProjection.getIsPromoted() );
        }
        if ( studentProjection.getLastLoginTime() != null ) {
            studentsResponseDto.setLastLoginTime( studentProjection.getLastLoginTime() );
        }
        if ( studentProjection.getLastName() != null ) {
            studentsResponseDto.setLastName( studentProjection.getLastName() );
        }
        if ( studentProjection.getLearningGeneration() != null ) {
            studentsResponseDto.setLearningGeneration( studentProjection.getLearningGeneration() );
        }
        if ( studentProjection.getMobile() != null ) {
            studentsResponseDto.setMobile( studentProjection.getMobile() );
        }
        if ( studentProjection.getSchoolCode() != null ) {
            studentsResponseDto.setSchoolCode( studentProjection.getSchoolCode() );
        }
        if ( studentProjection.getSchoolId() != null ) {
            studentsResponseDto.setSchoolId( studentProjection.getSchoolId() );
        }
        if ( studentProjection.getSchoolName() != null ) {
            studentsResponseDto.setSchoolName( studentProjection.getSchoolName() );
        }
        if ( studentProjection.getSecondLanguageId() != null ) {
            studentsResponseDto.setSecondLanguageId( studentProjection.getSecondLanguageId() );
        }
        if ( studentProjection.getSectionId() != null ) {
            studentsResponseDto.setSectionId( studentProjection.getSectionId() );
        }
        if ( studentProjection.getStudentCategoryId() != null ) {
            studentsResponseDto.setStudentCategoryId( studentProjection.getStudentCategoryId() );
        }
        if ( studentProjection.getUserId() != null ) {
            studentsResponseDto.setUserId( studentProjection.getUserId() );
        }
        if ( studentProjection.getUserName() != null ) {
            studentsResponseDto.setUserName( studentProjection.getUserName() );
        }
        if ( studentProjection.getYearEndProcess() != null ) {
            studentsResponseDto.setYearEndProcess( studentProjection.getYearEndProcess() );
        }

        return studentsResponseDto;
    }

    private String studentSchoolsId(Students students) {
        if ( students == null ) {
            return null;
        }
        Schools schools = students.getSchools();
        if ( schools == null ) {
            return null;
        }
        String id = schools.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String studentSchoolsName(Students students) {
        if ( students == null ) {
            return null;
        }
        Schools schools = students.getSchools();
        if ( schools == null ) {
            return null;
        }
        String name = schools.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String studentSchoolsCode(Students students) {
        if ( students == null ) {
            return null;
        }
        Schools schools = students.getSchools();
        if ( schools == null ) {
            return null;
        }
        String code = schools.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String studentBranchesId(Students students) {
        if ( students == null ) {
            return null;
        }
        Branches branches = students.getBranches();
        if ( branches == null ) {
            return null;
        }
        String id = branches.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    private String studentBranchesName(Students students) {
        if ( students == null ) {
            return null;
        }
        Branches branches = students.getBranches();
        if ( branches == null ) {
            return null;
        }
        String name = branches.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }
}
