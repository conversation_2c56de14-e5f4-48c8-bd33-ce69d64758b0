<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.lms.userservice.UserServiceApplicationTests" time="8.998" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="awt.toolkit" value="sun.awt.X11.XToolkit"/>
    <property name="java.specification.version" value="11"/>
    <property name="sun.cpu.isalist" value=""/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/test-classes:/home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/classes:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.6.6/spring-boot-starter-web-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.6.6/spring-boot-starter-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.6.6/spring-boot-starter-logging-2.6.6.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.11/logback-classic-1.2.11.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.11/logback-core-1.2.11.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.29/snakeyaml-1.29.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.6.6/spring-boot-starter-json-2.6.6.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.2/jackson-datatype-jdk8-2.13.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.2/jackson-datatype-jsr310-2.13.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.2/jackson-module-parameter-names-2.13.2.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.6.6/spring-boot-starter-tomcat-2.6.6.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.60/tomcat-embed-core-9.0.60.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.60/tomcat-embed-websocket-9.0.60.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/5.3.18/spring-web-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/5.3.18/spring-beans-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.18/spring-webmvc-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/5.3.18/spring-context-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/5.3.18/spring-expression-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web-services/2.6.6/spring-boot-starter-web-services-2.6.6.jar:/home/<USER>/.m2/repository/com/sun/xml/messaging/saaj/saaj-impl/1.5.3/saaj-impl-1.5.3.jar:/home/<USER>/.m2/repository/jakarta/xml/soap/jakarta.xml.soap-api/1.4.2/jakarta.xml.soap-api-1.4.2.jar:/home/<USER>/.m2/repository/org/jvnet/staxex/stax-ex/1.8.3/stax-ex-1.8.3.jar:/home/<USER>/.m2/repository/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2.jar:/home/<USER>/.m2/repository/jakarta/xml/ws/jakarta.xml.ws-api/2.3.3/jakarta.xml.ws-api-2.3.3.jar:/home/<USER>/.m2/repository/jakarta/jws/jakarta.jws-api/2.1.0/jakarta.jws-api-2.1.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.18/spring-oxm-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/ws/spring-ws-core/3.1.3/spring-ws-core-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/ws/spring-xml/3.1.3/spring-xml-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.6.6/spring-boot-starter-validation-2.6.6.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.60/tomcat-embed-el-9.0.60.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.3.Final/hibernate-validator-6.2.3.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.6.6/spring-boot-starter-test-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.6.6/spring-boot-test-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.6.6/spring-boot-test-autoconfigure-2.6.6.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.6.0/json-path-2.6.0.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.4.8/json-smart-2.4.8.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.8/accessors-smart-2.4.8.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.1/asm-9.1.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.21.0/assertj-core-3.21.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/4.0.0/mockito-core-4.0.0.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.22/byte-buddy-1.11.22.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.22/byte-buddy-agent-1.11.22.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.0.0/mockito-junit-jupiter-4.0.0.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.0/jsonassert-1.5.0.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/5.3.18/spring-core-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.18/spring-jcl-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/5.3.18/spring-test-5.3.18.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.8.4/xmlunit-core-2.8.4.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/2.6.6/spring-boot-starter-data-jpa-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.6.6/spring-boot-starter-jdbc-2.6.6.jar:/home/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.18/spring-jdbc-5.3.18.jar:/home/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/1.3.3/jakarta.transaction-api-1.3.3.jar:/home/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/2.2.3/jakarta.persistence-api-2.2.3.jar:/home/<USER>/.m2/repository/org/hibernate/hibernate-core/5.6.7.Final/hibernate-core-5.6.7.Final.jar:/home/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/home/<USER>/.m2/repository/org/jboss/jandex/2.4.2.Final/jandex-2.4.2.Final.jar:/home/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.1.2.Final/hibernate-commons-annotations-5.1.2.Final.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/2.3.6/jaxb-runtime-2.3.6.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/txw2/2.3.6/txw2-2.3.6.jar:/home/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/3.0.12/istack-commons-runtime-3.0.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/2.6.3/spring-data-jpa-2.6.3.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.6.3/spring-data-commons-2.6.3.jar:/home/<USER>/.m2/repository/org/springframework/spring-orm/5.3.18/spring-orm-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-tx/5.3.18/spring-tx-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-aspects/5.3.18/spring-aspects-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/2.6.6/spring-boot-devtools-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.6.6/spring-boot-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.6.6/spring-boot-autoconfigure-2.6.6.jar:/home/<USER>/.m2/repository/org/projectlombok/lombok/1.18.22/lombok-1.18.22.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.6.6/spring-boot-starter-security-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/5.3.18/spring-aop-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.6.2/spring-security-config-5.6.2.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.6.2/spring-security-web-5.6.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/home/<USER>/.m2/repository/com/opencsv/opencsv/5.5.2/opencsv-5.5.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-text/1.9/commons-text-1.9.jar:/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/home/<USER>/.m2/repository/au/com/bytecode/opencsv/2.4/opencsv-2.4.jar:/home/<USER>/.m2/repository/org/mapstruct/mapstruct/1.4.2.Final/mapstruct-1.4.2.Final.jar:/home/<USER>/.m2/repository/org/modelmapper/modelmapper/2.4.4/modelmapper-2.4.4.jar:/home/<USER>/.m2/repository/org/passay/passay/1.6.0/passay-1.6.0.jar:/home/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.2.2/jackson-databind-2.13.2.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.2/jackson-annotations-2.13.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.2/jackson-core-2.13.2.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.7.0/springfox-swagger-ui-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.jar:/home/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/home/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar:/home/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar:/home/<USER>/.m2/repository/org/json/json/20211205/json-20211205.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.6.6/spring-boot-starter-aop-2.6.6.jar:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.7/aspectjweaver-1.9.7.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar:/home/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar:/home/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-2.7.0.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/3.1.1/spring-cloud-starter-openfeign-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/3.1.1/spring-cloud-starter-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/3.1.1/spring-cloud-context-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.0.10.RELEASE/spring-security-rsa-1.0.10.RELEASE.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.68/bcpkix-jdk15on-1.68.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.68/bcprov-jdk15on-1.68.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-openfeign-core/3.1.1/spring-cloud-openfeign-core-3.1.1.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.4/commons-fileupload-1.4.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/3.1.1/spring-cloud-commons-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.6.2/spring-security-crypto-5.6.2.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-core/11.8/feign-core-11.8.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-slf4j/11.8/feign-slf4j-11.8.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-httpclient/13.2.1/feign-httpclient-13.2.1.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.15/httpcore-4.4.15.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-test/5.6.2/spring-security-test-5.6.2.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.6.2/spring-security-core-5.6.2.jar:/home/<USER>/.m2/repository/org/postgresql/postgresql/42.3.3/postgresql-42.3.3.jar:/home/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar:/home/<USER>/.m2/repository/com/vladmihalcea/hibernate-types-5/1.1.1/hibernate-types-5-1.1.1.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://openjdk.java.net/"/>
    <property name="user.timezone" value=""/>
    <property name="java.vm.specification.version" value="11"/>
    <property name="os.name" value="Linux"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="IN"/>
    <property name="sun.boot.library.path" value="/nix/store/2cmvk7jlpkwks1al541c28gsbvns3bq3-openjdk-11.0.26+4/lib/openjdk/lib"/>
    <property name="sun.java.command" value="/home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/surefire/surefirebooter8010104933833219601.jar /home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/surefire 2025-07-25T17-29-16_845-jvmRun1 surefire17762110442646460320tmp surefire_010050405986137298465tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/test-classes:/home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/classes:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.6.6/spring-boot-starter-web-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.6.6/spring-boot-starter-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.6.6/spring-boot-starter-logging-2.6.6.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.11/logback-classic-1.2.11.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.11/logback-core-1.2.11.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.29/snakeyaml-1.29.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.6.6/spring-boot-starter-json-2.6.6.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.2/jackson-datatype-jdk8-2.13.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.2/jackson-datatype-jsr310-2.13.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.2/jackson-module-parameter-names-2.13.2.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.6.6/spring-boot-starter-tomcat-2.6.6.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.60/tomcat-embed-core-9.0.60.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.60/tomcat-embed-websocket-9.0.60.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/5.3.18/spring-web-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/5.3.18/spring-beans-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.18/spring-webmvc-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/5.3.18/spring-context-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/5.3.18/spring-expression-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web-services/2.6.6/spring-boot-starter-web-services-2.6.6.jar:/home/<USER>/.m2/repository/com/sun/xml/messaging/saaj/saaj-impl/1.5.3/saaj-impl-1.5.3.jar:/home/<USER>/.m2/repository/jakarta/xml/soap/jakarta.xml.soap-api/1.4.2/jakarta.xml.soap-api-1.4.2.jar:/home/<USER>/.m2/repository/org/jvnet/staxex/stax-ex/1.8.3/stax-ex-1.8.3.jar:/home/<USER>/.m2/repository/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2.jar:/home/<USER>/.m2/repository/jakarta/xml/ws/jakarta.xml.ws-api/2.3.3/jakarta.xml.ws-api-2.3.3.jar:/home/<USER>/.m2/repository/jakarta/jws/jakarta.jws-api/2.1.0/jakarta.jws-api-2.1.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.18/spring-oxm-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/ws/spring-ws-core/3.1.3/spring-ws-core-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/ws/spring-xml/3.1.3/spring-xml-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.6.6/spring-boot-starter-validation-2.6.6.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.60/tomcat-embed-el-9.0.60.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.3.Final/hibernate-validator-6.2.3.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.6.6/spring-boot-starter-test-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.6.6/spring-boot-test-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.6.6/spring-boot-test-autoconfigure-2.6.6.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.6.0/json-path-2.6.0.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.4.8/json-smart-2.4.8.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.8/accessors-smart-2.4.8.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.1/asm-9.1.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.21.0/assertj-core-3.21.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/4.0.0/mockito-core-4.0.0.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.22/byte-buddy-1.11.22.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.22/byte-buddy-agent-1.11.22.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.0.0/mockito-junit-jupiter-4.0.0.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.0/jsonassert-1.5.0.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/5.3.18/spring-core-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.18/spring-jcl-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/5.3.18/spring-test-5.3.18.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.8.4/xmlunit-core-2.8.4.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/2.6.6/spring-boot-starter-data-jpa-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.6.6/spring-boot-starter-jdbc-2.6.6.jar:/home/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.18/spring-jdbc-5.3.18.jar:/home/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/1.3.3/jakarta.transaction-api-1.3.3.jar:/home/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/2.2.3/jakarta.persistence-api-2.2.3.jar:/home/<USER>/.m2/repository/org/hibernate/hibernate-core/5.6.7.Final/hibernate-core-5.6.7.Final.jar:/home/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/home/<USER>/.m2/repository/org/jboss/jandex/2.4.2.Final/jandex-2.4.2.Final.jar:/home/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.1.2.Final/hibernate-commons-annotations-5.1.2.Final.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/2.3.6/jaxb-runtime-2.3.6.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/txw2/2.3.6/txw2-2.3.6.jar:/home/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/3.0.12/istack-commons-runtime-3.0.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/2.6.3/spring-data-jpa-2.6.3.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.6.3/spring-data-commons-2.6.3.jar:/home/<USER>/.m2/repository/org/springframework/spring-orm/5.3.18/spring-orm-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-tx/5.3.18/spring-tx-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/spring-aspects/5.3.18/spring-aspects-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/2.6.6/spring-boot-devtools-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.6.6/spring-boot-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.6.6/spring-boot-autoconfigure-2.6.6.jar:/home/<USER>/.m2/repository/org/projectlombok/lombok/1.18.22/lombok-1.18.22.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.6.6/spring-boot-starter-security-2.6.6.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/5.3.18/spring-aop-5.3.18.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.6.2/spring-security-config-5.6.2.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.6.2/spring-security-web-5.6.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/home/<USER>/.m2/repository/com/opencsv/opencsv/5.5.2/opencsv-5.5.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-text/1.9/commons-text-1.9.jar:/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/home/<USER>/.m2/repository/au/com/bytecode/opencsv/2.4/opencsv-2.4.jar:/home/<USER>/.m2/repository/org/mapstruct/mapstruct/1.4.2.Final/mapstruct-1.4.2.Final.jar:/home/<USER>/.m2/repository/org/modelmapper/modelmapper/2.4.4/modelmapper-2.4.4.jar:/home/<USER>/.m2/repository/org/passay/passay/1.6.0/passay-1.6.0.jar:/home/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.2.2/jackson-databind-2.13.2.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.2/jackson-annotations-2.13.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.2/jackson-core-2.13.2.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.7.0/springfox-swagger-ui-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.jar:/home/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/home/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar:/home/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar:/home/<USER>/.m2/repository/org/json/json/20211205/json-20211205.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.6.6/spring-boot-starter-aop-2.6.6.jar:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.7/aspectjweaver-1.9.7.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar:/home/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar:/home/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-2.7.0.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/3.1.1/spring-cloud-starter-openfeign-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/3.1.1/spring-cloud-starter-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/3.1.1/spring-cloud-context-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.0.10.RELEASE/spring-security-rsa-1.0.10.RELEASE.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.68/bcpkix-jdk15on-1.68.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.68/bcprov-jdk15on-1.68.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-openfeign-core/3.1.1/spring-cloud-openfeign-core-3.1.1.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.4/commons-fileupload-1.4.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/3.1.1/spring-cloud-commons-3.1.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.6.2/spring-security-crypto-5.6.2.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-core/11.8/feign-core-11.8.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-slf4j/11.8/feign-slf4j-11.8.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-httpclient/13.2.1/feign-httpclient-13.2.1.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.15/httpcore-4.4.15.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-test/5.6.2/spring-security-test-5.6.2.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.6.2/spring-security-core-5.6.2.jar:/home/<USER>/.m2/repository/org/postgresql/postgresql/42.3.3/postgresql-42.3.3.jar:/home/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar:/home/<USER>/.m2/repository/com/vladmihalcea/hibernate-types-5/1.1.1/hibernate-types-5-1.1.1.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/home/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/nix/store/2cmvk7jlpkwks1al541c28gsbvns3bq3-openjdk-11.0.26+4/lib/openjdk"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/home/<USER>/Azwasa/lms/backend_registration/backend_registration"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.awt.graphicsenv" value="sun.awt.X11GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="/home/<USER>/Azwasa/lms/backend_registration/backend_registration/target/surefire/surefirebooter8010104933833219601.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="11.0.26+0-adhoc..source"/>
    <property name="user.name" value="world"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="6.14.8"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="/home/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/tmp"/>
    <property name="java.version" value="11.0.26"/>
    <property name="user.dir" value="/home/<USER>/Azwasa/lms/backend_registration/backend_registration"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.print.PSPrinterJob"/>
    <property name="sun.os.patch.level" value="unknown"/>
    <property name="java.library.path" value=""/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="11.0.26+0-adhoc..source"/>
    <property name="java.specification.maintenance.version" value="3"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="55.0"/>
  </properties>
  <testcase name="contextLoads" classname="com.lms.userservice.UserServiceApplicationTests" time="0.075"/>
</testsuite>